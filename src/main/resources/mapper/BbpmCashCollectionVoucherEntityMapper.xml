<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmCashCollectionVoucherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmCashCollectionVoucherEntity">
                            <id column="voucher_id" property="voucherId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="voucher_number" property="voucherNumber" javaType="String"/>
                              <result column="bill_id" property="billId" javaType="String"/>
                            <result column="collection_no" property="collectionNo" javaType="String"/>
                            <result column="paid_in_amount" property="paidInAmount" javaType="BigDecimal"/>
                            <result column="issuing_time" property="issuingTime" javaType="Date"/>
                            <result column="credential_file_address" property="credentialFileAddress" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="status" property="status" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="sign_status" property="signStatus" javaType="String"/>
        <result column="request_params" property="requestParams" javaType="String"/>
        <result column="opener" property="opener" javaType="String"/>
        <result column="project_format" property="projectFormat" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmCashCollectionVoucherPageResultVo">
        <result column="voucher_number" property="voucherNumber" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="collection_no" property="collectionNo" javaType="String"/>
        <result column="paid_in_amount" property="paidInAmount" javaType="BigDecimal"/>
        <result column="issuing_time" property="issuingTime" javaType="Date"/>
        <result column="credential_file_address" property="credentialFileAddress" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="status" property="status" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="sign_status" property="signStatus" javaType="String"/>
        <result column="request_params" property="requestParams" javaType="String"/>
        <result column="opener" property="opener" javaType="String"/>
        <result column="project_format" property="projectFormat" javaType="String"/>
    </resultMap>

    <resultMap id="QueryResultMapV2" type="com.bonc.ioc.bzf.business.payment.vo.BbpmCashCollectionVoucherVo">
        <result column="voucher_number" property="voucherNumber" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="collection_no" property="collectionNo" javaType="String"/>
        <result column="paid_in_amount" property="paidInAmount" javaType="BigDecimal"/>
        <result column="issuing_time" property="issuingTime" javaType="String"/>
        <result column="credential_file_address" property="credentialFileAddress" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="status" property="status" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="sign_status" property="signStatus" javaType="String"/>
        <result column="request_params" property="requestParams" javaType="String"/>
        <result column="opener" property="opener" javaType="String"/>
        <result column="project_format" property="projectFormat" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.voucher_id
        ,base.voucher_number
        ,base.collection_no
        ,base.bill_id
        ,ROUND(base.paid_in_amount, 2) paid_in_amount
        ,base.issuing_time
        ,base.credential_file_address
        ,base.del_flag
        ,base.status
            ,base.project_id
            ,base.contract_code
            ,base.sign_status
            ,base.request_params
            ,base.opener
            ,base.project_format
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_cash_collection_voucher base
        <where>
            base.del_flag='1'
            <if test="'' != vo.voucherId and vo.voucherId != null">
                and base.voucher_id = #{vo.voucherId}
            </if>
            <if test="'' != vo.voucherNumber and vo.voucherNumber != null">
                and base.voucher_number like CONCAT('%',#{vo.voucherNumber},'%')
            </if>
            <if test="'' != vo.collectionNo and vo.collectionNo != null">
                AND (FIND_IN_SET(#{vo.collectionNo},base.collection_no)
                    or
                    base.collection_no=#{vo.collectionNo}
                )
            </if>
            <if test="vo.paidInAmount != null">
                and ROUND(base.paid_in_amount, 2)  like CONCAT('%',#{vo.paidInAmount},'%')
            </if>
            <if test="vo.issuingTime != null">
                and substring(base.issuing_time,1,10) = #{vo.issuingTime}
            </if>
            <if test="'' != vo.credentialFileAddress and vo.credentialFileAddress != null">
                and base.credential_file_address = #{vo.credentialFileAddress}
            </if>
            <if test="'' != vo.status and vo.status != null">
                and base.status = #{vo.status}
            </if>
            <if test="'' != vo.signStatus and vo.signStatus != null">
                and base.sign_status = #{vo.signStatus}
            </if>
            <if test="'' != vo.opener and vo.opener != null">
                and base.opener = #{vo.opener}
            </if>
            <choose>
                <when  test="vo.projectFormat =='03'.toString()">
                    and base.project_format = #{vo.projectFormat}
                </when>
                <otherwise>
                    and (base.project_format is  null or base.project_format='' or base.project_format != '03')
                </otherwise>
            </choose>
        </where>
        order by base.issuing_time desc
    </select>



    <select id="selectByCollectionNo" resultMap="QueryResultMapV2">
        select
        <include refid="Base_Column_List"/>
        from bbpm_cash_collection_voucher base
        <where>
            base.del_flag='1'
            <if test="'' != vo.collectionNo and vo.collectionNo != null">
                AND (FIND_IN_SET(#{vo.collectionNo},base.collection_no)
                     or
                     base.collection_no=#{vo.collectionNo}
                    )
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                AND FIND_IN_SET(#{vo.billId},base.bill_id)
            </if>
            <if test="'' != vo.status and vo.status != null">
                and base.status = #{vo.status}
            </if>
            <if test="'' != vo.signStatus and vo.signStatus != null">
                and base.sign_status = #{vo.signStatus}
            </if>
        </where>
    </select>



</mapper>
