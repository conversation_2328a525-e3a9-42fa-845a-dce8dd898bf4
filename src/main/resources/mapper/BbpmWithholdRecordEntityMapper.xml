<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmWithholdRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmWithholdRecordEntity">
                            <id column="batch_no" property="batchNo" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="project_name" property="projectName" javaType="String"/>
                            <result column="tenant_num" property="tenantNum" javaType="Long"/>
                            <result column="bill_total_num" property="billTotalNum" javaType="Long"/>
                            <result column="bill_success_num" property="billSuccessNum" javaType="Long"/>
                            <result column="bill_failed_num" property="billFailedNum" javaType="Long"/>
                            <result column="bill_deaing_num" property="billDeaingNum" javaType="Long"/>
                            <result column="applicant" property="applicant" javaType="String"/>
                            <result column="apply_time" property="applyTime" javaType="String"/>
                            <result column="biz_type" property="bizType" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmWithholdRecordPageResultVo">
                        <result column="project_name" property="projectName" javaType="String"/>
                        <result column="tenant_num" property="tenantNum" javaType="Long"/>
                        <result column="bill_total_num" property="billTotalNum" javaType="Long"/>
                        <result column="bill_success_num" property="billSuccessNum" javaType="Long"/>
                        <result column="bill_failed_num" property="billFailedNum" javaType="Long"/>
                        <result column="bill_deaing_num" property="billDeaingNum" javaType="Long"/>
                        <result column="applicant" property="applicant" javaType="String"/>
                        <result column="apply_time" property="applyTime" javaType="String"/>
                        <result column="biz_type" property="bizType" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.batch_no
        ,base.project_name
        ,base.tenant_num
        ,base.bill_total_num
        ,base.bill_success_num
        ,base.bill_failed_num
        ,base.bill_deaing_num
        ,base.applicant
        ,base.apply_time
        ,base.biz_type
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_withhold_record base
        <where>
            <if test="'' != vo.batchNo and vo.batchNo != null">
                and base.batch_no = #{vo.batchNo}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name = #{vo.projectName}
            </if>
            <if test="vo.tenantNum != null">
                and base.tenant_num = #{vo.tenantNum}
            </if>
            <if test="vo.billTotalNum != null">
                and base.bill_total_num = #{vo.billTotalNum}
            </if>
            <if test="vo.billSuccessNum != null">
                and base.bill_success_num = #{vo.billSuccessNum}
            </if>
            <if test="vo.billFailedNum != null">
                and base.bill_failed_num = #{vo.billFailedNum}
            </if>
            <if test="vo.billDeaingNum != null">
                and base.bill_deaing_num = #{vo.billDeaingNum}
            </if>
            <if test="'' != vo.applicant and vo.applicant != null">
                and base.applicant = #{vo.applicant}
            </if>
            <if test="'' != vo.applyTime and vo.applyTime != null">
                and base.apply_time = #{vo.applyTime}
            </if>
            <if test="'' != vo.bizType and vo.bizType != null">
                and base.biz_type = #{vo.bizType}
            </if>
        </where>
    </select>
</mapper>
