<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.reminder.dao.BbpmCollectionWhitelistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.reminder.entity.BbpmCollectionWhitelistEntity">
                            <id column="white_id" property="whiteId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="person_customer_id" property="personCustomerId" javaType="String"/>
                            <result column="person_customer_name" property="personCustomerName" javaType="String"/>
                            <result column="nation_code" property="nationCode" javaType="String"/>
                            <result column="certificate_type_code" property="certificateTypeCode" javaType="String"/>
                            <result column="certificate_num" property="certificateNum" javaType="String"/>
                            <result column="gender_code" property="genderCode" javaType="String"/>
                            <result column="phone" property="phone" javaType="String"/>
                            <result column="operate_project_id" property="operateProjectId" javaType="String"/>
                            <result column="project_name" property="projectName" javaType="String"/>
                            <result column="rental_source" property="rentalSource" javaType="String"/>
                            <result column="fields1" property="fields1" javaType="String"/>
                            <result column="fields2" property="fields2" javaType="String"/>
                            <result column="fields3" property="fields3" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="type" property="type" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmCollectionWhitelistPageResultVo">
                        <result column="person_customer_id" property="personCustomerId" javaType="String"/>
                        <result column="person_customer_name" property="personCustomerName" javaType="String"/>
                        <result column="nation_code" property="nationCode" javaType="String"/>
                        <result column="certificate_type_code" property="certificateTypeCode" javaType="String"/>
                        <result column="certificate_num" property="certificateNum" javaType="String"/>
                        <result column="gender_code" property="genderCode" javaType="String"/>
                        <result column="phone" property="phone" javaType="String"/>
                        <result column="operate_project_id" property="operateProjectId" javaType="String"/>
                        <result column="project_name" property="projectName" javaType="String"/>
                        <result column="rental_source" property="rentalSource" javaType="String"/>
                        <result column="fields1" property="fields1" javaType="String"/>
                        <result column="fields2" property="fields2" javaType="String"/>
                        <result column="fields3" property="fields3" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="type" property="type" javaType="String"/>
    </resultMap>

    <resultMap id="QueryResultVoMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmCollectionWhitelistVo">
        <result column="person_customer_id" property="personCustomerId" javaType="String"/>
        <result column="person_customer_name" property="personCustomerName" javaType="String"/>
        <result column="nation_code" property="nationCode" javaType="String"/>
        <result column="certificate_type_code" property="certificateTypeCode" javaType="String"/>
        <result column="certificate_num" property="certificateNum" javaType="String"/>
        <result column="gender_code" property="genderCode" javaType="String"/>
        <result column="phone" property="phone" javaType="String"/>
        <result column="operate_project_id" property="operateProjectId" javaType="String"/>
        <result column="project_name" property="projectName" javaType="String"/>
        <result column="rental_source" property="rentalSource" javaType="String"/>
        <result column="fields1" property="fields1" javaType="String"/>
        <result column="fields2" property="fields2" javaType="String"/>
        <result column="fields3" property="fields3" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="type" property="type" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.white_id
        ,base.person_customer_id
        ,base.person_customer_name
        ,base.nation_code
        ,base.certificate_type_code
        ,base.certificate_num
        ,base.gender_code
        ,base.phone
        ,base.operate_project_id
        ,base.project_name
        ,base.rental_source
        ,base.fields1
        ,base.fields2
        ,base.fields3
        ,base.del_flag
            ,base.type
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_collection_whitelist base
        <where>
            base.del_flag = '1'
            <if test="'' != vo.whiteId and vo.whiteId != null">
                and base.white_id = #{vo.whiteId}
            </if>
            <if test="'' != vo.type and vo.type != null">
                and base.type = #{vo.type}
            </if>
            <if test="'' != vo.personCustomerId and vo.personCustomerId != null">
                and base.person_customer_id = #{vo.personCustomerId}
            </if>
            <if test="'' != vo.personCustomerName and vo.personCustomerName != null">
                and base.person_customer_name  like CONCAT('%',#{vo.personCustomerName},'%')
            </if>
            <if test="'' != vo.nationCode and vo.nationCode != null">
                and base.nation_code = #{vo.nationCode}
            </if>
            <if test="'' != vo.certificateTypeCode and vo.certificateTypeCode != null">
                and base.certificate_type_code = #{vo.certificateTypeCode}
            </if>
            <if test="'' != vo.certificateNum and vo.certificateNum != null">
                and base.certificate_num    like CONCAT('%',#{vo.certificateNum},'%')
            </if>
            <if test="'' != vo.genderCode and vo.genderCode != null">
                and base.gender_code = #{vo.genderCode}
            </if>
            <if test="'' != vo.phone and vo.phone != null">
                and base.phone   like CONCAT('%',#{vo.phone},'%')
            </if>
            <if test="'' != vo.operateProjectId and vo.operateProjectId != null">
                and FIND_IN_SET(#{vo.operateProjectId},base.operate_project_id)
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name like CONCAT('%',#{vo.projectName},'%')
            </if>
            <if test="'' != vo.rentalSource and vo.rentalSource != null">
                and base.rental_source = #{vo.rentalSource}
            </if>
            <if test="'' != vo.fields1 and vo.fields1 != null">
                and base.fields1 = #{vo.fields1}
            </if>
            <if test="'' != vo.fields2 and vo.fields2 != null">
                and base.fields2 = #{vo.fields2}
            </if>
            <if test="'' != vo.fields3 and vo.fields3 != null">
                and base.fields3 = #{vo.fields3}
            </if>
            <if test="'' != vo.projectIdStr and vo.projectIdStr != null">
                and (
                <foreach item="item" index="index" collection="vo.projectIdStr.split(',')" open="(" separator="OR"
                         close=")">
                     FIND_IN_SET(#{item},base.operate_project_id)
                </foreach>  OR base.operate_project_id is null OR base.operate_project_id = '')
            </if>

            <if test="'' != vo.createTimeStart and vo.createTimeStart != null">
                and SUBSTRING(base.create_time,1,10) <![CDATA[ >= ]]> #{vo.createTimeStart}
            </if>
            <if test="'' != vo.createTimeEnd and vo.createTimeEnd != null">
                and SUBSTRING(base.create_time,1,10)  <![CDATA[ <= ]]> #{vo.createTimeEnd}
            </if>
        </where>
        order by base.create_time desc
    </select>


    <select id="selectCustomerOrProject" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_collection_whitelist base
        <where>
            base.del_flag = '1'

            <if test="'' != vo.type and vo.type != null">
                and base.type = #{vo.type}
            </if>
            <if test="'' != vo.personCustomerId and vo.personCustomerId != null">
                and base.person_customer_id = #{vo.personCustomerId}
            </if>
            <if test="'' != vo.operateProjectId and vo.operateProjectId != null">
                and base.operate_project_id = #{vo.operateProjectId}
            </if>

        </where>
    </select>
</mapper>
