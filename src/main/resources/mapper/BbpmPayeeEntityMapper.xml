<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmPayeeMapper">
    <resultMap id="ResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmPayeeVo">
        <result column="pospbak_id" property="pospbakId" javaType="String"/>
        <result column="request" property="request" javaType="String"/>
        <result column="result" property="result" javaType="String"/>
    </resultMap>
    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageResultVo">
        <result column="request" property="request" javaType="String"/>
        <result column="result" property="result" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.pospbak_id
        ,base.request
        ,base.result
        ,base.del_flag
    </sql>

    <insert id="insertPayee">
        insert into
            bbpm_payee(pospbak_id,request,result)
            values (#{bbpmPayeeVo.pospbakId},#{bbpmPayeeVo.request},#{bbpmPayeeVo.result})
    </insert>



    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_payee base
        <where>
            <if test="'' != vo.pospbakId and vo.pospbakId != null">
                and base.pospbak_id = #{vo.pospbakId}
            </if>
            <if test="'' != vo.request and vo.request != null">
                and base.request  like CONCAT('%',#{vo.request},'%')
            </if>
            <if test="'' != vo.result and vo.result != null">
                and base.result = #{vo.result}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>

</mapper>