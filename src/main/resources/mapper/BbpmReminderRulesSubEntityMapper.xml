<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.reminder.dao.BbpmReminderRulesSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesSubEntity">
                            <id column="rules_sub_id" property="rulesSubId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="rules_id" property="rulesId" javaType="String"/>
                            <result column="bill_due_around" property="billDueAround" javaType="String"/>
                            <result column="bill_due_days" property="billDueDays" javaType="String"/>
                            <result column="text_message_template_id" property="textMessageTemplateId" javaType="String"/>
                            <result column="station_message_template_id" property="stationMessageTemplateId" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="seq_num" property="seqNum" javaType="Integer"/>
        <result column="collection_reason" property="collectionReason" javaType="String"/>

    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmReminderRulesSubPageResultVo">
                        <result column="rules_id" property="rulesId" javaType="String"/>
                        <result column="bill_due_around" property="billDueAround" javaType="String"/>
                        <result column="bill_due_days" property="billDueDays" javaType="String"/>
                        <result column="text_message_template_id" property="textMessageTemplateId" javaType="String"/>
                        <result column="station_message_template_id" property="stationMessageTemplateId" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="seq_num" property="seqNum" javaType="Integer"/>
        <result column="collection_reason" property="collectionReason" javaType="String"/>

    </resultMap>

    <resultMap id="QueryResultMapVo" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmReminderRulesSubVo">
        <result column="rules_id" property="rulesId" javaType="String"/>
        <result column="bill_due_around" property="billDueAround" javaType="String"/>
        <result column="bill_due_days" property="billDueDays" javaType="String"/>
        <result column="text_message_template_id" property="textMessageTemplateId" javaType="String"/>
        <result column="station_message_template_id" property="stationMessageTemplateId" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="seq_num" property="seqNum" javaType="Integer"/>
        <result column="collection_reason" property="collectionReason" javaType="String"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.rules_sub_id
        ,base.rules_id
        ,base.bill_due_around
        ,base.bill_due_days
        ,base.text_message_template_id
        ,base.station_message_template_id
        ,base.del_flag
            ,base.seq_num
            ,base.collection_reason
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_reminder_rules_sub base
        <where>
            base.del_flag = '1'
            <if test="'' != vo.rulesSubId and vo.rulesSubId != null">
                and base.rules_sub_id = #{vo.rulesSubId}
            </if>
            <if test="'' != vo.rulesId and vo.rulesId != null">
                and base.rules_id = #{vo.rulesId}
            </if>
            <if test="'' != vo.billDueAround and vo.billDueAround != null">
                and base.bill_due_around = #{vo.billDueAround}
            </if>
            <if test="'' != vo.billDueDays and vo.billDueDays != null">
                and base.bill_due_days = #{vo.billDueDays}
            </if>
            <if test="'' != vo.textMessageTemplateId and vo.textMessageTemplateId != null">
                and base.text_message_template_id = #{vo.textMessageTemplateId}
            </if>
            <if test="'' != vo.stationMessageTemplateId and vo.stationMessageTemplateId != null">
                and base.station_message_template_id = #{vo.stationMessageTemplateId}
            </if>
        </where>
    </select>
    
    <delete id="deleteByRulesId">
        delete from bbpm_reminder_rules_sub where rules_id=#{rulesId}
    </delete>

    <update id="updateByRulesId">
        update bbpm_reminder_rules_sub set del_flag = '0' where rules_id=#{rulesId}
    </update>


    <select id="selectByRulesIdList" resultMap="QueryResultMapVo">
        select
        <include refid="Base_Column_List"/>
        from bbpm_reminder_rules_sub base
        <where>
            base.del_flag = '1' and base.rules_id = #{vo.rulesId}
        </where>
        ORDER BY base.seq_num asc
    </select>

</mapper>
