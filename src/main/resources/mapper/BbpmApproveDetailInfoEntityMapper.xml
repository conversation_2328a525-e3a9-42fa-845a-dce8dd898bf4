<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.supplementary.dao.BbpmApproveDetailInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.supplementary.entity.BbpmApproveDetailInfoEntity">
        <id column="approve_detail_id" property="approveDetailId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="approve_id" property="approveId" javaType="String"/>
        <result column="approve_status" property="approveStatus" javaType="String"/>
        <result column="approver_role_id" property="approverRoleId" javaType="String"/>
        <result column="approver_user_id" property="approverUserId" javaType="String"/>
        <result column="approve_remark" property="approveRemark" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.supplementary.vo.BbpmApproveDetailInfoPageResultVo">
        <result column="approve_id" property="approveId" javaType="String"/>
        <result column="approve_status" property="approveStatus" javaType="String"/>
        <result column="approver_role_id" property="approverRoleId" javaType="String"/>
        <result column="approver_user_id" property="approverUserId" javaType="String"/>
        <result column="approve_remark" property="approveRemark" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.approve_detail_id
        ,base.approve_id
        ,base.approve_status
        ,base.approver_role_id
        ,base.approver_user_id
        ,base.approve_remark
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_approve_detail_info base
        <where>
            <if test="'' != vo.approveDetailId and vo.approveDetailId != null">
                and base.approve_detail_id = #{vo.approveDetailId}
            </if>
            <if test="'' != vo.approveId and vo.approveId != null">
                and base.approve_id = #{vo.approveId}
            </if>
            <if test="'' != vo.approveStatus and vo.approveStatus != null">
                and base.approve_status = #{vo.approveStatus}
            </if>
            <if test="'' != vo.approverRoleId and vo.approverRoleId != null">
                and base.approver_role_id = #{vo.approverRoleId}
            </if>
            <if test="'' != vo.approverUserId and vo.approverUserId != null">
                and base.approver_user_id = #{vo.approverUserId}
            </if>
            <if test="'' != vo.approveRemark and vo.approveRemark != null">
                and base.approve_remark = #{vo.approveRemark}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
    <select id="selectListByParentId" resultType="com.bonc.ioc.bzf.business.supplementary.vo.BbpmApproveDetailInfoVo">
        SELECT t.* FROM bbpm_approve_detail_info t
        INNER JOIN bbpm_approve_info a on t.approve_id = a.approve_id
        where t.del_flag = '1' and a.del_flag = '1' and a.parent_id = #{parentId} and a.approve_type = #{approveType}
        ORDER BY t.create_time DESC;
    </select>
</mapper>
