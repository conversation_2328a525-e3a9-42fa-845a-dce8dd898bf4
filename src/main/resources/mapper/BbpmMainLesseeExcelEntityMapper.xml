<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmMainLesseeExcelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmMainLesseeExcelEntity">
                            <id column="id" property="id" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="trace_id" property="traceId" javaType="String"/>
                            <result column="contract_no" property="contractNo" javaType="String"/>
                            <result column="req_body" property="reqBody" javaType="String"/>
                            <result column="bank_body" property="bankBody" javaType="String"/>
                            <result column="error_msg" property="errorMsg" javaType="String"/>
                            <result column="fields1" property="fields1" javaType="String"/>
                            <result column="fields2" property="fields2" javaType="String"/>
                            <result column="fields3" property="fields3" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmMainLesseeExcelPageResultVo">
                        <result column="trace_id" property="traceId" javaType="String"/>
                        <result column="contract_no" property="contractNo" javaType="String"/>
                        <result column="req_body" property="reqBody" javaType="String"/>
                        <result column="bank_body" property="bankBody" javaType="String"/>
                        <result column="error_msg" property="errorMsg" javaType="String"/>
                        <result column="fields1" property="fields1" javaType="String"/>
                        <result column="fields2" property="fields2" javaType="String"/>
                        <result column="fields3" property="fields3" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.id
        ,base.trace_id
        ,base.contract_no
        ,base.req_body
        ,base.bank_body
        ,base.error_msg
        ,base.fields1
        ,base.fields2
        ,base.fields3
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_main_lessee_excel base
        <where>
            <if test="'' != vo.id and vo.id != null">
                and base.id = #{vo.id}
            </if>
            <if test="'' != vo.traceId and vo.traceId != null">
                and base.trace_id = #{vo.traceId}
            </if>
            <if test="'' != vo.contractNo and vo.contractNo != null">
                and base.contract_no = #{vo.contractNo}
            </if>
            <if test="'' != vo.reqBody and vo.reqBody != null">
                and base.req_body = #{vo.reqBody}
            </if>
            <if test="'' != vo.bankBody and vo.bankBody != null">
                and base.bank_body = #{vo.bankBody}
            </if>
            <if test="'' != vo.errorMsg and vo.errorMsg != null">
                and base.error_msg  like concat('%',#{vo.errorMsg},'%')
            </if>
            <if test="'' != vo.fields1 and vo.fields1 != null">
                and base.fields1 = #{vo.fields1}
            </if>
            <if test="'' != vo.fields2 and vo.fields2 != null">
                and base.fields2 = #{vo.fields2}
            </if>
            <if test="'' != vo.fields3 and vo.fields3 != null">
                and base.fields3 = #{vo.fields3}
            </if>
        </where>
        order by base.create_time desc
    </select>
</mapper>
