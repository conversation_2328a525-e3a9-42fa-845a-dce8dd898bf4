<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmBillManagementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmBillManagementEntity">
                            <id column="bill_id" property="billId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="bill_no" property="billNo" javaType="String"/>
                            <result column="bill_status" property="billStatus" javaType="String"/>
                            <result column="expense_items" property="expenseItems" javaType="String"/>
                            <result column="bill_cycle" property="billCycle" javaType="String"/>
                            <result column="amount_receivable" property="amountReceivable" javaType="BigDecimal"/>
                            <result column="paid_in_amount" property="paidInAmount" javaType="BigDecimal"/>
                            <result column="collection_time" property="collectionTime" javaType="Date"/>
                            <result column="collection_channel" property="collectionChannel" javaType="String"/>
                            <result column="contract_no" property="contractNo" javaType="String"/>
                            <result column="customer_name" property="customerName" javaType="String"/>
                            <result column="customer_id" property="customerId" javaType="String"/>
                            <result column="contact_information" property="contactInformation" javaType="String"/>
                            <result column="project_id" property="projectId" javaType="String"/>
                            <result column="project_name" property="projectName" javaType="String"/>
                            <result column="source_address" property="sourceAddress" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo">
                        <result column="bill_no" property="billNo" javaType="String"/>
                        <result column="bill_status" property="billStatus" javaType="String"/>
                        <result column="expense_items" property="expenseItems" javaType="String"/>
                        <result column="bill_cycle" property="billCycle" javaType="String"/>
                        <result column="amount_receivable" property="amountReceivable" javaType="BigDecimal"/>
                        <result column="paid_in_amount" property="paidInAmount" javaType="BigDecimal"/>
                        <result column="collection_time" property="collectionTime" javaType="Date"/>
                        <result column="collection_channel" property="collectionChannel" javaType="String"/>
                        <result column="contract_no" property="contractNo" javaType="String"/>
                        <result column="customer_name" property="customerName" javaType="String"/>
                        <result column="customer_id" property="customerId" javaType="String"/>
                        <result column="contact_information" property="contactInformation" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
                        <result column="project_name" property="projectName" javaType="String"/>
                        <result column="source_address" property="sourceAddress" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.bill_id
        ,base.bill_no
        ,base.bill_status
        ,base.expense_items
        ,base.bill_cycle
        ,base.amount_receivable
        ,base.paid_in_amount
        ,base.collection_time
        ,base.collection_channel
        ,base.contract_no
        ,base.customer_name
        ,base.customer_id
        ,base.contact_information
        ,base.project_id
        ,base.project_name
        ,base.source_address
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_bill_management base
        <where>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.billNo and vo.billNo != null">
                and base.bill_no = #{vo.billNo}
            </if>
            <if test="'' != vo.billStatus and vo.billStatus != null">
                and base.bill_status = #{vo.billStatus}
            </if>
            <if test="'' != vo.expenseItems and vo.expenseItems != null">
                and base.expense_items = #{vo.expenseItems}
            </if>
            <if test="'' != vo.billCycle and vo.billCycle != null">
                and base.bill_cycle = #{vo.billCycle}
            </if>
            <if test="vo.amountReceivable != null">
                and base.amount_receivable = #{vo.amountReceivable}
            </if>
            <if test="vo.paidInAmount != null">
                and base.paid_in_amount = #{vo.paidInAmount}
            </if>
            <if test="vo.collectionTime != null">
                and base.collection_time = #{vo.collectionTime}
            </if>
            <if test="'' != vo.collectionChannel and vo.collectionChannel != null">
                and base.collection_channel = #{vo.collectionChannel}
            </if>
            <if test="'' != vo.contractNo and vo.contractNo != null">
                and base.contract_no = #{vo.contractNo}
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and base.customer_name = #{vo.customerName}
            </if>
            <if test="'' != vo.contactInformation and vo.contactInformation != null">
                and base.contact_information = #{vo.contactInformation}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name = #{vo.projectName}
            </if>
            <if test="'' != vo.sourceAddress and vo.sourceAddress != null">
                and base.source_address = #{vo.sourceAddress}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
