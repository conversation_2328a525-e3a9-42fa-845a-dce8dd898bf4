<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.reminder.dao.BbpmMessageSendSubLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendSubLogEntity">
                            <id column="notice_id" property="noticeId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="main_id" property="mainId" javaType="String"/>
                            <result column="rules_id" property="rulesId" javaType="String"/>
                            <result column="rules_sub_id" property="rulesSubId" javaType="String"/>
                            <result column="message_template_id" property="messageTemplateId" javaType="String"/>
                            <result column="notice_method" property="noticeMethod" javaType="String"/>
                            <result column="notice_content" property="noticeContent" javaType="String"/>
                            <result column="notice_result" property="noticeResult" javaType="String"/>
                            <result column="send_time" property="sendTime" javaType="Date"/>
                            <result column="send_num" property="sendNum" javaType="Integer"/>
                            <result column="request" property="request" javaType="String"/>
                            <result column="response" property="response" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmMessageSendSubLogPageResultVo">
                        <result column="main_id" property="mainId" javaType="String"/>
                        <result column="rules_id" property="rulesId" javaType="String"/>
                        <result column="rules_sub_id" property="rulesSubId" javaType="String"/>
                        <result column="message_template_id" property="messageTemplateId" javaType="String"/>
                        <result column="notice_method" property="noticeMethod" javaType="String"/>
                        <result column="notice_content" property="noticeContent" javaType="String"/>
                        <result column="notice_result" property="noticeResult" javaType="String"/>
                        <result column="send_time" property="sendTime" javaType="Date"/>
                        <result column="send_num" property="sendNum" javaType="Integer"/>
                        <result column="request" property="request" javaType="String"/>
                        <result column="response" property="response" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.notice_id
        ,base.main_id
        ,base.rules_id
        ,base.rules_sub_id
        ,base.message_template_id
        ,base.notice_method
        ,base.notice_content
        ,base.notice_result
        ,base.send_time
        ,base.send_num
        ,base.request
        ,base.response
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_message_send_sub_log base
        <where>
            <if test="'' != vo.noticeId and vo.noticeId != null">
                and base.notice_id = #{vo.noticeId}
            </if>
            <if test="'' != vo.mainId and vo.mainId != null">
                and base.main_id = #{vo.mainId}
            </if>
            <if test="'' != vo.rulesId and vo.rulesId != null">
                and base.rules_id = #{vo.rulesId}
            </if>
            <if test="'' != vo.rulesSubId and vo.rulesSubId != null">
                and base.rules_sub_id = #{vo.rulesSubId}
            </if>
            <if test="'' != vo.messageTemplateId and vo.messageTemplateId != null">
                and base.message_template_id = #{vo.messageTemplateId}
            </if>
            <if test="'' != vo.noticeMethod and vo.noticeMethod != null">
                and base.notice_method = #{vo.noticeMethod}
            </if>
            <if test="'' != vo.noticeContent and vo.noticeContent != null">
                and base.notice_content = #{vo.noticeContent}
            </if>
            <if test="'' != vo.noticeResult and vo.noticeResult != null">
                and base.notice_result = #{vo.noticeResult}
            </if>
            <if test="vo.sendTime != null">
                and base.send_time = #{vo.sendTime}
            </if>
            <if test="vo.sendNum != null">
                and base.send_num = #{vo.sendNum}
            </if>
            <if test="'' != vo.request and vo.request != null">
                and base.request = #{vo.request}
            </if>
            <if test="'' != vo.response and vo.response != null">
                and base.response = #{vo.response}
            </if>
        </where>
        ORDER BY base.create_time desc
    </select>
</mapper>
