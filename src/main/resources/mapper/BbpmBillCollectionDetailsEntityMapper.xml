<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmBillCollectionDetailsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmBillCollectionDetailsEntity">
                            <id column="details_id" property="detailsId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="bill_no" property="billNo" javaType="String"/>
                            <result column="collection_id" property="collectionId" javaType="String"/>
                            <result column="collection_no" property="collectionNo" javaType="String"/>
                            <result column="customer_id" property="customerId" javaType="String"/>
                            <result column="contract_id" property="contractId" javaType="String"/>
                            <result column="cash_amout" property="cashAmout" javaType="BigDecimal"/>
                            <result column="cash_time" property="cashTime" javaType="Date"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="Integer"/>

                            <result column="bill_charge_subject" property="billChargeSubject" javaType="String"/>
                            <result column="replace_pay_amount" property="replacePayAmount" javaType="BigDecimal"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmBillCollectionDetailsPageResultVo">
                        <result column="bill_no" property="billNo" javaType="String"/>
                        <result column="collection_id" property="collectionId" javaType="String"/>
                        <result column="collection_no" property="collectionNo" javaType="String"/>
                        <result column="customer_id" property="customerId" javaType="String"/>
                        <result column="contract_id" property="contractId" javaType="String"/>
                        <result column="cash_amout" property="cashAmout" javaType="BigDecimal"/>
                        <result column="cash_time" property="cashTime" javaType="Date"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="Integer"/>

        <result column="bill_charge_subject" property="billChargeSubject" javaType="String"/>
        <result column="replace_pay_amount" property="replacePayAmount" javaType="BigDecimal"/>
    </resultMap>

    <resultMap id="voResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmBillCollectionDetailsVo">
        <result column="bill_no" property="billNo" javaType="String"/>
        <result column="collection_id" property="collectionId" javaType="String"/>
        <result column="collection_no" property="collectionNo" javaType="String"/>
        <result column="customer_id" property="customerId" javaType="String"/>
        <result column="contract_id" property="contractId" javaType="String"/>
        <result column="cash_amout" property="cashAmout" javaType="BigDecimal"/>
        <result column="cash_time" property="cashTime" javaType="Date"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="Integer"/>

        <result column="bill_charge_subject" property="billChargeSubject" javaType="String"/>
        <result column="replace_pay_amount" property="replacePayAmount" javaType="BigDecimal"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.details_id
        ,base.bill_no
        ,base.collection_id
        ,base.collection_no
        ,base.customer_id
        ,base.contract_id
        ,base.cash_amout
        ,base.cash_time
        ,base.del_flag
        ,base.charge_subject_period
            ,base.bill_charge_subject
            ,base.replace_pay_amount
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_bill_collection_details base
        <where>
            <if test="'' != vo.detailsId and vo.detailsId != null">
                and base.details_id = #{vo.detailsId}
            </if>
            <if test="'' != vo.billNo and vo.billNo != null">
                and base.bill_no = #{vo.billNo}
            </if>
            <if test="'' != vo.collectionId and vo.collectionId != null">
                and base.collection_id = #{vo.collectionId}
            </if>
            <if test="'' != vo.collectionNo and vo.collectionNo != null">
                and base.collection_no = #{vo.collectionNo}
            </if>
            <if test="'' != vo.customerId and vo.customerId != null">
                and base.customer_id = #{vo.customerId}
            </if>
            <if test="'' != vo.contractId and vo.contractId != null">
                and base.contract_id = #{vo.contractId}
            </if>
            <if test="vo.cashAmout != null">
                and base.cash_amout = #{vo.cashAmout}
            </if>
            <if test="vo.cashTime != null">
                and base.cash_time = #{vo.cashTime}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>


    <select id="selectDetailsByCollectionId" resultMap="voResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_bill_collection_details base , bbpm_deposit_collection_relationship r
        WHERE r.collection_no = base.collection_id and r.deposit_slip_no = #{depositId}
        ORDER By collection_no,charge_subject_period
    </select>

</mapper>
