<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.invoice.dao.BbpmInvoiceHeaderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceHeaderEntity">
                            <id column="invoice_header_id" property="invoiceHeaderId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="type" property="type" javaType="String"/>
                            <result column="name" property="name" javaType="String"/>
                            <result column="tax_number" property="taxNumber" javaType="String"/>
                            <result column="register_addr" property="registerAddr" javaType="String"/>
                            <result column="register_phone" property="registerPhone" javaType="String"/>
                            <result column="bank_name" property="bankName" javaType="String"/>
                            <result column="bank_account" property="bankAccount" javaType="String"/>
                            <result column="is_default" property="isDefault" javaType="String"/>
                            <result column="remarks" property="remarks" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.invoice.vo.BbpmInvoiceHeaderPageResultVo">
                        <result column="type" property="type" javaType="String"/>
                        <result column="name" property="name" javaType="String"/>
                        <result column="tax_number" property="taxNumber" javaType="String"/>
                        <result column="register_addr" property="registerAddr" javaType="String"/>
                        <result column="register_phone" property="registerPhone" javaType="String"/>
                        <result column="bank_name" property="bankName" javaType="String"/>
                        <result column="bank_account" property="bankAccount" javaType="String"/>
                        <result column="is_default" property="isDefault" javaType="String"/>
                        <result column="remarks" property="remarks" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <resultMap id="voResultMap" type="com.bonc.ioc.bzf.business.invoice.vo.BbpmInvoiceHeaderVo">
        <result column="type" property="type" javaType="String"/>
        <result column="name" property="name" javaType="String"/>
        <result column="tax_number" property="taxNumber" javaType="String"/>
        <result column="register_addr" property="registerAddr" javaType="String"/>
        <result column="register_phone" property="registerPhone" javaType="String"/>
        <result column="bank_name" property="bankName" javaType="String"/>
        <result column="bank_account" property="bankAccount" javaType="String"/>
        <result column="is_default" property="isDefault" javaType="String"/>
        <result column="remarks" property="remarks" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.invoice_header_id
        ,base.type
        ,base.name
        ,base.tax_number
        ,base.register_addr
        ,base.register_phone
        ,base.bank_name
        ,base.bank_account
        ,base.is_default
        ,base.remarks
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_invoice_header base
        <where>
            <if test="'' != vo.invoiceHeaderId and vo.invoiceHeaderId != null">
                and base.invoice_header_id = #{vo.invoiceHeaderId}
            </if>
            <if test="'' != vo.type and vo.type != null">
                and base.type = #{vo.type}
            </if>
            <if test="'' != vo.name and vo.name != null">
                and base.name = #{vo.name}
            </if>
            <if test="'' != vo.taxNumber and vo.taxNumber != null">
                and base.tax_number = #{vo.taxNumber}
            </if>
            <if test="'' != vo.registerAddr and vo.registerAddr != null">
                and base.register_addr = #{vo.registerAddr}
            </if>
            <if test="'' != vo.registerPhone and vo.registerPhone != null">
                and base.register_phone = #{vo.registerPhone}
            </if>
            <if test="'' != vo.bankName and vo.bankName != null">
                and base.bank_name = #{vo.bankName}
            </if>
            <if test="'' != vo.bankAccount and vo.bankAccount != null">
                and base.bank_account = #{vo.bankAccount}
            </if>
            <if test="'' != vo.isDefault and vo.isDefault != null">
                and base.is_default = #{vo.isDefault}
            </if>
            <if test="'' != vo.remarks and vo.remarks != null">
                and base.remarks = #{vo.remarks}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.createUser and vo.createUser != null">
                and base.create_user = #{vo.createUser}
            </if>
        </where>
    </select>


    <select id="selectByIsDefault" resultMap="voResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_invoice_header base
        where   base.del_flag ='1'
                and base.is_default = #{isDefault}
                <if test="'' != invoiceHeaderId and invoiceHeaderId != null">
                    and base.invoice_header_id != #{invoiceHeaderId}
                </if>
        limit 1
    </select>


</mapper>
