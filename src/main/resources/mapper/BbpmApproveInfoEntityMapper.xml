<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.supplementary.dao.BbpmApproveInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.supplementary.entity.BbpmApproveInfoEntity">
        <id column="approve_id" property="approveId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="approve_type" property="approveType" javaType="String"/>
        <result column="approve_status" property="approveStatus" javaType="String"/>
        <result column="submit_time" property="submitTime" javaType="Date"/>
        <result column="approve_time" property="approveTime" javaType="Date"/>
        <result column="submit_user_id" property="submitUserId" javaType="String"/>
        <result column="submit_user_name" property="submitUserName" javaType="String"/>
        <result column="approver_user_id" property="approverUserId" javaType="String"/>
        <result column="approver_user_name" property="approverUserName" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.supplementary.vo.BbpmApproveInfoPageResultVo">
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="approve_type" property="approveType" javaType="String"/>
        <result column="approve_status" property="approveStatus" javaType="String"/>
        <result column="submit_time" property="submitTime" javaType="Date"/>
        <result column="approve_time" property="approveTime" javaType="Date"/>
        <result column="submit_user_id" property="submitUserId" javaType="String"/>
        <result column="submit_user_name" property="submitUserName" javaType="String"/>
        <result column="approver_user_id" property="approverUserId" javaType="String"/>
        <result column="approver_user_name" property="approverUserName" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.approve_id
        ,base.parent_id
        ,base.approve_type
        ,base.approve_status
        ,base.submit_time
        ,base.approve_time
        ,base.submit_user_id
        ,base.submit_user_name
        ,base.approver_user_id
        ,base.approver_user_name
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_approve_info base
        <where>
            <if test="'' != vo.approveId and vo.approveId != null">
                and base.approve_id = #{vo.approveId}
            </if>
            <if test="'' != vo.parentId and vo.parentId != null">
                and base.parent_id = #{vo.parentId}
            </if>
            <if test="'' != vo.approveType and vo.approveType != null">
                and base.approve_type = #{vo.approveType}
            </if>
            <if test="'' != vo.approveStatus and vo.approveStatus != null">
                and base.approve_status = #{vo.approveStatus}
            </if>
            <if test="vo.submitTime != null">
                and base.submit_time = #{vo.submitTime}
            </if>
            <if test="vo.approveTime != null">
                and base.approve_time = #{vo.approveTime}
            </if>
            <if test="'' != vo.submitUserId and vo.submitUserId != null">
                and base.submit_user_id = #{vo.submitUserId}
            </if>
            <if test="'' != vo.submitUserName and vo.submitUserName != null">
                and base.submit_user_name = #{vo.submitUserName}
            </if>
            <if test="'' != vo.approverUserId and vo.approverUserId != null">
                and base.approver_user_id = #{vo.approverUserId}
            </if>
            <if test="'' != vo.approverUserName and vo.approverUserName != null">
                and base.approver_user_name = #{vo.approverUserName}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
