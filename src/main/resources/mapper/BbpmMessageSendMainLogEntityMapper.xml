<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.reminder.dao.BbpmMessageSendMainLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendMainLogEntity">
                            <id column="main_id" property="mainId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="status" property="status" javaType="String"/>
                            <result column="error_msq" property="errorMsq" javaType="String"/>
                            <result column="error_msq_all" property="errorMsqAll" javaType="String"/>
        <result column="request" property="request" javaType="String"/>
        <result column="response" property="response" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmMessageSendMainLogPageResultVo">
                        <result column="status" property="status" javaType="String"/>
                        <result column="error_msq" property="errorMsq" javaType="String"/>
                        <result column="error_msq_all" property="errorMsqAll" javaType="String"/>
        <result column="request" property="request" javaType="String"/>
        <result column="response" property="response" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.main_id
        ,base.status
        ,base.error_msq
        ,base.error_msq_all
            ,base.request
        ,base.response
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_message_send_main_log base
        <where>
            <if test="'' != vo.mainId and vo.mainId != null">
                and base.main_id = #{vo.mainId}
            </if>
            <if test="'' != vo.status and vo.status != null">
                and base.status = #{vo.status}
            </if>
            <if test="'' != vo.errorMsq and vo.errorMsq != null">
                and base.error_msq = #{vo.errorMsq}
            </if>
            <if test="'' != vo.errorMsqAll and vo.errorMsqAll != null">
                and base.error_msq_all = #{vo.errorMsqAll}
            </if>
        </where>
        ORDER BY base.create_time desc
    </select>
</mapper>
