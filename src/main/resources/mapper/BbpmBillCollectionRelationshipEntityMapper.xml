<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmBillCollectionRelationshipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmBillCollectionRelationshipEntity">
                            <id column="bill_collection_id" property="billCollectionId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="bill_no" property="billNo" javaType="String"/>
                            <result column="collection_no" property="collectionNo" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmBillCollectionRelationshipPageResultVo">
                        <result column="bill_no" property="billNo" javaType="String"/>
                        <result column="collection_no" property="collectionNo" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.bill_collection_id
        ,base.bill_no
        ,base.collection_no
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_bill_collection_relationship base
        <where>
            <if test="'' != vo.billCollectionId and vo.billCollectionId != null">
                and base.bill_collection_id = #{vo.billCollectionId}
            </if>
            <if test="'' != vo.billNo and vo.billNo != null">
                and base.bill_no = #{vo.billNo}
            </if>
            <if test="'' != vo.collectionNo and vo.collectionNo != null">
                and base.collection_no = #{vo.collectionNo}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
