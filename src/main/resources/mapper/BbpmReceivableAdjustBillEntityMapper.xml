<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.adjust.dao.BbpmReceivableAdjustBillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustBillEntity">
                            <id column="id" property="id" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="adjust_id" property="adjustId" javaType="String"/>
                            <result column="bill_id" property="billId" javaType="String"/>
                            <result column="adjustment_amount" property="adjustmentAmount" javaType="BigDecimal"/>
                            <result column="ext1" property="ext1" javaType="String"/>
                            <result column="ext2" property="ext2" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.adjust.vo.BbpmReceivableAdjustBillPageResultVo">
                        <result column="adjust_id" property="adjustId" javaType="String"/>
                        <result column="bill_id" property="billId" javaType="String"/>
                        <result column="adjustment_amount" property="adjustmentAmount" javaType="BigDecimal"/>
                        <result column="ext1" property="ext1" javaType="String"/>
                        <result column="ext2" property="ext2" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.id
        ,base.adjust_id
        ,base.bill_id
        ,base.adjustment_amount
        ,base.ext1
        ,base.ext2
        ,base.del_flag
        ,base.create_user_name
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_receivable_adjust_bill base
        <where>
            <if test="'' != vo.id and vo.id != null">
                and base.id = #{vo.id}
            </if>
            <if test="'' != vo.adjustId and vo.adjustId != null">
                and base.adjust_id = #{vo.adjustId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="vo.adjustmentAmount != null">
                and base.adjustment_amount = #{vo.adjustmentAmount}
            </if>
            <if test="'' != vo.ext1 and vo.ext1 != null">
                and base.ext1 = #{vo.ext1}
            </if>
            <if test="'' != vo.ext2 and vo.ext2 != null">
                and base.ext2 = #{vo.ext2}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.createUserName and vo.createUserName != null">
                and base.create_user_name = #{vo.createUserName}
            </if>
        </where>
    </select>


    <delete id="removeByAdjustId">
        delete from bbpm_receivable_adjust_bill where adjust_id = #{adjustId}
    </delete>

    <select id="selectByAdjustId" resultType="com.bonc.ioc.bzf.business.adjust.vo.AdjustBillDTO">
        SELECT
            base.bill_id AS billCode,
            base.adjustment_amount AS adjustMoney,
            base.ext1 AS ext1
        FROM
            bbpm_receivable_adjust_bill base WHERE base.adjust_id = #{adjustId}
        ORDER BY
            CAST(base.ext2 AS SIGNED)
    </select>

    <select id="selectBillForAdjust" resultMap="BaseResultMap">
        SELECT
            base.*
        FROM
            bbpm_receivable_adjust_bill base WHERE base.adjust_id = #{adjustId}
        ORDER BY
            CAST(base.ext2 AS SIGNED)
    </select>
</mapper>
