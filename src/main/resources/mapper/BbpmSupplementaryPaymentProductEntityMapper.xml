<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.supplementary.dao.BbpmSupplementaryPaymentProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPaymentProductEntity">
        <id column="product_id" property="productId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="type" property="type" javaType="String"/>
        <result column="agreement_no" property="agreementNo" javaType="String"/>
        <result column="product_no" property="productNo" javaType="String"/>
        <result column="product_name" property="productName" javaType="String"/>
        <result column="customer_no" property="customerNo" javaType="String"/>
        <result column="customer_name" property="customerName" javaType="String"/>
        <result column="customer_tel" property="customerTel" javaType="String"/>
        <result column="customer_id_type" property="customerIdType" javaType="String"/>
        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="project_name" property="projectName" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap"
               type="com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryPaymentProductPageResultVo">
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="type" property="type" javaType="String"/>
        <result column="agreement_no" property="agreementNo" javaType="String"/>
        <result column="product_no" property="productNo" javaType="String"/>
        <result column="product_name" property="productName" javaType="String"/>
        <result column="customer_no" property="customerNo" javaType="String"/>
        <result column="customer_name" property="customerName" javaType="String"/>
        <result column="customer_tel" property="customerTel" javaType="String"/>
        <result column="customer_id_type" property="customerIdType" javaType="String"/>
        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="project_name" property="projectName" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.product_id
        ,base.parent_id
        ,base.type
        ,base.agreement_no
        ,base.product_no
        ,base.product_name
        ,base.customer_no
        ,base.customer_name
        ,base.customer_tel
        ,base.customer_id_type
        ,base.customer_id_number
        ,base.project_id
        ,base.project_name
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_supplementary_payment_product base
        <where>
            <if test="'' != vo.productId and vo.productId != null">
                and base.product_id = #{vo.productId}
            </if>
            <if test="'' != vo.parentId and vo.parentId != null">
                and base.parent_id = #{vo.parentId}
            </if>
            <if test="'' != vo.type and vo.type != null">
                and base.type = #{vo.type}
            </if>
            <if test="'' != vo.agreementNo and vo.agreementNo != null">
                and base.agreement_no = #{vo.agreementNo}
            </if>
            <if test="'' != vo.productNo and vo.productNo != null">
                and base.product_no = #{vo.productNo}
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and base.product_name = #{vo.productName}
            </if>
            <if test="'' != vo.customerNo and vo.customerNo != null">
                and base.customer_no = #{vo.customerNo}
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and base.customer_name = #{vo.customerName}
            </if>
            <if test="'' != vo.customerTel and vo.customerTel != null">
                and base.customer_tel = #{vo.customerTel}
            </if>
            <if test="'' != vo.customerIdType and vo.customerIdType != null">
                and base.customer_id_type = #{vo.customerIdType}
            </if>
            <if test="'' != vo.customerIdNumber and vo.customerIdNumber != null">
                and base.customer_id_number = #{vo.customerIdNumber}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name = #{vo.projectName}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
    <delete id="deleteBySupplementaryId">
        DELETE pp
        FROM
        bbpm_supplementary_payment_product pp
        LEFT JOIN bbpm_supplementary_payment pi ON pi.payment_id = pp.parent_id
        WHERE
        pi.parent_id = #{supplementaryId}
    </delete>
    <update id="updateDelFlagByParentId">
        UPDATE bbpm_supplementary_payment_product SET
        del_flag = #{delFlag}
        WHERE parent_id = #{parentId}
    </update>
    <select id="selectListByParentId"
            resultType="com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryPaymentProductVo">
        SELECT
        base.*
        FROM bbpm_supplementary_payment_product base
        WHERE
        base.del_flag = '1'
        AND
        base.parent_id = #{parentId}
    </select>
</mapper>
