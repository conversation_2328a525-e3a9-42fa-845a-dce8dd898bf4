<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.reminder.dao.BbpmMessageSendSubLogV2Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendSubLogV2Entity">
                            <id column="notice_id" property="noticeId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="main_id" property="mainId" javaType="String"/>
                            <result column="rules_id" property="rulesId" javaType="String"/>
                            <result column="rules_sub_id" property="rulesSubId" javaType="String"/>
                            <result column="business_id" property="businessId" javaType="String"/>
                            <result column="template_type_code_text" property="templateTypeCodeText" javaType="String"/>
                            <result column="message_template_id_text" property="messageTemplateIdText" javaType="String"/>
                            <result column="template_type_code_station" property="templateTypeCodeStation" javaType="String"/>
                            <result column="message_template_id_station" property="messageTemplateIdStation" javaType="String"/>
                            <result column="tenant_code" property="tenantCode" javaType="String"/>
                            <result column="tenant_name" property="tenantName" javaType="String"/>
                            <result column="tenant_mobile" property="tenantMobile" javaType="String"/>
                            <result column="cert_type" property="certType" javaType="String"/>
                            <result column="cert_no" property="certNo" javaType="String"/>
                            <result column="project_id" property="projectId" javaType="String"/>
                            <result column="project_name" property="projectName" javaType="String"/>
                            <result column="house_name" property="houseName" javaType="String"/>
                            <result column="collection_method" property="collectionMethod" javaType="String"/>
                            <result column="collection_reason" property="collectionReason" javaType="String"/>
                            <result column="notice_result_text" property="noticeResultText" javaType="String"/>
                            <result column="notice_result_station" property="noticeResultStation" javaType="String"/>
                            <result column="send_time_text" property="sendTimeText" javaType="Date"/>
                            <result column="send_time_station" property="sendTimeStation" javaType="Date"/>
                            <result column="relet_unit_name" property="reletUnitName" javaType="String"/>
                            <result column="contract_code" property="contractCode" javaType="String"/>
                            <result column="bill_id" property="billId" javaType="String"/>
                            <result column="bill_status" property="billStatus" javaType="String"/>
                            <result column="bill_cycle" property="billCycle" javaType="String"/>
                            <result column="payable_date" property="payableDate" javaType="String"/>
                            <result column="total_month" property="totalMonth" javaType="String"/>
                            <result column="total_amount" property="totalAmount" javaType="String"/>
                            <result column="project_format" property="projectFormat" javaType="String"/>
                            <result column="charge_owner" property="chargeOwner" javaType="String"/>
                            <result column="request" property="request" javaType="String"/>
                            <result column="response" property="response" javaType="String"/>
                            <result column="fields1" property="fields1" javaType="String"/>
                            <result column="fields2" property="fields2" javaType="String"/>
                            <result column="fields3" property="fields3" javaType="String"/>

        <result column="contract_type" property="contractType" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmMessageSendSubLogV2PageResultVo">
                        <result column="main_id" property="mainId" javaType="String"/>
                        <result column="rules_id" property="rulesId" javaType="String"/>
                        <result column="rules_sub_id" property="rulesSubId" javaType="String"/>
                        <result column="business_id" property="businessId" javaType="String"/>
                        <result column="template_type_code_text" property="templateTypeCodeText" javaType="String"/>
                        <result column="message_template_id_text" property="messageTemplateIdText" javaType="String"/>
                        <result column="template_type_code_station" property="templateTypeCodeStation" javaType="String"/>
                        <result column="message_template_id_station" property="messageTemplateIdStation" javaType="String"/>
                        <result column="tenant_code" property="tenantCode" javaType="String"/>
                        <result column="tenant_name" property="tenantName" javaType="String"/>
                        <result column="tenant_mobile" property="tenantMobile" javaType="String"/>
                        <result column="cert_type" property="certType" javaType="String"/>
                        <result column="cert_no" property="certNo" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
                        <result column="project_name" property="projectName" javaType="String"/>
                        <result column="house_name" property="houseName" javaType="String"/>
                        <result column="collection_method" property="collectionMethod" javaType="String"/>
                        <result column="collection_reason" property="collectionReason" javaType="String"/>
                        <result column="notice_result_text" property="noticeResultText" javaType="String"/>
                        <result column="notice_result_station" property="noticeResultStation" javaType="String"/>
                        <result column="send_time_text" property="sendTimeText" javaType="Date"/>
                        <result column="send_time_station" property="sendTimeStation" javaType="Date"/>
                        <result column="relet_unit_name" property="reletUnitName" javaType="String"/>
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="bill_id" property="billId" javaType="String"/>
                        <result column="bill_status" property="billStatus" javaType="String"/>
                        <result column="bill_cycle" property="billCycle" javaType="String"/>
                        <result column="payable_date" property="payableDate" javaType="String"/>
                        <result column="total_month" property="totalMonth" javaType="String"/>
                        <result column="total_amount" property="totalAmount" javaType="String"/>
                        <result column="project_format" property="projectFormat" javaType="String"/>
                        <result column="charge_owner" property="chargeOwner" javaType="String"/>
                        <result column="request" property="request" javaType="String"/>
                        <result column="response" property="response" javaType="String"/>
                        <result column="fields1" property="fields1" javaType="String"/>
                        <result column="fields2" property="fields2" javaType="String"/>
                        <result column="fields3" property="fields3" javaType="String"/>

        <result column="collection_reason_name" property="collectionReasonName" javaType="String"/>
        <result column="contract_type" property="contractType" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.notice_id
        ,base.main_id
        ,base.rules_id
        ,base.rules_sub_id
        ,base.business_id
        ,base.template_type_code_text
        ,base.message_template_id_text
        ,base.template_type_code_station
        ,base.message_template_id_station
        ,base.tenant_code
        ,base.tenant_name
        ,base.tenant_mobile
        ,base.cert_type
        ,base.cert_no
        ,base.project_id
        ,base.project_name
        ,base.house_name
        ,base.collection_method
        ,base.collection_reason
        ,base.notice_result_text
        ,base.notice_result_station
        ,base.send_time_text
        ,base.send_time_station
        ,base.relet_unit_name
        ,base.contract_code
        ,base.contract_type
        ,base.bill_id
        ,base.bill_status
        ,base.bill_cycle
        ,base.payable_date
        ,base.total_month
        ,base.total_amount
        ,base.project_format
        ,base.charge_owner
        ,base.request
        ,base.response
        ,base.fields1
        ,base.fields2
        ,base.fields3
        ,(
        SELECT
        GROUP_CONCAT( dict.meaning )
        FROM
        bbp_dict dict
        WHERE
        dict.type_code = 'BILLING_COLLECTION_REASON'
        AND FIND_IN_SET( dict.code, base.collection_reason )
        ) AS collection_reason_name
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_message_send_sub_log_v2 base
        <where>
            <if test="'' != vo.noticeId and vo.noticeId != null">
                and base.notice_id = #{vo.noticeId}
            </if>
            <if test="'' != vo.mainId and vo.mainId != null">
                and base.main_id = #{vo.mainId}
            </if>
            <if test="'' != vo.rulesId and vo.rulesId != null">
                and base.rules_id = #{vo.rulesId}
            </if>
            <if test="'' != vo.rulesSubId and vo.rulesSubId != null">
                and base.rules_sub_id = #{vo.rulesSubId}
            </if>
            <if test="'' != vo.businessId and vo.businessId != null">
                and base.business_id = #{vo.businessId}
            </if>
            <if test="'' != vo.templateTypeCodeText and vo.templateTypeCodeText != null">
                and base.template_type_code_text = #{vo.templateTypeCodeText}
            </if>
            <if test="'' != vo.messageTemplateIdText and vo.messageTemplateIdText != null">
                and base.message_template_id_text = #{vo.messageTemplateIdText}
            </if>
            <if test="'' != vo.templateTypeCodeStation and vo.templateTypeCodeStation != null">
                and base.template_type_code_station = #{vo.templateTypeCodeStation}
            </if>
            <if test="'' != vo.messageTemplateIdStation and vo.messageTemplateIdStation != null">
                and base.message_template_id_station = #{vo.messageTemplateIdStation}
            </if>
            <if test="'' != vo.tenantCode and vo.tenantCode != null">
                and base.tenant_code = #{vo.tenantCode}
            </if>
            <if test="'' != vo.tenantName and vo.tenantName != null">
                and base.tenant_name  like concat('%',#{vo.tenantName},'%')
            </if>
            <if test="'' != vo.tenantMobile and vo.tenantMobile != null">
                and base.tenant_mobile  like concat('%',#{vo.tenantMobile},'%')
            </if>
            <if test="'' != vo.certType and vo.certType != null">
                and base.cert_type = #{vo.certType}
            </if>
            <if test="'' != vo.certNo and vo.certNo != null">
                and base.cert_no like concat('%',#{vo.certNo},'%')
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name  like concat('%',#{vo.projectName},'%')
            </if>
            <if test="'' != vo.houseName and vo.houseName != null">
                and base.house_name = #{vo.houseName}
            </if>
            <if test="'' != vo.collectionMethod and vo.collectionMethod != null">
                and base.collection_method = #{vo.collectionMethod}
            </if>
            <if test="'' != vo.collectionReason and vo.collectionReason != null">
                and FIND_IN_SET(#{vo.collectionReason},base.collection_reason)
            </if>
            <if test="'' != vo.noticeResultText and vo.noticeResultText != null">
                and base.notice_result_text = #{vo.noticeResultText}
            </if>
            <if test="'' != vo.noticeResultStation and vo.noticeResultStation != null">
                and base.notice_result_station = #{vo.noticeResultStation}
            </if>
            <if test="vo.sendTimeText != null">
                and base.send_time_text = #{vo.sendTimeText}
            </if>
            <if test="vo.sendTimeStation != null">
                and base.send_time_station = #{vo.sendTimeStation}
            </if>
            <if test="'' != vo.reletUnitName and vo.reletUnitName != null">
                and base.relet_unit_name = #{vo.reletUnitName}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code  like CONCAT('%',#{vo.contractCode},'%')
            </if>
            <if test="'' != vo.contractType and vo.contractType != null">
                and base.contract_type = #{vo.contractType}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.billStatus and vo.billStatus != null">
                and base.bill_status = #{vo.billStatus}
            </if>
            <if test="'' != vo.billCycle and vo.billCycle != null">
                and base.bill_cycle = #{vo.billCycle}
            </if>
            <if test="'' != vo.payableDate and vo.payableDate != null">
                and base.payable_date = #{vo.payableDate}
            </if>
            <if test="'' != vo.totalMonth and vo.totalMonth != null">
                and base.total_month = #{vo.totalMonth}
            </if>
            <if test="'' != vo.totalAmount and vo.totalAmount != null">
                and base.total_amount = #{vo.totalAmount}
            </if>
            <if test="'' != vo.projectFormat and vo.projectFormat != null">
                and base.project_format = #{vo.projectFormat}
            </if>
            <if test="'' != vo.chargeOwner and vo.chargeOwner != null">
                and base.charge_owner = #{vo.chargeOwner}
            </if>
            <if test="'' != vo.request and vo.request != null">
                and base.request = #{vo.request}
            </if>
            <if test="'' != vo.response and vo.response != null">
                and base.response = #{vo.response}
            </if>
            <if test="'' != vo.fields1 and vo.fields1 != null">
                and base.fields1 = #{vo.fields1}
            </if>
            <if test="'' != vo.fields2 and vo.fields2 != null">
                and base.fields2 = #{vo.fields2}
            </if>
            <if test="'' != vo.fields3 and vo.fields3 != null">
                and base.fields3 = #{vo.fields3}
            </if>

            <if test="'' != vo.sendTimeTextStart and vo.sendTimeTextStart != null">
                and SUBSTRING(base.send_time_text,1,10) <![CDATA[ >= ]]> #{vo.sendTimeTextStart}
            </if>
            <if test="'' != vo.sendTimeTextEnd and vo.sendTimeTextEnd != null">
                and SUBSTRING(base.send_time_text,1,10)  <![CDATA[ <= ]]> #{vo.sendTimeTextEnd}
            </if>

            <if test="'' != vo.projectIdStr and vo.projectIdStr != null">
                and base.project_id IN
                <foreach item="item" index="index" collection="vo.projectIdStr.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by base.create_time desc
    </select>
</mapper>
