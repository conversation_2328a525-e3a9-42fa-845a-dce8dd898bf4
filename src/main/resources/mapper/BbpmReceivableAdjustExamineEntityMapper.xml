<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.adjust.dao.BbpmReceivableAdjustExamineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustExamineEntity">
                            <id column="id" property="id" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="adjust_id" property="adjustId" javaType="String"/>
                            <result column="examine_describe" property="examineDescribe" javaType="String"/>
                            <result column="examine_status" property="examineStatus" javaType="String"/>
                            <result column="ext1" property="ext1" javaType="String"/>
                            <result column="ext2" property="ext2" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.adjust.vo.BbpmReceivableAdjustExaminePageResultVo">
                        <result column="adjust_id" property="adjustId" javaType="String"/>
                        <result column="examine_describe" property="examineDescribe" javaType="String"/>
                        <result column="examine_status" property="examineStatus" javaType="String"/>
                        <result column="ext1" property="ext1" javaType="String"/>
                        <result column="ext2" property="ext2" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.id
        ,base.adjust_id
        ,base.examine_describe
        ,base.examine_status
        ,base.ext1
        ,base.ext2
        ,base.del_flag
        ,base.create_user_name
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_receivable_adjust_examine base
        <where>
            <if test="'' != vo.id and vo.id != null">
                and base.id = #{vo.id}
            </if>
            <if test="'' != vo.adjustId and vo.adjustId != null">
                and base.adjust_id = #{vo.adjustId}
            </if>
            <if test="'' != vo.examineDescribe and vo.examineDescribe != null">
                and base.examine_describe = #{vo.examineDescribe}
            </if>
            <if test="'' != vo.examineStatus and vo.examineStatus != null">
                and base.examine_status = #{vo.examineStatus}
            </if>
            <if test="'' != vo.ext1 and vo.ext1 != null">
                and base.ext1 = #{vo.ext1}
            </if>
            <if test="'' != vo.ext2 and vo.ext2 != null">
                and base.ext2 = #{vo.ext2}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.createUserName and vo.createUserName != null">
                and base.create_user_name = #{vo.createUserName}
            </if>
        </where>
    </select>

    <select id="selectExamineForAdjust" resultMap="BaseResultMap">
        SELECT
            base.*
        FROM
            bbpm_receivable_adjust_examine base WHERE base.adjust_id = #{adjustId}
        ORDER BY base.create_time desc
    </select>
</mapper>
