<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.reminder.dao.BbpmReminderRulesMainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesMainEntity">
                            <id column="rules_id" property="rulesId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="rule_name" property="ruleName" javaType="String"/>
                            <result column="contract_type" property="contractType" javaType="String"/>
                            <result column="business_type" property="businessType" javaType="String"/>
                            <result column="enable_status" property="enableStatus" javaType="String"/>
                            <result column="activation_time" property="activationTime" javaType="Date"/>
                            <result column="down_time" property="downTime" javaType="Date"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmReminderRulesMainPageResultVo">
                        <result column="rule_name" property="ruleName" javaType="String"/>
                        <result column="contract_type" property="contractType" javaType="String"/>
                        <result column="business_type" property="businessType" javaType="String"/>
                        <result column="enable_status" property="enableStatus" javaType="String"/>
                        <result column="activation_time" property="activationTime" javaType="Date"/>
                        <result column="down_time" property="downTime" javaType="Date"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <resultMap id="QueryResultVoMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmReminderRulesMainVo">
        <result column="rule_name" property="ruleName" javaType="String"/>
        <result column="contract_type" property="contractType" javaType="String"/>
        <result column="business_type" property="businessType" javaType="String"/>
        <result column="enable_status" property="enableStatus" javaType="String"/>
        <result column="activation_time" property="activationTime" javaType="Date"/>
        <result column="down_time" property="downTime" javaType="Date"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <resultMap id="MainAndSubVoMap" type="com.bonc.ioc.bzf.business.reminder.vo.BbpmReminderRulesMainAndSubVo">
        <result column="rules_id" property="rulesId" javaType="String"/>
        <result column="rule_name" property="ruleName" javaType="String"/>
        <result column="contract_type" property="contractType" javaType="String"/>
        <result column="business_type" property="businessType" javaType="String"/>
        <result column="enable_status" property="enableStatus" javaType="String"/>
        <result column="activation_time" property="activationTime" javaType="Date"/>
        <result column="down_time" property="downTime" javaType="Date"/>
        <result column="rules_sub_id" property="rulesSubId" javaType="String"/>
        <result column="bill_due_around" property="billDueAround" javaType="String"/>
        <result column="bill_due_days" property="billDueDays" javaType="String"/>
        <result column="text_message_template_id" property="textMessageTemplateId" javaType="String"/>
        <result column="station_message_template_id" property="stationMessageTemplateId" javaType="String"/>
        <result column="collection_reason" property="collectionReason" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.rules_id
        ,base.rule_name
        ,base.contract_type
        ,base.business_type
        ,base.enable_status
        ,base.activation_time
        ,base.down_time
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_reminder_rules_main base
        <where>
            base.del_flag = '1'
            <if test="'' != vo.rulesId and vo.rulesId != null">
                and base.rules_id = #{vo.rulesId}
            </if>
            <if test="'' != vo.ruleName and vo.ruleName != null">
                and base.rule_name  like CONCAT('%',#{vo.ruleName},'%')
            </if>
            <if test="'' != vo.contractType and vo.contractType != null">
                and base.contract_type = #{vo.contractType}
            </if>
            <if test="'' != vo.businessType and vo.businessType != null">
                and base.business_type = #{vo.businessType}
            </if>
            <if test="'' != vo.enableStatus and vo.enableStatus != null">
                and base.enable_status = #{vo.enableStatus}
            </if>
            <if test="'' != vo.activationTimeStart and vo.activationTimeStart != null">
                and SUBSTRING(base.activation_time,1,10) <![CDATA[ >= ]]> #{vo.activationTimeStart}
            </if>
            <if test="'' != vo.activationTimeEnd and vo.activationTimeEnd != null">
                and SUBSTRING(base.activation_time,1,10)  <![CDATA[ <= ]]> #{vo.activationTimeEnd}
            </if>
            <if test="'' != vo.createTimeStart and vo.createTimeStart != null">
                and SUBSTRING(base.create_time,1,10) <![CDATA[ >= ]]> #{vo.createTimeStart}
            </if>
            <if test="'' != vo.createTimeEnd and vo.createTimeEnd != null">
                and SUBSTRING(base.create_time,1,10)  <![CDATA[ <= ]]> #{vo.createTimeEnd}
            </if>
            <if test="vo.downTime != null">
                and base.down_time = #{vo.downTime}
            </if>
            <if test="'' != vo.modifyTimeStart and vo.modifyTimeStart != null">
                and SUBSTRING(base.modify_time,1,10) <![CDATA[ >= ]]> #{vo.modifyTimeStart}
            </if>
            <if test="'' != vo.modifyTimeEnd and vo.modifyTimeEnd != null">
                and SUBSTRING(base.modify_time,1,10)  <![CDATA[ <= ]]> #{vo.modifyTimeEnd}
            </if>

        </where>
        ORDER BY base.create_time desc
    </select>


    <select id="findByRuleName" resultMap="QueryResultVoMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_reminder_rules_main base
        <where>
            base.del_flag = '1'
            and base.rule_name = #{vo.ruleName}
            <if test="'' != vo.rulesId and vo.rulesId != null">
                and base.rules_id != #{vo.rulesId}
            </if>
        </where>
    </select>
    
    <select id="selectMainAndSubAll" resultMap="MainAndSubVoMap">
        select m.rules_id,
               m.rule_name,
               m.contract_type,
               m.business_type,
               m.enable_Status,
               m.activation_time,
               m.down_time,
               s.rules_sub_id,
               s.bill_due_around,
               s.bill_due_days,
               s.text_message_template_id,
               s.station_message_template_id,
               s.collection_reason
        from bbpm_reminder_rules_main m,
             bbpm_reminder_rules_sub s
        where m.del_flag = '1'
          and s.del_flag = '1'
          and m.rules_id = s.rules_id
          and m.enable_status = '02'
        ORDER BY s.rules_id asc,s.collection_reason asc
    </select>


</mapper>
