<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmDepositSlipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmDepositSlipEntity">
                            <id column="deposit_id" property="depositId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="deposit_slip_no" property="depositSlipNo" javaType="String"/>
                            <result column="deposit_status" property="depositStatus" javaType="String"/>
                            <result column="paid_in_amount" property="paidInAmount" javaType="BigDecimal"/>
                            <result column="collection_doc_qty" property="collectionDocQty" javaType="String"/>
                            <result column="transaction_no" property="transactionNo" javaType="String"/>
                            <result column="deposit_time" property="depositTime" javaType="Date"/>
                            <result column="depositor" property="depositor" javaType="String"/>
                            <result column="voucher_upload_status" property="voucherUploadStatus" javaType="String"/>
                            <result column="bank_voucher" property="bankVoucher" javaType="String"/>
                            <result column="uploader_id" property="uploaderId" javaType="String"/>
                            <result column="uploader_name" property="uploaderName" javaType="String"/>
                            <result column="uploader_date" property="uploaderDate" javaType="Date"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="receipt_no" property="receiptNo" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="bank_code" property="bankCode" javaType="String"/>
        <result column="bank_name" property="bankName" javaType="String"/>

        <result column="receipt_bank_acct" property="receiptBankAcct" javaType="String"/>
        <result column="total_cash_amount" property="totalCashAmount" javaType="BigDecimal"/>

        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_account_no" property="bankAccountNo" javaType="String"/>
        <result column="branch_name" property="branchName" javaType="String"/>

        <result column="multi_project" property="multiProject" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmDepositSlipPageResultVo">
                        <result column="deposit_slip_no" property="depositSlipNo" javaType="String"/>
                        <result column="deposit_status" property="depositStatus" javaType="String"/>
                        <result column="paid_in_amount" property="paidInAmount" javaType="BigDecimal"/>
                        <result column="collection_doc_qty" property="collectionDocQty" javaType="String"/>
                        <result column="transaction_no" property="transactionNo" javaType="String"/>
                        <result column="deposit_time" property="depositTime" javaType="Date"/>
                        <result column="depositor" property="depositor" javaType="String"/>
                        <result column="voucher_upload_status" property="voucherUploadStatus" javaType="String"/>
                        <result column="bank_voucher" property="bankVoucher" javaType="String"/>
                        <result column="uploader_id" property="uploaderId" javaType="String"/>
                        <result column="uploader_name" property="uploaderName" javaType="String"/>
                        <result column="uploader_date" property="uploaderDate" javaType="Date"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="receipt_no" property="receiptNo" javaType="String"/>
                        <result column="project_id" property="projectId" javaType="String"/>
                        <result column="bank_code" property="bankCode" javaType="String"/>
                        <result column="bank_name" property="bankName" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>

        <result column="receipt_bank_acct" property="receiptBankAcct" javaType="String"/>
        <result column="total_cash_amount" property="totalCashAmount" javaType="BigDecimal"/>

        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_account_no" property="bankAccountNo" javaType="String"/>
        <result column="branch_name" property="branchName" javaType="String"/>

        <result column="multi_project" property="multiProject" javaType="String"/>
    </resultMap>


    <resultMap id="SingleResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmDepositSlipVo">
        <result column="deposit_slip_no" property="depositSlipNo" javaType="String"/>
        <result column="deposit_status" property="depositStatus" javaType="String"/>
        <result column="paid_in_amount" property="paidInAmount" javaType="BigDecimal"/>
        <result column="collection_doc_qty" property="collectionDocQty" javaType="String"/>
        <result column="transaction_no" property="transactionNo" javaType="String"/>
        <result column="deposit_time" property="depositTime" javaType="String"/>
        <result column="depositor" property="depositor" javaType="String"/>
        <result column="voucher_upload_status" property="voucherUploadStatus" javaType="String"/>
        <result column="bank_voucher" property="bankVoucher" javaType="String"/>
        <result column="uploader_id" property="uploaderId" javaType="String"/>
        <result column="uploader_name" property="uploaderName" javaType="String"/>
        <result column="uploader_date" property="uploaderDate" javaType="Date"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="receipt_no" property="receiptNo" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="bank_code" property="bankCode" javaType="String"/>
        <result column="bank_name" property="bankName" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>

        <result column="receipt_bank_acct" property="receiptBankAcct" javaType="String"/>
        <result column="total_cash_amount" property="totalCashAmount" javaType="BigDecimal"/>

        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_account_no" property="bankAccountNo" javaType="String"/>
        <result column="branch_name" property="branchName" javaType="String"/>

        <result column="multi_project" property="multiProject" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.deposit_id
        ,base.deposit_slip_no
        ,base.deposit_status
        ,base.paid_in_amount
        ,base.collection_doc_qty
        ,base.transaction_no
        ,base.deposit_time
        ,base.depositor
        ,base.voucher_upload_status
        ,base.bank_voucher
        ,base.uploader_id
        ,base.uploader_name
        ,base.uploader_date
        ,base.del_flag
        ,base.receipt_no
        ,base.project_id
        ,base.bank_code
        ,base.bank_Name
        ,base.total_cash_amount
        ,base.receipt_bank_acct
        ,base.bank_account_name
        ,base.bank_account_no
        ,base.branch_name
        ,base.multi_project
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_deposit_slip base
        <where>
            <if test="'' != vo.depositId and vo.depositId != null">
                and base.deposit_id = #{vo.depositId}
            </if>
            <if test="'' != vo.depositSlipNo and vo.depositSlipNo != null">
                and base.deposit_slip_no   like CONCAT('%',#{vo.depositSlipNo},'%')
            </if>
            <if test="'' != vo.depositSlipNoV2 and vo.depositSlipNoV2 != null">
                and base.deposit_slip_no = #{vo.depositSlipNoV2}
            </if>
            <if test="vo.depositSlipNoList != null and vo.depositSlipNoList.size()>0">
                and base.deposit_slip_no IN
                <foreach collection="vo.depositSlipNoList" index="index" item="depositSlipNo" separator="," close=")" open="(">
                    #{depositSlipNo}
                </foreach>
            </if>
            <if test="'' != vo.depositStatus and vo.depositStatus != null">
                and base.deposit_status = #{vo.depositStatus}
            </if>
            <if test="vo.paidInAmount != null">
                and base.paid_in_amount = #{vo.paidInAmount}
            </if>
            <if test="'' != vo.collectionDocQty and vo.collectionDocQty != null">
                and base.collection_doc_qty = #{vo.collectionDocQty}
            </if>
            <if test="'' != vo.transactionNo and vo.transactionNo != null">
                and base.transaction_no = #{vo.transactionNo}
            </if>
            <if test="vo.depositTime != null">
                and substring(base.deposit_time,1,10) = #{vo.depositTime}
            </if>

            <if test="'' != vo.startDate and vo.startDate != null">
                and SUBSTRING(base.deposit_time,1,10) <![CDATA[ >= ]]> #{vo.startDate}
            </if>
            <if test="'' != vo.endDate and vo.endDate != null">
                and SUBSTRING(base.deposit_time,1,10)  <![CDATA[ <= ]]> #{vo.endDate}
            </if>

            <if test="'' != vo.depositor and vo.depositor != null">
                and base.depositor = #{vo.depositor}
            </if>
            <if test="'' != vo.voucherUploadStatus and vo.voucherUploadStatus != null">
                and base.voucher_upload_status = #{vo.voucherUploadStatus}
            </if>
            <if test="'' != vo.bankVoucher and vo.bankVoucher != null">
                and base.bank_voucher = #{vo.bankVoucher}
            </if>
            <if test="'' != vo.uploaderId and vo.uploaderId != null">
                and base.uploader_id = #{vo.uploaderId}
            </if>
            <if test="'' != vo.uploaderName and vo.uploaderName != null">
                and base.uploader_name = #{vo.uploaderName}
            </if>
            <if test="vo.uploaderDate != null">
                and base.uploader_date = #{vo.uploaderDate}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
        </where>
        order by base.modify_time desc,base.deposit_time desc
    </select>


    <select id="selectByDepositSlipNo" resultMap="SingleResultMap">
        select
            base.object_version_number
             ,base.create_user
             ,base.create_time
             ,base.modify_user
             ,base.modify_time
             ,base.tenant_id
             ,base.cid
             ,base.deposit_id
             ,base.deposit_slip_no
             ,base.deposit_status
             ,base.paid_in_amount
             ,base.collection_doc_qty
             ,base.transaction_no
             ,substring(base.deposit_time,1,10) deposit_time
             ,base.depositor
             ,base.voucher_upload_status
             ,base.bank_voucher
             ,base.uploader_id
             ,base.uploader_name
             ,base.uploader_date
             ,base.del_flag
             ,base.receipt_no
             ,base.project_id
             ,base.bank_code
             ,base.bank_Name
             ,base.total_cash_amount
             ,base.receipt_bank_acct
             ,base.bank_account_name
             ,base.bank_account_no
             ,base.branch_name
             ,base.multi_project
        from bbpm_deposit_slip base
        where base.deposit_slip_no = #{depositSlipNo}
    </select>
</mapper>
