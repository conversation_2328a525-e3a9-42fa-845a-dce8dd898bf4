<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmCashPledgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmCashPledgeEntity">
                            <id column="cash_pledge_id" property="cashPledgeId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="bill_id" property="billId" javaType="String"/>
                            <result column="contract_code" property="contractCode" javaType="String"/>
                            <result column="charge_subject_no" property="chargeSubjectNo" javaType="String"/>
                            <result column="sign_status" property="signStatus" javaType="String"/>
                            <result column="file" property="file" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="project_id" property="projectId" javaType="String"/>
                            <result column="project_format" property="projectFormat" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmCashPledgePageResultVo">
                        <result column="bill_id" property="billId" javaType="String"/>
                        <result column="contract_code" property="contractCode" javaType="String"/>
                        <result column="charge_subject_no" property="chargeSubjectNo" javaType="String"/>
                        <result column="sign_status" property="signStatus" javaType="String"/>
                        <result column="file" property="file" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="project_id" property="projectId" javaType="String"/>
                        <result column="project_format" property="projectFormat" javaType="String"/>
    </resultMap>

    <resultMap id="voMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmCashPledgeVo">
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="charge_subject_no" property="chargeSubjectNo" javaType="String"/>
        <result column="sign_status" property="signStatus" javaType="String"/>
        <result column="file" property="file" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="project_format" property="projectFormat" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="create_user" property="createUser" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.cash_pledge_id
        ,base.bill_id
        ,base.contract_code
        ,base.charge_subject_no
        ,base.sign_status
        ,base.file
        ,base.del_flag
        ,base.project_id
        ,base.project_format
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_cash_pledge base
        <where>
            <if test="'' != vo.cashPledgeId and vo.cashPledgeId != null">
                and base.cash_pledge_id = #{vo.cashPledgeId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.chargeSubjectNo and vo.chargeSubjectNo != null">
                and base.charge_subject_no = #{vo.chargeSubjectNo}
            </if>
            <if test="'' != vo.signStatus and vo.signStatus != null">
                and base.sign_status = #{vo.signStatus}
            </if>
            <if test="'' != vo.file and vo.file != null">
                and base.file = #{vo.file}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
        order by base.create_time desc
    </select>

    <select id="selectByConditions" resultMap="voMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_cash_pledge base
        <where>
            <if test="'' != vo.cashPledgeId and vo.cashPledgeId != null">
                and base.cash_pledge_id = #{vo.cashPledgeId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="'' != vo.chargeSubjectNo and vo.chargeSubjectNo != null">
                and base.charge_subject_no = #{vo.chargeSubjectNo}
            </if>
            <if test="'' != vo.signStatus and vo.signStatus != null">
                and base.sign_status = #{vo.signStatus}
            </if>
            <if test="'' != vo.file and vo.file != null">
                and base.file = #{vo.file}
            </if>
            <if test="'' != vo.createTimeStart and vo.createTimeStart != null">
                and DATE_FORMAT(base.create_time, '%Y-%m-%d') <![CDATA[ >= ]]> STR_TO_DATE( #{vo.createTimeStart}, '%Y-%m-%d')
            </if>
            <if test="'' != vo.createTimeEnd and vo.createTimeEnd != null">
                and DATE_FORMAT(base.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> STR_TO_DATE( #{vo.createTimeEnd}, '%Y-%m-%d')
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
        order by base.create_time desc
    </select>
</mapper>
