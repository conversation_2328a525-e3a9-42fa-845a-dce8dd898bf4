<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpDictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpDictEntity">
                            <id column="dict_id" property="dictId" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="type_name" property="typeName" javaType="String"/>
                            <result column="type_code" property="typeCode" javaType="String"/>
                            <result column="code" property="code" javaType="String"/>
                            <result column="meaning" property="meaning" javaType="String"/>
                            <result column="seq_num" property="seqNum" javaType="Integer"/>
                            <result column="expand" property="expand" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpDictPageResultVo">
                        <result column="type_name" property="typeName" javaType="String"/>
                        <result column="type_code" property="typeCode" javaType="String"/>
                        <result column="code" property="code" javaType="String"/>
                        <result column="meaning" property="meaning" javaType="String"/>
                        <result column="seq_num" property="seqNum" javaType="Integer"/>
                        <result column="expand" property="expand" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.dict_id
        ,base.type_name
        ,base.type_code
        ,base.code
        ,base.meaning
        ,base.seq_num
        ,base.expand
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbp_dict base
        <where>
            <if test="'' != vo.dictId and vo.dictId != null">
                and base.dict_id = #{vo.dictId}
            </if>
            <if test="'' != vo.typeName and vo.typeName != null">
                and base.type_name = #{vo.typeName}
            </if>
            <if test="'' != vo.typeCode and vo.typeCode != null">
                and base.type_code = #{vo.typeCode}
            </if>
            <if test="'' != vo.code and vo.code != null">
                and base.code = #{vo.code}
            </if>
            <if test="'' != vo.meaning and vo.meaning != null">
                and base.meaning = #{vo.meaning}
            </if>
            <if test="vo.seqNum != null">
                and base.seq_num = #{vo.seqNum}
            </if>
            <if test="'' != vo.expand and vo.expand != null">
                and base.expand = #{vo.expand}
            </if>
        </where>
    </select>
</mapper>
