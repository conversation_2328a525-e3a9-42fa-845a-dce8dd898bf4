<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.payment.dao.BbpmCollectionMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.payment.entity.BbpmCollectionEntity">
        <id column="collection_id" property="collectionId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="collection_no" property="collectionNo" javaType="String"/>
        <result column="receipt_no" property="receiptNo" javaType="String"/>
        <result column="electronic_voucher" property="electronicVoucher" javaType="String"/>
        <result column="deposit_status" property="depositStatus" javaType="String"/>
        <result column="bill_no" property="billNo" javaType="String"/>
        <result column="bill_cycle" property="billCycle" javaType="String"/>
        <result column="remaining_amount_payable" property="remainingAmountPayable" javaType="BigDecimal"/>
        <result column="total_undeposits" property="totalUndeposits" javaType="BigDecimal"/>
        <result column="project_name" property="projectName" javaType="String"/>
        <result column="residential_quarters_name" property="residentialQuartersName" javaType="String"/>
        <result column="building_no" property="buildingNo" javaType="String"/>
        <result column="unit_no" property="unitNo" javaType="String"/>
        <result column="floor_no" property="floorNo" javaType="String"/>
        <result column="room_no" property="roomNo" javaType="String"/>
        <result column="address" property="address" javaType="String"/>
        <result column="tenant_name" property="tenantName" javaType="String"/>
        <result column="tenant_code" property="tenantCode" javaType="String"/>
        <result column="cert_no" property="certNo" javaType="String"/>
        <result column="tenant_mobile" property="tenantMobile" javaType="String"/>
        <result column="operator_name" property="operatorName" javaType="String"/>
        <result column="charge_code" property="chargeCode" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="charge_date" property="chargeDate" javaType="Date"/>
        <result column="charge_date_start" property="chargeDateStart" javaType="String"/>
        <result column="charge_date_end" property="chargeDateEnd" javaType="String"/>
        <result column="charge_status" property="chargeStatus" javaType="String"/>
        <result column="bank_receipt_no" property="bankReceiptNo" javaType="String"/>
        <result column="bank_receipt_date" property="bankReceiptDate" javaType="Date"/>
        <result column="charge_subject" property="chargeSubject" javaType="String"/>
        <result column="payment_type" property="paymentType" javaType="String"/>
        <result column="charge_money" property="chargeMoney" javaType="BigDecimal"/>
        <result column="ser_fee" property="serFee" javaType="BigDecimal"/>
        <result column="reconciliation_status" property="reconciliationStatus" javaType="String"/>
        <result column="is_pre_payment_offset" property="isPrePaymentOffset" javaType="String"/>
        <result column="bank" property="bank" javaType="String"/>
        <result column="bank_branch" property="bankBranch" javaType="String"/>
        <result column="inter_bank_no" property="interBankNo" javaType="String"/>
        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_account_no" property="bankAccountNo" javaType="String"/>
        <result column="cny" property="cny" javaType="String"/>
        <result column="bank_account_type" property="bankAccountType" javaType="String"/>
        <result column="payment_channel" property="paymentChannel" javaType="String"/>
        <result column="payer_name" property="payerName" javaType="String"/>
        <result column="payment_bank" property="paymentBank" javaType="String"/>
        <result column="payment_bank_account_no" property="paymentBankAccountNo" javaType="String"/>
        <result column="payment_bank_branch_code" property="paymentBankBranchCode" javaType="String"/>
        <result column="receipt" property="receipt" javaType="String"/>
        <result column="bill_charge_subject" property="billChargeSubject" javaType="String"/>
        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="String"/>
        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="String"/>
        <result column="should_pay_amount" property="shouldPayAmount" javaType="BigDecimal"/>
        <result column="payed_amount" property="payedAmount" javaType="BigDecimal"/>
        <result column="pay_channel" property="payChannel" javaType="String"/>
        <result column="pay_time" property="payTime" javaType="String"/>
        <result column="pos_device_id" property="posDeviceId" javaType="String"/>
        <result column="reconciliation_result" property="reconciliationResult" javaType="String"/>
        <result column="contract_id" property="contractId" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="voucher_status" property="voucherStatus" javaType="String"/>
        <result column="accounting_month" property="accountingMonth" javaType="String"/>
        <result column="prepayment_offset_code" property="prepaymentOffsetCode" javaType="String"/>
        <result column="current" property="current" javaType="Integer"/>
        <result column="size" property="size" javaType="Integer"/>
        <result column="full_page" property="fullPage" javaType="String"/>
        <result column="ext1" property="ext1" javaType="String"/>
        <result column="ext2" property="ext2" javaType="String"/>
        <result column="ext3" property="ext3" javaType="String"/>
        <result column="ext4" property="ext4" javaType="String"/>
        <result column="ext5" property="ext5" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo">
        <result column="collection_no" property="collectionNo" javaType="String"/>
        <result column="receipt_no" property="receiptNo" javaType="String"/>
        <result column="electronic_voucher" property="electronicVoucher" javaType="String"/>
        <result column="deposit_status" property="depositStatus" javaType="String"/>
        <result column="bill_no" property="billNo" javaType="String"/>
        <result column="bill_cycle" property="billCycle" javaType="String"/>
        <result column="remaining_amount_payable" property="remainingAmountPayable" javaType="BigDecimal"/>
        <result column="total_undeposits" property="totalUndeposits" javaType="BigDecimal"/>
        <result column="project_name" property="projectName" javaType="String"/>
        <result column="residential_quarters_name" property="residentialQuartersName" javaType="String"/>
        <result column="building_no" property="buildingNo" javaType="String"/>
        <result column="unit_no" property="unitNo" javaType="String"/>
        <result column="floor_no" property="floorNo" javaType="String"/>
        <result column="room_no" property="roomNo" javaType="String"/>
        <result column="address" property="address" javaType="String"/>
        <result column="house_name" property="houseName" javaType="String"/>
        <result column="tenant_name" property="tenantName" javaType="String"/>
        <result column="tenant_code" property="tenantCode" javaType="String"/>
        <result column="cert_no" property="certNo" javaType="String"/>
        <result column="tenant_mobile" property="tenantMobile" javaType="String"/>
        <result column="operator_name" property="operatorName" javaType="String"/>
        <result column="charge_code" property="chargeCode" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="charge_date" property="chargeDate" javaType="Date"/>
        <result column="charge_date_start" property="chargeDateStart" javaType="String"/>
        <result column="charge_date_end" property="chargeDateEnd" javaType="String"/>
        <result column="charge_status" property="chargeStatus" javaType="String"/>
        <result column="bank_receipt_no" property="bankReceiptNo" javaType="String"/>
        <result column="bank_receipt_date" property="bankReceiptDate" javaType="Date"/>
        <result column="charge_subject" property="chargeSubject" javaType="String"/>
        <result column="payment_type" property="paymentType" javaType="String"/>
        <result column="charge_money" property="chargeMoney" javaType="BigDecimal"/>
        <result column="ser_fee" property="serFee" javaType="BigDecimal"/>
        <result column="reconciliation_status" property="reconciliationStatus" javaType="String"/>
        <result column="is_pre_payment_offset" property="isPrePaymentOffset" javaType="String"/>
        <result column="bank" property="bank" javaType="String"/>
        <result column="bank_branch" property="bankBranch" javaType="String"/>
        <result column="inter_bank_no" property="interBankNo" javaType="String"/>
        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_account_no" property="bankAccountNo" javaType="String"/>
        <result column="cny" property="cny" javaType="String"/>
        <result column="bank_account_type" property="bankAccountType" javaType="String"/>
        <result column="payment_channel" property="paymentChannel" javaType="String"/>
        <result column="payer_name" property="payerName" javaType="String"/>
        <result column="payment_bank" property="paymentBank" javaType="String"/>
        <result column="payment_bank_account_no" property="paymentBankAccountNo" javaType="String"/>
        <result column="payment_bank_branch_code" property="paymentBankBranchCode" javaType="String"/>
        <result column="receipt" property="receipt" javaType="String"/>
        <result column="bill_charge_subject" property="billChargeSubject" javaType="String"/>
        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="String"/>
        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="String"/>
        <result column="should_pay_amount" property="shouldPayAmount" javaType="BigDecimal"/>
        <result column="payed_amount" property="payedAmount" javaType="BigDecimal"/>
        <result column="pay_channel" property="payChannel" javaType="String"/>
        <result column="pay_time" property="payTime" javaType="String"/>
        <result column="pos_device_id" property="posDeviceId" javaType="String"/>
        <result column="reconciliation_result" property="reconciliationResult" javaType="String"/>
        <result column="contract_id" property="contractId" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="voucher_status" property="voucherStatus" javaType="String"/>
        <result column="accounting_month" property="accountingMonth" javaType="String"/>
        <result column="prepayment_offset_code" property="prepaymentOffsetCode" javaType="String"/>
        <result column="current" property="current" javaType="Integer"/>
        <result column="size" property="size" javaType="Integer"/>
        <result column="full_page" property="fullPage" javaType="String"/>
        <result column="ext1" property="ext1" javaType="String"/>
        <result column="ext2" property="ext2" javaType="String"/>
        <result column="ext3" property="ext3" javaType="String"/>
        <result column="ext4" property="ext4" javaType="String"/>
        <result column="ext5" property="ext5" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>

        <result column="project_id" property="projectId" javaType="String"/>
    </resultMap>


    <resultMap id="voResultMap" type="com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionVo">
        <result column="collection_no" property="collectionNo" javaType="String"/>
        <result column="receipt_no" property="receiptNo" javaType="String"/>
        <result column="electronic_voucher" property="electronicVoucher" javaType="String"/>
        <result column="deposit_status" property="depositStatus" javaType="String"/>
        <result column="bill_no" property="billNo" javaType="String"/>
        <result column="bill_cycle" property="billCycle" javaType="String"/>
        <result column="remaining_amount_payable" property="remainingAmountPayable" javaType="BigDecimal"/>
        <result column="total_undeposits" property="totalUndeposits" javaType="BigDecimal"/>
        <result column="project_name" property="projectName" javaType="String"/>
        <result column="residential_quarters_name" property="residentialQuartersName" javaType="String"/>
        <result column="building_no" property="buildingNo" javaType="String"/>
        <result column="unit_no" property="unitNo" javaType="String"/>
        <result column="floor_no" property="floorNo" javaType="String"/>
        <result column="room_no" property="roomNo" javaType="String"/>
        <result column="address" property="address" javaType="String"/>
        <result column="house_name" property="houseName" javaType="String"/>
        <result column="tenant_name" property="tenantName" javaType="String"/>
        <result column="tenant_code" property="tenantCode" javaType="String"/>
        <result column="cert_no" property="certNo" javaType="String"/>
        <result column="tenant_mobile" property="tenantMobile" javaType="String"/>
        <result column="operator_name" property="operatorName" javaType="String"/>
        <result column="charge_code" property="chargeCode" javaType="String"/>
        <result column="contract_code" property="contractCode" javaType="String"/>
        <result column="charge_date" property="chargeDate" javaType="String"/>
        <result column="charge_date_start" property="chargeDateStart" javaType="String"/>
        <result column="charge_date_end" property="chargeDateEnd" javaType="String"/>
        <result column="charge_status" property="chargeStatus" javaType="String"/>
        <result column="bank_receipt_no" property="bankReceiptNo" javaType="String"/>
        <result column="bank_receipt_date" property="bankReceiptDate" javaType="Date"/>
        <result column="charge_subject" property="chargeSubject" javaType="String"/>
        <result column="payment_type" property="paymentType" javaType="String"/>
        <result column="charge_money" property="chargeMoney" javaType="BigDecimal"/>
        <result column="ser_fee" property="serFee" javaType="BigDecimal"/>
        <result column="reconciliation_status" property="reconciliationStatus" javaType="String"/>
        <result column="is_pre_payment_offset" property="isPrePaymentOffset" javaType="String"/>
        <result column="bank" property="bank" javaType="String"/>
        <result column="bank_branch" property="bankBranch" javaType="String"/>
        <result column="inter_bank_no" property="interBankNo" javaType="String"/>
        <result column="bank_account_name" property="bankAccountName" javaType="String"/>
        <result column="bank_account_no" property="bankAccountNo" javaType="String"/>
        <result column="cny" property="cny" javaType="String"/>
        <result column="bank_account_type" property="bankAccountType" javaType="String"/>
        <result column="payment_channel" property="paymentChannel" javaType="String"/>
        <result column="payer_name" property="payerName" javaType="String"/>
        <result column="payment_bank" property="paymentBank" javaType="String"/>
        <result column="payment_bank_account_no" property="paymentBankAccountNo" javaType="String"/>
        <result column="payment_bank_branch_code" property="paymentBankBranchCode" javaType="String"/>
        <result column="receipt" property="receipt" javaType="String"/>
        <result column="bill_charge_subject" property="billChargeSubject" javaType="String"/>
        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="String"/>
        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="String"/>
        <result column="should_pay_amount" property="shouldPayAmount" javaType="BigDecimal"/>
        <result column="payed_amount" property="payedAmount" javaType="BigDecimal"/>
        <result column="pay_channel" property="payChannel" javaType="String"/>
        <result column="pay_time" property="payTime" javaType="String"/>
        <result column="pos_device_id" property="posDeviceId" javaType="String"/>
        <result column="reconciliation_result" property="reconciliationResult" javaType="String"/>
        <result column="contract_id" property="contractId" javaType="String"/>
        <result column="bill_id" property="billId" javaType="String"/>
        <result column="voucher_status" property="voucherStatus" javaType="String"/>
        <result column="accounting_month" property="accountingMonth" javaType="String"/>
        <result column="prepayment_offset_code" property="prepaymentOffsetCode" javaType="String"/>
        <result column="current" property="current" javaType="Integer"/>
        <result column="size" property="size" javaType="Integer"/>
        <result column="full_page" property="fullPage" javaType="String"/>
        <result column="ext1" property="ext1" javaType="String"/>
        <result column="ext2" property="ext2" javaType="String"/>
        <result column="ext3" property="ext3" javaType="String"/>
        <result column="ext4" property="ext4" javaType="String"/>
        <result column="ext5" property="ext5" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.collection_id
        ,base.collection_no
        ,base.receipt_no
        ,base.electronic_voucher
        ,base.deposit_status
        ,base.bill_no
        ,base.bill_cycle
        ,base.remaining_amount_payable
        ,base.total_undeposits
        ,base.project_name
        ,base.residential_quarters_name
        ,base.building_no
        ,base.unit_no
        ,base.floor_no
        ,base.room_no
        ,base.address
        ,base.house_name
        ,base.tenant_name
        ,base.tenant_code
        ,base.cert_no
        ,base.tenant_mobile
        ,base.operator_name
        ,base.charge_code
        ,base.contract_code
        ,base.charge_date
        ,base.charge_date_start
        ,base.charge_date_end
        ,base.charge_status
        ,base.bank_receipt_no
        ,base.bank_receipt_date
        ,base.charge_subject
        ,base.payment_type
        ,base.charge_money
        ,base.ser_fee
        ,base.reconciliation_status
        ,base.is_pre_payment_offset
        ,base.bank
        ,base.bank_branch
        ,base.inter_bank_no
        ,base.bank_account_name
        ,base.bank_account_no
        ,base.cny
        ,base.bank_account_type
        ,base.payment_channel
        ,base.payer_name
        ,base.payment_bank
        ,base.payment_bank_account_no
        ,base.payment_bank_branch_code
        ,base.receipt
        ,base.bill_charge_subject
        ,base.charge_subject_begin_date
        ,base.charge_subject_end_date
        ,base.should_pay_amount
        ,base.payed_amount
        ,base.pay_channel
        ,base.pay_time
        ,base.pos_device_id
        ,base.reconciliation_result
        ,base.contract_id
        ,base.bill_id
        ,base.voucher_status
        ,base.accounting_month
        ,base.prepayment_offset_code
        ,base.full_page
        ,base.ext1
        ,base.ext2
        ,base.ext3
        ,base.ext4
        ,base.ext5
        ,base.del_flag
        ,base.project_id
    </sql>



    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_collection base
        <where>
            <if test="'' != vo.collectionId and vo.collectionId != null">
                and base.collection_id = #{vo.collectionId}
            </if>
            <if test="'' != vo.collectionNo and vo.collectionNo != null">
                and base.collection_no = #{vo.collectionNo}
            </if>
            <if test="'' != vo.receiptNo and vo.receiptNo != null">
                and base.receipt_no = #{vo.receiptNo}
            </if>
            <if test="'' != vo.electronicVoucher and vo.electronicVoucher != null">
                and base.electronic_voucher = #{vo.electronicVoucher}
            </if>
            <if test="'' != vo.depositStatus and vo.depositStatus != null">
                and base.deposit_status = #{vo.depositStatus}
            </if>
            <if test="vo.billNo != null">
                and base.bill_no = #{vo.billNo}
            </if>
            <if test="'' != vo.billCycle and vo.billCycle != null">
                and base.bill_cycle = #{vo.billCycle}
            </if>
            <if test="vo.remainingAmountPayable != null">
                and base.remaining_amount_payable = #{vo.remainingAmountPayable}
            </if>
            <if test="vo.totalUndeposits != null">
                and base.total_undeposits = #{vo.totalUndeposits}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name = #{vo.projectName}
            </if>
            <if test="'' != vo.residentialQuartersName and vo.residentialQuartersName != null">
                and base.residential_quarters_name = #{vo.residentialQuartersName}
            </if>
            <if test="'' != vo.buildingNo and vo.buildingNo != null">
                and base.building_no = #{vo.buildingNo}
            </if>
            <if test="'' != vo.unitNo and vo.unitNo != null">
                and base.unit_no = #{vo.unitNo}
            </if>
            <if test="'' != vo.floorNo and vo.floorNo != null">
                and base.floor_no = #{vo.floorNo}
            </if>
            <if test="'' != vo.roomNo and vo.roomNo != null">
                and base.room_no = #{vo.roomNo}
            </if>
            <if test="'' != vo.address and vo.address != null">
                and base.address = #{vo.address}
            </if>
            <if test="'' != vo.houseName and vo.houseName != null">
                and base.house_name  like concat('%',#{vo.houseName},'%')
            </if>
            <if test="'' != vo.tenantName and vo.tenantName != null">
                and base.tenant_name = #{vo.tenantName}
            </if>
            <if test="'' != vo.certNo and vo.certNo != null">
                and base.cert_no = #{vo.certNo}
            </if>
            <if test="'' != vo.tenantMobile and vo.tenantMobile != null">
                and base.tenant_mobile = #{vo.tenantMobile}
            </if>
            <if test="'' != vo.operatorName and vo.operatorName != null">
                and base.operator_name = #{vo.operatorName}
            </if>
            <if test="'' != vo.chargeCode and vo.chargeCode != null">
                and base.charge_code = #{vo.chargeCode}
            </if>
            <if test="'' != vo.contractCode and vo.contractCode != null">
                and base.contract_code = #{vo.contractCode}
            </if>
            <if test="vo.chargeDate != null">
                and base.charge_date = #{vo.chargeDate}
            </if>
            <if test="'' != vo.chargeDateStart and vo.chargeDateStart != null">
                and base.charge_date_start = #{vo.chargeDateStart}
            </if>
            <if test="'' != vo.chargeDateEnd and vo.chargeDateEnd != null">
                and base.charge_date_end = #{vo.chargeDateEnd}
            </if>
            <if test="'' != vo.chargeStatus and vo.chargeStatus != null">
                and base.charge_status = #{vo.chargeStatus}
            </if>
            <if test="'' != vo.bankReceiptNo and vo.bankReceiptNo != null">
                and base.bank_receipt_no = #{vo.bankReceiptNo}
            </if>
            <if test="vo.bankReceiptDate != null">
                and base.bank_receipt_date = #{vo.bankReceiptDate}
            </if>
            <if test="'' != vo.chargeSubject and vo.chargeSubject != null">
                and base.charge_subject = #{vo.chargeSubject}
            </if>
            <if test="'' != vo.paymentType and vo.paymentType != null">
                and base.payment_type = #{vo.paymentType}
            </if>
            <if test="vo.chargeMoney != null">
                and base.charge_money = #{vo.chargeMoney}
            </if>
            <if test="vo.serFee != null">
                and base.ser_fee = #{vo.serFee}
            </if>
            <if test="'' != vo.reconciliationStatus and vo.reconciliationStatus != null">
                and base.reconciliation_status = #{vo.reconciliationStatus}
            </if>
            <if test="'' != vo.isPrePaymentOffset and vo.isPrePaymentOffset != null">
                and base.is_pre_payment_offset = #{vo.isPrePaymentOffset}
            </if>
            <if test="'' != vo.bank and vo.bank != null">
                and base.bank = #{vo.bank}
            </if>
            <if test="'' != vo.bankBranch and vo.bankBranch != null">
                and base.bank_branch = #{vo.bankBranch}
            </if>
            <if test="'' != vo.interBankNo and vo.interBankNo != null">
                and base.inter_bank_no = #{vo.interBankNo}
            </if>
            <if test="'' != vo.bankAccountName and vo.bankAccountName != null">
                and base.bank_account_name = #{vo.bankAccountName}
            </if>
            <if test="'' != vo.bankAccountNo and vo.bankAccountNo != null">
                and base.bank_account_no = #{vo.bankAccountNo}
            </if>
            <if test="'' != vo.cny and vo.cny != null">
                and base.cny = #{vo.cny}
            </if>
            <if test="'' != vo.bankAccountType and vo.bankAccountType != null">
                and base.bank_account_type = #{vo.bankAccountType}
            </if>
            <if test="'' != vo.paymentChannel and vo.paymentChannel != null">
                and base.payment_channel = #{vo.paymentChannel}
            </if>
            <if test="'' != vo.payerName and vo.payerName != null">
                and base.payer_name = #{vo.payerName}
            </if>
            <if test="'' != vo.paymentBank and vo.paymentBank != null">
                and base.payment_bank = #{vo.paymentBank}
            </if>
            <if test="'' != vo.paymentBankAccountNo and vo.paymentBankAccountNo != null">
                and base.payment_bank_account_no = #{vo.paymentBankAccountNo}
            </if>
            <if test="'' != vo.paymentBankBranchCode and vo.paymentBankBranchCode != null">
                and base.payment_bank_branch_code = #{vo.paymentBankBranchCode}
            </if>
            <if test="'' != vo.receipt and vo.receipt != null">
                and base.receipt = #{vo.receipt}
            </if>
            <if test="'' != vo.billChargeSubject and vo.billChargeSubject != null">
                and base.bill_charge_subject = #{vo.billChargeSubject}
            </if>
            <if test="'' != vo.chargeSubjectBeginDate and vo.chargeSubjectBeginDate != null">
                and base.charge_subject_begin_date = #{vo.chargeSubjectBeginDate}
            </if>
            <if test="'' != vo.chargeSubjectEndDate and vo.chargeSubjectEndDate != null">
                and base.charge_subject_end_date = #{vo.chargeSubjectEndDate}
            </if>
            <if test="vo.shouldPayAmount != null">
                and base.should_pay_amount = #{vo.shouldPayAmount}
            </if>
            <if test="vo.payedAmount != null">
                and base.payed_amount = #{vo.payedAmount}
            </if>
            <if test="'' != vo.payChannel and vo.payChannel != null">
                and base.pay_channel = #{vo.payChannel}
            </if>
            <if test="'' != vo.payTime and vo.payTime != null">
                and base.pay_time = #{vo.payTime}
            </if>
            <if test="'' != vo.posDeviceId and vo.posDeviceId != null">
                and base.pos_device_id = #{vo.posDeviceId}
            </if>
            <if test="'' != vo.reconciliationResult and vo.reconciliationResult != null">
                and base.reconciliation_result = #{vo.reconciliationResult}
            </if>
            <if test="'' != vo.contractId and vo.contractId != null">
                and base.contract_id = #{vo.contractId}
            </if>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.voucherStatus and vo.voucherStatus != null">
                and base.voucher_status = #{vo.voucherStatus}
            </if>
            <if test="'' != vo.accountingMonth and vo.accountingMonth != null">
                and base.accounting_month = #{vo.accountingMonth}
            </if>
            <if test="'' != vo.prepaymentOffsetCode and vo.prepaymentOffsetCode != null">
                and base.prepayment_offset_code = #{vo.prepaymentOffsetCode}
            </if>
            <if test="vo.current != null">
                and base.current = #{vo.current}
            </if>
            <if test="vo.size != null">
                and base.size = #{vo.size}
            </if>
            <if test="'' != vo.fullPage and vo.fullPage != null">
                and base.full_page = #{vo.fullPage}
            </if>
            <if test="'' != vo.ext1 and vo.ext1 != null">
                and base.ext1 = #{vo.ext1}
            </if>
            <if test="'' != vo.ext2 and vo.ext2 != null">
                and base.ext2 = #{vo.ext2}
            </if>
            <if test="'' != vo.ext3 and vo.ext3 != null">
                and base.ext3 = #{vo.ext3}
            </if>
            <if test="'' != vo.ext4 and vo.ext4 != null">
                and base.ext4 = #{vo.ext4}
            </if>
            <if test="'' != vo.ext5 and vo.ext5 != null">
                and base.ext5 = #{vo.ext5}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
        order by base.charge_date desc
    </select>


    <select id="selectCollectionById" resultMap="voResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_collection base
        WHERE base.collection_id = #{collectionId}
    </select>


    <select id="selectUndepositsList" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_collection base
        <where>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.depositStatus and vo.depositStatus != null">
                and base.deposit_status = #{vo.depositStatus}
            </if>
            <if test="'' != vo.receiptNo and vo.receiptNo != null">
                and base.receipt_no = #{vo.receiptNo}
            </if>
        </where>
        order by base.charge_date desc
    </select>


    <select id="selectDepositAndCollectionList" resultMap="voResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_collection base
        right join bbpm_deposit_collection_relationship rel on rel.collection_no = base.collection_id
        <where>
              rel.deposit_slip_no=#{depositId}
        </where>
    </select>

    <select id="selectBillAndCollectionList" resultMap="voResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_collection base
        right join bbpm_bill_collection_relationship rel on rel.collection_no = base.collection_id
        <where>
            base.del_flag='1'
            and rel.del_flag='1'
            and rel.bill_no=#{billNo}
        </where>
    </select>

    <update id="updateCertificateStateById">
        update bbpm_collection
        set electronic_voucher = '2'
        where collection_id = #{id}
    </update>
</mapper>
