<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.invoice.dao.BbpmInvoiceQueryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceQueryEntity">
                            <id column="fpqqlsh" property="fpqqlsh" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="fp_dm" property="fpDm" javaType="String"/>
                            <result column="fp_hm" property="fpHm" javaType="String"/>
                            <result column="kprq" property="kprq" javaType="String"/>
                            <result column="kplx" property="kplx" javaType="String"/>
                            <result column="gmf_nsrsbh" property="gmfNsrsbh" javaType="String"/>
                            <result column="gmf_mc" property="gmfMc" javaType="String"/>
                            <result column="gmf_yhzh" property="gmfYhzh" javaType="String"/>
                            <result column="gmf_yhmc" property="gmfYhmc" javaType="String"/>
                            <result column="gmf_dz" property="gmfDz" javaType="String"/>
                            <result column="gmf_dh" property="gmfDh" javaType="String"/>
                            <result column="gmf_dzyx" property="gmfDzyx" javaType="String"/>
                            <result column="gmf_sjh" property="gmfSjh" javaType="String"/>
                            <result column="invoice_status" property="invoiceStatus" javaType="String"/>
                            <result column="invoice_money" property="invoiceMoney" javaType="String"/>
                            <result column="expense_type" property="expenseType" javaType="String"/>
                            <result column="title_type" property="titleType" javaType="String"/>
                            <result column="bz" property="bz" javaType="String"/>
                            <result column="tenant_name" property="tenantName" javaType="String"/>
                            <result column="charge_paymentList" property="chargePaymentlist" javaType="String"/>
                            <result column="pdflj" property="pdflj" javaType="String"/>
                            <result column="house_addr" property="houseAddr" javaType="String"/>
                            <result column="xsf_mc" property="xsfMc" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.invoice.vo.BbpmInvoiceQueryPageResultVo">
                        <result column="fp_dm" property="fpDm" javaType="String"/>
                        <result column="fp_hm" property="fpHm" javaType="String"/>
                        <result column="kprq" property="kprq" javaType="String"/>
                        <result column="kplx" property="kplx" javaType="String"/>
                        <result column="gmf_nsrsbh" property="gmfNsrsbh" javaType="String"/>
                        <result column="gmf_mc" property="gmfMc" javaType="String"/>
                        <result column="gmf_yhzh" property="gmfYhzh" javaType="String"/>
                        <result column="gmf_yhmc" property="gmfYhmc" javaType="String"/>
                        <result column="gmf_dz" property="gmfDz" javaType="String"/>
                        <result column="gmf_dh" property="gmfDh" javaType="String"/>
                        <result column="gmf_dzyx" property="gmfDzyx" javaType="String"/>
                        <result column="gmf_sjh" property="gmfSjh" javaType="String"/>
                        <result column="invoice_status" property="invoiceStatus" javaType="String"/>
                        <result column="invoice_money" property="invoiceMoney" javaType="String"/>
                        <result column="expense_type" property="expenseType" javaType="String"/>
                        <result column="title_type" property="titleType" javaType="String"/>
                        <result column="bz" property="bz" javaType="String"/>
                        <result column="tenant_name" property="tenantName" javaType="String"/>
                        <result column="charge_paymentList" property="chargePaymentlist" javaType="String"/>
                        <result column="pdflj" property="pdflj" javaType="String"/>
                        <result column="house_addr" property="houseAddr" javaType="String"/>
                        <result column="xsf_mc" property="xsfMc" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.tenant_id
        ,base.cid
        ,base.fpqqlsh
        ,base.fp_dm
        ,base.fp_hm
        ,base.kprq
        ,base.kplx
        ,base.gmf_nsrsbh
        ,base.gmf_mc
        ,base.gmf_yhzh
        ,base.gmf_yhmc
        ,base.gmf_dz
        ,base.gmf_dh
        ,base.gmf_dzyx
        ,base.gmf_sjh
        ,base.invoice_status
        ,base.invoice_money
        ,base.expense_type
        ,base.title_type
        ,base.bz
        ,base.tenant_name
        ,base.charge_paymentList
        ,base.pdflj
        ,base.house_addr
        ,base.xsf_mc
        ,base.del_flag
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_invoice_query base
        <where>
            <if test="'' != vo.fpqqlsh and vo.fpqqlsh != null">
                and base.fpqqlsh = #{vo.fpqqlsh}
            </if>
            <if test="'' != vo.fpDm and vo.fpDm != null">
                and base.fp_dm = #{vo.fpDm}
            </if>
            <if test="'' != vo.fpHm and vo.fpHm != null">
                and base.fp_hm = #{vo.fpHm}
            </if>
            <if test="'' != vo.kprq and vo.kprq != null">
                and base.kprq = #{vo.kprq}
            </if>
            <if test="'' != vo.kplx and vo.kplx != null">
                and base.kplx = #{vo.kplx}
            </if>
            <if test="'' != vo.gmfNsrsbh and vo.gmfNsrsbh != null">
                and base.gmf_nsrsbh = #{vo.gmfNsrsbh}
            </if>
            <if test="'' != vo.gmfMc and vo.gmfMc != null">
                and base.gmf_mc = #{vo.gmfMc}
            </if>
            <if test="'' != vo.gmfYhzh and vo.gmfYhzh != null">
                and base.gmf_yhzh = #{vo.gmfYhzh}
            </if>
            <if test="'' != vo.gmfYhmc and vo.gmfYhmc != null">
                and base.gmf_yhmc = #{vo.gmfYhmc}
            </if>
            <if test="'' != vo.gmfDz and vo.gmfDz != null">
                and base.gmf_dz = #{vo.gmfDz}
            </if>
            <if test="'' != vo.gmfDh and vo.gmfDh != null">
                and base.gmf_dh = #{vo.gmfDh}
            </if>
            <if test="'' != vo.gmfDzyx and vo.gmfDzyx != null">
                and base.gmf_dzyx = #{vo.gmfDzyx}
            </if>
            <if test="'' != vo.gmfSjh and vo.gmfSjh != null">
                and base.gmf_sjh = #{vo.gmfSjh}
            </if>
            <if test="'' != vo.invoiceStatus and vo.invoiceStatus != null">
                and base.invoice_status = #{vo.invoiceStatus}
            </if>
            <if test="'' != vo.invoiceMoney and vo.invoiceMoney != null">
                and base.invoice_money = #{vo.invoiceMoney}
            </if>
            <if test="'' != vo.expenseType and vo.expenseType != null">
                and base.expense_type = #{vo.expenseType}
            </if>
            <if test="'' != vo.titleType and vo.titleType != null">
                and base.title_type = #{vo.titleType}
            </if>
            <if test="'' != vo.bz and vo.bz != null">
                and base.bz = #{vo.bz}
            </if>
            <if test="'' != vo.tenantName and vo.tenantName != null">
                and base.tenant_name = #{vo.tenantName}
            </if>
            <if test="'' != vo.chargePaymentlist and vo.chargePaymentlist != null">
                and base.charge_paymentList = #{vo.chargePaymentlist}
            </if>
            <if test="'' != vo.pdflj and vo.pdflj != null">
                and base.pdflj = #{vo.pdflj}
            </if>
            <if test="'' != vo.houseAddr and vo.houseAddr != null">
                and base.house_addr = #{vo.houseAddr}
            </if>
            <if test="'' != vo.xsfMc and vo.xsfMc != null">
                and base.xsf_mc = #{vo.xsfMc}
            </if>
            <if test="vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
</mapper>
