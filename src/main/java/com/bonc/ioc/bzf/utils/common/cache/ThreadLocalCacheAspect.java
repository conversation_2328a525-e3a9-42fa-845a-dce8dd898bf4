package com.bonc.ioc.bzf.utils.common.cache;

import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ThreadLocalCacheAspect
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-11-15 22:21
 **/
@Component
@Aspect
@Slf4j
@Order(1)
public class ThreadLocalCacheAspect {
    @Around(value = "@annotation(localCache)")
    public Object aroundAdvice(ProceedingJoinPoint joinpoint, ThreadLocalCache localCache) throws Throwable {
        Object[] args = joinpoint.getArgs();
        Method method = ((MethodSignature) joinpoint.getSignature()).getMethod();
        String className = joinpoint.getTarget().getClass().getName();
        String methodName = method.getName();
        String key =  getDefaultKey(className, methodName, args);

        Map cache = ThreadLocalCacheManager.getCache();
        if (cache == null) {
            cache = new HashMap();
        }


        Map<String, Object> data = new HashMap<>();
        data.put("methodName", className + "." + methodName);


        if(cache.containsKey(key)){
            log.info("命中,key:{}",key);
            return cache.get(key);
        }

        Object result = null;
        try {
            result = joinpoint.proceed();
            cache.put(key, result);
            ThreadLocalCacheManager.setCache(cache);
            return result;
        } catch (Throwable throwable) {
            throw new RuntimeException(throwable);
        }

    }

    private String getDefaultKey(String className, String methodName, Object[] args) {
        String defaultKey = className + "." + methodName;
        if (args != null) {
            defaultKey = defaultKey + "." + (args!=null && args.length > 0 ? JSONArray.toJSON(args) :"-");
        }
        return defaultKey;
    }



}
