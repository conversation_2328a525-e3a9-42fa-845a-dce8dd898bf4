package com.bonc.ioc.bzf.utils.common.convert;

import java.lang.annotation.*;
/**
 * @description: 微服务字段复制
 * @author: 宋鑫
 * @date: 2022-11-09 20:46
 * @return:
 * @since 1.0.0
 **/
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CopyFieldPoint {
    /**
     * @description: 按照制定字段获取，如果没用定义，用实体字段名获取
     * @author: 宋鑫
     * @date: 2022-11-09 21:15
     * @param: []
     * @return: java.lang.String
     * @since 1.0.0
     **/
    String fieldName() default "";
    /**
     * @description:  为服务分类
     * @author: 宋鑫
     * @date: 2022-11-10 8:24
     * @since 1.0.0
     **/
    String type() ;

}
