package com.bonc.ioc.bzf.utils.common.convert;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName CopyFileUtil
 * @Description 微服务之间的字段复制，只支撑一层的数据
 * @AUTHOR 宋鑫
 * @Date 2022-11-09 20:40
 **/
public class CopyFieldUtil {
    /**
     * @description:
     *  项目信息相关
     **/
    public static final String TYPE_PROJECT = "project";

    /**
     * @description:
     *  房源信息相关
     **/
    public static final String TYPE_HOUSE ="House";

    /**
     * @description: 客户
     * @author: 宋鑫
     * @date: 2022-11-30 22:53
     * @param:
     * @return:
     * @since 1.0.0
     **/
    public static final String TYPE_CUSTOMER = "customer";

    /**
     * @description: 小区
     * @author: 宋鑫
     * @date: 2022-12-22 18:01
     * @param:
     * @return:
     * @since 1.0.0
     **/
    public static final String TYPE_COMMUNITY ="community";
    /**
     * @description:
     * 本地唯一标识
     **/
    public static final String PROJECT_LOCAL_UNIQUE = "projectId";
    /**
     * @description:
     * 微服务数据唯一标识
     **/
    public static final String PROJECT_EXTERNAL_UNIQUE = "infoId";

    /**
     * @description: 微服务数据唯一标识字段类型INT
     * @author: 宋鑫
     * @date: 2022-11-10 8:34
     * @since 1.0.0
     **/
    public static final String UNIQUE_TYPE_INTEGER="INT";
    /**
     * @description: 微服务数据唯一标识字段类型STRING
     * @author: 宋鑫
     * @date: 2022-11-10 8:34
     * @since 1.0.0
     **/
    public static final String UNIQUE_TYPE_STRING="String";

    /**
     * @description:
     *  入住中心
     **/
    public static final String TYPE_CHECKIN ="CheckIn";
    /**
     * @description:
     *  合同中心
     **/
    public static final String TYPE_CONTRACT ="Contract";

    /**
     * @description:
     *  消息中心 通知
     **/
    public static final String TYPE_MESSAGE_NOTICE ="Message_notice";

    /**
     * @description:
     *  消息中心 短信
     **/
    public static final String TYPE_MESSAGE_MESSAGE ="Message_message";
    /**
     * @description: 基于 jsonobject复制
     * @author: 宋鑫
     * @date: 2022-11-10 8:39
     * @param: [result：本服务查询的数据, jo：微服务提供的数据, type:转换制定微服务的属性]
     * @return: void
     * @since 1.0.0
     **/
    public static <T> void transJSONObject(T result, JSONObject jo,String type) throws Exception {
        if(jo!=null){
            Class clazz = result.getClass();
            Field[] fields = clazz.getDeclaredFields();
            for (Field f : fields) {
                String fieldName = f.getName();
                Annotation annotation = f.getAnnotation(CopyFieldPoint.class);
                if(annotation!=null&&StringUtils.isNotEmpty(type)){
                    CopyFieldPoint cfp=(CopyFieldPoint)annotation;
                    if(type.equals(cfp.type())) {
                        if (StringUtils.isNotEmpty(cfp.fieldName())) {
                            fieldName = cfp.fieldName();
                        }
                        if (jo.containsKey(fieldName)) {
                            Object o = jo.get(fieldName);
                            f.setAccessible(true);
                            Class fType=f.getType();
                            if(f.get(result)==null) { //不覆盖
                                if (fType.equals(String.class)) {
                                    f.set(result, (o == null ? null : o.toString()));
                                } else if (fType.equals(Double.class)) {
                                    f.set(result, (o == null ? null : Double.valueOf(o.toString())));
                                } else if (fType.equals(Integer.class)) {
                                    f.set(result, (o == null ? null : Integer.valueOf(o.toString())));
                                } else if (fType.equals(Float.class)) {
                                    f.set(result, (o == null ? null : Float.valueOf(o.toString())));
                                }
                            }
                        }
                    }
                }
            }
        }
    }




    /**
     * @description: 基于BEAN 的复制
     * @author: 宋鑫
     * @date: 2022-11-10 18:22
     * @param: [result, bean, type]
     * @return: void
     * @since 1.0.0
     **/
    @Deprecated
    public static <T> void trans(T result, T bean,String type) throws IllegalAccessException {
        Class clazz = result.getClass();
        Class beanClazz = bean.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            String fieldName = f.getName();
            Annotation annotation = f.getAnnotation(CopyFieldPoint.class);
            if(annotation!=null&&StringUtils.isNotEmpty(type)){
                CopyFieldPoint cfp=(CopyFieldPoint)annotation;
                if(type.equals(cfp.type())) {
                    if (StringUtils.isNotEmpty(cfp.fieldName())) {
                        fieldName = cfp.fieldName();
                    }
                    try {
                        Field field = beanClazz.getDeclaredField(fieldName);
                        field.setAccessible(true);
                        Object v = field.get(bean);
                        f.setAccessible(true);
                        f.set(result,v);
                    } catch (NoSuchFieldException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }
    /**
     * @description:
     * @author: 宋鑫
     * @date: 2022-11-10 13:49
     * @param: [results 本地数据集, jo 微服务数据纪, sourceUnique 本地唯一标识字段, unique 微服务标识字段, uniqueType 微服务标识字段类型, type 微服务类型]
     * @return: void
     * @since 1.0.0
     **/
    public static <T> void transList(List<T> results, JSONArray jo,String sourceUnique,String unique,String uniqueType, String type) throws NoSuchFieldException, IllegalAccessException {
        List<JSONObject> jsonObjects = JSONArray.parseArray(JSONArray.toJSONString(jo), JSONObject.class);
        if(results!=null){
            Map<?, JSONObject> map =null;
            if(CopyFieldUtil.UNIQUE_TYPE_INTEGER.equals(uniqueType)){
                map = jsonObjects.stream().collect(Collectors.toMap(item -> item.getInteger(unique), item -> item));
            }else if(CopyFieldUtil.UNIQUE_TYPE_STRING.equals(uniqueType)){
                map = jsonObjects.stream().collect(Collectors.toMap(item -> item.getString(unique), item -> item));
            }
            for(T t: results){
                Field field =t.getClass().getDeclaredField(sourceUnique);
                field.setAccessible(true);
                Object v= field.get(t);
                JSONObject val = null;
                if(v!=null) {
                    if (CopyFieldUtil.UNIQUE_TYPE_INTEGER.equals(uniqueType)) {
                        Integer key = Integer.parseInt(v.toString());
                        val = map.get(key);
                    } else if (CopyFieldUtil.UNIQUE_TYPE_STRING.equals(uniqueType)) {
                        val = map.get(v);
                    }
                    if (val != null) {
                        CopyFieldUtil.trans(t, val, type);
                    }
                }

            }
        }
    }



    public static void main(String[] args) {
//        System.out.println( Integer.parseInt("70"));
//        JSONObject o= new JSONObject();
//        o.put("projectId","1");
//        o.put("projectName","12sd");
//        JSONObject o1= new JSONObject();
//        o1.put("projectId","2");
//        o1.put("projectName","测试");
//        JSONArray ja= new JSONArray();
//        ja.add(o1);
//        ja.add(o);
//        List<BscIntentionInfoPageResultVo> list =new ArrayList<>();
//
//        BscIntentionInfoPageResultVo vo =new BscIntentionInfoPageResultVo();
//        vo.setProjectId("1");
//        list.add(vo);
//        BscIntentionInfoPageResultVo vo1 =new BscIntentionInfoPageResultVo();
//        vo1.setProjectId("2");
//        list.add(vo1);
//
//        BscIntentionInfoPageResultVo vo3 =new BscIntentionInfoPageResultVo();
//        vo3.setProjectId("3");
        //list.add(vo3);
//        try {
//            CopyFieldUtil.transList(list,ja,"projectId",CopyFieldUtil.UNIQUE_TYPE_STRING,CopyFieldUtil.PROJECT);
//        } catch (IllegalAccessException e) {
//            throw new RuntimeException(e);
//        } catch (NoSuchFieldException e) {
//            throw new RuntimeException(e);
//        }
//        System.out.println(11);
    }

}
