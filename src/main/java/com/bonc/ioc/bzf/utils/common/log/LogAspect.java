package com.bonc.ioc.bzf.utils.common.log;

import com.alibaba.fastjson.JSONObject;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.common.util.TraceUtils;
import com.bonc.ioc.common.utils.CurrentUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;
import java.util.UUID;

/**
 * @ClassName LogAspect
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-11-30 21:05
 **/
@Component
@Aspect
@Slf4j
@Order(100)
public class LogAspect {
    @Resource
    private KafkaSender sender;

    @Value("${log.topic:LOG_STATE}")
    private String topic;

    @Around(value = "@annotation(logPoint)")
    public Object aroundAdvice(ProceedingJoinPoint joinpoint, LogPoint logPoint) throws Throwable {

       // HttpServletRequest request =  ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
       // log.info("ip:{}",request.getRemoteAddr());
        Object[] args = joinpoint.getArgs();
        Method method = ((MethodSignature) joinpoint.getSignature()).getMethod();
        String methodName = this.getUrl(method,logPoint);
        String traceId = TraceUtils.getTraceId();
        Object result = null;
        LogVo logVo = new LogVo();
        logVo.setTraceId(traceId);
        logVo.setParams(JSONObject.toJSONString(Arrays.asList(args), SerializerFeature.WriteMapNullValue));
        logVo.setTime(new Date());
        logVo.setUserId(CurrentUtil.getUserId());
        logVo.setSystemType(logPoint.system());
        logVo.setMethodName(methodName);
        logVo.setType(logPoint.type());
        Long start=System.currentTimeMillis();
        try {
            result = joinpoint.proceed();
            logVo.setResponse(result==null ? null :JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue));
            return result;
        } catch (Throwable throwable) {
            if(throwable!=null && throwable.getMessage()!=null) {
                if (throwable.getMessage().length() > 500) { //异常信息只获取前500个字符信息
                    logVo.setExceptionMsg(throwable.getMessage().substring(0, 500));
                } else {
                    logVo.setExceptionMsg(throwable.getMessage());
                }
            }
            throw new RuntimeException(throwable);
        } finally {
            Long end= System.currentTimeMillis();
            Long duration = end -start;
            logVo.setDuration(duration);
            String content=JSONObject.toJSONString(logVo, SerializerFeature.WriteMapNullValue);
            try {
                sender.send(topic, UUID.randomUUID().toString(), content);
            }catch(Exception e){
                log.error("发送消息失败："+e.getMessage());
            }
        }
    }
    /**
     * @description: 获取请求地址
     * @author: 宋鑫
     * @date: 2022-12-08 13:27
     * @param: [method, logPoint]
     * @return: java.lang.String
     * @since 1.0.0
     **/
    private String getUrl(Method method,LogPoint logPoint){
        String methodName = null;
        if(StringUtils.isNotBlank(logPoint.path())){
            methodName = logPoint.path();
        }else {
            GetMapping getMapping = method.getAnnotation(GetMapping.class);

            if (getMapping != null) {
                methodName = getMapping.value()[0];
            } else {
                PostMapping postMapping = method.getAnnotation(PostMapping.class);
                methodName = postMapping.value()[0];
            }
        }
        return methodName;
    }

    /**
     * @description: 获取中心地址
     * @author: 宋鑫
     * @date: 2022-12-08 14:06
     * @param: [joinpoint, logPoint]
     * @return: java.lang.String
     * @since 1.0.0
     **/
//    private String getSystem(ProceedingJoinPoint joinpoint,LogPoint logPoint){
//        if(StringUtils.isNotBlank(logPoint.system())){
//            return logPoint.system();
//        }else{
//            Class clazz = joinpoint.getTarget().getClass().getSuperclass();
//            FeignClient feignClient = (FeignClient)clazz.getm.getAnnotation(FeignClient.class);
//            if(feignClient!=null) {
//                return feignClient.value();
//            }else {
//                return "未知系统";
//            }
//        }
//    }
}
