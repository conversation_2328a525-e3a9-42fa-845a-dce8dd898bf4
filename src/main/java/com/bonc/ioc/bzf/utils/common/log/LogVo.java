package com.bonc.ioc.bzf.utils.common.log;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @ClassName LogVo
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-12-06 21:02
 **/

public class LogVo extends McpBaseVo implements java.io.Serializable{

    /**
     * traceId
     */
    @ApiModelProperty(value = "traceId")
    private String traceId;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    private String params;

    /**
     * 响应报文
     */
    @ApiModelProperty(value = "响应报文")
    private String response;

    /**
     * 操作用户
     */
    @ApiModelProperty(value = "操作用户")
    private String userId;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    /**
     * 所属中心
     */
    @ApiModelProperty(value = "所属中心")
    private String systemType;



    /**
     * 返回报文类型
     */
    @ApiModelProperty(value = "返回报文类型")
    private String type;

    /**
     * 操作状态（1：成功，2：失败）
     */
    @ApiModelProperty(value = "操作状态（1：成功，2：失败）")
    private String code;


    private String exceptionMsg;


    private Long duration;


    private String methodName;

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getExceptionMsg() {
        return exceptionMsg;
    }

    public void setExceptionMsg(String exceptionMsg) {
        this.exceptionMsg = exceptionMsg;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


}
