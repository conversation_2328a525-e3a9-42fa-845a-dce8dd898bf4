package com.bonc.ioc.bzf.utils.common.cache;


import java.util.Map;

/**
 * @ClassName ThreadLocalCacheManager
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-11-15 22:25
 **/
public class ThreadLocalCacheManager {

    private static ThreadLocal<Map> threadLocalCache = new ThreadLocal<>();

    public static void setCache(Map value) {
        threadLocalCache.set(value);
    }

    public static Map getCache() {
        return threadLocalCache.get();
    }

    public static void removeCache() {
        threadLocalCache.remove();
    }

    public static void removeCache(String key) {
        Map cache = threadLocalCache.get();
        if (cache != null) {
            cache.remove(key);
        }
    }

}
