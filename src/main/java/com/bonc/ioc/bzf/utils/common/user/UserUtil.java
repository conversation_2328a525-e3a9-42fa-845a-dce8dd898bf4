package com.bonc.ioc.bzf.utils.common.user;

import com.bonc.ioc.bzf.business.adjust.utils.RequestUtil;
import com.bonc.ioc.bzf.business.payment.feign.feign.BmsFeignClient;
import com.bonc.ioc.common.exception.McpException;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserReq;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserResp;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserAuthReq;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserAuthResp;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户相关 工具类
 *
 * <AUTHOR>
 * @since 2023/5/29
 */
@Slf4j
@Component
public class UserUtil {

    /**
     * 用户中心 feign实例
     */
    @Resource
    private BmsFeignClient bmsFeignClient;

    /**
     * 获取用户id
     *
     * @return 用户id
     */
    public String getUserId() {
        return getUserId(RequestUtil.getUserToken());
    }

    /**
     * 获取用户id
     *
     * @param request
     * @return
     */
    public String getUserId(HttpServletRequest request) {
        return getUserId(RequestUtil.getUserToken(request));
    }

    /**
     * 获取用户id
     *
     * @param token
     * @return
     */
    public String getUserId(String token) {
        if (StringUtils.isBlank(token)) {
            return null;
        }
        Request<BmsUserRpcServiceUserAuthReq> req = new Request<>();
        BmsUserRpcServiceUserAuthReq data = new BmsUserRpcServiceUserAuthReq();
        data.setToken(token);
        req.setData(data);
        Response<BmsUserRpcServiceUserAuthResp> response = bmsFeignClient.userAuth(req);
        if (response.isSuccess()) {
            return response.getData().getUserId();
        } else {
            log.error("获取用户名称失败:" + response.toString());
            throw new McpException("获取用户名称失败");
        }
    }

    /**
     * 根据用户id获取用户名
     *
     * @param userId 用户id
     * @return 用户名
     */
    public String getUserName(String userId) {
        Request<BmsUserRpcServiceGetOneUserReq> req = new Request<>();
        BmsUserRpcServiceGetOneUserReq data = new BmsUserRpcServiceGetOneUserReq();
        data.setId(userId);
        req.setData(data);
        Response<BmsUserRpcServiceGetOneUserResp> response = bmsFeignClient.getOneUser(req);
        if (response.isSuccess()) {
            return response.getData().getUserRealName();
        } else {
            log.error("获取审批人名称失败:" + response.toString());
            throw new McpException("获取审批人名称失败");
        }
    }

    /**
     * 根据用户id获取用户名
     *
     * @return 用户名
     */
    public String getUserName() {
        return getUserName(getUserId());
    }
}
