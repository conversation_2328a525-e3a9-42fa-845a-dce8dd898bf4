package com.bonc.ioc.bzf.utils.common.log;

import java.lang.annotation.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogPoint {
    /**
     * @description: 所属中心
     * @author: 宋鑫
     * @date: 2022-12-08 12:54
     * @param: []
     * @return: java.lang.String
     * @since 1.0.0
     **/
    String system();

    /**
     * @description: 返回报文格式分类
     * @author: 宋鑫
     * @date: 2022-12-08 12:53
     * @param: []
     * @return: java.lang.String
     * @since 1.0.0
     **/
    String type() default "bonc";

    /**
     * @description: 请求地址
     * @author: 宋鑫
     * @date: 2022-12-08 13:14
     * @param: []
     * @return: java.lang.String
     * @since 1.0.0
     **/
    String path() default "";
}
