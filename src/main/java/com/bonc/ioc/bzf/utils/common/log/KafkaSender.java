package com.bonc.ioc.bzf.utils.common.log;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * @ClassName KafkaSender
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-12-06 20:02
 **/
@Component
@Slf4j
public class KafkaSender {
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public void send(String topic, String taskid, String jsonStr) {
        //发送消息
        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(topic, taskid, jsonStr);
        future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
            @Override
            //推送成功
            public void onSuccess(SendResult<String, String> result) {
                log.info(topic + " 生产者 发送消息成功：" );
            }

            @Override
            //推送失败
            public void onFailure(Throwable ex) {
                log.info(topic + " 生产者 发送消息失败：" + ex.getMessage());
            }
        });
    }
}
