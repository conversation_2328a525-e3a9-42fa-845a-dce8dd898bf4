package com.bonc.ioc.bzf.utils.common.cache;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import java.io.IOException;
/**
 * @ClassName ThreadLocalCacheFilter
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-11-15 22:24
 **/

@Slf4j
public class ThreadLocalCacheFilter implements Filter {

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {

        filterChain.doFilter(servletRequest, servletResponse);
        // 执行完后清除缓存
        ThreadLocalCacheManager.removeCache();
    }

}