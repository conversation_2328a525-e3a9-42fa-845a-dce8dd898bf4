package com.bonc.ioc.bzf.utils.common.cache;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName FilterConfig
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-12-25 13:04
 **/
@Configuration
public class ThreadLocalCacheFilterConfig {

    @Bean
    public FilterRegistrationBean registerAuthFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new ThreadLocalCacheFilter());
        registration.addUrlPatterns("/*");
        registration.setName("threadLocalCacheFilter");
        registration.setOrder(1);  //值越小，Filter越靠前。
        return registration;
    }
}
