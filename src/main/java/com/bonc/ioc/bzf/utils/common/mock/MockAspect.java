package com.bonc.ioc.bzf.utils.common.mock;


import com.alibaba.dubbo.common.json.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.vo.BusinessGeneratePaymentByGXResultVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeBankResultV2Vo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsResultVo;
import com.bonc.ioc.bzf.utils.common.cache.ThreadLocalCache;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.client.RestTemplate;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Aspect
@Slf4j
public class MockAspect {

    @Value("${mock.enabled:false}")
    private Boolean enabled;

    @Value("${mock.url:}")
    private String mockUrl;

    private List<String> feignValues;

    @Around(value = "@annotation(Mock)")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        if (enabled){//执行MOCK
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            String key = method.getAnnotation(Mock.class).key();
            Object result = null;
            if(method.getAnnotation(PostMapping.class)!=null){
               result = this.postMethod(this.mockUrl(method),getResutBean(key));
            }else if(method.getAnnotation(GetMapping.class)!=null){
                result = this.getMethod(this.mockUrl(method),getResutBean(key));
            }
            return result;
        }else{ //不执行MOCK
            return joinPoint.proceed();
        }
    }

    private Object postMethod(String url ,ParameterizedTypeReference typeRef){
        log.info(String.format("mock发送地址：%s",url));
        RestTemplate restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity requestEntity = new HttpEntity(new JSONObject(), headers);
        Object o = restTemplate.exchange(url, HttpMethod.POST,requestEntity,typeRef).getBody();
        log.info(String.format("mock返回报文：%s",JSONObject.toJSONString(o)));
        return o;
    }

    private Object getMethod(String url,ParameterizedTypeReference typeRef){
        log.info(String.format("mock发送地址：%s",url));
        RestTemplate restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity requestEntity = new HttpEntity(new JSONObject(), headers);
        Object o = restTemplate.exchange(url, HttpMethod.GET,requestEntity,typeRef).getBody();
        log.info(String.format("mock返回报文：%s",JSONObject.toJSONString(o)));
        return o;
    }

    public String mockUrl(Method method){
        String key = method.getAnnotation(Mock.class).key();
        Class clazz = method.getDeclaringClass();
        FeignClient feignClient = (FeignClient)clazz.getAnnotation(FeignClient.class);
        String path = feignClient.path();
        String methodPath = null;
        if(method.getAnnotation(PostMapping.class)!=null){
            methodPath = method.getAnnotation(PostMapping.class).value()[0];
        }else if(method.getAnnotation(GetMapping.class)!=null){
            methodPath = method.getAnnotation(GetMapping.class).value()[0];
        }
        return mockUrl + path + methodPath +"?apipost_id=" + key + "&mockReturnType=" + returnType(method);
    }

    public String returnType(Method method){
        return method.getAnnotation(Mock.class).returnType();
    }


    private ParameterizedTypeReference getResutBean(String key){
        if("2356c81bfcb007".equals(key)){ //3.39. 商业合同账单预览接口
            return new ParameterizedTypeReference<ChargeRespondVo<PreviewBillsResultVo>>() {};
        }
        else if("2503eedff9a003".equals(key)) { //3.44根据项目查询保障房收款开户行
            return new ParameterizedTypeReference<ChargeRespondVo<List<ChargeBankResultV2Vo>>>() {};
        }
        else if("2716dd1cfcb00a".equals(key)){ //3.58.账单关闭或开启接口
            return new ParameterizedTypeReference<ChargeRespondVo>() {};
        }
        else if("2719e1fdbcb03a".equals(key)){ // 3.59.商业合同变更退款生成付款单接口
            return new ParameterizedTypeReference<ChargeRespondVo<BusinessGeneratePaymentByGXResultVo>>() {};
        }
        else{
            return new ParameterizedTypeReference<String>() {};
        }
    }

}
