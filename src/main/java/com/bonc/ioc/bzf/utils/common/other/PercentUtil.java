package com.bonc.ioc.bzf.utils.common.other;

import org.apache.commons.lang3.StringUtils;

/**
 * 百分比相关 工具类
 *
 * <AUTHOR>
 * @since 2023/6/10
 */
public class PercentUtil {

    /**
     * 百分比转换double
     *
     * @param percent 百分比
     * @return double
     */
    public static String parsePercentToDouble(String percent) {
        if (StringUtils.isBlank(percent)) {
            return null;
        }
        return String.format("%.4f", Double.parseDouble(percent) * 0.01);
    }

    /**
     * 百分比格式化
     *
     * @param percent    百分比
     * @param mult       乘数
     * @param leaveDigit 留位
     * @return 格式化的百分比
     */
    public static String formatPercent(String percent, double mult, String leaveDigit) {
        if (StringUtils.isBlank(percent)) {
            return null;
        }
        return String.format(leaveDigit, Double.parseDouble(percent) * mult);
    }

    /**
     * 百分比转换double
     *
     * @param percent 百分比
     * @return double
     */
    public static String parsePercentToDouble(float percent) {
        return String.format("%.2f", percent);
    }

    /**
     * 百分比转换double
     *
     * @param percent 百分比
     * @return double
     */
    public static String parsePercentToDouble(double percent) {
        return String.format("%.4f", percent * 0.01);
    }

    /**
     * 构造方法
     */
    private PercentUtil() {
    }
}
