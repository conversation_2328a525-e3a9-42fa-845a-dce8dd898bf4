package com.bonc.ioc.bzf.utils.common.convert;


import com.bonc.ioc.bzf.business.payment.feign.feign.BmsFeignClient;

import com.bonc.ioc.bzf.business.payment.vo.BaseVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserReq;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserResp;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName UserAspect
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-12-08 20:29
 **/
@Component
@Aspect
@Slf4j
@Order(2)
public class UserAspect {

    @Autowired
    private BmsFeignClient client;
    @Around(value = "@annotation(point)")
    public Object aroundAdvice(ProceedingJoinPoint joinpoint, UserPoint point) throws Throwable {
        try {
            Object result = joinpoint.proceed();
            if(result instanceof AppReply){
                AppReply appReply = (AppReply) result;
                if(appReply.getData() instanceof BaseVo) { //单个数据
                    Object item = appReply.getData();
                    try {
                        set(item);
                    } catch (Exception e) {
                       throw new McpException(e.getMessage());
                    }
                }else if(appReply.getData() instanceof PageResult){ //分页数据
                    PageResult pageResult = (PageResult)appReply.getData();
                    if(pageResult.getRows()!=null){
                        List<Object> list = (ArrayList)pageResult.getRows();
                        list.forEach(item->{
                            try {
                                set(item);
                            } catch (Exception e) {
                                throw new McpException(e.getMessage());
                            }
                        });
                    }
                }
            }
            return result;
        } catch (McpException throwable) {
            throw new McpException("获取用户信息失败："+throwable.getMessage());
        }
    }
    private void set(Object item) throws Exception{
        Field fieldCreateId = item.getClass().getSuperclass().getDeclaredField("createUser");
        Field fieldCreateName= item.getClass().getSuperclass().getDeclaredField("createUserName");
        this.setName(fieldCreateId,fieldCreateName,item);
        Field fieldModifyUserId = item.getClass().getSuperclass().getDeclaredField("modifyUser");
        Field fieldModifyUserName= item.getClass().getSuperclass().getDeclaredField("modifyUserName");
        this.setName(fieldModifyUserId,fieldModifyUserName,item);

        Field fieldPublishUserId = item.getClass().getSuperclass().getDeclaredField("publishUser");
        Field fieldPublishUserName= item.getClass().getSuperclass().getDeclaredField("publishUserName");
        this.setName(fieldPublishUserId,fieldPublishUserName,item);

        Field fieldExamineUserId = item.getClass().getSuperclass().getDeclaredField("examineId");
        Field fieldExamineUserName= item.getClass().getSuperclass().getDeclaredField("examineName");
        this.setName(fieldExamineUserId,fieldExamineUserName,item);

    }
    private void setName(Field idField,Field nameFiled,Object item) throws Exception{
        if(idField!=null&&nameFiled!=null) {
            idField.setAccessible(true);
            nameFiled.setAccessible(true);
            Object _id = idField.get(item);
            if (_id != null) {
                String id = (String) _id;
                if(StringUtils.isNotBlank(id)) {
                    Request<BmsUserRpcServiceGetOneUserReq> req = new Request<>();
                    BmsUserRpcServiceGetOneUserReq data = new BmsUserRpcServiceGetOneUserReq();
                    data.setId(id);
                    req.setData(data);
                    Response<BmsUserRpcServiceGetOneUserResp> response = client.getOneUser(req);
                    if(response.isSuccess()){
                        nameFiled.set(item,response.getData().getUserRealName());
                    }else{
                        nameFiled.set(item,"--");
                    }
                } else
                    nameFiled.set(item,"--");
            }
        }
    }
}
