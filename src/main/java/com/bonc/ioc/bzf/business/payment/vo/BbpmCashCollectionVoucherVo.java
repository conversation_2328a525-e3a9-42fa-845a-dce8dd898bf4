package com.bonc.ioc.bzf.business.payment.vo;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 现金收缴凭证表v3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@ApiModel(value="BbpmCashCollectionVoucherVo对象", description="现金收缴凭证表v3.0")
public class BbpmCashCollectionVoucherVo extends McpBaseVo implements Serializable{


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String voucherId;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
                            private String voucherNumber;

    /**
     * 收款编号
     */
    @ApiModelProperty(value = "收款编号")
                            private String collectionNo;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
                            private BigDecimal paidInAmount;

    /**
     * 开具时间
     */
    @ApiModelProperty(value = "开具时间")
                    private String issuingTime;

    /**
     * 凭证文件地址
     */
    @ApiModelProperty(value = "凭证文件地址")
                            private String credentialFileAddress;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;
    @ApiModelProperty(value = "账单ID")
    private String billId;

    @ApiModelProperty(value = "状态(01正常，02已作废）")
    @McpDictPoint(dictCode = "COLLECTION_VOUCHER_STATUS", overTransCopyTo = "statusName")
    private String status;

    @ApiModelProperty(value = "状态(01正常，02已作废）名称")
    private String statusName;
    @ApiModelProperty(value = "签字状态:01未签字,02已签字,03不用签字")
    private String signStatus;
    @ApiModelProperty(value = "合同code")
    private String contractCode;
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "签字前请求合同模板接口参数")
    private String requestParams;

    @ApiModelProperty(value = "开具人")
    private String opener;

    /**
     * 项目业态
     */
    @ApiModelProperty(value = "项目业态")
    private String projectFormat;

    public String getProjectFormat() {
        return projectFormat;
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = projectFormat;
    }

    public String getOpener() {
        return opener;
    }

    public void setOpener(String opener) {
        this.opener = opener;
    }
    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }
    public String getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(String signStatus) {
        this.signStatus = signStatus;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }
    /**
     * @return 主键id
     */
    public String getVoucherId() {
        return voucherId;
    }

    public void setVoucherId(String voucherId) {
        this.voucherId = voucherId;
    }

    /**
     * @return 凭证编号
     */
    public String getVoucherNumber() {
        return voucherNumber;
    }

    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }

    /**
     * @return 收款编号
     */
    public String getCollectionNo() {
        return collectionNo;
    }

    public void setCollectionNo(String collectionNo) {
        this.collectionNo = collectionNo;
    }

    /**
     * @return 金额
     */
    public BigDecimal getPaidInAmount() {
        return paidInAmount;
    }

    public void setPaidInAmount(BigDecimal paidInAmount) {
        this.paidInAmount = paidInAmount;
    }

    /**
     * @return 开具时间
     */
    public String getIssuingTime() {
        return issuingTime;
    }

    public void setIssuingTime(String issuingTime) {
        this.issuingTime = issuingTime;
    }

    /**
     * @return 凭证文件地址
     */
    public String getCredentialFileAddress() {
        return credentialFileAddress;
    }

    public void setCredentialFileAddress(String credentialFileAddress) {
        this.credentialFileAddress = credentialFileAddress;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmCashCollectionVoucherVo{" +
            "voucherId=" + voucherId +
            ", voucherNumber=" + voucherNumber +
            ", collectionNo=" + collectionNo +
            ", paidInAmount=" + paidInAmount +
            ", issuingTime=" + issuingTime +
            ", credentialFileAddress=" + credentialFileAddress +
            ", delFlag=" + delFlag +
        "}";
    }
}
