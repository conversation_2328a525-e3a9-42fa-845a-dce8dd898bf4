package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 缴费周期变更 vo实体类
 *
 * <AUTHOR>
 * @since 2025/6/27
 */
@Data
public class ContractChangePeriodDTO implements java.io.Serializable {

    @ApiModelProperty(value = "变更合同号")
    private String contractCode;

    @ApiModelProperty(value = "协议号")
    private String agreementCode;

    @ApiModelProperty(value = "变更账期开始时间")
    private String changeBillStartDate;

    @ApiModelProperty(value = "变更账期结束时间")
    private String changeBillEndDate;

    @ApiModelProperty(value = "变更后的缴费周期")
    private String chargeSubjectPeriod;

    @ApiModelProperty(value = "商铺信息")
    private List<RoomInfoVo> roomList;
}
