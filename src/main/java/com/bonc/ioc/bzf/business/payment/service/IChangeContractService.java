package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.vo.BbsChangeRecordVo;
import com.bonc.ioc.bzf.business.payment.vo.BusinessGeneratePaymentByGXParamVo;
import com.bonc.ioc.bzf.business.payment.vo.BusinessGeneratePaymentByGXResultVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRespondVo;

/**
 * 合同变更请求处理
 *
 */
public interface IChangeContractService {

    /**
     * 散租1，趸租2，趸租管理协议
     *
     * @param vo
     * @return
     */
    String changeContractPersonal(BbsChangeRecordVo vo);

    /**
     * 3.59.商业合同变更退款生成付款单接口
     * @param vo
     * @return
     */
    BusinessGeneratePaymentByGXResultVo businessGeneratePaymentByGX(BusinessGeneratePaymentByGXParamVo vo);

}