package com.bonc.ioc.bzf.business.invoice.service;

import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceQueryEntity;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

/**
 * 发票开具 服务类
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
public interface IBbpmInvoiceIssueService extends IMcpBaseService<BbpmInvoiceQueryEntity>{


    /**
     *  preCheckBill 账单预校验
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    String preCheckBill(BbpmPreCheckBillVo vo);


    /**
     * issueInvoice 发票开具
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    String issueInvoice(BbpmInvoiceIssueVo vo);

    /**
     * 发票红冲和开具
     * @param vo
     * @return
     */
    String redFlushAndIssue(BbpmInvoiceRedFlushAndIssueVo vo);


    /**
     * redflush 发票红冲
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    String redFlush(BbpmInvoiceRedFlushVo vo);

    /**
     * redFlushBatch 批量发票红冲
     * @param voList 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    String redFlushBatch(List<BbpmInvoiceRedFlushVo> voList);

    /**
     * pushInvoice 发票推送
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    String pushInvoice(BbpmInvoicePushVo vo);

    /**
     * pushInvoiceBatch 批量发票推送
     * @param voList 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    String pushInvoiceBatch(List<BbpmInvoicePushVo> voList);


}
