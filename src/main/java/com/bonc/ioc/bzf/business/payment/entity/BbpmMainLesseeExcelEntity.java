package com.bonc.ioc.bzf.business.payment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 合同变更-主承租人导入参数日志表 实体类
 *
 * <AUTHOR>
 * @date 2023-11-01
 * @change 2023-11-01 by binghong.tang for init
 */
@TableName("bbpm_main_lessee_excel")
@ApiModel(value="BbpmMainLesseeExcelEntity对象", description="合同变更-主承租人导入参数日志表")
public class BbpmMainLesseeExcelEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_ID = "id";
    public static final String FIELD_TRACE_ID = "trace_id";
    public static final String FIELD_CONTRACT_NO = "contract_no";
    public static final String FIELD_REQ_BODY = "req_body";
    public static final String FIELD_BANK_BODY = "bank_body";
    public static final String FIELD_ERROR_MSG = "error_msg";
    public static final String FIELD_FIELDS1 = "fields1";
    public static final String FIELD_FIELDS2 = "fields2";
    public static final String FIELD_FIELDS3 = "fields3";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
                                @TableId(value = "id", type = IdType.ASSIGN_UUID)
                                  private String id;

    /**
     * traceId
     */
    @ApiModelProperty(value = "traceId")
                            private String traceId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractNo;

    /**
     * 签约系统请求参数
     */
    @ApiModelProperty(value = "签约系统请求参数")
                            private String reqBody;

    /**
     * 请求工银参数
     */
    @ApiModelProperty(value = "请求工银参数")
                            private String bankBody;

    /**
     * 返回信息
     */
    @ApiModelProperty(value = "返回信息")
                            private String errorMsg;

    /**
     * 备用字段1
     */
    @ApiModelProperty(value = "备用字段1")
                            private String fields1;

    /**
     * 备用字段2
     */
    @ApiModelProperty(value = "备用字段2")
                            private String fields2;

    /**
     * 备用字段3
     */
    @ApiModelProperty(value = "备用字段3")
                            private String fields3;

    /**
     * @return 主键
     */
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return traceId
     */
    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    /**
     * @return 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    /**
     * @return 签约系统请求参数
     */
    public String getReqBody() {
        return reqBody;
    }

    public void setReqBody(String reqBody) {
        this.reqBody = reqBody;
    }

    /**
     * @return 请求工银参数
     */
    public String getBankBody() {
        return bankBody;
    }

    public void setBankBody(String bankBody) {
        this.bankBody = bankBody;
    }

    /**
     * @return 返回信息
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    /**
     * @return 备用字段1
     */
    public String getFields1() {
        return fields1;
    }

    public void setFields1(String fields1) {
        this.fields1 = fields1;
    }

    /**
     * @return 备用字段2
     */
    public String getFields2() {
        return fields2;
    }

    public void setFields2(String fields2) {
        this.fields2 = fields2;
    }

    /**
     * @return 备用字段3
     */
    public String getFields3() {
        return fields3;
    }

    public void setFields3(String fields3) {
        this.fields3 = fields3;
    }

      @Override
    public String toString() {
        return "BbpmMainLesseeExcelEntity{" +
            "id=" + id +
            ", traceId=" + traceId +
            ", contractNo=" + contractNo +
            ", reqBody=" + reqBody +
            ", bankBody=" + bankBody +
            ", errorMsg=" + errorMsg +
            ", fields1=" + fields1 +
            ", fields2=" + fields2 +
            ", fields3=" + fields3 +
        "}";
    }
}