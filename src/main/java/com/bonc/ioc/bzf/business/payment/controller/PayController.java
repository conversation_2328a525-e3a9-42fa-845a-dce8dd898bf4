package com.bonc.ioc.bzf.business.payment.controller;

import com.bonc.ioc.bzf.business.payment.service.IPayService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付模块控制层
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023/3/4 22:23
 */
@Validated
@RestController
@RequestMapping("/pay")
@Api(tags = "1、支付模块")
@ApiSort(1)
public class PayController {

    @Autowired
    private IPayService payService;


    @PostMapping(value = "", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "姚春雨")
    @ApiOperation(value = "1.1、支付", notes = "支付")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:支付结果")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/pay/pay")
    public AppReply<PayResultVo> pay(@Validated @RequestBody PayParmsVo vo){
        return new AppReply<>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,payService.pay(vo));
    }

    @PostMapping(value = "unlock", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "姚春雨")
    @ApiOperation(value = "1.2、支付解锁", notes = "移动端调整到第三方平台进行支付，跳转到第三方平台后未支付，返回到我们应用的时候通知工银解锁支付状态。")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:解锁结果")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/pay/unlock")
    public AppReply<PayUnlockResultVo> payUnlock(@Validated(InsertValidatorGroup.class) @RequestBody PayUnlockParmsVo vo){
        return new AppReply<>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,payService.payUnlock(vo));
    }
}
