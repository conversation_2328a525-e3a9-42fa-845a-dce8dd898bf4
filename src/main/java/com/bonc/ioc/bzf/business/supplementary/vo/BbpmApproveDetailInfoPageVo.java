package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 审批明细表 实体类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Data
@ApiModel(value = "BbpmApproveDetailInfoPageVo对象", description = "审批明细表")
public class BbpmApproveDetailInfoPageVo extends McpBasePageVo implements Serializable {

    /**
     * 审批明细id
     */
    @ApiModelProperty(value = "审批明细id")
    @NotBlank(message = "审批明细id不能为空", groups = {UpdateValidatorGroup.class})
    private String approveDetailId;

    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    private String approveId;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 审批人角色id
     */
    @ApiModelProperty(value = "审批人角色id")
    private String approverRoleId;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverUserId;

    /**
     * 审批评论
     */
    @ApiModelProperty(value = "审批评论")
    private String approveRemark;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;
}
