package com.bonc.ioc.bzf.business.adjust.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AdjustBillExportVO implements java.io.Serializable{

    @ApiModelProperty(value = "房屋地址")
    private String productAddress;
    @ApiModelProperty(value = "承租人")
    private String customerName;
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;
    @ApiModelProperty(value = "趸租单位")
    private String company;
    @ApiModelProperty(value = "社会统一信用代码")
    private String customerCreditCode;
    @ApiModelProperty(value = "合同编号")
    private String contractNo;
    //    contractBeginTime-contractEndTime
    @ApiModelProperty(value = "租赁期限")
    private String zlqx;
    @ApiModelProperty(value = "合同类型")
    private String contractTypeName;

    @ApiModelProperty(value = "账单周期")
    private String chargePeriod;

    @ApiModelProperty(value = "房源地址")
    private String roomAddress;

    @ApiModelProperty(value = "费用项目")
    private String chargeSubject;

//    @ApiModelProperty(value = "账单类别")
//    private String owner;

    @ApiModelProperty(value = "调整后应缴金额")
    private String adjustPayableMoney;

    @ApiModelProperty(value = "调整后实缴金额")
    private String adjustPaidInMoney;

    @ApiModelProperty(value = "调整后待缴金额")
    private String adjustToBePaidMoney;

    @ApiModelProperty(value = "调整后账单缴费状态")
    private String adjustChargeStatus;

    @ApiModelProperty(value = "调整后账单状态")
    private String adjustBillStatus;

    @ApiModelProperty(value = "实际调整金额")
    private String adjustMoney;

    @ApiModelProperty(value = "应缴金额")
    private String payableMoney;

    @ApiModelProperty(value = "实缴金额")
    private String paidInMoney;

    @ApiModelProperty(value = "待缴金额")
    private String toBePaidMoney;

    @ApiModelProperty(value = "账单缴费状态")
    private String chargeStatus;

    @ApiModelProperty(value = "账单状态")
    private String billStatus;

    @ApiModelProperty(value = "转入金额")
    private String zrje;

    @ApiModelProperty(value = "转出金额")
    private String zcje;

    @ApiModelProperty(value = "应退/抵金额")
    private String rentingOutMoney;

    @ApiModelProperty(value = "账单号")
    private Long billCode;

}
