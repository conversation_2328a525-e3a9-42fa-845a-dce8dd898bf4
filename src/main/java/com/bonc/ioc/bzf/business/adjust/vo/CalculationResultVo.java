package com.bonc.ioc.bzf.business.adjust.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CalculationResultVo implements java.io.Serializable{

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "是否支持原路退,01支持02不支持")
    private String originReturn;

    @ApiModelProperty(value = "应退/抵扣金额")
    private List<RentingOutDetailVO> rentingOutDetailVOList;

    @ApiModelProperty(value = "试算后的账单列表")
    private List<ChargeableAdjustBillVO> chargeableAdjustBillVOList;

    /**
     * 计算个人应退金额之和
     * @return 个人应退金额之和
     */
    public BigDecimal calculateTotalPersonalRefundAmount() {
        BigDecimal total = BigDecimal.ZERO;
        if (rentingOutDetailVOList != null) {
            for (RentingOutDetailVO detail : rentingOutDetailVOList) {
                if (detail.getRentingOutMoneyPerson() != null) {
                    total = total.add(detail.getRentingOutMoneyPerson());
                }
            }
        }
        return total;
    }

    /**
     * 计算企业应退金额之和
     * @return 企业应退金额之和
     */
    public BigDecimal calculateTotalCompanyRefundAmount() {
        BigDecimal total = BigDecimal.ZERO;
        if (rentingOutDetailVOList != null) {
            for (RentingOutDetailVO detail : rentingOutDetailVOList) {
                if (detail.getRentingOutMoneyCompany() != null) {
                    total = total.add(detail.getRentingOutMoneyCompany());
                }
            }
        }
        return total;
    }


}
