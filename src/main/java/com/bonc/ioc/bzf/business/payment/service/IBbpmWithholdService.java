package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpmBillManagementEntity;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 账单管理(来源业财)v3.0 服务类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
public interface IBbpmWithholdService extends IMcpBaseService<BbpmBillManagementEntity>{


    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    PageResult<List<BbpmBillManagementPageResultVo>> selectByPageRecordList(BbpmWithholdListPageVo vo);

    PageResult<List<BbpmBillManagementPageResultVo>> listByContract(ParentRequest parentRequest);

    /**
     * 3.21.3 手动代扣-报盘记录查询
     * @param vo
     * @return
     */
    PageResult<List<BbpmWithholdRecordPageResultVo>> selectByPageRecordRecord(BbpmWithholdRecordPageVo vo);

    /**
     * 3.21.4 手动代扣-报盘明细查询
     * @param vo
     * @return
     */
    PageResult<List<BbpmWithholdDetailPageResultVo>> selectByPageRecordDetail(BbpmWithholdDetailPageVo vo);

    void exceldownload(ExcelDownloadV2Vo vo, HttpServletResponse httpServletResponse);
}
