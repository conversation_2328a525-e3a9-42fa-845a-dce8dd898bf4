package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageVo;
import com.bonc.ioc.bzf.business.payment.entity.BbpmPayeeEntity;
import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeeVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

/**
 * pos机 收款回传
 *
 * <AUTHOR>
 * @date 2023/1/3 11:07
 * @change 2023/1/3 11:07 by l<PERSON><PERSON>an for init
 */
public interface IBbpmPayeeService extends IMcpBaseService<BbpmPayeeEntity> {
    Boolean savePayee(BbpmPayeeVo bbpmPayeeVo);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-01-24
     * @change
     * 2024-01-24 by binghong.tang for init
     */
    PageResult<List<BbpmPayeePageResultVo>> selectByPageRecord(BbpmPayeePageVo vo);
}
