package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 获取开户行 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeBankParamsVo {

	@ApiModelProperty(value = "项目id")
	@NotBlank(message = "项目id",groups = {UpdateValidatorGroup.class})
	private  String projectId;

	@ApiModelProperty(value = "银行总行联行号")
	@NotBlank(message = "银行总行联行号",groups = {UpdateValidatorGroup.class})
	private  String bankCode;

	@ApiModelProperty(value = "银行账户")
	@NotBlank(message = "银行账户",groups = {UpdateValidatorGroup.class})
	private  String bankAccountNo;

}
