package com.bonc.ioc.bzf.business.reminder.service.impl;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmScheduledEntity;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmScheduledMapper;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmScheduledService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 缴费定时器 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-09
 * @change 2023-08-09 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmScheduledServiceImpl extends McpBaseServiceImpl<BbpmScheduledEntity> implements IBbpmScheduledService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmScheduledMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmScheduledService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmScheduledVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmScheduledEntity entity = new BbpmScheduledEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setCronId(null);
        if(!baseService.insert(entity)) {
            log.error("缴费定时器新增失败:" + entity.toString());
            throw new McpException("缴费定时器新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getCronId(),1)) {
                log.error("缴费定时器新增后保存历史失败:" + entity.toString());
                throw new McpException("缴费定时器新增后保存历史失败");
            }

            log.debug("缴费定时器新增成功:"+entity.getCronId());
            return entity.getCronId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmScheduledVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmScheduledEntity> entityList = new ArrayList<>();
        for (BbpmScheduledVo item:voList) {
            BbpmScheduledEntity entity = new BbpmScheduledEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmScheduledEntity item:entityList){
            item.setCronId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("缴费定时器新增失败");
            throw new McpException("缴费定时器新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmScheduledEntity::getCronId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("缴费定时器批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("缴费定时器批量新增后保存历史失败");
            }

            log.debug("缴费定时器新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param cronId 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String cronId) {
        if(!StringUtils.isEmpty(cronId)) {
            if(!baseService.saveOperationHisById(cronId,3)) {
                log.error("缴费定时器删除后保存历史失败:" + cronId);
                throw new McpException("缴费定时器删除后保存历史失败");
            }

            if(!baseService.removeById(cronId)) {
                log.error("缴费定时器删除失败");
                throw new McpException("缴费定时器删除失败"+cronId);
            }
        } else {
            throw new McpException("缴费定时器删除失败唯一标识符为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param cronIdList 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> cronIdList) {
        if(!CollectionUtils.isEmpty(cronIdList)) {
            int oldSize = cronIdList.size();
            cronIdList = cronIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(cronIdList) || oldSize != cronIdList.size()) {
                throw new McpException("缴费定时器批量删除失败 存在主键id为空的记录"+StringUtils.join(cronIdList));
            }

            if(!baseService.saveOperationHisByIds(cronIdList,3)) {
                log.error("缴费定时器批量删除后保存历史失败:" + StringUtils.join(cronIdList));
                throw new McpException("缴费定时器批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(cronIdList)) {
                log.error("缴费定时器批量删除失败");
                throw new McpException("缴费定时器批量删除失败"+StringUtils.join(cronIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缴费定时器
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmScheduledVo vo) {
        if(vo != null) {
            BbpmScheduledEntity entity = new BbpmScheduledEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getCronId())) {
                throw new McpException("缴费定时器更新失败传入唯一标识符为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("缴费定时器更新失败");
                throw new McpException("缴费定时器更新失败"+entity.getCronId());
            } else {
                if(!baseService.saveOperationHisById(entity.getCronId(),2)) {
                    log.error("缴费定时器更新后保存历史失败:" + entity.getCronId());
                    throw new McpException("缴费定时器更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("缴费定时器更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缴费定时器
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmScheduledVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmScheduledEntity> entityList = new ArrayList<>();

            for (BbpmScheduledVo item:voList){
                BbpmScheduledEntity entity = new BbpmScheduledEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getCronId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("缴费定时器批量更新失败 存在唯一标识符为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("缴费定时器批量更新失败");
                throw new McpException("缴费定时器批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getCronId())).map(BbpmScheduledEntity::getCronId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("缴费定时器批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("缴费定时器批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缴费定时器
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmScheduledVo vo) {
        if(vo != null) {
            BbpmScheduledEntity entity = new BbpmScheduledEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("缴费定时器保存失败");
                throw new McpException("缴费定时器保存失败"+entity.getCronId());
            } else {
                if(!baseService.saveOperationHisById(entity.getCronId(),4)) {
                    log.error("缴费定时器保存后保存历史失败:" + entity.getCronId());
                    throw new McpException("缴费定时器保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("缴费定时器保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缴费定时器
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmScheduledVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmScheduledEntity> entityList = new ArrayList<>();

            for (BbpmScheduledVo item:voList){
                BbpmScheduledEntity entity = new BbpmScheduledEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("缴费定时器批量保存失败");
                throw new McpException("缴费定时器批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getCronId())).map(BbpmScheduledEntity::getCronId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("缴费定时器批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("缴费定时器批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param cronId 需要查询的唯一标识符
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmScheduledVo selectByIdRecord(String cronId) {
        BbpmScheduledVo vo = new BbpmScheduledVo();

        if(!StringUtils.isEmpty(cronId)) {
            BbpmScheduledEntity entity = baseService.selectById(cronId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmScheduledPageResultVo>> selectByPageRecord(BbpmScheduledPageVo vo) {
        List<BbpmScheduledPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
