package com.bonc.ioc.bzf.business.payment.feign.feign;

import com.bonc.ioc.bzf.business.adjust.vo.CalculationParamVo;
import com.bonc.ioc.bzf.business.adjust.vo.CalculationResultVo;
import com.bonc.ioc.bzf.business.payment.feign.fallback.BfipChargeFeignClientFallback;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.result.create.*;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmAppendBillVo;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.bzf.utils.common.mock.Mock;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 *  bfip-charge 计费中心
 */
@FeignClient(value = "bfip-charge", fallback = BfipChargeFeignClientFallback.class,configuration = FeignExceptionConfiguration.class)
public interface BfipChargeFeignClient {

	/**
	 * 3.4查询账单列表接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/listByContract")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	String listByContract(@RequestBody ParentRequest parentRequest);



	/**
	 * 3.11收款单查询接口
	 * @param parentRequest
	 * @return
	 */
//	@PostMapping(value = "/charge/v1/receipt/listByBill")
	@PostMapping(value = "/charge/v2/receipt/listByBill")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	String listByBill(@RequestBody ParentRequest parentRequest);


	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR>  合同签订生成账单接口
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/create")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo billCreate(@RequestBody BankRequestVo<BillCreateParamsRequest> bankRequestVo);

	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR>  3.32. 散租续签生成账单
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/looseRentRelet")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo looseRentRelet(@RequestBody BankRequestVo<BillLooseRentReletCreateParamsRequest> bankRequestVo);

	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR> 3.26 合同签订生成账单接口
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/relet/create")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo billReletCreate(@RequestBody BankRequestVo<BillReletCreateParamsRequest> bankRequestVo);

	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR> 3.28. 趸租大合同续租生成账单接口
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/singleRelet")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo singleRelet(@RequestBody BankRequestVo<BillSingleReletParamsRequest> bankRequestVo);

	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR>  3.27 趸租管理协议生成账单接口和独立管理协议接口
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/relet/agreement/create")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo billAgreementCreate(@RequestBody BankRequestVo<BillAgreementCreateParamsRequest> bankRequestVo);

	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR> 3.34. 管理协议续签生成账单
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/agreementRelet")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo agreementRelet(@RequestBody BankRequestVo<BillAgreementReletCreateParamsRequest> bankRequestVo);


	/**
	 * 3.5.合同变更单据更新接口
	 * (以前的名字 5.23合同变更公用接口)
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/updateByContract")
	//@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	@Mock(key = "248c6cfab8900a")
	String updateByContract(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.64合同变更试算
	 *
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/changeTrial")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo<List<ChangeTrialPreviewBillsResultVo>> changeTrial(@RequestBody ParentRequest parentRequest);

	/**
	 * 6.26 公租房散租五期合同生成账单接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/pubLooseRentFivePhase")
	@LogPoint(system = "bfip-charge")
	String pubLooseRentFivePhase(@RequestBody ParentRequest parentRequest);

	/**
	 * 6.27 管理协议候审期生成账单
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/agreementPhase")
	@LogPoint(system = "bfip-charge")
	String agreementPhase(@RequestBody ParentRequest parentRequest);

	/**
	 * 6.27 趸租大合同候审期生成账单
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/singlePhase")
	@LogPoint(system = "bfip-charge")
	String singlePhase(@RequestBody ParentRequest parentRequest);

	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR>  3.30.空置费生成账单接口
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/vacancyFee/create")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo billVacancyFeeCreate(@RequestBody BankRequestVo<BillVacancyFeeCreateParamsVo> bankRequestVo);

	/**
	 * 项目下合同欠费状态查询
	 * @param bankRequestVo
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/overdue")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo billOverdue(@RequestBody BankRequestVo<ChargeArrearsVo> bankRequestVo);


	/**
	 * 3.9.接收白名单推送接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/whiteList")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	String whiteList(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.36. 查询未缴费账单列表
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/queryUnPaidBillList")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	String listByPayableDate(@RequestBody ParentRequest parentRequest);

	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR> 商业合同生成账单接口
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/business/create")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo billBusinessCreate(@RequestBody BankRequestVo<BillBusinessCreateParamsRequest> bankRequestVo);


	/**
	 * 3.45 增值服务费试算接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/try_service_fee")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	String tryServiceFee(@RequestBody ParentRequest parentRequest);


	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR> 商业合同续签生成账单
	 *
	 */
	@PostMapping(value = "/charge/v1/bill/business/relet")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo billBusinessRelet(@RequestBody BankRequestVo<BillBusinessCreateParamsRequest> bankRequestVo);


	/**
	 * 3.50 提交收款单调整功能接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/chargePaymentAdjust/v1/submit")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	String submit(@RequestBody ParentRequest parentRequest);


	/**
	 * 3.11.1收款单查询统计接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/receipt/chargeMoneyTotal")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	String chargeMoneyTotal(@RequestBody ParentRequest parentRequest);


	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR>  3.54 仓储续签
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/charge/v1/bill/storageRelet")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo storageRelet(@RequestBody BankRequestVo<BillStorageReletCreateParamsRequest> bankRequestVo);

	/**
	 * 3.39. 商业合同账单预览接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/business/getPreviewBills" )
	@LogPoint(system = "bfip-charge")
	@Mock(key = "2356c81bfcb007")
	ChargeRespondVo<PreviewBillsResultVo> getPreviewBills(@RequestBody ParentRequest<PreviewBillsParamsVo> parentRequest);

	@PostMapping(value = "/charge/v1/bill/business/getPreviewBills" )
	@LogPoint(system = "bfip-charge")
	@Mock(key = "2356c81bfcb007")
	String getPreviewBillsString(@RequestBody ParentRequest<PreviewBillsParamsVo> parentRequest);


	/**
	 * 3.58.账单关闭或开启接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/closeOrOpenBillAndBillBranks" )
	@LogPoint(system = "bfip-charge")
	@Mock(key = "2716dd1cfcb00a")
	ChargeRespondVo closeOrOpenBillAndBillBranks(@RequestBody ParentRequest<CloseOrOpenBillAndBillBranksParamVo> parentRequest);


	/**
	 * 3.60.查询月度账单接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/queryMonthBillList")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-charge")
	String queryMonthBillList(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.61.应收调整试算接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/trial/calculation" )
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo calculation(@RequestBody ParentRequest<CalculationParamVo> parentRequest);

	@PostMapping(value = "/charge/v1/bill/trial/calculation" )
	@LogPoint(system = "bfip-charge")
	String calculationString(@RequestBody ParentRequest<CalculationParamVo> parentRequest);

	/**
	 * 3.5 变更变更-应收账单调整
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/updateByContract" )
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo updateBill(@RequestBody ParentRequest<CalculationParamVo> parentRequest);

	/**
	 * 3.62 试算追加账单接口
	 *
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/appendBill/try")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo tryAppendBill(@RequestBody ParentRequest<BbpmAppendBillVo> parentRequest);

	/**
	 * 3.63 追加账单接口
	 *
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/charge/v1/bill/appendBill")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo appendBill(@RequestBody ParentRequest<BbpmAppendBillVo> parentRequest);

}



