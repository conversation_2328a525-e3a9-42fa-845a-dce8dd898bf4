package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value="AdjustmentFromToVo对象", description="收款调整-账单数据信息")
@Data
public class AdjustmentFromToVo extends McpBasePageVo implements Serializable {

    @ApiModelProperty(value = "主账单BizId")
    private String billCode;
    @ApiModelProperty(value = "承租人BizId")
    private String tenantBizId;
    @ApiModelProperty(value = "承租人姓名")
    private String tenantName;


}
