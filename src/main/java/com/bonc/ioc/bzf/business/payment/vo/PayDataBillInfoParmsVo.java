package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 支付数据账单参数对象
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023/3/5 10:53
 */
@Data
@ApiModel(value = "PayDataBillInfoParmsVo对象", description = "支付数据账单参数对象")
public class PayDataBillInfoParmsVo {

    @ApiModelProperty(value = "支付方式(1:工行iphne；2:工行Android；23:微信APP；24:支付宝APP)")
    @NotBlank(message = "billId不能为空")
    @Max(value = 20,message = "支付方式长度不能超过2")
    private Long billId;

    @ApiModelProperty(value = "缴纳金额（单位：元）")
    @NotBlank(message = "缴纳金额不能为空")
    @Max(value = 20,message = "支付方式长度不能超过20，小数点后不能超过四位")
    private BigDecimal paidMoney;
}
