package com.bonc.ioc.bzf.business.payment.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * pos  收款回传
 *
 * <AUTHOR>
 * @date 2023/1/3 11:35
 * @change 2023/1/3 11:35 by l<PERSON><PERSON><PERSON> for init
 */
@ApiModel(value="BbpmPayeeVo对象", description="pos机收款回传数据")
public class BbpmPayeeVo {
    @ApiModelProperty(value = "主键id")
    private String pospbakId;

    @ApiModelProperty(value = "收款回传请求json")
    private String request;

    @ApiModelProperty(value = "收款回传响应json")
    private String result;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    public String getPospbakId() {
        return pospbakId;
    }

    public void setPospbakId(String pospbakId) {
        this.pospbakId = pospbakId;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "BbpmPayeeVo{" +
                "pospbakId='" + pospbakId + '\'' +
                ", request='" + request + '\'' +
                ", result='" + result + '\'' +
                ", delFlag=" + delFlag +
                '}';
    }
}
