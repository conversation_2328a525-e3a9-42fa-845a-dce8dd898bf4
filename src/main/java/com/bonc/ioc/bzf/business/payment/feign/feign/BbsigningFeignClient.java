package com.bonc.ioc.bzf.business.payment.feign.feign;


import com.bonc.ioc.bzf.business.payment.feign.fallback.BbsigningFeignClientFallback;
import com.bonc.ioc.bzf.business.payment.vo.BbsiRuleVo;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Mcp sys server 系统管理
 *
 * <AUTHOR>
 * @date 2021/5/20 11:54
 * @change: 2021/5/20 11:54 by jin.xu for init
 */
@FeignClient(value = "bzf-business-signing", fallback = BbsigningFeignClientFallback.class,configuration = FeignExceptionConfiguration.class)
public interface BbsigningFeignClient {

    @PostMapping(value = "/bzf-business-signing/bbsiRuleEntity/selectByIdRecord")
    @FeignExceptionCheck
    AppReply<BbsiRuleVo> selectByIdRecord(@RequestBody String ruleId);
}
