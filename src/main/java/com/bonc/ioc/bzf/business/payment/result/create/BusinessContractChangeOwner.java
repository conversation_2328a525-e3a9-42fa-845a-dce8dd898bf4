package com.bonc.ioc.bzf.business.payment.result.create;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.io.Serializable;
import java.util.List;

/**
 * 08商业承租人变更（businessContractChangeOwner）
 */
@Data
public class BusinessContractChangeOwner implements Serializable {
    @ApiModelProperty(value = "申请时间")
    private String requestTime;//申请时间	String
    @ApiModelProperty(value = "签订时间")
    private String signDate;//	签订时间	String
    @ApiModelProperty(value = "协议号")
    private String agreementCode;//	协议号	String
    @ApiModelProperty(value = "合同编码")
    private String contractCode;//	合同编码	String

    @ApiModelProperty(value = "计费科目列表")
    private List<ChargeSubjectParamsBusinessRequest> chargeSubjectList;//计费科目列表

    @ApiModelProperty(value = "租户ID")
    private String tenantId;//	租户ID	String
    @ApiModelProperty(value = "租户开户总行名称")
    private String tenantBankName;//	租户开户总行名称	String
    @ApiModelProperty(value = "租户开户总行编码")
    private String tenantBankCode;//	租户开户总行编码	String
    @ApiModelProperty(value = "租户开户支行名称")
    private String tenantBankBranchName;//	租户开户支行名称	String
    @ApiModelProperty(value = "租户开户支行编码")
    private String tenantBankBrachCode;//	租户开户支行编码	String
    @ApiModelProperty(value = "租户银行卡户名")
    private String tenantBankAccountName;//	租户银行卡户名	String
    @ApiModelProperty(value = "租户银行卡卡号")
    private String tenantBankAccountNo;//	租户银行卡卡号	String
    @ApiModelProperty(value = "是否进行银行卡代扣")
    private String withholding;//	是否进行银行卡代扣	String
    @ApiModelProperty(value = "银行卡代扣摘要")
    private String withholdingSummary;//	银行卡代扣摘要	String
    @ApiModelProperty(value = "银行卡代扣备注")
    private String withholdingRemark;//	银行卡代扣备注	String
    @ApiModelProperty(value = "代扣鉴权协议号")
    private String agreementNo;//	代扣鉴权协议号	String
    @ApiModelProperty(value = "租户姓名")
    private String tenantName;//	租户姓名	String
    @ApiModelProperty(value = "租户手机号")
    private String tenantMobile;//租户手机号	String
    @ApiModelProperty(value = "租户证件类型")
    private String tenantIDType;//租户证件类型	String
    @ApiModelProperty(value = "租户证件号码")
    private String idNumber;//租户证件号码	String
    @ApiModelProperty(value = "租户邮箱地址")
    private String mailUrl;//租户邮箱地址	String
    @ApiModelProperty(value = "公租房备案号")
    private String publicRecordNo;//	公租房备案号	String
    @ApiModelProperty(value = "租户客户编号")
    private String tenantCustomerNo;//租户客户编号	String
    @ApiModelProperty(value = "租户客商编号")
    private String tenantSupplierNo;//租户客商编号	String
    @ApiModelProperty(value = "租户客商名称")
    private String tenantSupplierName;//	租户客商名称	String
    @ApiModelProperty(value = "商租企业ID")
    private String companyId;//商租企业ID	String
    @ApiModelProperty(value = "商租企业证照类型")
    private String companyIDType;//	商租企业证照类型	String
    @ApiModelProperty(value = "商租企业社会信用代码")
    private String socialCreditCode;//商租企业社会信用代码	String
    @ApiModelProperty(value = "商租企业名称")
    private String companyName;//商租企业名称	String
    @ApiModelProperty(value = "商租企业客户编号")
    private String companyCustomerNo;//商租企业客户编号	String
    @ApiModelProperty(value = "商租企业客商编号")
    private String companySupplierNo;//商租企业客商编号	String
    @ApiModelProperty(value = "商租企业客商名称")
    private String companySupplierName;//	商租企业客商名称	String
    @ApiModelProperty(value = "企业开户总行名称")
    private String companyBankName;//企业开户总行名称	String
    @ApiModelProperty(value = "企业开户总行编码")
    private String companyBankCode;//企业开户总行编码	String
    @ApiModelProperty(value = "企业开户支行名称")
    private String companyBankBranchName;//企业开户支行名称	String
    @ApiModelProperty(value = "企业开户支行编码")
    private String companyBankBranchCode;//企业开户支行编码	String
    @ApiModelProperty(value = "企业银行卡户名")
    private String companyBankAccountName;//企业银行卡户名	String
    @ApiModelProperty(value = "企业银行卡卡号")
    private String companyBankAccountNo;//企业银行卡卡号	String
    @ApiModelProperty(value = "企业纳税识别号")
    private String companyTaxNo;//企业纳税识别号	String
    @ApiModelProperty(value = "委托代理人")
    private String authorizedAgent;//委托代理人	String
    @ApiModelProperty(value = "委托代理人电话")
    private String authorizedAgentMobile;//委托代理人电话	String


}
