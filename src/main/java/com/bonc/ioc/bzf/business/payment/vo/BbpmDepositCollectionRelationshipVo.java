package com.bonc.ioc.bzf.business.payment.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 存款与收款对应关系表V2 实体类
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@ApiModel(value="BbpmDepositCollectionRelationshipV2Vo对象", description="存款与收款对应关系表V2")
public class BbpmDepositCollectionRelationshipVo extends McpBaseVo implements Serializable{


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String billCollectionId;

    /**
     * 存款单号
     */
    @ApiModelProperty(value = "存款单号")
                            private String depositSlipNo;

    /**
     * 收款编号
     */
    @ApiModelProperty(value = "收款编号--入库时是收款表的主键id")
                            private String collectionNo;

    /**
     * 业财返回的收款单ID
     */
    @ApiModelProperty(value = "业财返回的收款单ID")
                            private String receiptNo;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 主键id
     */
    public String getBillCollectionId() {
        return billCollectionId;
    }

    public void setBillCollectionId(String billCollectionId) {
        this.billCollectionId = billCollectionId;
    }

    /**
     * @return 存款单号
     */
    public String getDepositSlipNo() {
        return depositSlipNo;
    }

    public void setDepositSlipNo(String depositSlipNo) {
        this.depositSlipNo = depositSlipNo;
    }

    /**
     * @return 收款编号
     */
    public String getCollectionNo() {
        return collectionNo;
    }

    public void setCollectionNo(String collectionNo) {
        this.collectionNo = collectionNo;
    }

    /**
     * @return 业财返回的收款单ID
     */
    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmDepositCollectionRelationshipV2Vo{" +
            "billCollectionId=" + billCollectionId +
            ", depositSlipNo=" + depositSlipNo +
            ", collectionNo=" + collectionNo +
            ", receiptNo=" + receiptNo +
            ", delFlag=" + delFlag +
        "}";
    }
}
