package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 支付解锁参数对象
 *
 * <AUTHOR>
 * @date 2022-03-04
 * @change 2022-03-04 by 姚春雨 for init
 */
@Data
@ApiModel(value = "PayUnlockParmsVo对象", description = "支付解锁参数对象")
public class PayUnlockParmsVo extends McpBaseVo implements Serializable {

    @ApiModelProperty(value = "订单交易流水号")
    private String orderTrxid;

    @ApiModelProperty(value = "项目ID（业务中台）")
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

}
