package com.bonc.ioc.bzf.business.reminder.dao;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendMainLogEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import java.util.List;

/**
 * 催缴规则消息发送日志--子表  Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-08-09
 * @change 2023-08-09 by binghong.tang for init
 */
@Mapper
public interface BbpmMessageSendMainLogMapper extends McpBaseMapper<BbpmMessageSendMainLogEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change 2023-08-09 by binghong.tang for init
     */
    List<BbpmMessageSendMainLogPageResultVo> selectByPageCustom(@Param("vo") BbpmMessageSendMainLogPageVo vo );
}
