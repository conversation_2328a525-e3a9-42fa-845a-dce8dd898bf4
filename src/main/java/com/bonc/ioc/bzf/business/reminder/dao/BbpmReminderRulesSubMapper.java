package com.bonc.ioc.bzf.business.reminder.dao;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesSubEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import java.util.List;

/**
 * 缴费提醒规则--子表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@Mapper
public interface BbpmReminderRulesSubMapper extends McpBaseMapper<BbpmReminderRulesSubEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change 2023-08-04 by binghong.tang for init
     */
    List<BbpmReminderRulesSubPageResultVo> selectByPageCustom(@Param("vo") BbpmReminderRulesSubPageVo vo );

    /**
     * 根据主表id删除
     * @param rulesId
     */
    void deleteByRulesId (@Param("rulesId") String rulesId);

    void updateByRulesId (@Param("rulesId") String rulesId);

    /**
     * 根据主表id查询
     * @param vo
     * @return
     */
    List<BbpmReminderRulesSubVo> selectByRulesIdList(@Param("vo") BbpmReminderRulesSubVo vo);
}
