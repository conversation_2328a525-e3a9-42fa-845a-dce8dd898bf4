package com.bonc.ioc.bzf.business.adjust.utils;

import com.bonc.ioc.bzf.business.adjust.entity.RoleEnum;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * 请求相关 工具类
 *
 * <AUTHOR>
 * @since 2023/5/24
 */
@Log4j
public class RequestUtil {

    /**
     * tokenId
     */
    public static final String TOKEN_ID = "tokenid";

    /**
     * 客户端 token
     */
    public static final String CLIENT_TOKEN = "clienttoken";

    /**
     * 角色id 关键字
     */
    private static final String ROLE_ID = "roleid";

    /**
     * 项目ids 关键字
     */
    private static final String PROJECT_IDS = "projectids";

    /**
     * 获取当前请求实体
     *
     * @return 请求实体
     */
    public static HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
    }

    /**
     * 获取用户token
     *
     * @return 用户token
     */
    public static String getUserToken() {
        HttpServletRequest request = getRequest();
        return getUserToken(request);
    }

    /**
     * 获取用户token
     *
     * @param request 请求体
     * @return 用户token
     */
    public static String getUserToken(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            if (TOKEN_ID.equals(name)) {
                return request.getHeader(name);
            }
        }
        return null;
    }

    /**
     * 获取客户端token
     *
     * @param request 请求体
     * @return 用户token
     */
    public static String getClientToken(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            if (CLIENT_TOKEN.equals(name)) {
                return request.getHeader(name);
            }
        }
        return null;
    }

    /**
     * 获取角色
     *
     * @return 角色
     */
    public static String getRole() {
        HttpServletRequest request = getRequest();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            if (ROLE_ID.equals(name)) {
                String header = request.getHeader(name);
                log.info("角色==============================："+header);
                return request.getHeader(name);
            }
        }
        return null;
    }

    /**
     * 获取项目
     *
     * @return 项目
     */
    public static String getProject() {
        HttpServletRequest request = getRequest();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            if (PROJECT_IDS.equals(name)) {
                return request.getHeader(name);
            }
        }
        return null;
    }

    /**
     * 根据角色获取项目
     *
     * @return 根据角色获取项目
     */
    public static String getProjects() {
        String projectIdStr = "";
        String role = RequestUtil.getRole();
        if (StringUtils.isNotBlank(role)) {
            //管理员查询所有
            if (RoleEnum.MANAGER.getCode().equals(role)) {
                return projectIdStr;
            } else if (RoleEnum.STAFF.getCode().equals(role)) {
                projectIdStr = RequestUtil.getProject();
                //工作人员管理的项目为空 不能看到列表数据
                if (StringUtils.isBlank(projectIdStr)) {
                    return "当前登录人没有访问权限!";
                }
            }
        } else {
            throw new McpException("请配置角色！");
        }
        return projectIdStr;
    }
}
