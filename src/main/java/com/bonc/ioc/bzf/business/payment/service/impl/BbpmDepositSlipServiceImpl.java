package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.dao.BbpmBillCollectionDetailsMapper;
import com.bonc.ioc.bzf.business.payment.dao.BbpmDepositCollectionRelationshipMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmDepositSlipEntity;
import com.bonc.ioc.bzf.business.payment.dao.BbpmDepositSlipMapper;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BmsFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCollectionService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmDepositCollectionRelationshipService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmDepositSlipService;
import com.bonc.ioc.bzf.business.payment.utils.RedisDistributedId;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.utils.SnowflakeIdWorker;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserReq;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserResp;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;

import com.bonc.ioc.bzf.business.payment.vo.*;
import org.springframework.beans.BeanUtils;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 现金盘点存款单表v3.0 服务类实现
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmDepositSlipServiceImpl extends McpBaseServiceImpl<BbpmDepositSlipEntity> implements IBbpmDepositSlipService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmDepositSlipMapper baseMapper;

    @Resource
    private BbpmDepositCollectionRelationshipMapper bbpmDepositCollectionRelationshipMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmDepositSlipService baseService;

    @Resource
    private IBbpmDepositCollectionRelationshipService iBbpmDepositCollectionRelationshipService;

    @Resource
    private IBbpmCollectionService iBbpmCollectionService;

    @Resource
    private McpDictSession mcpDictSession;

    @Autowired
    RedisDistributedId redisDistributedId;

    @Resource
    private BbpmBillCollectionDetailsMapper bbpmBillCollectionDetailsMapper;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;


    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    private static SnowflakeIdWorker idWorker = new SnowflakeIdWorker(0, 0);


    @Autowired
    private BmsFeignClient client;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbpmDepositSlipVo vo) {
        if (vo == null) {
            return null;
        }

        BbpmDepositSlipEntity entity = new BbpmDepositSlipEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setDepositId(null);
        if (!baseService.insert(entity)) {
            log.error("现金盘点存款单表v3.0新增失败:" + entity.toString());
            throw new McpException("现金盘点存款单表v3.0新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getDepositId(), 1)) {
                log.error("现金盘点存款单表v3.0新增后保存历史失败:" + entity.toString());
                throw new McpException("现金盘点存款单表v3.0新增后保存历史失败");
            }

            log.debug("现金盘点存款单表v3.0新增成功:" + entity.getDepositId());
            return entity.getDepositId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmDepositSlipVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmDepositSlipEntity> entityList = new ArrayList<>();
        for (BbpmDepositSlipVo item : voList) {
            BbpmDepositSlipEntity entity = new BbpmDepositSlipEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmDepositSlipEntity item : entityList) {
            item.setDepositId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("现金盘点存款单表v3.0新增失败");
            throw new McpException("现金盘点存款单表v3.0新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbpmDepositSlipEntity::getDepositId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("现金盘点存款单表v3.0批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("现金盘点存款单表v3.0批量新增后保存历史失败");
            }

            log.debug("现金盘点存款单表v3.0新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param depositId 需要删除的主键id
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String depositId) {
        if (!StringUtils.isEmpty(depositId)) {
            if (!baseService.saveOperationHisById(depositId, 3)) {
                log.error("现金盘点存款单表v3.0删除后保存历史失败:" + depositId);
                throw new McpException("现金盘点存款单表v3.0删除后保存历史失败");
            }

            if (!baseService.removeById(depositId)) {
                log.error("现金盘点存款单表v3.0删除失败");
                throw new McpException("现金盘点存款单表v3.0删除失败" + depositId);
            }
        } else {
            throw new McpException("现金盘点存款单表v3.0删除失败主键id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param depositIdList 需要删除的主键id
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> depositIdList) {
        if (!CollectionUtils.isEmpty(depositIdList)) {
            int oldSize = depositIdList.size();
            depositIdList = depositIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(depositIdList) || oldSize != depositIdList.size()) {
                throw new McpException("现金盘点存款单表v3.0批量删除失败 存在主键id为空的记录" + StringUtils.join(depositIdList));
            }

            if (!baseService.saveOperationHisByIds(depositIdList, 3)) {
                log.error("现金盘点存款单表v3.0批量删除后保存历史失败:" + StringUtils.join(depositIdList));
                throw new McpException("现金盘点存款单表v3.0批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(depositIdList)) {
                log.error("现金盘点存款单表v3.0批量删除失败");
                throw new McpException("现金盘点存款单表v3.0批量删除失败" + StringUtils.join(depositIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的现金盘点存款单表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmDepositSlipVo vo) {
        if (vo != null) {
            BbpmDepositSlipEntity entity = new BbpmDepositSlipEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getDepositId())) {
                throw new McpException("现金盘点存款单表v3.0更新失败传入主键id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("现金盘点存款单表v3.0更新失败");
                throw new McpException("现金盘点存款单表v3.0更新失败" + entity.getDepositId());
            } else {
                if (!baseService.saveOperationHisById(entity.getDepositId(), 2)) {
                    log.error("现金盘点存款单表v3.0更新后保存历史失败:" + entity.getDepositId());
                    throw new McpException("现金盘点存款单表v3.0更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("现金盘点存款单表v3.0更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的现金盘点存款单表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmDepositSlipVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmDepositSlipEntity> entityList = new ArrayList<>();

            for (BbpmDepositSlipVo item : voList) {
                BbpmDepositSlipEntity entity = new BbpmDepositSlipEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getDepositId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("现金盘点存款单表v3.0批量更新失败 存在主键id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("现金盘点存款单表v3.0批量更新失败");
                throw new McpException("现金盘点存款单表v3.0批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getDepositId())).map(BbpmDepositSlipEntity::getDepositId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("现金盘点存款单表v3.0批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("现金盘点存款单表v3.0批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的现金盘点存款单表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmDepositSlipVo vo) {
        if (vo != null) {
            BbpmDepositSlipEntity entity = new BbpmDepositSlipEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("现金盘点存款单表v3.0保存失败");
                throw new McpException("现金盘点存款单表v3.0保存失败" + entity.getDepositId());
            } else {
                if (!baseService.saveOperationHisById(entity.getDepositId(), 4)) {
                    log.error("现金盘点存款单表v3.0保存后保存历史失败:" + entity.getDepositId());
                    throw new McpException("现金盘点存款单表v3.0保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("现金盘点存款单表v3.0保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的现金盘点存款单表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmDepositSlipVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmDepositSlipEntity> entityList = new ArrayList<>();

            for (BbpmDepositSlipVo item : voList) {
                BbpmDepositSlipEntity entity = new BbpmDepositSlipEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("现金盘点存款单表v3.0批量保存失败");
                throw new McpException("现金盘点存款单表v3.0批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getDepositId())).map(BbpmDepositSlipEntity::getDepositId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("现金盘点存款单表v3.0批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("现金盘点存款单表v3.0批量保存后保存历史失败");
                }
            }
        }
    }


    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmDepositSlipPageResultVo>> selectByPageRecord(BbpmDepositSlipPageVo vo) {

        //查询“已退回“的要先查工银,然后查本地库组装数据
        if(PaymentEnums.BILLING_CASH_STATUS_THREE.getCode().equals(vo.getDepositStatus())){
            BbpmOffLinePageVo bbpmOffLinePageVo = new BbpmOffLinePageVo();
            //拼接请求参数
            SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
            bbpmOffLinePageVo.setProjectId(vo.getProjectId());
            bbpmOffLinePageVo.setSummaryNo(vo.getDepositSlipNo());
            bbpmOffLinePageVo.setReceiptNo(vo.getTransactionNo());
            bbpmOffLinePageVo.setStartDate(vo.getStartDate());
            bbpmOffLinePageVo.setEndDate(vo.getEndDate());
            //已退回
            bbpmOffLinePageVo.setBackStatus(PaymentEnums.BACKSTATUS_ONE.getCode());
            bbpmOffLinePageVo.setSize(Integer.valueOf(vo.getPageSize()+""));
            bbpmOffLinePageVo.setCurrent(Integer.valueOf(vo.getPageNumber()+""));
            ParentRequest<BbpmOffLinePageVo> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(bbpmOffLinePageVo);

            log.info("3.52根据项目等查询现金存款单回退信息接口请求参数:{}", JSONObject.toJSONString(parentRequest));
            //请求业财接口
            String responseBody = null;
            if (yecaiFeign){
                responseBody = bfipSettlementFeignClient.getOffLinePages(parentRequest);
            }else{
                responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/bill/getOffLinePages", parentRequest);
            }
            log.info("3.52根据项目等查询现金存款单回退信息接口返回: {}",responseBody);

            if(StringUtils.isBlank(responseBody)){
                return new PageResult<>(null);
            }
            FaceHttpResultTwo<BbpmOffLinePageResultVo> faceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

            if(!("00000").equals(faceHttpResultTwo.getCode())){
                throw new McpException("*提示:"+faceHttpResultTwo.getMessage());
            }
            if(faceHttpResultTwo.getData() == null || faceHttpResultTwo.getData().getRecords() == null){
                return new PageResult<>(null);
            }
            List<BbpmOffLinePageResultVo> billListResultList = JSON.parseArray(faceHttpResultTwo.getData().getRecords().toString(),BbpmOffLinePageResultVo.class);
            List<BbpmDepositSlipPageResultVo> result = new ArrayList<>();
            if(billListResultList!=null && billListResultList.size()>0){
                for(BbpmOffLinePageResultVo item : billListResultList){
                    BbpmDepositSlipPageVo newVo = new BbpmDepositSlipPageVo();
                    newVo.setDepositSlipNoV2(item.getSummaryNo());
                    newVo.setPageNumber(1);
                    newVo.setPageSize(1);
                    List<BbpmDepositSlipPageResultVo> query = baseMapper.selectByPageCustom(newVo);
                    if(query != null && query.size() > 0){
                        BbpmDepositSlipPageResultVo bbpmDepositSlipPageResultVo = query.get(0);
                        if(bbpmDepositSlipPageResultVo.getDepositSlipNo().equals(item.getSummaryNo())){
                            bbpmDepositSlipPageResultVo.setBackReason(item.getBackReason());
                            bbpmDepositSlipPageResultVo.setBackTime(item.getBackTime());
                            bbpmDepositSlipPageResultVo.setBackOperator(item.getBackOperator());
                            if(PaymentEnums.BACKSTATUS_ONE.getCode().equals(item.getBackStatus())){
                                bbpmDepositSlipPageResultVo.setDepositStatus(PaymentEnums.BILLING_CASH_STATUS_THREE.getMsg());
                            }else {
                                bbpmDepositSlipPageResultVo.setDepositStatus(item.getBackStatus());
                            }
//                            //用户id转用户名
//                            if(StringUtils.isNotBlank(item.getBackOperator())) {
//                                try {
//                                    Request<BmsUserRpcServiceGetOneUserReq> req = new Request<>();
//                                    BmsUserRpcServiceGetOneUserReq data = new BmsUserRpcServiceGetOneUserReq();
//                                    data.setId(item.getBackOperator());
//                                    req.setData(data);
//                                    Response<BmsUserRpcServiceGetOneUserResp> response = client.getOneUser(req);
//                                    if(response.isSuccess()){
//                                        bbpmDepositSlipPageResultVo.setBackOperator(response.getData().getUserRealName());
//                                    }
//                                }catch (Exception e){
//                                    log.error("通过用户id:+"+item.getBackOperator()+"获取用户信息失败");
//                                    e.printStackTrace();
//                                }
//                            }
                        }
                        result.add(bbpmDepositSlipPageResultVo);
                    }
                }
            }
            return new PageResult<>(faceHttpResultTwo.getData().getTotal(),result);
        }else{
            List<BbpmDepositSlipPageResultVo> result = baseMapper.selectByPageCustom(vo);
            mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
            return new PageResult(result);
        }

    }

    /**
     * confirmDeposit 确认生成存款单
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String confirmDeposit(BbpmDepositSlipVo vo) {
        if (vo == null) {
            return null;
        }

        if(StringUtils.isBlank(vo.getProjectId())){
            throw new McpException("项目projectId为空");
        }

        BbpmDepositSlipEntity entity = new BbpmDepositSlipEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setDepositId(null);
        //存款单号
//        entity.setDepositSlipNo("CBC"+idWorker.nextId());
//        entity.setDepositSlipNo(String.valueOf(redisDistributedId.nextId("deposit")));
        entity.setDepositSlipNo(String.valueOf(redisDistributedId.nextIdV3("deposit", "000000")));
        //待上传凭证
        entity.setDepositStatus("1");
        if (!baseService.insert(entity)) {
            log.error("确认生成存款单失败:" + entity.toString());
            throw new McpException("确认生成存款单失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getDepositId(), 1)) {
                log.error("确认生成存款单后保存历史失败:" + entity.toString());
                throw new McpException("确认生成存款单后保存历史失败");
            }

            log.debug("确认生成存款单成功:" + entity.getDepositId());

            List<BbpmDepositCollectionRelationshipVo> voList = new ArrayList<>();
            List<BbpmCollectionVo> bbpmCollectionVoList = new ArrayList<>();
            for (String collectionId : vo.getCollectionNoList()) {
                BbpmDepositCollectionRelationshipVo bbpmDepositCollectionRelationshipVo = new BbpmDepositCollectionRelationshipVo();
                bbpmDepositCollectionRelationshipVo.setCollectionNo(collectionId);
                bbpmDepositCollectionRelationshipVo.setDepositSlipNo(entity.getDepositId());
                voList.add(bbpmDepositCollectionRelationshipVo);

                BbpmCollectionVo bbpmCollectionVo = new BbpmCollectionVo();
                bbpmCollectionVo.setCollectionId(collectionId);
                //2已存款
                bbpmCollectionVo.setDepositStatus("2");
                bbpmCollectionVoList.add(bbpmCollectionVo);
            }
            //插入 存款与收款对应关系表
            iBbpmDepositCollectionRelationshipService.insertBatchRecord(voList);
            //更新收款表的现金盘点存款状态为已存款 2已存款
            iBbpmCollectionService.updateBatchByIdRecord(bbpmCollectionVoList);

            return entity.getDepositSlipNo();
        }
    }

    /**
     * uploadBankVoucher 根据主键更新 上传银行凭证相关信息
     *
     * @param vo 需要更新的现金盘点存款单表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void uploadBankVoucher(BbpmDepositSlipVo vo) {
        if (vo != null) {
            BbpmDepositSlipEntity entity = new BbpmDepositSlipEntity();
            BeanUtils.copyProperties(vo, entity);

            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                entity.setDepositTime(sdf.parse(vo.getDepositTime()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            entity.setUploaderDate(new Date());
            //2已存款
            entity.setDepositStatus("2");

            //根据存款单id找到对应收款单，然后查询出具体的存款明细信息，传给工银，r
            //！！！重要：bbpm_deposit_collection_relationship的collection_no 与 bbpm_bill_collection_details的collection_id对应
            List<BbpmBillCollectionDetailsVo> bbpmBillCollectionDetailsVoList = bbpmBillCollectionDetailsMapper.selectDetailsByCollectionId(entity.getDepositId());
            //存款与收款对应关系表
            List<BbpmDepositCollectionRelationshipVo> bbpmDepositCollectionRelationshipVoList = new ArrayList<>();
            // 调用工银收款接口  上传凭证不返还收款单号了
            String receiptNo = updateBillOffline(entity, bbpmBillCollectionDetailsVoList, bbpmDepositCollectionRelationshipVoList);
//            entity.setReceiptNo(receiptNo);

            if (StringUtils.isEmpty(entity.getDepositId())) {
                throw new McpException("上传银行凭证失败传入主键id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("上传银行凭证更新失败");
                throw new McpException("上传银行凭证更新失败,主键deposit_id=" + entity.getDepositId());
            } else {

//                更新 存款与收款对应关系表
//                iBbpmDepositCollectionRelationshipService.updateBatchByIdRecord(bbpmDepositCollectionRelationshipVoList);

                if (!baseService.saveOperationHisById(entity.getDepositId(), 2)) {
                    log.error("上传银行凭证后保存历史失败:" + entity.getDepositId());
                    throw new McpException("上传银行凭证后保存历史失败");
                }
            }
        } else {
            throw new McpException("上传银行凭证失败传入为空");
        }
    }


    /**
     * 调用工银收款接口
     *
     * @return 返回 chargeIds工银返回的收款单id（他们的收款单c)
     */
    private String updateBillOffline(BbpmDepositSlipEntity entity, List<BbpmBillCollectionDetailsVo> bbpmBillCollectionDetailsVoList, List<BbpmDepositCollectionRelationshipVo> bbpmDepositCollectionRelationshipVoList) {

        StringBuffer chargeIds = new StringBuffer();

        // 调用工银收款接口
        CashOfflineCollectionRequest cashOfflineCollectionRequest = new CashOfflineCollectionRequest();

        cashOfflineCollectionRequest.setSummaryNo(entity.getDepositSlipNo());
        cashOfflineCollectionRequest.setReceiptNoUrl(entity.getBankVoucher());
        cashOfflineCollectionRequest.setReceiptNo(entity.getTransactionNo());
        cashOfflineCollectionRequest.setDepositor(entity.getDepositor());
        cashOfflineCollectionRequest.setDepositDate(new SimpleDateFormat("yyyy-MM-dd").format(entity.getDepositTime()));
        cashOfflineCollectionRequest.setUploader(entity.getUploaderName());
        cashOfflineCollectionRequest.setUploadDate(new SimpleDateFormat("yyyy-MM-dd").format(entity.getUploaderDate()));
        cashOfflineCollectionRequest.setBankCode(entity.getBankCode());
        cashOfflineCollectionRequest.setProjectId(entity.getProjectId());
        //后加
//        cashOfflineCollectionRequest.setReceiptBankName(entity.getBankName());
//        cashOfflineCollectionRequest.setReceiptBankAcct(entity.getReceiptBankAcct());
//        cashOfflineCollectionRequest.setReceiptBankCode(entity.getBankCode());
        //再次加r
        cashOfflineCollectionRequest.setReceiptBankName(entity.getBankName());
        cashOfflineCollectionRequest.setReceiptBankAcctNo(entity.getBankAccountNo());
        cashOfflineCollectionRequest.setReceiptBankAccountName(entity.getBankAccountName());
        cashOfflineCollectionRequest.setReceiptBankBranchName(entity.getBranchName());

        cashOfflineCollectionRequest.setTotalCashAmount(entity.getTotalCashAmount());
        cashOfflineCollectionRequest.setMultiProject(entity.getMultiProject());

        //2024年12月冲刺增加 外面的
        cashOfflineCollectionRequest.setActualTotalAmount(entity.getPaidInAmount());

        BigDecimal amountReceivable = BigDecimal.ZERO;
        List<CashOfflineCollectionSubRequest> cashOfflineCollectionSubRequestList = new ArrayList<>();

        for (BbpmBillCollectionDetailsVo bbpmBillCollectionDetailsVo : bbpmBillCollectionDetailsVoList) {
            CashOfflineCollectionSubRequest cashOfflineCollectionSubRequest = new CashOfflineCollectionSubRequest();
            cashOfflineCollectionSubRequest.setTenantId(bbpmBillCollectionDetailsVo.getCustomerId());
            cashOfflineCollectionSubRequest.setContractId(bbpmBillCollectionDetailsVo.getContractId());
            cashOfflineCollectionSubRequest.setDepositNo(bbpmBillCollectionDetailsVo.getCollectionNo());
            cashOfflineCollectionSubRequest.setBillId(bbpmBillCollectionDetailsVo.getBillNo());
            cashOfflineCollectionSubRequest.setCashAmout(bbpmBillCollectionDetailsVo.getCashAmout());
            cashOfflineCollectionSubRequest.setCashTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(bbpmBillCollectionDetailsVo.getCashTime()));

            //2024年12月冲刺增加 里面的  工银也许不要，到时联调看
            cashOfflineCollectionSubRequest.setBillChargeSubject(bbpmBillCollectionDetailsVo.getBillChargeSubject());
            cashOfflineCollectionSubRequest.setChargeSubjectPeriod(String.valueOf(bbpmBillCollectionDetailsVo.getChargeSubjectPeriod()));
            cashOfflineCollectionSubRequest.setBillPayableAmount(bbpmBillCollectionDetailsVo.getReplacePayAmount());

            // 不太准确 amountReceivable = amountReceivable.add(bbpmBillCollectionDetailsVo.getReplacePayAmount());

            cashOfflineCollectionSubRequestList.add(cashOfflineCollectionSubRequest);
        }
        //2024年12月冲刺增加 外面的  和工银沟通
        cashOfflineCollectionRequest.setAmountReceivable(amountReceivable);

        cashOfflineCollectionRequest.setOfflineList(cashOfflineCollectionSubRequestList);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<CashOfflineCollectionRequest> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(cashOfflineCollectionRequest);

        log.info("上传银行凭证接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("上传银行凭证接口请求参数json:" + jsonRequest);

        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipSettlementFeignClient.updateBillOffline(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl + "/settlement/v1/bill/updateBillOffline", parentRequest);
        }
        log.info("调用工银上传银行凭证接口返回:" + responseBody);

        //String responseBody ="{\"busiCode\":\"\",\"code\":\"00000\",\"data\":{\"receipt\":\"1222\"},\"message\":\"提交成功\"}";
        FaceMdMapResult cashOfflineCollectionResultFaceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

        if (!("00000").equals(cashOfflineCollectionResultFaceMdMapResult.getCode())) {
            log.error("调用工银上传银行凭证接口失败:" + responseBody);
            throw new McpException("*提示:"+cashOfflineCollectionResultFaceMdMapResult.getMessage());
        }

        if (cashOfflineCollectionResultFaceMdMapResult != null && cashOfflineCollectionResultFaceMdMapResult.getData() != null) {
            for (BbpmBillCollectionDetailsVo bbpmBillCollectionDetailsVo : bbpmBillCollectionDetailsVoList) {

                Object ycMessage = cashOfflineCollectionResultFaceMdMapResult.getData().get("Message");
                if (ycMessage != null && StringUtils.isNotBlank(ycMessage.toString())) {
                    log.error("调用工银上传银行凭证接口失败,工银返回结果为:" + responseBody);
                    throw new McpException("*提示:"+ycMessage.toString());
                }

                String depositNo = bbpmBillCollectionDetailsVo.getCollectionNo();
                List<CashOfflineCollectionResult> cashOfflineCollectionResultList = JSON.parseArray(cashOfflineCollectionResultFaceMdMapResult.getData().get(depositNo).toString(), CashOfflineCollectionResult.class);

                if (cashOfflineCollectionResultList == null || cashOfflineCollectionResultList.size() == 0) {
                    log.error("调用工银上传银行凭证接口失败,工银返回结果为:" + responseBody);
                    continue;
//                    throw new McpException("调用*上传银行凭证接口失败,*返回结果有一个节点为空:" + responseBody);
//                    throw new McpException(cashOfflineCollectionResultFaceMdMapResult.getMessage());
                }

                for (CashOfflineCollectionResult cashOfflineCollectionResult : cashOfflineCollectionResultList) {

                    String message = cashOfflineCollectionResult.getMessage();

                    if (StringUtils.isBlank(message) || !message.contains("成功")) {
                        log.error("调用工银上传银行凭证接口失败,工银返回结果为:" + responseBody);
                        throw new McpException("*提示:"+message);
                    }
//                    //为空表示这个账单收款失败（重复缴费等情况）
//                    if(StringUtils.isBlank(cashOfflineCollectionResult.getChargeId())){
//                        log.error("调用工银收款接口失败:"+responseBody);
//                        throw new McpException(cashOfflineCollectionResult.getMessage());
//                    }else {
//                        chargeIds.append(cashOfflineCollectionResult.getChargeId());
//
//                        String collectionId = bbpmBillCollectionDetailsVo.getCollectionId();
//                        String depositId = entity.getDepositId();
//                        //depositId 和 collectionId找到表id 然后 更新关系表中的receipt_no
//                        BbpmDepositCollectionRelationshipVo bbpmDepositCollectionRelationshipVo = new BbpmDepositCollectionRelationshipVo();
//                        bbpmDepositCollectionRelationshipVo.setCollectionNo(collectionId);
//                        bbpmDepositCollectionRelationshipVo.setDepositSlipNo(depositId);
//                        bbpmDepositCollectionRelationshipVo  =bbpmDepositCollectionRelationshipMapper.selectByDepositSlipNoAndCollectionId(bbpmDepositCollectionRelationshipVo);
//                        bbpmDepositCollectionRelationshipVo.setReceiptNo(cashOfflineCollectionResult.getChargeId());
//                        bbpmDepositCollectionRelationshipVoList.add(bbpmDepositCollectionRelationshipVo);
//
//                    }
                }
            }
            return chargeIds.toString();
        } else {
            log.error("调用工银上传银行凭证接口失败,工银返回结果为:" + responseBody);
            throw new McpException("调用工银上传银行凭证接口失败,工银返回结果为:" + responseBody);
        }

    }


    /**
     * selectByIdRecord 根据主键查询
     *
     * @param depositId 需要查询的主键id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmDepositSlipVo selectByIdRecord(String depositId) {
        BbpmDepositSlipVo vo = new BbpmDepositSlipVo();

        if (!StringUtils.isEmpty(depositId)) {
            BbpmDepositSlipEntity entity = baseService.selectById(depositId);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            if (entity != null) {
                Date depositTime = entity.getDepositTime();
                BeanUtils.copyProperties(entity, vo);
                if (depositTime != null) {
                    vo.setDepositTime(sdf.format(depositTime));
                }

//                //查询工银，看看是不是”已退回“状态
//                if(1!=1){
//                    vo.setDepositStatus("已退回");
//                }

                mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

}
