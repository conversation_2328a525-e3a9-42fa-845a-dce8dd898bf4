package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.OfferEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 报盘记录 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-10-20
 * @change 2023-10-20 by binghong.tang for init
 */
@Mapper
public interface OfferMapper extends McpBaseMapper<OfferEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change 2023-10-20 by binghong.tang for init
     */
    List<OfferPageResultVo> selectByPageCustom(@Param("vo") OfferPageVo vo );
}
