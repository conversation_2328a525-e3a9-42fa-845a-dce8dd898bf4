package com.bonc.ioc.bzf.business.payment.result.create;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 计费科目
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeSubjectParamsBusinessRequest  implements Serializable {
    @ApiModelProperty(value = "计费科目编号")
    private String chargeSubjectNo;

    @ApiModelProperty(value = "计费科目税率")
    private BigDecimal taxRate;

}
