package com.bonc.ioc.bzf.business.payment.result.create;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 01 主承租人变更,备案家庭不变(散租详见tenantChangeDto
 */
@Data
@Builder
public class TenantChangeDto {
    private String beforeContractCode;//	变更前合同code	String
    private String contractCode;//	新合同code	String
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginDate;//	合同起始日期	Date
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;//合同终止日期	Date
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractSignTime;//	合同签订时间	Date
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractCommencementDate;//	合同起租日	Date
    private String tenantBankName;//租户开户总行名称	String
    private String tenantBankCode;//租户开户总行编码	String
    private String tenantBankBranchName;//	租户开户支行名称	String
    private String tenantBankBrachCode;//	租户开户支行编码	String
    private String tenantBankAccountName;//	租户银行卡户名	String
    private String tenantBankAccountNo;//	租户银行卡卡号	String
    private String withholding;//是否进行银行卡代扣	String
    private String withholdingSummary;// 银行卡代扣摘要	String
    private String withholdingRemark;//	 银行卡代扣备注	String
    private String agreementNo;//	代扣鉴权协议号	String
    private String tenantName;//	租户姓名	String
    private String tenantMobile;//租户手机号	String
    private String tenantId;//租户ID	String
    private String tenantIDType;//租户证件类型	String
    private String idNumber;//租户证件号码	String
    private String mailUrl;//租户邮箱地址	String
    private String publicRecordNo;//	公租房备案号	String
    private String tenantCustomerNo;//	租户客户编号	String
    private String tenantSupplierNo;//租户客商编号	String
    private String tenantSupplierName;//	租户客商名称	String


}
