package com.bonc.ioc.bzf.business.payment.controller;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.result.WhiteListRequest;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCashPledgeService;
import com.bonc.ioc.bzf.business.payment.service.IWhiteListService;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCashPledgeVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionVo;
import com.bonc.ioc.bzf.business.payment.vo.WhiteListRequestVo;
import com.bonc.ioc.bzf.business.payment.vo.WhiteListVo;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 白名单
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/whiteList")
@Api(tags = "白名单")
@Validated
public class WhiteListController extends McpBaseController {
    @Resource
    private IWhiteListService iWhiteListService;


    /**
     * whiteList 接收白名单推送接口
     * @param list 白名单信息
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/send", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "binghong.tang")
    @ApiOperation(value = "推送白名单", notes = "白名单信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:请求提交成功消息或者执行错误信息")})
    @LogPoint(system = "bzf-business-payment",path = "/bzf-business-payment/v2/business/whiteList/send")
    public AppReply<JSONObject> send(@ApiParam(value = "白名单信息" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<WhiteListVo> list){
        AppReply<JSONObject> appReply = new AppReply<JSONObject>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iWhiteListService.whiteList(list));
        return appReply;
    }



    @PostMapping(value = "/whiteListSendGy", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "binghong.tang")
    @ApiOperation(value = "直接调用工银白名单接口", notes = "直接调用工银白名单接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:请求提交成功消息或者执行错误信息")})
    @LogPoint(system = "bzf-business-payment",path = "/bzf-business-payment/v2/business/whiteList/whiteListSendGy")
    public AppReply<JSONObject> whiteListSendGy(@ApiParam(value = "白名单信息" ,required = false) @RequestBody List<WhiteListRequestVo> whiteListRequestList){
        AppReply<JSONObject> appReply = new AppReply<JSONObject>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iWhiteListService.whiteListSendGy(whiteListRequestList));
        return appReply;
    }

}

