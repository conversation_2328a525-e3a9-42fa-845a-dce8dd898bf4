package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票推送
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value = "BbpmInvoicePushVo对象", description = "发票推送")
@Data
public class BbpmInvoicePushVo extends McpBaseVo implements Serializable {
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "发票代码")
    private String fpDm;
    @ApiModelProperty(value = "发票号码")
    private String fpHm;
    @ApiModelProperty(value = "推送类型0：短信 1：邮箱")
    private String tslx;
    @ApiModelProperty(value = "用户方邮箱或手机号")
    private String lxfs;

    @ApiModelProperty(value = "用户方邮箱")
    private String email;
    @ApiModelProperty(value = "手机号")
    private String phone;

}
