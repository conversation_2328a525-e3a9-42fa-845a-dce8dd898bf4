package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@ApiModel(value="优惠规则", description="优惠规则")
@Data
public class PreviewBillparamsRoomChargePreferentRule implements java.io.Serializable{

    @ApiModelProperty(value = "优惠方式 01 - 免租 02 - 折扣")
    private String preferentialType;

    @ApiModelProperty(value = "优惠开始日期")
    private String preferentialBeginDate;

    @ApiModelProperty(value = "优惠结束日期")
    private String preferentialEndDate;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal preferentialAmount;

    @ApiModelProperty(value = "优惠比例")
    private BigDecimal preferentialRatio;

    @ApiModelProperty(value = "优惠规则ID")
    private Integer preferentRuleId;

}
