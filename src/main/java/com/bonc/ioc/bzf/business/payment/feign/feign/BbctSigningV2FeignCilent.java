package com.bonc.ioc.bzf.business.payment.feign.feign;


import com.bonc.ioc.bzf.business.payment.feign.fallback.BbctSigningV2FeignClientFallback;
import com.bonc.ioc.bzf.business.payment.vo.ProjectVoV2;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 签约中心v2 feign类
 *
 * <AUTHOR>
 * @since 2023/5/8
 */
@FeignClient(contextId = "bbsSigningV2",
        value = "bzf-business-signing-630",
        path = "/bzf-business-signing/v2",
        fallback = BbctSigningV2FeignClientFallback.class,
        configuration = FeignExceptionConfiguration.class)
public interface BbctSigningV2FeignCilent {
    /**
     * @description: 查询项目信息
     * @author: 宋鑫
     * @date: 2023-05-25 16:17
     * @param: []
     * @return: com.bonc.ioc.common.util.AppReply<java.util.List < com.bonc.ioc.bzf.system.signing.signing.v2.vo.ProjectVo>>
     * @since 1.0.0
     **/
    @GetMapping(value = "/atomic/bbsResultProductEntity/selectProject", produces = "application/json;charset=UTF-8")
    @FeignExceptionCheck
    @LogPoint(system = "bzf-business-signing-630")
    AppReply<List<ProjectVoV2>> selectProject();

    /**
     * 免登录查询项目信息（所有权限）
     * @return
     */
    @GetMapping(value = "/atomic/bbsResultProductEntity/selectProjectByAllAuthority", produces = "application/json;charset=UTF-8")
    @FeignExceptionCheck
    @LogPoint(system = "bzf-business-signing-630")
    AppReply<List<ProjectVoV2>> selectProjectByAllAuthority();


}


