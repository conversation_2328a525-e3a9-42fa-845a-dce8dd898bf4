package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeeVo;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * pos机收款回传 Mapper接口
 *
 * <AUTHOR>
 * @date 2023/1/3 11:05
 * @change 2023/1/3 11:05 by l<PERSON><PERSON><PERSON> for init
 */
@Mapper
public interface BbpmPayeeMapper extends McpBaseMapper<BbpmPayeeMapper> {
    Integer insertPayee(@Param("bbpmPayeeVo") BbpmPayeeVo bbpmPayeeVo);

    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-01-24
     * @change 2024-01-24 by binghong.tang for init
     */
    List<BbpmPayeePageResultVo> selectByPageCustom(@Param("vo") BbpmPayeePageVo vo );
}
