package com.bonc.ioc.bzf.business.payment.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 账单管理V2 实体类
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@TableName("bbpm_bill_management")
@ApiModel(value="BbpmBillManagementV2Entity对象", description="账单管理V2")
public class BbpmBillManagementEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_BILL_ID = "bill_id";
    public static final String FIELD_BILL_NO = "bill_no";
    public static final String FIELD_CONTRACT_CODE = "contract_code";
    public static final String FIELD_CONTRACT_NO = "contract_no";
    public static final String FIELD_BILL_CYCLE = "bill_cycle";
    public static final String FIELD_TENANT_CODE = "tenant_code";
    public static final String FIELD_TENANT_NAME = "tenant_name";
    public static final String FIELD_CERT_NO = "cert_no";
    public static final String FIELD_TENANT_MOBILE = "tenant_mobile";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_BUILDING_NO = "building_no";
    public static final String FIELD_UNIT_NO = "unit_no";
    public static final String FIELD_FLOOR_NO = "floor_no";
    public static final String FIELD_ROOM_NO = "room_no";
    public static final String FIELD_BILL_CHARGE_SUBJECT = "bill_charge_subject";
    public static final String FIELD_BILL_CHARGE_SUBJECT_NAME = "bill_charge_subject_name";
    public static final String FIELD_CHARGE_SUBJECT_NO = "charge_subject_no";
    public static final String FIELD_BILL_STATUS = "bill_status";
    public static final String FIELD_BILL_STATUS_NAME = "bill_status_name";
    public static final String FIELD_ACCOUNTING_MONTH = "accounting_month";
    public static final String FIELD_CHARGE_SUBJECT_PERIOD = "charge_subject_period";
    public static final String FIELD_PAYABLE_DATE = "payable_date";
    public static final String FIELD_PAYABLE_START_DATE = "payable_start_date";
    public static final String FIELD_PAYABLE_END_DATE = "payable_end_date";
    public static final String FIELD_SHOULD_PAY_AMOUNT = "should_pay_amount";
    public static final String FIELD_PAYED_AMOUNT = "payed_amount";
    public static final String FIELD_REPLACE_PAY_AMOUNT = "replace_pay_amount";
    public static final String FIELD_PREPAYMENT_AMOUNT = "prepayment_amount";
    public static final String FIELD_CHANGE_SHOULD_PAY_AMOUNT = "change_should_pay_amount";
    public static final String FIELD_CHARGE_STANDARD_CNY = "charge_standard_cny";
    public static final String FIELD_CHARGE_STANDARD_CNY_NAME = "charge_standard_cny_name";
    public static final String FIELD_CHARGE_CYCLE = "charge_cycle";
    public static final String FIELD_CHARGE_CYCLE_NAME = "charge_cycle_name";
    public static final String FIELD_IS_PROVISION = "is_provision";
    public static final String FIELD_IS_PROVISION_NAME = "is_provision_name";
    public static final String FIELD_CHARGE_TYPE = "charge_type";
    public static final String FIELD_CHARGE_TYPE_NAME = "charge_type_name";
    public static final String FIELD_SUBSIDY_RADIO = "subsidy_radio";
    public static final String FIELD_SUBSIDY_MONEY = "subsidy_money";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_STATUS_NAME = "status_name";
    public static final String FIELD_ACCOUNT_STATUS = "account_status";
    public static final String FIELD_ACCOUNT_STATUS_NAME = "account_status_name";
    public static final String FIELD_INVOICING_STATUS = "invoicing_status";
    public static final String FIELD_INVOICING_STATUS_NAME = "invoicing_status_name";
    public static final String FIELD_INVOICE_MONEY = "invoice_money";
    public static final String FIELD_PRIMARY_CHARGE_CODE = "primary_charge_code";
    public static final String FIELD_CHARGE_SUBJECT_BEGIN_DATE = "charge_subject_begin_date";
    public static final String FIELD_CHARGE_SUBJECT_END_DATE = "charge_subject_end_date";
    public static final String FIELD_BILL_PAY_STATUS = "bill_pay_status";
    public static final String FIELD_BILL_PAY_STATUS_NAME = "bill_pay_status_name";
    public static final String FIELD_CAL_CURRENT_MONTH = "cal_current_month";
    public static final String FIELD_EXIT_DATE = "exit_date";
    public static final String FIELD_FULL_PAGE = "full_page";
    public static final String FIELD_PAGE_SIZE = "page_size";
    public static final String FIELD_PAGE_NO = "page_no";
    public static final String FIELD_EXT1 = "ext1";
    public static final String FIELD_EXT2 = "ext2";
    public static final String FIELD_EXT3 = "ext3";
    public static final String FIELD_EXT4 = "ext4";
    public static final String FIELD_EXT5 = "ext5";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 账单ID
     */
    @ApiModelProperty(value = "账单ID")
                                @TableId(value = "bill_id", type = IdType.ASSIGN_UUID)
                                  private String billId;

    /**
     * 账单编号
     */
    @ApiModelProperty(value = "账单编号")
                            private String billNo;

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
                            private String contractCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractNo;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
                            private String billCycle;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
                            private String tenantCode;

    /**
     * 租户名称
     */
    @ApiModelProperty(value = "租户名称")
                            private String tenantName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
                            private String certNo;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
                            private String tenantMobile;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 楼栋
     */
    @ApiModelProperty(value = "楼栋")
                            private String buildingNo;

    /**
     * 单元
     */
    @ApiModelProperty(value = "单元")
                            private String unitNo;

    /**
     * 所在楼层
     */
    @ApiModelProperty(value = "所在楼层")
                            private String floorNo;

    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
                            private String roomNo;

    /**
     * 账单对应的收费科目
     */
    @ApiModelProperty(value = "账单对应的收费科目")
                            private String billChargeSubject;

    /**
     * 账单对应的收费科目名称
     */
    @ApiModelProperty(value = "账单对应的收费科目名称")
                            private String billChargeSubjectName;

    /**
     * 计费科目编号(查询条件）
     */
    @ApiModelProperty(value = "计费科目编号(查询条件）")
                            private String chargeSubjectNo;

    /**
     * 账单缴费状态
     */
    @ApiModelProperty(value = "账单缴费状态")
                            private String billStatus;

    /**
     * 账单缴费状态名称
     */
    @ApiModelProperty(value = "账单缴费状态名称")
                            private String billStatusName;

    /**
     * 账单会计月
     */
    @ApiModelProperty(value = "账单会计月")
                            private String accountingMonth;

    /**
     * 账单周期(从1开始递增)
     */
    @ApiModelProperty(value = "账单周期(从1开始递增)")
                            private Integer chargeSubjectPeriod;

    /**
     * 应缴费日期
     */
    @ApiModelProperty(value = "应缴费日期")
                            private String payableDate;

    /**
     * 应缴费开始日期
     */
    @ApiModelProperty(value = "应缴费开始日期")
                            private String payableStartDate;

    /**
     * 应缴费结束日期
     */
    @ApiModelProperty(value = "应缴费结束日期")
                            private String payableEndDate;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
                            private BigDecimal shouldPayAmount;

    /**
     * 实缴金额
     */
    @ApiModelProperty(value = "实缴金额")
                            private BigDecimal payedAmount;

    /**
     * 待缴金额
     */
    @ApiModelProperty(value = "待缴金额")
                            private BigDecimal replacePayAmount;

    /**
     * 预收金额
     */
    @ApiModelProperty(value = "预收金额")
                            private BigDecimal prepaymentAmount;

    /**
     * 调整后应缴金额
     */
    @ApiModelProperty(value = "调整后应缴金额")
                            private BigDecimal changeShouldPayAmount;

    /**
     * 收款标准币种
     */
    @ApiModelProperty(value = "收款标准币种")
                            private String chargeStandardCny;

    /**
     * 收款标准币种名称
     */
    @ApiModelProperty(value = "收款标准币种名称")
                            private String chargeStandardCnyName;

    /**
     * 缴费周期
     */
    @ApiModelProperty(value = "缴费周期")
                            private String chargeCycle;

    /**
     * 缴费周期名称
     */
    @ApiModelProperty(value = "缴费周期名称")
                            private String chargeCycleName;

    /**
     * 是否计提
     */
    @ApiModelProperty(value = "是否计提")
                            private String isProvision;

    /**
     * 是否计提名称
     */
    @ApiModelProperty(value = "是否计提名称")
                            private String isProvisionName;

    /**
     * 是否循环计费
     */
    @ApiModelProperty(value = "是否循环计费")
                            private String chargeType;

    /**
     * 是否循环计费名称
     */
    @ApiModelProperty(value = "是否循环计费名称")
                            private String chargeTypeName;

    /**
     * 补贴比例
     */
    @ApiModelProperty(value = "补贴比例")
                            private String subsidyRadio;

    /**
     * 补贴金额
     */
    @ApiModelProperty(value = "补贴金额")
                            private String subsidyMoney;

    /**
     * 账单状态
     */
    @ApiModelProperty(value = "账单状态")
                            private String status;

    /**
     * 账单状态名称
     */
    @ApiModelProperty(value = "账单状态名称")
                            private String statusName;

    /**
     * 账单对账状态
     */
    @ApiModelProperty(value = "账单对账状态")
                            private String accountStatus;

    /**
     * 账单对账状态名称
     */
    @ApiModelProperty(value = "账单对账状态名称")
                            private String accountStatusName;

    /**
     * 开票状态
     */
    @ApiModelProperty(value = "开票状态")
                            private String invoicingStatus;

    /**
     * 开票状态名称
     */
    @ApiModelProperty(value = "开票状态名称")
                            private String invoicingStatusName;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
                            private BigDecimal invoiceMoney;

    /**
     * 关联收款单
     */
    @ApiModelProperty(value = "关联收款单")
                            private Long primaryChargeCode;

    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
                            private String chargeSubjectBeginDate;

    /**
     * 收费科目终止日期
     */
    @ApiModelProperty(value = "收费科目终止日期")
                            private String chargeSubjectEndDate;

    /**
     * 账单支付状态
     */
    @ApiModelProperty(value = "账单支付状态")
                            private String billPayStatus;

    /**
     * 账单支付状态名称
     */
    @ApiModelProperty(value = "账单支付状态名称")
                            private String billPayStatusName;

    /**
     * 是否计算退租当月应退租金
     */
    @ApiModelProperty(value = "是否计算退租当月应退租金")
                            private String calCurrentMonth;

    /**
     * 租户实际退场日期
     */
    @ApiModelProperty(value = "租户实际退场日期")
                            private String exitDate;

    /**
     * 全量分页标识
     */
    @ApiModelProperty(value = "全量分页标识")
                            private String fullPage;

    /**
     * page_size
     */
    @ApiModelProperty(value = "page_size")
                            private Integer pageSize;

    /**
     * page_no
     */
    @ApiModelProperty(value = "page_no")
                            private Integer pageNo;

    /**
     * ext1
     */
    @ApiModelProperty(value = "ext1")
                            private String ext1;

    /**
     * ext2
     */
    @ApiModelProperty(value = "ext2")
                            private String ext2;

    /**
     * ext3
     */
    @ApiModelProperty(value = "ext3")
                            private String ext3;

    /**
     * ext4
     */
    @ApiModelProperty(value = "ext4")
                            private String ext4;

    /**
     * ext5
     */
    @ApiModelProperty(value = "ext5")
                            private String ext5;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 账单ID
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    /**
     * @return 合同ID
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    /**
     * @return 账单周期
     */
    public String getBillCycle() {
        return billCycle;
    }

    public void setBillCycle(String billCycle) {
        this.billCycle = billCycle;
    }

    /**
     * @return 租户ID
     */
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    /**
     * @return 租户名称
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 证件号码
     */
    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    /**
     * @return 联系电话
     */
    public String getTenantMobile() {
        return tenantMobile;
    }

    public void setTenantMobile(String tenantMobile) {
        this.tenantMobile = tenantMobile;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 楼栋
     */
    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    /**
     * @return 单元
     */
    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    /**
     * @return 所在楼层
     */
    public String getFloorNo() {
        return floorNo;
    }

    public void setFloorNo(String floorNo) {
        this.floorNo = floorNo;
    }

    /**
     * @return 门牌号
     */
    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    /**
     * @return 账单对应的收费科目
     */
    public String getBillChargeSubject() {
        return billChargeSubject;
    }

    public void setBillChargeSubject(String billChargeSubject) {
        this.billChargeSubject = billChargeSubject;
    }

    /**
     * @return 账单对应的收费科目名称
     */
    public String getBillChargeSubjectName() {
        return billChargeSubjectName;
    }

    public void setBillChargeSubjectName(String billChargeSubjectName) {
        this.billChargeSubjectName = billChargeSubjectName;
    }

    /**
     * @return 计费科目编号(查询条件）
     */
    public String getChargeSubjectNo() {
        return chargeSubjectNo;
    }

    public void setChargeSubjectNo(String chargeSubjectNo) {
        this.chargeSubjectNo = chargeSubjectNo;
    }

    /**
     * @return 账单缴费状态
     */
    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    /**
     * @return 账单缴费状态名称
     */
    public String getBillStatusName() {
        return billStatusName;
    }

    public void setBillStatusName(String billStatusName) {
        this.billStatusName = billStatusName;
    }

    /**
     * @return 账单会计月
     */
    public String getAccountingMonth() {
        return accountingMonth;
    }

    public void setAccountingMonth(String accountingMonth) {
        this.accountingMonth = accountingMonth;
    }

    /**
     * @return 账单周期(从1开始递增)
     */
    public Integer getChargeSubjectPeriod() {
        return chargeSubjectPeriod;
    }

    public void setChargeSubjectPeriod(Integer chargeSubjectPeriod) {
        this.chargeSubjectPeriod = chargeSubjectPeriod;
    }

    /**
     * @return 应缴费日期
     */
    public String getPayableDate() {
        return payableDate;
    }

    public void setPayableDate(String payableDate) {
        this.payableDate = payableDate;
    }

    /**
     * @return 应缴费开始日期
     */
    public String getPayableStartDate() {
        return payableStartDate;
    }

    public void setPayableStartDate(String payableStartDate) {
        this.payableStartDate = payableStartDate;
    }

    /**
     * @return 应缴费结束日期
     */
    public String getPayableEndDate() {
        return payableEndDate;
    }

    public void setPayableEndDate(String payableEndDate) {
        this.payableEndDate = payableEndDate;
    }

    /**
     * @return 应缴金额
     */
    public BigDecimal getShouldPayAmount() {
        return shouldPayAmount;
    }

    public void setShouldPayAmount(BigDecimal shouldPayAmount) {
        this.shouldPayAmount = shouldPayAmount;
    }

    /**
     * @return 实缴金额
     */
    public BigDecimal getPayedAmount() {
        return payedAmount;
    }

    public void setPayedAmount(BigDecimal payedAmount) {
        this.payedAmount = payedAmount;
    }

    /**
     * @return 待缴金额
     */
    public BigDecimal getReplacePayAmount() {
        return replacePayAmount;
    }

    public void setReplacePayAmount(BigDecimal replacePayAmount) {
        this.replacePayAmount = replacePayAmount;
    }

    /**
     * @return 预收金额
     */
    public BigDecimal getPrepaymentAmount() {
        return prepaymentAmount;
    }

    public void setPrepaymentAmount(BigDecimal prepaymentAmount) {
        this.prepaymentAmount = prepaymentAmount;
    }

    /**
     * @return 调整后应缴金额
     */
    public BigDecimal getChangeShouldPayAmount() {
        return changeShouldPayAmount;
    }

    public void setChangeShouldPayAmount(BigDecimal changeShouldPayAmount) {
        this.changeShouldPayAmount = changeShouldPayAmount;
    }

    /**
     * @return 收款标准币种
     */
    public String getChargeStandardCny() {
        return chargeStandardCny;
    }

    public void setChargeStandardCny(String chargeStandardCny) {
        this.chargeStandardCny = chargeStandardCny;
    }

    /**
     * @return 收款标准币种名称
     */
    public String getChargeStandardCnyName() {
        return chargeStandardCnyName;
    }

    public void setChargeStandardCnyName(String chargeStandardCnyName) {
        this.chargeStandardCnyName = chargeStandardCnyName;
    }

    /**
     * @return 缴费周期
     */
    public String getChargeCycle() {
        return chargeCycle;
    }

    public void setChargeCycle(String chargeCycle) {
        this.chargeCycle = chargeCycle;
    }

    /**
     * @return 缴费周期名称
     */
    public String getChargeCycleName() {
        return chargeCycleName;
    }

    public void setChargeCycleName(String chargeCycleName) {
        this.chargeCycleName = chargeCycleName;
    }

    /**
     * @return 是否计提
     */
    public String getIsProvision() {
        return isProvision;
    }

    public void setIsProvision(String isProvision) {
        this.isProvision = isProvision;
    }

    /**
     * @return 是否计提名称
     */
    public String getIsProvisionName() {
        return isProvisionName;
    }

    public void setIsProvisionName(String isProvisionName) {
        this.isProvisionName = isProvisionName;
    }

    /**
     * @return 是否循环计费
     */
    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    /**
     * @return 是否循环计费名称
     */
    public String getChargeTypeName() {
        return chargeTypeName;
    }

    public void setChargeTypeName(String chargeTypeName) {
        this.chargeTypeName = chargeTypeName;
    }

    /**
     * @return 补贴比例
     */
    public String getSubsidyRadio() {
        return subsidyRadio;
    }

    public void setSubsidyRadio(String subsidyRadio) {
        this.subsidyRadio = subsidyRadio;
    }

    /**
     * @return 补贴金额
     */
    public String getSubsidyMoney() {
        return subsidyMoney;
    }

    public void setSubsidyMoney(String subsidyMoney) {
        this.subsidyMoney = subsidyMoney;
    }

    /**
     * @return 账单状态
     */
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return 账单状态名称
     */
    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    /**
     * @return 账单对账状态
     */
    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    /**
     * @return 账单对账状态名称
     */
    public String getAccountStatusName() {
        return accountStatusName;
    }

    public void setAccountStatusName(String accountStatusName) {
        this.accountStatusName = accountStatusName;
    }

    /**
     * @return 开票状态
     */
    public String getInvoicingStatus() {
        return invoicingStatus;
    }

    public void setInvoicingStatus(String invoicingStatus) {
        this.invoicingStatus = invoicingStatus;
    }

    /**
     * @return 开票状态名称
     */
    public String getInvoicingStatusName() {
        return invoicingStatusName;
    }

    public void setInvoicingStatusName(String invoicingStatusName) {
        this.invoicingStatusName = invoicingStatusName;
    }

    /**
     * @return 开票金额
     */
    public BigDecimal getInvoiceMoney() {
        return invoiceMoney;
    }

    public void setInvoiceMoney(BigDecimal invoiceMoney) {
        this.invoiceMoney = invoiceMoney;
    }

    /**
     * @return 关联收款单
     */
    public Long getPrimaryChargeCode() {
        return primaryChargeCode;
    }

    public void setPrimaryChargeCode(Long primaryChargeCode) {
        this.primaryChargeCode = primaryChargeCode;
    }

    /**
     * @return 收费科目起始日期
     */
    public String getChargeSubjectBeginDate() {
        return chargeSubjectBeginDate;
    }

    public void setChargeSubjectBeginDate(String chargeSubjectBeginDate) {
        this.chargeSubjectBeginDate = chargeSubjectBeginDate;
    }

    /**
     * @return 收费科目终止日期
     */
    public String getChargeSubjectEndDate() {
        return chargeSubjectEndDate;
    }

    public void setChargeSubjectEndDate(String chargeSubjectEndDate) {
        this.chargeSubjectEndDate = chargeSubjectEndDate;
    }

    /**
     * @return 账单支付状态
     */
    public String getBillPayStatus() {
        return billPayStatus;
    }

    public void setBillPayStatus(String billPayStatus) {
        this.billPayStatus = billPayStatus;
    }

    /**
     * @return 账单支付状态名称
     */
    public String getBillPayStatusName() {
        return billPayStatusName;
    }

    public void setBillPayStatusName(String billPayStatusName) {
        this.billPayStatusName = billPayStatusName;
    }

    /**
     * @return 是否计算退租当月应退租金
     */
    public String getCalCurrentMonth() {
        return calCurrentMonth;
    }

    public void setCalCurrentMonth(String calCurrentMonth) {
        this.calCurrentMonth = calCurrentMonth;
    }

    /**
     * @return 租户实际退场日期
     */
    public String getExitDate() {
        return exitDate;
    }

    public void setExitDate(String exitDate) {
        this.exitDate = exitDate;
    }

    /**
     * @return 全量分页标识
     */
    public String getFullPage() {
        return fullPage;
    }

    public void setFullPage(String fullPage) {
        this.fullPage = fullPage;
    }

    /**
     * @return page_size
     */
    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * @return page_no
     */
    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    /**
     * @return ext1
     */
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * @return ext2
     */
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * @return ext3
     */
    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    /**
     * @return ext4
     */
    public String getExt4() {
        return ext4;
    }

    public void setExt4(String ext4) {
        this.ext4 = ext4;
    }

    /**
     * @return ext5
     */
    public String getExt5() {
        return ext5;
    }

    public void setExt5(String ext5) {
        this.ext5 = ext5;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmBillManagementV2Entity{" +
            "billId=" + billId +
            ", billNo=" + billNo +
            ", contractCode=" + contractCode +
            ", contractNo=" + contractNo +
            ", billCycle=" + billCycle +
            ", tenantCode=" + tenantCode +
            ", tenantName=" + tenantName +
            ", certNo=" + certNo +
            ", tenantMobile=" + tenantMobile +
            ", projectName=" + projectName +
            ", buildingNo=" + buildingNo +
            ", unitNo=" + unitNo +
            ", floorNo=" + floorNo +
            ", roomNo=" + roomNo +
            ", billChargeSubject=" + billChargeSubject +
            ", billChargeSubjectName=" + billChargeSubjectName +
            ", chargeSubjectNo=" + chargeSubjectNo +
            ", billStatus=" + billStatus +
            ", billStatusName=" + billStatusName +
            ", accountingMonth=" + accountingMonth +
            ", chargeSubjectPeriod=" + chargeSubjectPeriod +
            ", payableDate=" + payableDate +
            ", payableStartDate=" + payableStartDate +
            ", payableEndDate=" + payableEndDate +
            ", shouldPayAmount=" + shouldPayAmount +
            ", payedAmount=" + payedAmount +
            ", replacePayAmount=" + replacePayAmount +
            ", prepaymentAmount=" + prepaymentAmount +
            ", changeShouldPayAmount=" + changeShouldPayAmount +
            ", chargeStandardCny=" + chargeStandardCny +
            ", chargeStandardCnyName=" + chargeStandardCnyName +
            ", chargeCycle=" + chargeCycle +
            ", chargeCycleName=" + chargeCycleName +
            ", isProvision=" + isProvision +
            ", isProvisionName=" + isProvisionName +
            ", chargeType=" + chargeType +
            ", chargeTypeName=" + chargeTypeName +
            ", subsidyRadio=" + subsidyRadio +
            ", subsidyMoney=" + subsidyMoney +
            ", status=" + status +
            ", statusName=" + statusName +
            ", accountStatus=" + accountStatus +
            ", accountStatusName=" + accountStatusName +
            ", invoicingStatus=" + invoicingStatus +
            ", invoicingStatusName=" + invoicingStatusName +
            ", invoiceMoney=" + invoiceMoney +
            ", primaryChargeCode=" + primaryChargeCode +
            ", chargeSubjectBeginDate=" + chargeSubjectBeginDate +
            ", chargeSubjectEndDate=" + chargeSubjectEndDate +
            ", billPayStatus=" + billPayStatus +
            ", billPayStatusName=" + billPayStatusName +
            ", calCurrentMonth=" + calCurrentMonth +
            ", exitDate=" + exitDate +
            ", fullPage=" + fullPage +
            ", pageSize=" + pageSize +
            ", pageNo=" + pageNo +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", ext3=" + ext3 +
            ", ext4=" + ext4 +
            ", ext5=" + ext5 +
            ", delFlag=" + delFlag +
        "}";
    }
}