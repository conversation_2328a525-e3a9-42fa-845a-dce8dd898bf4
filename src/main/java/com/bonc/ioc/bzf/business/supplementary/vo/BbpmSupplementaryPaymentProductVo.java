package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 追加账单产品表 实体类
 *
 * <AUTHOR>
 * @date 2025-04-01
 * @change 2025-04-01 by pyj for init
 */
@Data
@ApiModel(value = "BbpmSupplementaryPaymentProductVo对象", description = "追加账单产品表")
public class BbpmSupplementaryPaymentProductVo extends McpBaseVo implements Serializable {

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @NotBlank(message = "产品id不能为空", groups = {UpdateValidatorGroup.class})
    private String productId;

    /**
     * 上级id
     */
    @ApiModelProperty(value = "上级id")
    private String parentId;

    /**
     * 类型(1.管理协议 2.使用协议)
     */
    @ApiModelProperty(value = "类型(1.管理协议 2.使用协议)")
    private String type;

    /**
     * 协议编号
     */
    @ApiModelProperty(value = "协议编号")
    private String agreementNo;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
    private String productNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerNo;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String customerTel;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    @McpDictPoint(dictCode = "CERTIFICATE_TYPE", overTransCopyTo = "customerIdTypeName")
    private String customerIdType;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型名称")
    private String customerIdTypeName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;
}
