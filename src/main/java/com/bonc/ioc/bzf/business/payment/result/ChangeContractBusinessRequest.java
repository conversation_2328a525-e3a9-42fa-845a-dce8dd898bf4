package com.bonc.ioc.bzf.business.payment.result;

import com.bonc.ioc.bzf.business.payment.result.create.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 合同变更 商业
 */
@Data
public class ChangeContractBusinessRequest implements Serializable {
    @ApiModelProperty(value = "变更类型")
    private String changeType;
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "商业承租人变更")
    private BusinessContractChangeOwner businessContractChangeOwnerDTO;//08商业承租人变更（businessContractChangeOwner）

}
