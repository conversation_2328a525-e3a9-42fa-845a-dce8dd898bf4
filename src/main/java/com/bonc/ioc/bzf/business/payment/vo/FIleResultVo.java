package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户签约表 实体类
 *
 * <AUTHOR>
 * @date 2022-06-08
 * @change 2022-06-08 by wtl for init
 */
@Data
@ApiModel(value="FIleResultVo")
public class FIleResultVo extends McpBaseVo implements Serializable{

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private Integer id;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String  fileUrl;

    /**
     * 文件原名称
     */
    @ApiModelProperty(value = "文件原名称")
    private String  fileName;

    /**
     * 文件hashcode
     */
    @ApiModelProperty(value = "文件hashcode")
    private String  fileHashCode;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 业务系统名称
     */
    @ApiModelProperty(value = "业务系统名称")
    private String  project;

    /**
     * 文件在服务器上的真实路径
     */
    @ApiModelProperty(value = "文件在服务器上的真实路径")
    private String  realPath;

    /**
     * 文件删除标志 1-存在 0-删除
     */
    @ApiModelProperty(value = "文件删除标志 1-存在 0-删除")
    private String  state;

    @Override
    public String toString() {
        return "FIleResultVo{" +
                "id=" + id +
                ", fileUrl='" + fileUrl + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileHashCode='" + fileHashCode + '\'' +
                ", createTime=" + createTime +
                ", project='" + project + '\'' +
                ", realPath='" + realPath + '\'' +
                ", state='" + state + '\'' +
                '}';
    }
}
