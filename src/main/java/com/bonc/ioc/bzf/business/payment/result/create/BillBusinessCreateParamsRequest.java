package com.bonc.ioc.bzf.business.payment.result.create;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 3.37. 商业合同生成账单接口
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
@Builder
public class BillBusinessCreateParamsRequest {
    @ApiModelProperty(value = "合同ID")
    @NotBlank(message = "合同ID", groups = {UpdateValidatorGroup.class})
    private String contractId;
    @ApiModelProperty(value = "合同分类")
    @NotBlank(message = "合同分类", groups = {UpdateValidatorGroup.class})
    private String contractClassification;
    @ApiModelProperty(value = "合同类型")
    @NotBlank(message = "合同类型", groups = {UpdateValidatorGroup.class})
    private String contractType;
    @ApiModelProperty(value = "合同当前状态")
    @NotBlank(message = "合同当前状态", groups = {UpdateValidatorGroup.class})
    private String contractStatus;
    @ApiModelProperty(value = "合同起始日期")
    @NotBlank(message = "合同起始日期", groups = {UpdateValidatorGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginDate;
    @ApiModelProperty(value = "合同终止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotBlank(message = "合同终止日期", groups = {UpdateValidatorGroup.class})
    private Date contractEndDate;
    @ApiModelProperty(value = "合同签订时间")
    @NotBlank(message = "合同签订时间", groups = {UpdateValidatorGroup.class})
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractSignTime;
    @ApiModelProperty(value = "合同起租日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotBlank(message = "合同起租日", groups = {UpdateValidatorGroup.class})
    private Date contractCommencementDate;
    @ApiModelProperty(value = "合同签约计价单位")
    @NotBlank(message = "合同签约计价单位", groups = {UpdateValidatorGroup.class})
    private String contractPriceUnit;
    @ApiModelProperty(value = "合同计价周期")
    @NotBlank(message = "合同计价周期", groups = {UpdateValidatorGroup.class})
    private String contractPricePeriod;
    @ApiModelProperty(value = "合同面积")
    @NotBlank(message = "合同面积", groups = {UpdateValidatorGroup.class})
    private BigDecimal contractArea;

    @ApiModelProperty(value = "签约房间类型")
    @NotBlank(message = "签约房间类型", groups = {UpdateValidatorGroup.class})
    private String roomType;
    @ApiModelProperty(value = "审批人")
    @NotBlank(message = "审批人", groups = {UpdateValidatorGroup.class})
    private String approver;
    @ApiModelProperty(value = "审批时间")
    @NotBlank(message = "审批时间", groups = {UpdateValidatorGroup.class})
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    @ApiModelProperty(value = "租户ID")
    @NotBlank(message = "租户ID", groups = {UpdateValidatorGroup.class})
    private String tenantId;
    @ApiModelProperty(value = "租户开户总行名称")
    @NotBlank(message = "租户开户总行名称", groups = {UpdateValidatorGroup.class})
    private String tenantBankName;
    @ApiModelProperty(value = "租户开户总行编码")
    @NotBlank(message = "租户开户总行编码", groups = {UpdateValidatorGroup.class})
    private String tenantBankCode;
    @ApiModelProperty(value = "租户开户支行名称")
    @NotBlank(message = "租户开户支行名称", groups = {UpdateValidatorGroup.class})
    private String tenantBankBranchName;
    @ApiModelProperty(value = "租户开户支行编码")
    @NotBlank(message = "租户开户支行编码", groups = {UpdateValidatorGroup.class})
    private String tenantBankBrachCode;
    @ApiModelProperty(value = "租户银行卡户名")
    @NotBlank(message = "租户银行卡户名", groups = {UpdateValidatorGroup.class})
    private String tenantBankAccountName;
    @ApiModelProperty(value = "租户银行卡卡号")
    @NotBlank(message = "租户银行卡卡号", groups = {UpdateValidatorGroup.class})
    private String tenantBankAccountNo;
    @ApiModelProperty(value = "是否进行银行卡代扣")
    @NotBlank(message = "是否进行银行卡代扣", groups = {UpdateValidatorGroup.class})
    private String withholding;
    @ApiModelProperty(value = "银行卡代扣摘要")
    @NotBlank(message = "银行卡代扣摘要", groups = {UpdateValidatorGroup.class})
    private String withholdingSummary;
    @ApiModelProperty(value = "银行卡代扣备注")
    @NotBlank(message = "银行卡代扣备注", groups = {UpdateValidatorGroup.class})
    private String withholdingRemark;
    @ApiModelProperty(value = "代扣鉴权协议号")
    @NotBlank(message = "代扣鉴权协议号", groups = {UpdateValidatorGroup.class})
    private String agreementNo;
    @ApiModelProperty(value = "租户姓名")
    @NotBlank(message = "租户姓名", groups = {UpdateValidatorGroup.class})
    private String tenantName;
    @ApiModelProperty(value = "租户手机号")
    @NotBlank(message = "租户手机号", groups = {UpdateValidatorGroup.class})
    private String tenantMobile;
    @ApiModelProperty(value = "租户证件类型")
    @NotBlank(message = "租户证件类型", groups = {UpdateValidatorGroup.class})
    private String tenantIDType;

    @ApiModelProperty(value = "租户证件号码")
    @NotBlank(message = "租户证件号码", groups = {UpdateValidatorGroup.class})
    private String idNumber;
    @ApiModelProperty(value = "租户邮箱地址")
    @NotBlank(message = "租户邮箱地址", groups = {UpdateValidatorGroup.class})
    private String mailUrl;
    @ApiModelProperty(value = "公租房备案号")
    @NotBlank(message = "公租房备案号", groups = {UpdateValidatorGroup.class})
    private String publicRecordNo;
    @ApiModelProperty(value = "租户客户编号")
    @NotBlank(message = "租户客户编号", groups = {UpdateValidatorGroup.class})
    private String tenantCustomerNo;
    @ApiModelProperty(value = "租户客商编号")
    @NotBlank(message = "公租房备案号", groups = {UpdateValidatorGroup.class})
    private String tenantSupplierNo;
    @ApiModelProperty(value = "租户客商名称")
    @NotBlank(message = "租户客商名称", groups = {UpdateValidatorGroup.class})
    private String tenantSupplierName;
    @ApiModelProperty(value = "趸租企业ID")
    private String companyId;
    @ApiModelProperty(value = "趸租企业证照类型")
    private String companyIDType;
    @ApiModelProperty(value = "趸租企业社会信用代码")
    private String socialCreditCode;
    @ApiModelProperty(value = "趸租企业名称")
    private String companyName;
    @ApiModelProperty(value = "趸租企业客户编号")
    private String companyCustomerNo;
    @ApiModelProperty(value = "趸租企业客商编号")
    private String companySupplierNo;
    @ApiModelProperty(value = "趸租企业客商名称")
    private String companySupplierName;
    @ApiModelProperty(value = "企业开户总行名称")
    private String companyBankName;
    @ApiModelProperty(value = "企业开户总行编码")
    private String companyBankCode;
    @ApiModelProperty(value = "企业开户支行名称")
    private String companyBankBranchName;
    @ApiModelProperty(value = "企业开户支行编码")
    private String companyBankBranchCode;
    @ApiModelProperty(value = "企业银行卡户名")
    private String companyBankAccountName;
    @ApiModelProperty(value = "企业银行卡卡号")
    private String companyBankAccountNo;
    @ApiModelProperty(value = "企业纳税识别号")
    private String companyTaxNo;
    @ApiModelProperty(value = "委托代理人")
    private String authorizedAgent ;
    @ApiModelProperty(value = "委托代理人电话")
    private String  authorizedAgentMobile ;

    @ApiModelProperty(value = "项目列表")
    @Singular("projectRequest")
    private List<ProjectRequest> projectList;

    //商业续租新增字段
    @ApiModelProperty(value = "旧合同ID")
    private String  originalContractId;
    @ApiModelProperty(value = "押金退回相关")
    private DepositReturnRequest depositReturn;


    @Override
    public String toString() {
        return "BillBusinessCreateParamsRequest{" +
                "contractId='" + contractId + '\'' +
                ", contractClassification='" + contractClassification + '\'' +
                ", contractType='" + contractType + '\'' +
                ", contractStatus='" + contractStatus + '\'' +
                ", contractBeginDate=" + contractBeginDate +
                ", contractEndDate=" + contractEndDate +
                ", contractSignTime=" + contractSignTime +
                ", contractCommencementDate=" + contractCommencementDate +
                ", contractPriceUnit='" + contractPriceUnit + '\'' +
                ", contractPricePeriod='" + contractPricePeriod + '\'' +
                ", contractArea=" + contractArea +
                ", roomType='" + roomType + '\'' +
                ", approver='" + approver + '\'' +
                ", approveTime=" + approveTime +
                ", tenantId='" + tenantId + '\'' +
                ", tenantBankName='" + tenantBankName + '\'' +
                ", tenantBankCode='" + tenantBankCode + '\'' +
                ", tenantBankBranchName='" + tenantBankBranchName + '\'' +
                ", tenantBankBrachCode='" + tenantBankBrachCode + '\'' +
                ", tenantBankAccountName='" + tenantBankAccountName + '\'' +
                ", tenantBankAccountNo='" + tenantBankAccountNo + '\'' +
                ", withholding='" + withholding + '\'' +
                ", withholdingSummary='" + withholdingSummary + '\'' +
                ", withholdingRemark='" + withholdingRemark + '\'' +
                ", agreementNo='" + agreementNo + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", tenantMobile='" + tenantMobile + '\'' +
                ", tenantIDType='" + tenantIDType + '\'' +
                ", idNumber='" + idNumber + '\'' +
                ", mailUrl='" + mailUrl + '\'' +
                ", publicRecordNo='" + publicRecordNo + '\'' +
                ", tenantCustomerNo='" + tenantCustomerNo + '\'' +
                ", tenantSupplierNo='" + tenantSupplierNo + '\'' +
                ", tenantSupplierName='" + tenantSupplierName + '\'' +
                ", companyId='" + companyId + '\'' +
                ", companyIDType='" + companyIDType + '\'' +
                ", socialCreditCode='" + socialCreditCode + '\'' +
                ", companyName='" + companyName + '\'' +
                ", companyCustomerNo='" + companyCustomerNo + '\'' +
                ", companySupplierNo='" + companySupplierNo + '\'' +
                ", companySupplierName='" + companySupplierName + '\'' +
                ", companyBankName='" + companyBankName + '\'' +
                ", companyBankCode='" + companyBankCode + '\'' +
                ", companyBankBranchName='" + companyBankBranchName + '\'' +
                ", companyBankBranchCode='" + companyBankBranchCode + '\'' +
                ", companyBankAccountName='" + companyBankAccountName + '\'' +
                ", companyBankAccountNo='" + companyBankAccountNo + '\'' +
                ", companyTaxNo='" + companyTaxNo + '\'' +
                ", authorizedAgent='" + authorizedAgent + '\'' +
                ", authorizedAgentMobile='" + authorizedAgentMobile + '\'' +
                ", projectList=" + projectList +
                ", originalContractId='" + originalContractId + '\'' +
                ", depositReturn=" + depositReturn +
                '}';
    }
}
