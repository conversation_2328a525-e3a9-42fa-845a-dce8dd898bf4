package com.bonc.ioc.bzf.business.reminder.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.bzf.business.invoice.vo.BbpmInvoiceQueryPageResultVo;
import com.bonc.ioc.bzf.business.invoice.vo.BbpmInvoiceQueryPageVo;
import com.bonc.ioc.bzf.business.payment.config.FeignConfiguration;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BbctSigningV2FeignCilent;
import com.bonc.ioc.bzf.business.payment.feign.feign.BbmessageFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BmsFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementVo;
import com.bonc.ioc.bzf.business.payment.vo.ProjectVoV2;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmCollectionWhitelistMapper;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmReminderRulesMainMapper;
import com.bonc.ioc.bzf.business.reminder.enums.ReminderEnums;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmMessageSendMainLogService;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmMessageSendSubLogService;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmMessageSendSubLogV2Service;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmReminderRulesMainService;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.feign.service.IBmsBaseServiceFeignService;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserLoginReq;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserLoginResp;
import com.sinovatech.rd.bms.commons.util.Des1;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;
import com.sinovatech.unifiedmanage.client.module.timTask.dto.UniManJobCallBackDTO;
import com.sinovatech.unifiedmanage.client.module.timTask.util.UniManConstants;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@EnableScheduling
@Slf4j
@Component
public class TaskPlan {
    @Autowired
    private RedissonClient redissonClient;
    @Resource
    private IBbpmReminderRulesMainService baseService;

    @Resource
    private IBbpmMessageSendMainLogService iBbpmMessageSendMainLogService;

    @Resource
    private IBbpmMessageSendSubLogService iBbpmMessageSendSubLogService;

    @Resource
    private IBbpmMessageSendSubLogV2Service iBbpmMessageSendSubLogV2Service;

    @Autowired
    private BbctSigningV2FeignCilent signingV2FeignCilent;

    @Resource
    private BbmessageFeignClient bbmessageFeignClient;

    @Resource
    private FeignConfiguration feignConfiguration;


    /**
     * 是否发送短信
     */
    @Value("${reminderRules.shortMessage.isSend}")
    private boolean isSend;

    /**
     * 是否用我自己手机号码
     */
    @Value("${reminderRules.shortMessage.useMyMobile}")
    private boolean useMyMobile;

    /**
     * 我的手机号码
     */
    @Value("${reminderRules.shortMessage.tenantMobile}")
    private String tenantMobile;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;


    @Value("${yecai.url}")
    private String yecaiUrl;

    @Resource
    private BbpmReminderRulesMainMapper baseMapper;

    @Resource
    private BbpmCollectionWhitelistMapper bbpmCollectionWhitelistMapper;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

//    /**
//     * 在程序中获取token的用户名和密码
//     */
//    @Value("${freepass.userName}")
//    private String userName;
//    @Value("${freepass.password}")
//    private String password;
//    @Value("${freepass.firstKey}")
//    private String firstKey;
//    @Value("${freepass.secondKey}")
//    private String secondKey;
//    @Value("${freepass.thirdKey}")
//    private String thirdKey;
    @Resource
    private IBmsBaseServiceFeignService bmsBaseServiceFeignService;

    @Resource
    private BmsFeignClient bmsFeignClient;

    private static String token;
//    缴费提醒通知站内信：2e6d85259e72495d898d30c35bcbea3e
//    缴费提醒通知短信：d01f2b5a09cc46df8e0268f893beb2f2
//    催缴通知站内信：9174a7fb352846889b40bfea17e44e45
//    催缴通知短信：c50e0566aa7a4862ba7992edb21e9061

    /**
     * 催缴通知站内信
     */
    private static final String REMINDER_TEMPLATE_ID = "9174a7fb352846889b40bfea17e44e45";
    /**
     * 催缴通知短信
     */
    private static final String REMINDER_SMS_TEMPLATE_ID = "c50e0566aa7a4862ba7992edb21e9061";
    /**
     * 缴费提醒通知站内信
     */
    private static final String PAYMENT_REMINDER_TEMPLATE_ID = "2e6d85259e72495d898d30c35bcbea3e";
    /**
     * 缴费提醒通知短信
     */
    private static final String PAYMENT_REMINDER_SMS_TEMPLATE_ID = "d01f2b5a09cc46df8e0268f893beb2f2";

    private static final String REMINDER_TYPE = "reminder";

    private static final String PAYMENT_TYPE = "paymentReminder";

    public static final Map<String, String> THREAD_SAFE_MAP = Collections.unmodifiableMap(
            new ConcurrentHashMap<String, String>() {{
                put(REMINDER_TEMPLATE_ID, REMINDER_TYPE);
                put(REMINDER_SMS_TEMPLATE_ID, REMINDER_TYPE);
                put(PAYMENT_REMINDER_TEMPLATE_ID, PAYMENT_TYPE);
                put(PAYMENT_REMINDER_SMS_TEMPLATE_ID, PAYMENT_TYPE);
            }}
    );

    private static final SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 每天上午10点运行
     * 注意：签约系统获取项目要配置免登录
     * 缴费中心配置cron表达式等
     */
//    @Scheduled(cron ="${reminderRules.schedule.cron}")
//    @Scheduled(cron = "*/10 * * * * ?")
//    @Scheduled(cron ="0 0 10 * * ?")
//    @Transactional(rollbackFor = {Exception.class})

    public UniManJobCallBackDTO reminderRulesSendScheduledTasks() {
        log.info("----定时任务开始执行----");

        UniManJobCallBackDTO dto = new UniManJobCallBackDTO();

        String reuslt = "成功";

        String lockKey = "lock:reminderRules";
        RLock disLock = redissonClient.getLock(lockKey);
        if (disLock.tryLock()) {
            token = getTokenByFixedUser();
            log.info("----定时任务获取的token----"+token);

            StringBuffer textRequestAll = new StringBuffer("");
            StringBuffer textResponseAll = new StringBuffer("");
            BbpmMessageSendMainLogVo bbpmMessageSendMainLogVo = new BbpmMessageSendMainLogVo();
            try {
                //插入任务日志记录
                bbpmMessageSendMainLogVo.setStatus(ReminderEnums.LOG_STATUS_ST.getCode());
                String mainId = iBbpmMessageSendMainLogService.insertRecord(bbpmMessageSendMainLogVo);
                bbpmMessageSendMainLogVo.setMainId(mainId);

                //查催缴规则 启用中的
                List<BbpmReminderRulesMainAndSubVo> mainAndSubVoList = baseMapper.selectMainAndSubAll();
                if (mainAndSubVoList == null || mainAndSubVoList.size() == 0) {
                    throw new McpException("缴费提醒规则表没有启用状态的数据");
                }

                List<BbpmReminderRulesMainAndSubAllVo> newList = new ArrayList<>();
                //前端现在有限制， 一条规则中 未缴/未足额缴纳  和  已缴但未对平 最多都只有一条记录
                List<BbpmReminderRulesMainAndSubVo> unpaidList = mainAndSubVoList.stream().filter(mainAndSubVo ->
                        ReminderEnums.BILLING_COLLECTION_REASON_UNPAID.getCode().equals(mainAndSubVo.getCollectionReason())
                ).collect(Collectors.toList());
                List<BbpmReminderRulesMainAndSubVo> unequalList = mainAndSubVoList.stream().filter(mainAndSubVo ->
                        ReminderEnums.BILLING_COLLECTION_REASON_UNEQUAL.getCode().equals(mainAndSubVo.getCollectionReason())
                ).collect(Collectors.toList());
                if(unpaidList!=null && unpaidList.size()>0){
                    Map<String,BbpmReminderRulesMainAndSubVo> unequalMap = new HashMap<>();
                    if(unequalList!=null && unequalList.size()>0){
                        //把unequalList转map,用rulesId做key
                        unequalMap = unequalList.stream().collect(Collectors.toMap(BbpmReminderRulesMainAndSubVo::getRulesId, Function.identity()));
                    }
                    for(BbpmReminderRulesMainAndSubVo unpaid: unpaidList){
                        BbpmReminderRulesMainAndSubAllVo bbpmReminderRulesMainAndSubAllVo = new BbpmReminderRulesMainAndSubAllVo();
                        bbpmReminderRulesMainAndSubAllVo.setUnpaid(unpaid);
                        //如果已缴但未对平存在，则合并到未缴/未足额缴纳中
                        if(unequalMap!=null && unequalMap.containsKey(unpaid.getRulesId())){
                            bbpmReminderRulesMainAndSubAllVo.setUnequal(unequalMap.get(unpaid.getRulesId()));
                        }
                        newList.add(bbpmReminderRulesMainAndSubAllVo);
                    }
                }else{
                    if(unequalList!=null && unequalList.size()>0){
                        for(BbpmReminderRulesMainAndSubVo unequal: unequalList){
                            BbpmReminderRulesMainAndSubAllVo bbpmReminderRulesMainAndSubAllVo = new BbpmReminderRulesMainAndSubAllVo();
                            bbpmReminderRulesMainAndSubAllVo.setUnequal(unequal);
                            newList.add(bbpmReminderRulesMainAndSubAllVo);
                        }
                    }
                }

                //查询所有相关项目信息
                AppReply<List<ProjectVoV2>> projectListAppReply = signingV2FeignCilent.selectProjectByAllAuthority();
                if (projectListAppReply != null && AppReply.SUCCESS_CODE.equals(projectListAppReply.getCode())
                        && projectListAppReply.getData() != null && projectListAppReply.getData().size() > 0) {
                    //是否有数据发送短信
                    boolean flag = false;

                    List<ProjectVoV2> projectVoV2List = projectListAppReply.getData();
                    //projectVoV2List按照projectId去重
                   List<String> projectIdList = projectVoV2List.stream()
                            .map(ProjectVoV2::getProjectId)
                            .distinct().collect(Collectors.toList());

//                    for (ProjectVoV2 projectVoV2 : projectVoV2List) {
//                        String projectId = projectVoV2.getProjectId();
                    for(String projectId:projectIdList){
                        //过滤掉 项目白名单数据
                        BbpmCollectionWhitelistVo bbpmCollectionWhitelistVo = new BbpmCollectionWhitelistVo();
                        bbpmCollectionWhitelistVo.setType(PaymentEnums.COLLECTION_WHITE_PROJECT.getCode());
                        bbpmCollectionWhitelistVo.setOperateProjectId(projectId);
                        List<BbpmCollectionWhitelistVo> bbpmCollectionWhitelistVoList = bbpmCollectionWhitelistMapper.selectCustomerOrProject(bbpmCollectionWhitelistVo);
                        //有数据就跳过
                        if(bbpmCollectionWhitelistVoList != null && bbpmCollectionWhitelistVoList.size()>0){
                            continue;
                        }

//                        for (BbpmReminderRulesMainAndSubVo bbpmReminderRulesMainAndSubVo : newMainAndSubVoList) {
                        for(BbpmReminderRulesMainAndSubAllVo bbpmReminderRulesMainAndSubAllVo:newList){
                            BbpmReminderRulesMainAndSubVo unpaid = bbpmReminderRulesMainAndSubAllVo.getUnpaid();
                            BbpmReminderRulesMainAndSubVo unequal = bbpmReminderRulesMainAndSubAllVo.getUnequal();

                            String payableDate = null;
                            if(unpaid != null){
                                //账单应缴日期前后:01前,02后
                                String billDueAround = unpaid.getBillDueAround();
                                //应缴日期天数 ---> 工银计算方法是： 账单里的日期     -  payableDateDays = 当前日期
                                //                 "payableDate": "2023-10-01" - 38 = 2023-08-24
                                //                 "payableDate": "2023-08-01" - (-23) = 2023-08-24
                                //                 "payableDate": "2023-07-25"- (-30) =  2023-08-24
                                //按工银算法，应缴日期后算负数--》也就是当前日期前算负数
                                //当前日期是2024-04-16，payableDateDays=-2时，工银查出来的数据为： "payableDate": "2024-04-14"
                                //                    payableDateDays=3时，工银查出来的数据为： "payableDate": "2024-04-19"
                                int payableDateDays = Integer.parseInt(unpaid.getBillDueDays());
                                if (ReminderEnums.BILLDUEAROUND_AFTER.getCode().equals(billDueAround)) {
                                    payableDateDays = -Integer.parseInt(unpaid.getBillDueDays());
                                }
                                //计算传给工银的应缴日期
                               payableDate = getDateWithDayOffset(payableDateDays);
                            }
                            String chargeDate = null;
                            if (unequal != null) {
                                //账单应缴日期前后:01前,02后
                                String billDueAround = unequal.getBillDueAround();
                                //应缴日期天数 ---> 工银计算方法是： 账单里的日期     -  payableDateDays = 当前日期
                                //                 "payableDate": "2023-10-01" - 38 = 2023-08-24
                                //                 "payableDate": "2023-08-01" - (-23) = 2023-08-24
                                //                 "payableDate": "2023-07-25"- (-30) =  2023-08-24
                                //按工银算法，应缴日期后算负数--》也就是当前日期前算负数
                                //当前日期是2024-04-16，payableDateDays=-2时，工银查出来的数据为： "payableDate": "2024-04-14"
                                //                    payableDateDays=3时，工银查出来的数据为： "payableDate": "2024-04-19"
                                int chargeDateDays = Integer.parseInt(unequal.getBillDueDays());
                                if (ReminderEnums.BILLDUEAROUND_AFTER.getCode().equals(billDueAround)) {
                                    chargeDateDays = -Integer.parseInt(unequal.getBillDueDays());
                                }
                                //计算传给工银的应缴日期
                                chargeDate = getDateWithDayOffset(chargeDateDays);
                            }

                            BbpmReminderRulesMainAndSubVo bbpmReminderRulesMainAndSubVo = new BbpmReminderRulesMainAndSubVo();
                            if(unpaid!=null){
                                bbpmReminderRulesMainAndSubVo = unpaid;
                            }else{
                                bbpmReminderRulesMainAndSubVo = unequal;
                            }

                            //合同类型:01散租合同,02趸租合同,03趸租管理协议
                            String contractType = bbpmReminderRulesMainAndSubVo.getContractType();
                            //业务类型:01公租房,02保租房
                            String businessType = bbpmReminderRulesMainAndSubVo.getBusinessType();
                            //定时任务只催缴个人账单，不催缴企业账单（企业账单仅支持手动催缴）；
                            List<String> owerList = new ArrayList<>();
                            if (ReminderEnums.CONTRACTTYPE_SZ.getCode().equals(contractType)) {
                                owerList.add(ReminderEnums.OWER_PERSON.getCode());
                            } else if (ReminderEnums.CONTRACTTYPE_DZ.getCode().equals(contractType)) {
//                                owerList.add(ReminderEnums.OWER_COMPANY.getCode());
                            } else if (ReminderEnums.CONTRACTTYPE_GL.getCode().equals(contractType)) {
//                                owerList.add(ReminderEnums.OWER_COMPANY.getCode());
                                owerList.add(ReminderEnums.OWER_PERSON.getCode());
                            }

                            if(owerList==null || owerList.size()==0){
                                continue;
                            }

                            for (String ower : owerList) {
                                //拼接参数请求工银
                                ListByPayableDateVo listByPayableDateVo = new ListByPayableDateVo();
                                listByPayableDateVo.setProjectId(projectId);
                                listByPayableDateVo.setPayableDate(payableDate);
                                listByPayableDateVo.setChargeDate(chargeDate);
                                listByPayableDateVo.setContractType(contractType);
                                listByPayableDateVo.setBusinessType(businessType);
                                listByPayableDateVo.setOwner(ower);
                                listByPayableDateVo.setChargeSubject("01,03,04,05,07,08,09,10,11,12,13,14");

                                List<UnpaidBillMainVo> unpaidBillMainVoList = null;
                                try{
                                    //工银返回合同和账单信息
                                    unpaidBillMainVoList = baseService.listByPayableDate(listByPayableDateVo);
                                }catch (Exception e){
                                    log.error("定时调用工银3.36接口报错:"+e.getMessage());
                                }

                                //账单先处理
                                if (unpaidBillMainVoList != null && unpaidBillMainVoList.size() > 0) {
                                    List<UnpaidBillMix> unpaidBillMixList = new ArrayList<>();
                                    for(UnpaidBillMainVo unpaidBillMainVo : unpaidBillMainVoList){
                                        List<UnpaidBillVo> billVoList = unpaidBillMainVo.getUnPaidBillDetailVOList();
                                        //判断billVoList中是否存在与查询payableDate 和 chargeDate一样的值，没有匹配不要发短信 ddddd1
                                        if (StringUtils.isNotBlank(payableDate) && StringUtils.isNotBlank(chargeDate)) {
//                                            boolean containsPayableDateString = billVoList.stream()
//                                                    .anyMatch(bill -> (
//                                                                    listByPayableDateVo.getPayableDate().equals(bill.getPayableDate())
//                                                                            && ((ReminderEnums.CHARGE_STATUS_PART.getCode().equals(bill.getChargeStatus()) && (bill.getUnevenMoney() == null || BigDecimal.ZERO.equals(bill.getUnevenMoney())))
//                                                                            || ReminderEnums.CHARGE_STATUS_NOT.getCode().equals(bill.getChargeStatus())))
//                                                                    ||
//                                                                    (listByPayableDateVo.getChargeDate().equals(bill.getChargeDate())
//                                                                    && ((ReminderEnums.CHARGE_STATUS_PART.getCode().equals(bill.getChargeStatus()) && (bill.getUnevenMoney() != null && !BigDecimal.ZERO.equals(bill.getUnevenMoney())))
//                                                                    || ReminderEnums.CHARGE_STATUS_ALL.getCode().equals(bill.getChargeStatus()))
//                                                            )
//                                                    );
                                            boolean containsPayableDateString = billVoList.stream()
                                                    .anyMatch(bill -> (getDay(listByPayableDateVo.getPayableDate()).equals(getDay(bill.getPayableDate()))
                                                                        && (ReminderEnums.CHARGE_STATUS_PART.getCode().equals(bill.getChargeStatus()) || ReminderEnums.CHARGE_STATUS_NOT.getCode().equals(bill.getChargeStatus()))
                                                                      )
                                                                    ||
                                                                      (listByPayableDateVo.getChargeDate().equals( safeSubstring(bill.getChargeDate()) )
                                                                        && (ReminderEnums.CHARGE_STATUS_ALL.getCode().equals(bill.getChargeStatus()))
                                                                      )
                                                    );
                                            if (!containsPayableDateString) {
                                                continue;
                                            }
                                       }else if(StringUtils.isNotBlank(payableDate) && StringUtils.isBlank(chargeDate)){
                                           //判断billVoList中是否存在与查询payableDate一样的值，没有匹配不要发短信 ddddd1
                                           boolean containsPayableDateString = billVoList.stream()
                                                   .anyMatch(bill -> getDay(listByPayableDateVo.getPayableDate()).equals(getDay(bill.getPayableDate())));
                                           if(!containsPayableDateString){
                                               continue;
                                           }
                                       }else if(StringUtils.isBlank(payableDate) && StringUtils.isNotBlank(chargeDate)){
                                           boolean containsPayableDateString = billVoList.stream()
                                                   .anyMatch(bill -> listByPayableDateVo.getChargeDate().equals( safeSubstring(bill.getChargeDate()) ));
                                           if(!containsPayableDateString){
                                               continue;
                                           }
                                       }

                                        if(billVoList != null && billVoList.size() > 0){
                                            UnpaidBillVo unpaidBillVo = billVoList.get(0);
                                            if(StringUtils.isNotBlank(unpaidBillVo.getTenantId())){
                                                //根据租户id  过滤人员白名单数据
                                                BbpmCollectionWhitelistVo bbpmCollectionWhitelistVo2 = new BbpmCollectionWhitelistVo();
                                                bbpmCollectionWhitelistVo2.setType(PaymentEnums.COLLECTION_WHITE_PEOPLE.getCode());
                                                bbpmCollectionWhitelistVo2.setPersonCustomerId(unpaidBillVo.getTenantId());
                                                List<BbpmCollectionWhitelistVo> bbpmCollectionWhitelistVoList2 = bbpmCollectionWhitelistMapper.selectCustomerOrProject(bbpmCollectionWhitelistVo2);
                                                //有数据就跳过
                                                if(bbpmCollectionWhitelistVoList2 != null && bbpmCollectionWhitelistVoList2.size()>0){
                                                    continue;
                                                }
                                            }
                                            UnpaidBillMix unpaidBillMix = new UnpaidBillMix();
                                            BeanUtils.copyProperties(unpaidBillMainVo, unpaidBillMix);
                                            BeanUtils.copyProperties(unpaidBillVo, unpaidBillMix);
                                            //催缴原因提取 ddddd2
                                            StringBuffer collectionReason = new StringBuffer("");
//                                            boolean containsChargeStatus02Or03 = billVoList.stream()
//                                                    .anyMatch(bill -> (ReminderEnums.CHARGE_STATUS_PART.getCode().equals(bill.getChargeStatus()) && (bill.getUnevenMoney()==null || BigDecimal.ZERO.equals(bill.getUnevenMoney())))
//                                                            || ReminderEnums.CHARGE_STATUS_NOT.getCode().equals(bill.getChargeStatus()));
                                            boolean containsChargeStatus02Or03 = billVoList.stream()
                                                    .anyMatch(bill -> ReminderEnums.CHARGE_STATUS_PART.getCode().equals(bill.getChargeStatus()) || ReminderEnums.CHARGE_STATUS_NOT.getCode().equals(bill.getChargeStatus()));
                                            if (containsChargeStatus02Or03) {
                                                collectionReason.append(ReminderEnums.BILLING_COLLECTION_REASON_UNPAID.getCode()).append(",");
                                            }
//                                            boolean containsChargeStatus01 = billVoList.stream()
//                                                    .anyMatch(bill -> (ReminderEnums.CHARGE_STATUS_PART.getCode().equals(bill.getChargeStatus()) && (bill.getUnevenMoney()!=null && !BigDecimal.ZERO.equals(bill.getUnevenMoney())))
//                                                            || ReminderEnums.CHARGE_STATUS_ALL.getCode().equals(bill.getChargeStatus()));
                                            boolean containsChargeStatus01 = billVoList.stream()
                                                    .anyMatch(bill -> ReminderEnums.CHARGE_STATUS_ALL.getCode().equals(bill.getChargeStatus()));
                                            if (containsChargeStatus01) {
                                                collectionReason.append((ReminderEnums.BILLING_COLLECTION_REASON_UNEQUAL.getCode())).append(",");
                                            }
                                            unpaidBillMix.setCollectionReason(collectionReason!=null?collectionReason.substring(0,collectionReason.length()-1):"");
                                            unpaidBillMix.setBusinessId(UUID.randomUUID().toString().replace("-", ""));

                                            unpaidBillMixList.add(unpaidBillMix);
                                        }
                                    }

                                    if(unpaidBillMixList != null && unpaidBillMixList.size() > 0){
                                        if(isSend){
                                            flag = true;
                                            //调用消息中心发信息
                                            sendMessage(unpaidBillMixList, bbpmReminderRulesMainAndSubVo, bbpmMessageSendMainLogVo,listByPayableDateVo,textRequestAll,textResponseAll);
//                                            if(REMINDER_SMS_TEMPLATE_ID.equals(textMessageTemplateId) || REMINDER_TEMPLATE_ID.equals(stationMessageTemplateId)){
//                                                //催缴
//                                            }else if(PAYMENT_REMINDER_SMS_TEMPLATE_ID.equals(textMessageTemplateId) || PAYMENT_REMINDER_TEMPLATE_ID.equals(stationMessageTemplateId)){
//                                                //缴费提醒
//                                            }
                                        }else{
                                            throw new McpException("nacos配置未开启发送短信开关(isSend)");
                                        }
                                    }else {
                                       log.info("----------项目"+projectId+"["+ower+"]"+"经过白名单筛选后,无需要发送短信的用户");
                                    }

                                } else {
                                    log.info("----------项目"+projectId+"["+ower+"]"+"未查询到账单,不用发送信息----------,具体请求参数"+JSONObject.toJSONString(listByPayableDateVo));
                                }

                            }
                        }
//                        break;
                    }

                    if(!flag){
                        bbpmMessageSendMainLogVo.setErrorMsq("没有符合条件数据,未发送短信");
                        reuslt = "没有符合条件数据,未发送短信";
                    }
                    //任务完成后更新日志主表
                    bbpmMessageSendMainLogVo.setStatus(ReminderEnums.LOG_STATUS_EN.getCode());
                    bbpmMessageSendMainLogVo.setRequest("["+((textRequestAll!=null&&textRequestAll.length()>0)?textRequestAll.substring(0,textRequestAll.length()-1):"")+"]");
                    bbpmMessageSendMainLogVo.setResponse("["+((textResponseAll!=null&&textResponseAll.length()>0)?textResponseAll.substring(0,textResponseAll.length()-1):"")+"]");
                    iBbpmMessageSendMainLogService.updateByIdRecord(bbpmMessageSendMainLogVo);

                } else {
                    //无项目信息
                    throw new McpException("从签约中心没有查到项目信息");
                }
            } catch (Exception e) {
                e.printStackTrace();
                //异常后更新主日志
                bbpmMessageSendMainLogVo.setStatus(ReminderEnums.LOG_STATUS_EX.getCode());
                bbpmMessageSendMainLogVo.setErrorMsq(e.getMessage());
                bbpmMessageSendMainLogVo.setErrorMsqAll(formatStackTrace(e));
                iBbpmMessageSendMainLogService.updateByIdRecord(bbpmMessageSendMainLogVo);

//                return e.getMessage();
                reuslt = e.getMessage();
            } finally {
                if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                    disLock.unlock();
                }
            }
        } else {
            log.info("催缴提醒任务已在运行中...");
//            return "催缴提醒任务已在运行中...";
            reuslt = "催缴提醒任务已在运行中...";
        }
        log.info("----定时任务结束执行----");
//        return reuslt;

        dto.setExecuteStatus(UniManConstants.EXE_STATUS_SUCC);
        dto.setExecuteDesc(reuslt);
        return dto;
    }



    /**
     * 拼接参数，发送短信  -
     *
     * @param unpaidBillMixList
     * @param bbpmMessageSendMainLogVo
     */
//    public void sendMessage(List<UnpaidBillVo> unpaidBillVoList, BbpmReminderRulesMainAndSubVo bbpmReminderRulesMainAndSubVo, BbpmMessageSendMainLogVo bbpmMessageSendMainLogVo,String ower) {
    public void sendMessage(List<UnpaidBillMix> unpaidBillMixList, BbpmReminderRulesMainAndSubVo bbpmReminderRulesMainAndSubVo, BbpmMessageSendMainLogVo bbpmMessageSendMainLogVo,ListByPayableDateVo listByPayableDateVo,StringBuffer textRequestAll,StringBuffer textResponseAll) {
         // 短信消息模板id
        String textMessageTemplateId = bbpmReminderRulesMainAndSubVo.getTextMessageTemplateId();
        // 站内消息模板id
        String stationMessageTemplateId = bbpmReminderRulesMainAndSubVo.getStationMessageTemplateId();

        AppReply<MessageInterfaceResponseVo> textMessageAppReply = null;
        AppReply<MessageInterfaceResponseVo> stationMessageAppReply = null;
        //短信
        MessageInterfaceParamVo textMessageVo = new MessageInterfaceParamVo();
        //站内信
        MessageInterfaceParamVo stationMessageVo = new MessageInterfaceParamVo();
        List<MessageInterfaceParamContentVo> messageInterfaceParamContentVoList = new ArrayList<>();

        textMessageVo.setTitle("缴费短信信通知");
        textMessageVo.setTemplateId(textMessageTemplateId);
        stationMessageVo.setTitle("缴费站内信通知");
        stationMessageVo.setTemplateId(stationMessageTemplateId);

        Date sendTime = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String time = sdf.format(sendTime);
        HashSet<String> set = new HashSet<String>();

        //发送日志子表 就是 催缴记录表
        List<BbpmMessageSendSubLogV2Vo> subLogV2VoList = new ArrayList<>();
        Map<String, BbpmMessageSendSubLogV2Vo> subLogV2VoMap = new ConcurrentHashMap<>();
        //循环账单获取信息
        for (UnpaidBillMix unpaidBillMix : unpaidBillMixList) {
//            //如果是企业，按照合同编号去重
//            if(PaymentEnums.CHARGEOWNER_ENTERPRISE.getCode().equals(ower)){
//                if (set.contains(unpaidBillMix.getContractCode())) {
//                    continue;
//                } else {
//                    set.add(unpaidBillMix.getContractCode());
//                }
//            }
            //第X期（帐单开始时间 至 帐单结束时间）
            StringBuffer periodsBillDate = new StringBuffer()
                    .append("第").append(unpaidBillMix.getChargePeriod())
                    .append("期(").append(unpaidBillMix.getChargeStartDate())
                    .append("至").append(unpaidBillMix.getChargeEndDate())
                    .append(")");

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("customerName", unpaidBillMix.getTenantName());
            paramMap.put("contractNumber", unpaidBillMix.getContractCode());
            paramMap.put("periodsBillDate", periodsBillDate.toString());
            paramMap.put("dueDate", unpaidBillMix.getPayableDate());
            paramMap.put("monthNumber",String.valueOf(unpaidBillMix.getUnPaidMonthTotal()));
            BigDecimal totalAmount = unpaidBillMix.getUnPaidMoney()!=null?unpaidBillMix.getUnPaidMoney().setScale(2,BigDecimal.ROUND_HALF_UP):unpaidBillMix.getUnPaidMoney();
            paramMap.put("totalAmount",String.valueOf(totalAmount));

            MessageInterfaceParamContentVo messageInterfaceParamContentVo = new MessageInterfaceParamContentVo();
            if(useMyMobile){
                //使用自己手机号测试，避免信息发给客户
                messageInterfaceParamContentVo.setPhone(tenantMobile);
            }else {
                if(StringUtils.isBlank(unpaidBillMix.getTenantMobile())){
                    log.info("账单"+unpaidBillMix.getBillCode()+"手机号为空");
                    continue;
                }
                messageInterfaceParamContentVo.setPhone(unpaidBillMix.getTenantMobile());
            }
            messageInterfaceParamContentVo.setParamMap(paramMap);
            messageInterfaceParamContentVo.setBusinessId(unpaidBillMix.getBusinessId());
            messageInterfaceParamContentVoList.add(messageInterfaceParamContentVo);

            //插入日志子表 催缴记录
            BbpmMessageSendSubLogV2Vo subLogV2Vo = new BbpmMessageSendSubLogV2Vo();
            subLogV2Vo.setMainId(bbpmMessageSendMainLogVo.getMainId());
            subLogV2Vo.setRulesId(bbpmReminderRulesMainAndSubVo.getRulesId());
            subLogV2Vo.setRulesSubId(bbpmReminderRulesMainAndSubVo.getRulesSubId());
            subLogV2Vo.setBusinessId(unpaidBillMix.getBusinessId());
            subLogV2Vo.setTemplateTypeCodeText(THREAD_SAFE_MAP.get(textMessageTemplateId));
            subLogV2Vo.setMessageTemplateIdText(textMessageTemplateId);
            subLogV2Vo.setTemplateTypeCodeStation(THREAD_SAFE_MAP.get(stationMessageTemplateId));
            subLogV2Vo.setMessageTemplateIdStation(stationMessageTemplateId);
            subLogV2Vo.setTenantCode(unpaidBillMix.getTenantId());
            subLogV2Vo.setTenantName(unpaidBillMix.getTenantName());
            subLogV2Vo.setTenantMobile(unpaidBillMix.getTenantMobile());
            subLogV2Vo.setCertNo(unpaidBillMix.getCertNo());
            subLogV2Vo.setProjectId(unpaidBillMix.getProjectId());
            subLogV2Vo.setProjectName(unpaidBillMix.getProjectName());
            subLogV2Vo.setHouseName(unpaidBillMix.getHouseName());
            subLogV2Vo.setCollectionMethod(ReminderEnums.BILL_COLLECTION_METHOD_AUTO.getCode());
            subLogV2Vo.setCollectionReason(unpaidBillMix.getCollectionReason());
            subLogV2Vo.setNoticeResultText(ReminderEnums.BILLING_COLLECTION_RESULT_FAIL.getCode());
            subLogV2Vo.setNoticeResultStation(ReminderEnums.BILLING_COLLECTION_RESULT_FAIL.getCode());
            Date date = new Date();
            subLogV2Vo.setSendTimeText(date);
            subLogV2Vo.setSendTimeStation(date);
            subLogV2Vo.setContractCode(unpaidBillMix.getContractCode());
            subLogV2Vo.setBillId(unpaidBillMix.getBillCode());
            subLogV2Vo.setBillStatus(unpaidBillMix.getChargeStatus());
            subLogV2Vo.setBillCycle(unpaidBillMix.getBillCycle());
            subLogV2Vo.setPayableDate(unpaidBillMix.getPayableDate());
            subLogV2Vo.setTotalMonth(String.valueOf(unpaidBillMix.getUnPaidMonthTotal()));
            subLogV2Vo.setTotalAmount(String.valueOf(totalAmount));
            subLogV2Vo.setProjectFormat(listByPayableDateVo.getBusinessType() );
            subLogV2Vo.setContractType(listByPayableDateVo.getContractType());
            subLogV2Vo.setChargeOwner(unpaidBillMix.getOwner());

            subLogV2VoList.add(subLogV2Vo);
            subLogV2VoMap.put(unpaidBillMix.getBusinessId(), subLogV2Vo);
        }

        textMessageVo.setMessageContent(messageInterfaceParamContentVoList);
        stationMessageVo.setMessageContent(messageInterfaceParamContentVoList);

        if(messageInterfaceParamContentVoList !=null && messageInterfaceParamContentVoList.size() > 0){
            //向请求头中添加token
            feignConfiguration.setToken(token);

            String textRequest = JSONObject.toJSONString(textMessageVo);
            textRequestAll.append(textRequest).append(",");
            log.info("调用发送-短信 ===========================" + textRequest);
            textMessageAppReply = bbmessageFeignClient.realTimeInternalMessagePush(textMessageVo);
            String textResponse = JSONObject.toJSONString(textMessageAppReply);
            textResponseAll.append(textResponse).append(",");
            log.info("短信发送返回:"+ textResponse);

            String stationRequest = JSONObject.toJSONString(stationMessageVo);
            log.info("调用发送-站内信 ===========================" + stationRequest);
            stationMessageAppReply = bbmessageFeignClient.realTimemailMessagePush(stationMessageVo);
            String stationResponse = JSONObject.toJSONString(stationMessageAppReply);
            log.info("站内信发送返回:"+ stationResponse);

            if(textMessageAppReply!=null && AppReply.SUCCESS_CODE.equals(textMessageAppReply.getCode()) && textMessageAppReply.getData()!=null){
                List<MessageInterfaceResponseDetailVo> textDetailVoList = textMessageAppReply.getData().getDetailVos();
                if(textDetailVoList!=null && textDetailVoList.size() >0){
                    for (MessageInterfaceResponseDetailVo detailVo : textDetailVoList){
                        if ("00".equals(detailVo.getCode())) {
                            String businessId = detailVo.getBusinessId();
                            BbpmMessageSendSubLogV2Vo subLogV2Vo = subLogV2VoMap.get(businessId);
                            if(subLogV2Vo!=null){
                                subLogV2Vo.setNoticeResultText(ReminderEnums.BILLING_COLLECTION_RESULT_SUCCESS.getCode());
                            }
                        }
                    }
                }
            }

            if(stationMessageAppReply!=null && AppReply.SUCCESS_CODE.equals(stationMessageAppReply.getCode()) && stationMessageAppReply.getData()!=null){
                List<MessageInterfaceResponseDetailVo> stationDetailVoList = stationMessageAppReply.getData().getDetailVos();
                if(stationDetailVoList!=null && stationDetailVoList.size() >0){
                    for (MessageInterfaceResponseDetailVo detailVo : stationDetailVoList){
                        if ("00".equals(detailVo.getCode())) {
                            String businessId = detailVo.getBusinessId();
                            BbpmMessageSendSubLogV2Vo subLogV2Vo = subLogV2VoMap.get(businessId);
                            if(subLogV2Vo!=null){
                                subLogV2Vo.setNoticeResultStation(ReminderEnums.BILLING_COLLECTION_RESULT_SUCCESS.getCode());
                            }
                        }
                    }
                }
            }

            iBbpmMessageSendSubLogV2Service.insertBatchRecord(subLogV2VoList);

            if( textMessageAppReply == null || !AppReply.SUCCESS_CODE.equals(textMessageAppReply.getCode())
                    || stationMessageAppReply == null || !AppReply.SUCCESS_CODE.equals(stationMessageAppReply.getCode())){
                throw new McpException("调用消息中心出错了.短信返回"+textResponse+".站内信息返回"+stationResponse);
            }
        }else{
            throw new McpException("账单里手机号都是空");
        }
    }


//    /**
//     * 获取异常详细信息，知道出了什么错，错在哪个类的第几行 .
//     *
//     * @param ex
//     * @return
//     */
//    public static String getExceptionDetail(Exception ex) {
//        StringWriter stringWriter = new StringWriter();
//        PrintWriter printWriter = new PrintWriter(stringWriter);
//        ex.printStackTrace(printWriter);
//        return stringWriter.toString();
//    }

    private String formatStackTrace(Exception e) {
        return ExceptionUtils.getStackTrace(e);
//        StringBuilder stackTrace = new StringBuilder();
//        for (StackTraceElement element : e.getStackTrace()) {
//            stackTrace.append("\tat ").append(element).append("\n");
//        }
//        return stackTrace.toString();
    }

    public String getTokenByFixedUser(){
//        String userName = "ycy";
//        String password = "a123456";

//        //获取加密密码
//        String userLoginPwd = Des1.strEnc(password, firstKey, secondKey, thirdKey);
//        //组装参数
//        BmsUserRpcServiceUserLoginReq bmsUserRpcServiceUserLoginReq = new BmsUserRpcServiceUserLoginReq();
//        bmsUserRpcServiceUserLoginReq.setUserLoginName(userName);
//        bmsUserRpcServiceUserLoginReq.setUserLoginPwd(userLoginPwd);
//        bmsUserRpcServiceUserLoginReq.setFailureTime("21600");
//        Request<BmsUserRpcServiceUserLoginReq> reqRequest = new Request<>();
//        reqRequest.setData(bmsUserRpcServiceUserLoginReq);
//        reqRequest.setTime(System.currentTimeMillis()+"");
//        //请求登录接口，获取token
//        BmsUserRpcServiceUserLoginResp bmsUserRpcServiceUserLoginResp = null;
//        String reqJson = JSONObject.toJSONString(reqRequest);
//        log.info("请求登录接口获取token请求参数：{}",reqJson);
//        try {
//            Response<BmsUserRpcServiceUserLoginResp> respResponse = bmsFeignClient.userLogin(reqRequest);
//            log.info("请求登录接口获取token返回参数：{}",JSONObject.toJSONString(respResponse));
//            bmsUserRpcServiceUserLoginResp = respResponse.getData();
//        }catch (Exception e) {
//            e.printStackTrace();
//            throw new McpException("调用登录接口获取token出错了");
//        }
//        String token = bmsUserRpcServiceUserLoginResp.getToken();
        String token = bmsBaseServiceFeignService.getToken();
        return token;
//        return "bms_token_1f9f9cd9-cdee-425f-838e-b156c054377f";
    }

    public static String getDateWithDayOffset(int offsetDays) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, offsetDays);
        Date offsetDate = calendar.getTime();
        return DATE_FORMATTER.format(offsetDate);
    }

    public static String safeSubstring(String str) {
        int startIndex = 0;
        int length = 10;
        if (StringUtils.isBlank(str) || str.length() < startIndex + length) {
            return str; // 或者 throw new IllegalArgumentException("字符串长度不足");
        }
        // 截取字符串
        return str.substring(startIndex, startIndex + length);
    }

    /**
     * 获取日期  日
     * @param date
     * @return
     */
    public static String getDay(String date){
        String temp [] = date.split("-");
        if(temp.length == 3){
            return temp[2];
        }else{
            return null;
        }
    }

    public static void main(String[] args) {
        // 测试方法
//        System.out.println("加3天后的日期: " + getDateWithDayOffset(3));
//        System.out.println("减3天后的日期: " + getDateWithDayOffset(-3));
//
//        System.out.println(safeSubstring("2024-09-25 15:31:34"));
        System.out.println(getDay("2025-07-08"));
    }
}
