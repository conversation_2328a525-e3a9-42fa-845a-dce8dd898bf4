package com.bonc.ioc.bzf.business.payment.result;

import lombok.Data;

/**
 * 业财账单列表请求实体
 */
@Data
public class BillListRequest {
    private String contractCode;	//合同ID
    private String projectName;//项目名称
    private Long billCode;//账单ID
    private Integer accountingMonth;//账单会计月
    private String invoicingStatus;//开票状态
    private String projectEstate;//所在小区或楼宇名称
    private String houseLocation;//房源地址
    private String chargeSubjectNo;//计费科目编号
    private String chargeTime;//收费时间
    private String startChargeTime;//	收费科目起始日期
    private String endChargeTime;//	收费科目终止日期
    private String tenantName;//租户名称
    private String tenantCode;//租户ID
    private String deductionDirection;//抵扣方向
    private String payChannel;//支付方式
    private String billStatus;//账单状态列表
    private Integer chargeSubjectPeriod;//收费科目当期期次
    private String calCurrentMonth;//是否计算退租当月应退租金
    private String exitDate;//租户实际退场日期

    private Integer pageSize;
    private Integer pageNo;
    private String fullPage;//": "True"

}
