package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 基础计算规则 枚举类
 *
 * <AUTHOR>
 * @since 2024/3/12
 */
public enum BaseRentCalculateEnum {

    /**
     * 单价乘面积规则
     */
    UNIT_PRICE_RIDE_AREA("1010", "单价乘面积规则"),

    /**
     * 一口价计费规则
     */
    FIXED_PRICE("1011", "一房一价计费规则"),

    /**
     * 年租金规则
     */
    RIDE_MONTH_PRICE("1039", "年租金规则");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    BaseRentCalculateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    BaseRentCalculateEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        BaseRentCalculateEnum[] enums = values();
        for (BaseRentCalculateEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述信息查询编号
     *
     * @param desc 描述信息
     * @return 编号
     */
    public static String getCodeByDesc(String desc) {
        BaseRentCalculateEnum[] enums = values();
        for (BaseRentCalculateEnum en : enums) {
            if (en.getDesc().equals(desc)) {
                return en.getCode();
            }
        }
        return null;
    }
}
