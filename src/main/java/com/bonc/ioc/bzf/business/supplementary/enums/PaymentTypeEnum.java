package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 账单类型 枚举类
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
public enum PaymentTypeEnum {

    /**
     * 周期性账单
     */
    PERIODICITY_PAYMENT("1", "周期性账单"),

    /**
     * 一次性账单
     */
    DISPOSABLE_PAYMENT("2", "一次性账单");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 合同字典id
     */
    private String contractDictId;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    PaymentTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    PaymentTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        PaymentTypeEnum[] enums = values();
        for (PaymentTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述信息查询编号
     *
     * @param desc 描述信息
     * @return 编号
     */
    public static String getCodeByDesc(String desc) {
        PaymentTypeEnum[] enums = values();
        for (PaymentTypeEnum en : enums) {
            if (en.getDesc().equals(desc)) {
                return en.getCode();
            }
        }
        return null;
    }
}
