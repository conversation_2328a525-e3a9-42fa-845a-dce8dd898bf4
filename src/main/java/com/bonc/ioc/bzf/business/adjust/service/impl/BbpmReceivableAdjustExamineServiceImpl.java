package com.bonc.ioc.bzf.business.adjust.service.impl;

import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustExamineEntity;
import com.bonc.ioc.bzf.business.adjust.dao.BbpmReceivableAdjustExamineMapper;
import com.bonc.ioc.bzf.business.adjust.service.IBbpmReceivableAdjustExamineService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 应收调整审核表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@Slf4j
@Service
public class BbpmReceivableAdjustExamineServiceImpl extends McpBaseServiceImpl<BbpmReceivableAdjustExamineEntity> implements IBbpmReceivableAdjustExamineService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmReceivableAdjustExamineMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmReceivableAdjustExamineService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmReceivableAdjustExamineVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmReceivableAdjustExamineEntity entity = new BbpmReceivableAdjustExamineEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setId(null);
        if(!baseService.insert(entity)) {
            log.error("应收调整审核表新增失败:" + entity.toString());
            throw new McpException("应收调整审核表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getId(),1)) {
                log.error("应收调整审核表新增后保存历史失败:" + entity.toString());
                throw new McpException("应收调整审核表新增后保存历史失败");
            }

            log.debug("应收调整审核表新增成功:"+entity.getId());
            return entity.getId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmReceivableAdjustExamineVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmReceivableAdjustExamineEntity> entityList = new ArrayList<>();
        for (BbpmReceivableAdjustExamineVo item:voList) {
            BbpmReceivableAdjustExamineEntity entity = new BbpmReceivableAdjustExamineEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmReceivableAdjustExamineEntity item:entityList){
            item.setId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("应收调整审核表新增失败");
            throw new McpException("应收调整审核表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmReceivableAdjustExamineEntity::getId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("应收调整审核表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("应收调整审核表批量新增后保存历史失败");
            }

            log.debug("应收调整审核表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String id) {
        if(!StringUtils.isEmpty(id)) {
            if(!baseService.saveOperationHisById(id,3)) {
                log.error("应收调整审核表删除后保存历史失败:" + id);
                throw new McpException("应收调整审核表删除后保存历史失败");
            }

            if(!baseService.removeById(id)) {
                log.error("应收调整审核表删除失败");
                throw new McpException("应收调整审核表删除失败"+id);
            }
        } else {
            throw new McpException("应收调整审核表删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param idList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> idList) {
        if(!CollectionUtils.isEmpty(idList)) {
            int oldSize = idList.size();
            idList = idList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(idList) || oldSize != idList.size()) {
                throw new McpException("应收调整审核表批量删除失败 存在主键id为空的记录"+StringUtils.join(idList));
            }

            if(!baseService.saveOperationHisByIds(idList,3)) {
                log.error("应收调整审核表批量删除后保存历史失败:" + StringUtils.join(idList));
                throw new McpException("应收调整审核表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(idList)) {
                log.error("应收调整审核表批量删除失败");
                throw new McpException("应收调整审核表批量删除失败"+StringUtils.join(idList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的应收调整审核表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmReceivableAdjustExamineVo vo) {
        if(vo != null) {
            BbpmReceivableAdjustExamineEntity entity = new BbpmReceivableAdjustExamineEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getId())) {
                throw new McpException("应收调整审核表更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("应收调整审核表更新失败");
                throw new McpException("应收调整审核表更新失败"+entity.getId());
            } else {
                if(!baseService.saveOperationHisById(entity.getId(),2)) {
                    log.error("应收调整审核表更新后保存历史失败:" + entity.getId());
                    throw new McpException("应收调整审核表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("应收调整审核表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的应收调整审核表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmReceivableAdjustExamineVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReceivableAdjustExamineEntity> entityList = new ArrayList<>();

            for (BbpmReceivableAdjustExamineVo item:voList){
                BbpmReceivableAdjustExamineEntity entity = new BbpmReceivableAdjustExamineEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("应收调整审核表批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("应收调整审核表批量更新失败");
                throw new McpException("应收调整审核表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getId())).map(BbpmReceivableAdjustExamineEntity::getId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("应收调整审核表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("应收调整审核表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应收调整审核表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmReceivableAdjustExamineVo vo) {
        if(vo != null) {
            BbpmReceivableAdjustExamineEntity entity = new BbpmReceivableAdjustExamineEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("应收调整审核表保存失败");
                throw new McpException("应收调整审核表保存失败"+entity.getId());
            } else {
                if(!baseService.saveOperationHisById(entity.getId(),4)) {
                    log.error("应收调整审核表保存后保存历史失败:" + entity.getId());
                    throw new McpException("应收调整审核表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("应收调整审核表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的应收调整审核表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmReceivableAdjustExamineVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReceivableAdjustExamineEntity> entityList = new ArrayList<>();

            for (BbpmReceivableAdjustExamineVo item:voList){
                BbpmReceivableAdjustExamineEntity entity = new BbpmReceivableAdjustExamineEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("应收调整审核表批量保存失败");
                throw new McpException("应收调整审核表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getId())).map(BbpmReceivableAdjustExamineEntity::getId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("应收调整审核表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("应收调整审核表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmReceivableAdjustExamineVo selectByIdRecord(String id) {
        BbpmReceivableAdjustExamineVo vo = new BbpmReceivableAdjustExamineVo();

        if(!StringUtils.isEmpty(id)) {
            BbpmReceivableAdjustExamineEntity entity = baseService.selectById(id);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmReceivableAdjustExaminePageResultVo>> selectByPageRecord(BbpmReceivableAdjustExaminePageVo vo) {
        List<BbpmReceivableAdjustExaminePageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
