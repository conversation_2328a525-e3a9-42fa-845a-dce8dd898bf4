package com.bonc.ioc.bzf.business.payment.vo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> 3.9.接收白名单推送接口
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class WhiteListRequestVo {

	private String tenantId;//	租户ID	String	是	64
	private String publicRecordNo;//	备案家庭号	String	是	20
	private String tenantName;//	姓名	String	是	20
	private String tenantNo;//	身份证证件号	String	是	30
	private BigDecimal withholdRatio;//	代扣比例	BigDecimal	是	13,2	样例：0.7（含义为70%）
	private String operateType;//	操作类型	String	是	10	01 新增 02 删除 03更新
	private String projectId;//	项目ID	String	是	64	业务中台ID


}
