package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 推送的签约方信息 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/25
 */
@Data
@ApiModel(value = "推送的签约方信息", description = "推送的签约方信息")
public class BbctPushSignatoryInfoVo extends McpBaseVo implements Serializable {

    /**
     * 签约方类型
     */
    @ApiModelProperty(value = "签约方类型")
    private String signatoryType;

    /**
     * 租户姓名
     */
    @ApiModelProperty(value = "租户姓名")
    private String tenantName;

    /**
     * 租户手机号
     */
    @ApiModelProperty(value = "租户手机号")
    private String tenantMobile;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 租户证件类型
     */
    @ApiModelProperty(value = "租户证件类型")
    private String tenantIDType;

    /**
     * 租户证件号码
     */
    @ApiModelProperty(value = "租户证件号码")
    private String idNumber;

    /**
     * 租户邮箱地址
     */
    @ApiModelProperty(value = "租户邮箱地址")
    private String mailUrl;

    /**
     * 公租房备案号
     */
    @ApiModelProperty(value = "公租房备案号")
    private String publicRecordNo;

    /**
     * 租户客户编号
     */
    @ApiModelProperty(value = "租户客户编号")
    private String tenantCustomerNo;

    /**
     * 租户客商编号
     */
    @ApiModelProperty(value = "租户客商编号")
    private String tenantSupplierNo;

    /**
     * 租户客商名称
     */
    @ApiModelProperty(value = "租户客商名称")
    private String tenantSupplierName;

    /**
     * 趸租企业证照类型
     */
    @ApiModelProperty(value = "趸租企业证照类型")
    private String companyIDType;

    /**
     * 趸租企业id
     */
    @ApiModelProperty(value = "趸租企业id")
    private String companyId;

    /**
     * 趸租企业社会信用代码
     */
    @ApiModelProperty(value = "趸租企业社会信用代码")
    private String socialCreditCode;

    /**
     * 趸租企业名称
     */
    @ApiModelProperty(value = "趸租企业名称")
    private String companyName;

    /**
     * 趸租企业客户编号
     */
    @ApiModelProperty(value = "趸租企业客户编号")
    private String companyCustomerNo;

    /**
     * 趸租企业客商编号
     */
    @ApiModelProperty(value = "趸租企业客商编号")
    private String companySupplierNo;

    /**
     * 趸租企业客商名称
     */
    @ApiModelProperty(value = "趸租企业客商名称")
    private String companySupplierName;

    /**
     * 租户开户总行名称
     */
    @ApiModelProperty(value = "租户开户总行名称")
    private String tenantBankName;

    /**
     * 租户开户总行编码
     */
    @ApiModelProperty(value = "租户开户总行编码")
    private String tenantBankCode;

    /**
     * 租户银行卡户名
     */
    @ApiModelProperty(value = "租户银行卡户名")
    private String tenantBankAccountName;

    /**
     * 租户银行卡卡号
     */
    @ApiModelProperty(value = "租户银行卡卡号")
    private String tenantBankAccountNo;

    /**
     * 是否进行银行卡代扣
     */
    @ApiModelProperty(value = "是否进行银行卡代扣")
    private String withholding;

    /**
     * 代扣鉴权协议号
     */
    @ApiModelProperty(value = "代扣鉴权协议号")
    private String agreementNo;

    /**
     * 企业支付比例
     */
    @ApiModelProperty(value = "企业支付比例")
    private BigDecimal companyRate;

    /**
     * 个人支付比例
     */
    @ApiModelProperty(value = "个人支付比例")
    private BigDecimal personalRate;

    /**
     * 企业支付金额
     */
    @ApiModelProperty(value = "企业支付金额")
    private BigDecimal companyAmount;

    /**
     * 个人支付金额
     */
    @ApiModelProperty(value = "个人支付金额")
    private BigDecimal personalAmount;

    @ApiModelProperty(value = "委托代理人")
    private String authorizedAgent ;
    @ApiModelProperty(value = "委托代理人电话")
    private String  authorizedAgentMobile ;


    /**
     * 企业押金支付比例
     */
    @ApiModelProperty(value = "企业押金支付比例")
    private BigDecimal companyCashPledgePercent;

    /**
     * 企业押金支付金额
     */
    @ApiModelProperty(value = "企业押金支付金额")
    private BigDecimal companyCashPledgeAmount;


    @ApiModelProperty(value = "企业开户总行名称")
    private String companyBankName;
    @ApiModelProperty(value = "企业开户总行编码")
    private String companyBankCode;
    @ApiModelProperty(value = "企业开户支行名称")
    private String companyBankBranchName;
    @ApiModelProperty(value = "企业开户支行编码")
    private String companyBankBranchCode;
    @ApiModelProperty(value = "企业银行卡户名")
    private String companyBankAccountName;
    @ApiModelProperty(value = "企业银行卡卡号")
    private String companyBankAccountNo;
    @ApiModelProperty(value = "企业纳税识别号")
    private String companyTaxNo;

}
