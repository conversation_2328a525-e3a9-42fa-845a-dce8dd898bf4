package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.BbpDictEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 字典表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2022-09-22
 * @change 2022-09-22 by wtl for init
 */
@Mapper
public interface BbpDictMapper extends McpBaseMapper<BbpDictEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change 2022-09-22 by wtl for init
     */
    List<BbpDictPageResultVo> selectByPageCustom(@Param("vo") BbpDictPageVo vo );
}
