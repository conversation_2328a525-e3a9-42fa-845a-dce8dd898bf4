package com.bonc.ioc.bzf.business.payment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 签约规则信息表 实体类
 *
 * <AUTHOR>
 * @date 2022-11-29
 * @change 2022-11-29 by ly for init
 */
@TableName("bbsi_rule_info")
@ApiModel(value="BbsiRuleInfoEntity对象", description="签约规则信息表")
@Data
public class BbsiRuleInfoEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_SIGN_RULE_ID = "sign_rule_id";
    public static final String FIELD_RULE_NAME = "rule_name";
    public static final String FIELD_PROJECT_ID = "project_id";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_CONTRACT_TEMPLATE_ID = "contract_template_id";
    public static final String FIELD_SIGN_NOTICE_ID = "sign_notice_id";
    public static final String FIELD_RENT_STANDARD_ID = "rent_standard_id";
    public static final String FIELD_PAYMENT_CYCLE_CODE = "payment_cycle_code";
    public static final String FIELD_CASH_PLEDGE_CODE = "cash_pledge_code";
    public static final String FIELD_SIGN_IN_BEGIN_TIME = "sign_in_begin_time";
    public static final String FIELD_SIGN_IN_END_TIME = "sign_in_end_time";
    public static final String FIELD_RENT_BEGIN_TIME = "rent_begin_time";
    public static final String FIELD_RENT_END_TIME = "rent_end_time";
    public static final String FIELD_DUE_BANK = "due_bank";
    public static final String FIELD_NOTICE_TEMPLATE_ID = "notice_template_id";
    public static final String FIELD_MESSAGE_TEMPLATE_ID = "message_template_id";
    public static final String FIELD_IS_HEATING_COMPANY = "is_heating_company";
    public static final String FIELD_IS_MOVE_INTO = "is_move_into";
    public static final String FIELD_RENT_RULES_ID = "rent_rules_id";
    public static final String FIELD_PUBLISH_STATUS = "publish_status";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
                                @TableId(value = "sign_rule_id", type = IdType.ASSIGN_UUID)
                                  private String signRuleId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
                            private String ruleName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
                            private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 合同模板id
     */
    @ApiModelProperty(value = "合同模板id")
                            private String contractTemplateId;

    /**
     * 签约告知书id
     */
    @ApiModelProperty(value = "签约告知书id")
                            private String signNoticeId;

    /**
     * 租金标准id
     */
    @ApiModelProperty(value = "租金标准id")
                            private String rentStandardId;

    /**
     * 缴费周期code
     */
    @ApiModelProperty(value = "缴费周期code")
                            private String paymentCycleCode;

    /**
     * 押金标准code
     */
    @ApiModelProperty(value = "押金标准code")
                            private String cashPledgeCode;

    /**
     * sign_in_begin_time签约开始时间
     */
    @ApiModelProperty(value = "sign_in_begin_time签约开始时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date signInBeginTime;

    /**
     * 签约结束时间
     */
    @ApiModelProperty(value = "签约结束时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date signInEndTime;

    /**
     * 租赁开始日期
     */
    @ApiModelProperty(value = "租赁开始日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date rentBeginTime;

    /**
     * 租赁结束日期
     */
    @ApiModelProperty(value = "租赁结束日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date rentEndTime;

    /**
     * 收款银行
     */
    @ApiModelProperty(value = "收款银行")
                            private String dueBank;

    /**
     * 通知消息模板id
     */
    @ApiModelProperty(value = "通知消息模板id")
                            private String noticeTemplateId;

    /**
     * 短信消息模板id
     */
    @ApiModelProperty(value = "短信消息模板id")
                            private String messageTemplateId;

    /**
     * 是否报送热力公司
     */
    @ApiModelProperty(value = "是否报送热力公司")
                            private String isHeatingCompany;

    /**
     * 是否同时办理入
     */
    @ApiModelProperty(value = "是否同时办理入")
                            private String isMoveInto;

    /**
     * 租金规则id
     */
    @ApiModelProperty(value = "租金规则id")
                            private String rentRulesId;




    /**
     * 组织机构id
     */
    @ApiModelProperty(value = "组织机构id")
    private String orgId;

    /**
     * 租金计算参数
     */
    @ApiModelProperty(value = "租金计算参数")
    private String rentRulesParams;


    @ApiModelProperty(value = "租金计算规则名称")
    private String chargeRuleName;

    @ApiModelProperty(value = "租金计算规则编号")
    private String chargeRuleNo;

    @ApiModelProperty(value = "配租计划id")
    private String peizuPlanId;


    @ApiModelProperty(value = "配租计划名称")
    private String peizuPlanName;


    @ApiModelProperty(value = "发布人")
    private String publishUser;

    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 收款银行编码
     */
    @ApiModelProperty(value = "收款银行编码")
    private String dueBankCode;


    /**
     * 收款银行支行
     */
    @ApiModelProperty(value = "收款银行支行编码")
    private String bankBranchCode;

    /**
     * 收款银行支行
     */
    @ApiModelProperty(value = "收款银行支行名称")
    private String bankBranchName;


    @ApiModelProperty(value = "签约计划编号")
    private String signPlanCode;

    /**
     * 是否查验 1-查验
     */
    @ApiModelProperty(value = "是否查验 1-查验")
    private String checkStatus;

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getBankBranchCode() {
        return bankBranchCode;
    }

    public void setBankBranchCode(String bankBranchCode) {
        this.bankBranchCode = bankBranchCode;
    }

    public String getBankBranchName() {
        return bankBranchName;
    }

    public void setBankBranchName(String bankBranchName) {
        this.bankBranchName = bankBranchName;
    }

    public String getDueBankCode() {
        return dueBankCode;
    }

    public void setDueBankCode(String dueBankCode) {
        this.dueBankCode = dueBankCode;
    }


    public String getPeizuPlanId() {
        return peizuPlanId;
    }

    public void setPeizuPlanId(String peizuPlanId) {
        this.peizuPlanId = peizuPlanId;
    }

    public String getPeizuPlanName() {
        return peizuPlanName;
    }

    public void setPeizuPlanName(String peizuPlanName) {
        this.peizuPlanName = peizuPlanName;
    }

    public String getPublishUser() {
        return publishUser;
    }

    public void setPublishUser(String publishUser) {
        this.publishUser = publishUser;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getChargeRuleName() {
        return chargeRuleName;
    }

    public void setChargeRuleName(String chargeRuleName) {
        this.chargeRuleName = chargeRuleName;
    }

    public String getChargeRuleNo() {
        return chargeRuleNo;
    }

    public void setChargeRuleNo(String chargeRuleNo) {
        this.chargeRuleNo = chargeRuleNo;
    }

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
                            private String publishStatus;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 主键id
     */
    public String getSignRuleId() {
        return signRuleId;
    }

    public void setSignRuleId(String signRuleId) {
        this.signRuleId = signRuleId;
    }

    /**
     * @return 规则名称
     */
    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    /**
     * @return 项目id
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 合同模板id
     */
    public String getContractTemplateId() {
        return contractTemplateId;
    }

    public void setContractTemplateId(String contractTemplateId) {
        this.contractTemplateId = contractTemplateId;
    }

    /**
     * @return 签约告知书id
     */
    public String getSignNoticeId() {
        return signNoticeId;
    }

    public void setSignNoticeId(String signNoticeId) {
        this.signNoticeId = signNoticeId;
    }

    /**
     * @return 租金标准id
     */
    public String getRentStandardId() {
        return rentStandardId;
    }

    public void setRentStandardId(String rentStandardId) {
        this.rentStandardId = rentStandardId;
    }

    /**
     * @return 缴费周期code
     */
    public String getPaymentCycleCode() {
        return paymentCycleCode;
    }

    public void setPaymentCycleCode(String paymentCycleCode) {
        this.paymentCycleCode = paymentCycleCode;
    }

    /**
     * @return 押金标准code
     */
    public String getCashPledgeCode() {
        return cashPledgeCode;
    }

    public void setCashPledgeCode(String cashPledgeCode) {
        this.cashPledgeCode = cashPledgeCode;
    }

    /**
     * @return sign_in_begin_time签约开始时间
     */
    public Date getSignInBeginTime(){
        if(signInBeginTime!=null){
            return (Date)signInBeginTime.clone();
        }else{
            return null;
        }
    }

    public void setSignInBeginTime(Date signInBeginTime) {
        if(signInBeginTime==null){
            this.signInBeginTime = null;
        }else{
            this.signInBeginTime = (Date)signInBeginTime.clone();
        }
    }

    /**
     * @return 签约结束时间
     */
    public Date getSignInEndTime(){
        if(signInEndTime!=null){
            return (Date)signInEndTime.clone();
        }else{
            return null;
        }
    }

    public void setSignInEndTime(Date signInEndTime) {
        if(signInEndTime==null){
            this.signInEndTime = null;
        }else{
            this.signInEndTime = (Date)signInEndTime.clone();
        }
    }

    /**
     * @return 租赁开始日期
     */
    public Date getRentBeginTime(){
        if(rentBeginTime!=null){
            return (Date)rentBeginTime.clone();
        }else{
            return null;
        }
    }

    public void setRentBeginTime(Date rentBeginTime) {
        if(rentBeginTime==null){
            this.rentBeginTime = null;
        }else{
            this.rentBeginTime = (Date)rentBeginTime.clone();
        }
    }

    /**
     * @return 租赁结束日期
     */
    public Date getRentEndTime(){
        if(rentEndTime!=null){
            return (Date)rentEndTime.clone();
        }else{
            return null;
        }
    }

    public void setRentEndTime(Date rentEndTime) {
        if(rentEndTime==null){
            this.rentEndTime = null;
        }else{
            this.rentEndTime = (Date)rentEndTime.clone();
        }
    }

    /**
     * @return 收款银行
     */
    public String getDueBank() {
        return dueBank;
    }

    public void setDueBank(String dueBank) {
        this.dueBank = dueBank;
    }

    /**
     * @return 通知消息模板id
     */
    public String getNoticeTemplateId() {
        return noticeTemplateId;
    }

    public void setNoticeTemplateId(String noticeTemplateId) {
        this.noticeTemplateId = noticeTemplateId;
    }

    /**
     * @return 短信消息模板id
     */
    public String getMessageTemplateId() {
        return messageTemplateId;
    }

    public void setMessageTemplateId(String messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    /**
     * @return 是否报送热力公司
     */
    public String getIsHeatingCompany() {
        return isHeatingCompany;
    }

    public void setIsHeatingCompany(String isHeatingCompany) {
        this.isHeatingCompany = isHeatingCompany;
    }

    /**
     * @return 是否同时办理入
     */
    public String getIsMoveInto() {
        return isMoveInto;
    }

    public void setIsMoveInto(String isMoveInto) {
        this.isMoveInto = isMoveInto;
    }

    /**
     * @return 租金规则id
     */
    public String getRentRulesId() {
        return rentRulesId;
    }

    public void setRentRulesId(String rentRulesId) {
        this.rentRulesId = rentRulesId;
    }

    /**
     * @return 发布状态
     */
    public String getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(String publishStatus) {
        this.publishStatus = publishStatus;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }


    public String getRentRulesParams() {
        return rentRulesParams;
    }

    public void setRentRulesParams(String rentRulesParams) {
        this.rentRulesParams = rentRulesParams;
    }

    @Override
    public String toString() {
        return "BbsiRuleInfoEntity{" +
            "signRuleId=" + signRuleId +
            ", ruleName=" + ruleName +
            ", projectId=" + projectId +
            ", projectName=" + projectName +
            ", contractTemplateId=" + contractTemplateId +
            ", signNoticeId=" + signNoticeId +
            ", rentStandardId=" + rentStandardId +
            ", paymentCycleCode=" + paymentCycleCode +
            ", cashPledgeCode=" + cashPledgeCode +
            ", signInBeginTime=" + signInBeginTime +
            ", signInEndTime=" + signInEndTime +
            ", rentBeginTime=" + rentBeginTime +
            ", rentEndTime=" + rentEndTime +
            ", dueBank=" + dueBank +
            ", noticeTemplateId=" + noticeTemplateId +
            ", messageTemplateId=" + messageTemplateId +
            ", isHeatingCompany=" + isHeatingCompany +
            ", isMoveInto=" + isMoveInto +
            ", rentRulesId=" + rentRulesId +
            ", publishStatus=" + publishStatus +
            ", delFlag=" + delFlag +
        "}";
    }
}
