package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.OfferEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

import javax.servlet.http.HttpServletResponse;

/**
 * 报盘记录 服务类
 *
 * <AUTHOR>
 * @date 2023-10-20
 * @change 2023-10-20 by binghong.tang for init
 */
public interface IOfferService extends IMcpBaseService<OfferEntity>{


    void exceldownload(ExcelDownloadVo vo, HttpServletResponse httpServletResponse);

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    String insertRecord(OfferVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<OfferVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param reqno 需要删除的请求编号
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    void removeByIdRecord(String reqno);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param reqnoList 需要删除的请求编号
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> reqnoList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的报盘记录
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    void updateByIdRecord(OfferVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的报盘记录
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<OfferVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的报盘记录
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    void saveByIdRecord(OfferVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的报盘记录
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<OfferVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param reqno 需要查询的请求编号
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    OfferVo selectByIdRecord(String reqno);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    PageResult<List<OfferPageResultVo>> selectByPageRecord(OfferPageVo vo);
}
