package com.bonc.ioc.bzf.business.reminder.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 催缴白名单表 实体类
 *
 * <AUTHOR>
 * @date 2024-04-24
 * @change 2024-04-24 by binghong.tang for init
 */
@ApiModel(value="BbpmCollectionWhitelistPageVo对象", description="催缴白名单表")
public class BbpmCollectionWhitelistPageVo extends McpBasePageVo implements Serializable{


    /**
     * 白名单id
     */
    @ApiModelProperty(value = "白名单id")
    @NotBlank(message = "白名单id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String whiteId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
                            private String personCustomerId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
                            private String personCustomerName;

    /**
     * 民族编码
     */
    @ApiModelProperty(value = "民族编码")
                            private String nationCode;

    /**
     * 证件类型编码
     */
    @ApiModelProperty(value = "证件类型编码")
                            private String certificateTypeCode;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
                            private String certificateNum;

    /**
     * 性别编码
     */
    @ApiModelProperty(value = "性别编码")
                            private String genderCode;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
                            private String phone;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String operateProjectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 在租房源
     */
    @ApiModelProperty(value = "在租房源")
                            private String rentalSource;

    /**
     * 备用字段1
     */
    @ApiModelProperty(value = "备用字段1--projectNo")
                            private String fields1;

    /**
     * 备用字段2
     */
    @ApiModelProperty(value = "备用字段2")
                            private String fields2;

    /**
     * 备用字段3
     */
    @ApiModelProperty(value = "备用字段3")
                            private String fields3;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;
    @ApiModelProperty(value = "项目编号ids 逗号分隔")
    private String projectIdStr;
    @ApiModelProperty(value = "类型(01人员白名单,02项目白名单）")
    private String type;

    @ApiModelProperty(value = "创建时间开始" )
    private String createTimeStart;

    @ApiModelProperty(value = "创建时间结束" )
    private String createTimeEnd;

    public String getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getProjectIdStr() {
        return projectIdStr;
    }

    public void setProjectIdStr(String projectIdStr) {
        this.projectIdStr = projectIdStr;
    }

    /**
     * @return 白名单id
     */
    public String getWhiteId() {
        return whiteId;
    }

    public void setWhiteId(String whiteId) {
        this.whiteId = whiteId;
    }

    /**
     * @return 客户id
     */
    public String getPersonCustomerId() {
        return personCustomerId;
    }

    public void setPersonCustomerId(String personCustomerId) {
        this.personCustomerId = personCustomerId;
    }

    /**
     * @return 姓名
     */
    public String getPersonCustomerName() {
        return personCustomerName;
    }

    public void setPersonCustomerName(String personCustomerName) {
        this.personCustomerName = personCustomerName;
    }

    /**
     * @return 民族编码
     */
    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }

    /**
     * @return 证件类型编码
     */
    public String getCertificateTypeCode() {
        return certificateTypeCode;
    }

    public void setCertificateTypeCode(String certificateTypeCode) {
        this.certificateTypeCode = certificateTypeCode;
    }

    /**
     * @return 证件号码
     */
    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    /**
     * @return 性别编码
     */
    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }

    /**
     * @return 联系电话
     */
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * @return 项目ID
     */
    public String getOperateProjectId() {
        return operateProjectId;
    }

    public void setOperateProjectId(String operateProjectId) {
        this.operateProjectId = operateProjectId;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 在租房源
     */
    public String getRentalSource() {
        return rentalSource;
    }

    public void setRentalSource(String rentalSource) {
        this.rentalSource = rentalSource;
    }

    /**
     * @return 备用字段1
     */
    public String getFields1() {
        return fields1;
    }

    public void setFields1(String fields1) {
        this.fields1 = fields1;
    }

    /**
     * @return 备用字段2
     */
    public String getFields2() {
        return fields2;
    }

    public void setFields2(String fields2) {
        this.fields2 = fields2;
    }

    /**
     * @return 备用字段3
     */
    public String getFields3() {
        return fields3;
    }

    public void setFields3(String fields3) {
        this.fields3 = fields3;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmCollectionWhitelistPageVo{" +
            "whiteId=" + whiteId +
            ", personCustomerId=" + personCustomerId +
            ", personCustomerName=" + personCustomerName +
            ", nationCode=" + nationCode +
            ", certificateTypeCode=" + certificateTypeCode +
            ", certificateNum=" + certificateNum +
            ", genderCode=" + genderCode +
            ", phone=" + phone +
            ", operateProjectId=" + operateProjectId +
            ", projectName=" + projectName +
            ", rentalSource=" + rentalSource +
            ", fields1=" + fields1 +
            ", fields2=" + fields2 +
            ", fields3=" + fields3 +
            ", delFlag=" + delFlag +
        "}";
    }
}
