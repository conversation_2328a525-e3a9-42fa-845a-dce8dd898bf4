package com.bonc.ioc.bzf.business.payment.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.bzf.business.invoice.vo.BbpmInvoiceIssueVo;
import com.bonc.ioc.bzf.business.invoice.vo.InvoiceBillVo;
import com.bonc.ioc.bzf.business.payment.entity.BbpmCashCollectionVoucherEntity;
import com.bonc.ioc.bzf.business.payment.entity.BbsiRuleInfoEntity;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.YecaiFeignClient;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.FaceMdMapResultTwo;
import com.bonc.ioc.bzf.business.payment.result.InvoiceIssueResult;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.service.IBbsiRuleInfoService;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 签约规则信息表 服务类实现
 *
 * <AUTHOR>
 * @date 2022-11-29
 * @change 2022-11-29 by ly for init
 */
@Slf4j
@Service
public class BbsiRuleInfoServiceImpl extends McpBaseServiceImpl<BbsiRuleInfoEntity> implements IBbsiRuleInfoService {


    @Resource
    private RestTemplateUtil restTemplateUtil ;

    @Resource
    private YecaiFeignClient yecaiFeignClient;

    @Resource
    private BfipSettlementFeignClient settlementFeignClient;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;


    @Override
    public List<ChargeRuleResultVo> selectRentRuleList(Map<String, Object> vo) {
        ChargeRuleParamsVo chargeRuleParamsVo  = new ChargeRuleParamsVo();
        BankRequestVo<ChargeRuleParamsVo> params= new BankRequestVo();
        chargeRuleParamsVo.setChargeSubjectNo("01");
        chargeRuleParamsVo.setServiceType("01");
        params.setData(chargeRuleParamsVo);
        ChargeRespondVo<List<ChargeRuleResultVo>> result ;
        log.info("调用*月租金计算规则参数："+JSONObject.toJSONString(params, SerializerFeature.WriteMapNullValue));
        if(yecaiFeign){
            result = yecaiFeignClient.getChargeRuleList(params);
        }else{
            String url=yecaiUrl+"/charge/v1/chargeRule/list";
            result = restTemplateUtil.postJsonDataByVo(url,params);
        }

        log.info("调用*月租金计算规则返回："+JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue));

        if(result == null){
            throw new McpException("*3.1.查询计费规则列表接口返回为:"+result);
        }

        if(!("00000").equals(result.getCode()) || result.getData() == null){
            throw new McpException("*调用获取租金结算规则失败(3.1.查询计费规则列表)");
        }else {
            return result.getData();
        }
    }

    /**
     * 动态参数
     * @param vo
     * @return
     */
    @Override
    public List<ChargeRuleResultVo> selectRentRuleListV2(ChargeRuleParamsVo chargeRuleParamsVo) {
//        ChargeRuleParamsVo chargeRuleParamsVo  = new ChargeRuleParamsVo();
        BankRequestVo<ChargeRuleParamsVo> params= new BankRequestVo();
//        chargeRuleParamsVo.setChargeSubjectNo(vo.get("chargeSubjectNo").toString());
//        chargeRuleParamsVo.setServiceType(vo.get("serviceType").toString());
        params.setData(chargeRuleParamsVo);
        ChargeRespondVo<List<ChargeRuleResultVo>> result ;
        log.info("调用*月租金计算规则参数："+JSONObject.toJSONString(params, SerializerFeature.WriteMapNullValue));
        if(yecaiFeign){
            result = yecaiFeignClient.getChargeRuleList(params);
        }else{
            String url=yecaiUrl+"/charge/v1/chargeRule/list";
            result = restTemplateUtil.postJsonDataByVo(url,params);
        }
        log.info("调用*月租金计算规则返回："+JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue));

        if(result == null){
            throw new McpException("*3.1.查询计费规则列表接口返回为:"+result);
        }

        if(!("00000").equals(result.getCode()) || result.getData() == null){
            throw new McpException("*调用获取租金结算规则失败(3.1.查询计费规则列表)");
        }else {
            return result.getData();
        }
    }

    /**
     * @return
     * @version 1.0 获取计费结果接口
     * <AUTHOR>
     * @Date 2022/12/9
     */
    @Override
    public ChargeRespondVo getChargeResult(ChargeResultParamsVo chargeResultParamsVo) {

        BankRequestVo<ChargeResultParamsVo> bankRequestVo = new BankRequestVo<>();
        bankRequestVo.setData(chargeResultParamsVo);

        JSONObject jo= (JSONObject) JSONObject.toJSON(bankRequestVo);
        log.info("bankRequestVo调用租金计算结果参数1："+bankRequestVo.toString());
        log.info("jo调用租金计算结果参数2："+jo.toString());
        ChargeRespondVo result;
        if(yecaiFeign){
            result = yecaiFeignClient.getChargeResult(jo);
        }else {
            String url=yecaiUrl+"/charge/v1/chargeRule/getChargeResult";
            result = restTemplateUtil.postJsonDataByVo(url,jo);
        }
        log.info("*调用租金计算结果返回："+JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue));
        if(!("00000").equals(result.getCode())  || result.getData() == null){
            throw new McpException("*调用获取租金计算结果失败(3.2.获取计费结果)");
        }else {
           return result;
        }
    }

    @Override
    public List<ChargeBankResultVo> getBankByPorjectId(String projectId,String bankCode,String bankAccountNo) {
        BankRequestVo<ChargeBankParamsVo> bankRequestVo= new BankRequestVo<>();
        ChargeBankParamsVo chargeBankParamsVo= new ChargeBankParamsVo();
        chargeBankParamsVo.setProjectId(projectId);
        chargeBankParamsVo.setBankCode(bankCode);
        chargeBankParamsVo.setBankAccountNo(bankAccountNo);
        bankRequestVo.setData(chargeBankParamsVo);
        ChargeRespondVo<List<ChargeBankResultVo>> result ;
        log.info("3.12.根据项目查询保障房收款开户行请求参数:"+JSONObject.toJSONString(bankRequestVo, SerializerFeature.WriteMapNullValue));
        if(yecaiFeign){
            result = settlementFeignClient.listByProjectId(bankRequestVo);
        }else {
            String url=yecaiUrl+"/settlement/v1/centerCharge/listByProjectId";
            result = restTemplateUtil.postJsonDataByVo(url,bankRequestVo);
        }
        log.info("3.12.根据项目查询保障房收款开户行返回参数:"+JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue));
        if(!"00000".equals(result.getCode())){
            if(result == null || result.getData()== null){
                throw new McpException("*调用3.12获取收款银行失败");
            }else{
                throw new McpException("*"+result.getMessage());
            }
        }else{
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(result.getData()));
            List<ChargeBankResultVo> a =JSONArray.parseArray(jsonArray.toJSONString(), ChargeBankResultVo.class);  ;
            return a;
        }
    }

    /**
     * 3.44根据项目查询保障房收款开户行
     * @param projectId
     * @return
     */
    @Override
    public List<ChargeBankResultV2Vo> getBankByPorjectIdV2(String projectId) {
        BankRequestVo<ChargeBankParamsV2Vo> bankRequestVo= new BankRequestVo<>();
        ChargeBankParamsV2Vo chargeBankParamsVo= new ChargeBankParamsV2Vo();
        chargeBankParamsVo.setProjectId(projectId);
        bankRequestVo.setData(chargeBankParamsVo);
        ChargeRespondVo<List<ChargeBankResultV2Vo>> result ;
        log.info("3.44根据项目查询保障房收款开户行-请求参数:"+JSONObject.toJSONString(bankRequestVo, SerializerFeature.WriteMapNullValue));
        if(yecaiFeign){
            result = settlementFeignClient.listBankByProjectId(bankRequestVo);
        }else {
            String url=yecaiUrl+"/settlement/v1/centerCharge/listBankByProjectId";
            String mockUrl = url+ "?apipost_id=2503eedff9a003";

            result = restTemplateUtil.postJsonDataByVo(mockUrl,bankRequestVo);
        }
        log.info("3.44根据项目查询保障房收款开户行-返回参数:"+JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue));
        if(!"00000".equals(result.getCode()) || result == null || result.getData()== null){
            throw new McpException("*调用3.44获取收款银行失败");
        }else{
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(result.getData()));
            List<ChargeBankResultV2Vo> a =JSONArray.parseArray(jsonArray.toJSONString(), ChargeBankResultV2Vo.class);  ;
            return a;
        }
    }
    /**
     * 3.42.分行银行编码以及名称分页查询接口
     * @param bankBranchCodeParamsVo
     * @return
     */
    @Override
    public PageResult getListBankBranchCode(BankBranchCodeParamsVo bankBranchCodeParamsVo) {
        ParentRequest parentRequest = new ParentRequest();

        parentRequest.setData(bankBranchCodeParamsVo);
        String responseBody = null;
        log.info("3.42. 分行银行编码以及名称分页查询接口-请求参数:"+JSONObject.toJSONString(parentRequest, SerializerFeature.WriteMapNullValue));
        if(yecaiFeign){
            responseBody = settlementFeignClient.listBankBranchCode(parentRequest);
        }else {
            String url=yecaiUrl+"/settlement/v1/centerCharge/listBankBranchCod";
            responseBody = restTemplateUtil.postJsonBankBranchCodeVo(url,parentRequest);
        }
        FaceHttpResultTwo<BankBranchCodeResultVo> result = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);
        log.info("3.42. 分行银行编码以及名称分页查询接口-返回参数:"+JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue));
        if(!("00000").equals(result.getCode())){
            throw new McpException("*提示:"+result.getMessage());
        }
        if(result.getData() == null || result.getData().getRecords() == null){
            return new PageResult<>();
        }
        List<BankBranchCodeResultVo> collectionListResultList = JSON.parseArray(result.getData().getRecords().toString(),BankBranchCodeResultVo.class);

       return new PageResult<>(result.getData().getTotal(),collectionListResultList);
    }
    /**
     * 增值服务费试算接口
     * @param vo
     * @return
     */
    @Override
    public TryServiceFeeResponseVo tryServiceFee(TryServiceFeeMainParamsVo vo) {
        log.info("3.45增值服务费试算接口--传入参数:"+JSONObject.toJSONString(vo, SerializerFeature.WriteMapNullValue));

//        ChargeRuleSubParamsVo paramsVo = vo.getChargeSubject().getParamListV2();
//        ChargeRuleSubParamsVo paramValues = vo.getChargeSubject().getParamValueListV2();
//        JSONObject paramList = null;
//        JSONObject paramValueList = null;
//        if(paramsVo != null){
//            paramList = (JSONObject) JSONObject.toJSON(paramsVo);
//        }
//        if(paramValues != null){
//            paramValueList = (JSONObject) JSONObject.toJSON(paramValues);
//        }
//        if(vo.getChargeSubject().getParamList()== null){
//            vo.getChargeSubject().setParamList(paramList);
//        }
//        if(vo.getChargeSubject().getParamValueList()== null){
//            vo.getChargeSubject().setParamValueList(paramValueList);
//        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<TryServiceFeeMainParamsVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        log.info("3.45增值服务费试算接口请求参数:" + parentRequest.toString());
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setTimeZone(TimeZone.getDefault());

        try {
            log.info("3.45增值服务费试算接口请求参数json:" +  objectMapper.writeValueAsString(parentRequest));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        String responseBody = "";
        if (yecaiFeign) {
            responseBody = bfipChargeFeignClient.tryServiceFee(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/bill/try_service_fee", parentRequest);
        }
        log.info("调用工银3.45增值服务费试算接口返回结果为:" + responseBody);

        FaceMdMapResultTwo<TryServiceFeeResponseVo> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.45增值服务费试算接口失败:"+responseBody);
            throw new McpException("*提示:"+faceMdMapResult.getMessage());
        }else{
            JSONObject jsonObject = JSON.parseObject(String.valueOf(faceMdMapResult.getData()));
            TryServiceFeeResponseVo tryServiceFeeResponseVo = jsonObject.toJavaObject(TryServiceFeeResponseVo.class);
            return tryServiceFeeResponseVo;
        }
    }



    /**
     * 3.38 查询税率配置列表
     *
     * @return
     */
    @Override
    public TaxRateResultVo getTaxRateList(TaxRateVo requestVo){
        ParentRequest<TaxRateVo> parentRequest = new ParentRequest<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));

//        TaxRateVo requestVo = new TaxRateVo();
//        requestVo.setProjectId(vo.getProjectId());
//        requestVo.setCompanyId(vo.getCompanyId());
//        requestVo.setYeTai(vo.getProductType().equals(ProductTypeEnum.PUBLIC_TYPE.getCode()) ? "01" : "02");
//        requestVo.setChargeItemId(chargeItemId);
//        requestVo.setTenantry("01");

        parentRequest.setData(requestVo);
        String requestJson = JSONObject.toJSONString(parentRequest);
        log.info("3.38查询税率配置列表接口请求参数json:" + requestJson);
        //请求业财接口
        String responseBody = null;
        if (yecaiFeign) {
            responseBody = settlementFeignClient.getTaxRateList(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post("https://bfip.dccnet.com.cn/api/settlement/taxrate/list", parentRequest);
        }
        log.info("3.38查询税率配置列表接口返回:" + responseBody);
        //重新set值到分页实体、转换
        if (StringUtils.isBlank(responseBody)) {
            return new TaxRateResultVo();
        }
        FaceHttpResultTwo<TaxRateResultVo> faceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);
        if (!("00000").equals(faceHttpResultTwo.getCode())) {
            throw new McpException("*3.38查询税率配置列表接口提示:" + faceHttpResultTwo.getMessage());
        }

        if (faceHttpResultTwo.getData() == null || faceHttpResultTwo.getData().getRecords() == null) {
//           throw new McpException("3.38查询税率配置列表接口提示:查询结果为空,请求参数"+requestJson);
            log.info("3.38查询税率配置列表接口提示:查询结果为空,请求参数"+requestJson);
            return null;
        }
        List<TaxRateResultVo> list = JSON.parseArray(faceHttpResultTwo.getData().getRecords().toString(), TaxRateResultVo.class);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }else{
            log.info("3.38查询税率配置列表接口提示:查询结果为空,请求参数"+requestJson);
            return null;
//            throw new McpException("*3.38查询税率配置列表接口提示:查询结果为空,请求参数"+requestJson);
        }
    }

    /**
     * 收款单调整接口
     * @param adjustmentCollectionVo
     * @return
     */
    @Override
    public String chargePaymentAdjustSubmit(AdjustmentCollectionVo adjustmentCollectionVo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<AdjustmentCollectionVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(adjustmentCollectionVo);
        log.info("3.50提交收款单调整功能接口请求参数:" + parentRequest.toString());
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setTimeZone(TimeZone.getDefault());
//        String jsonRequest = JSONObject.toJSONString(parentRequest);
        try {
            log.info("3.50提交收款单调整功能接口请求参数json:" +  objectMapper.writeValueAsString(parentRequest));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        String responseBody = "";
        if (yecaiFeign) {
            responseBody = bfipChargeFeignClient.submit(parentRequest);;
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/chargePaymentAdjust/v1/submit", parentRequest);
        }

        log.info("调用工银3.50提交收款单调整功能接口返回结果为:" + responseBody);

        FaceMdMapResultTwo<String> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.50提交收款单调整功能接口失败:"+responseBody);
            throw new McpException("*提示:"+faceMdMapResult.getMessage());
        }else{
            return responseBody;
        }
    }


}
