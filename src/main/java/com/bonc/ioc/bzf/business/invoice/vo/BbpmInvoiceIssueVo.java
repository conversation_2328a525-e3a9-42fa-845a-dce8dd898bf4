package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 发票开具--主类
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value="BbpmInvoiceIssueVo对象", description="发票开具--主类")
@Data
public class BbpmInvoiceIssueVo extends McpBaseVo implements Serializable{
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "开票信息")
    InvoiceIssueVo invoiceIssue;
//    @ApiModelProperty(value = "主收款单票据列表")
//    private List<ChargeReceiptVo> chargeReceiptList;
    @ApiModelProperty(value = "开票账单信息列表")
    private List<InvoiceBillVo> invoiceBillList;

    @ApiModelProperty(value = "操作类型01:批量 02:合并")
    private String operationType;

}
