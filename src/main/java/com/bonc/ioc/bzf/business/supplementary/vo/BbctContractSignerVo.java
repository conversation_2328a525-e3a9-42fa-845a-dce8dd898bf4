package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 租户信息表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-20
 * @change 2023-05-20 by sx for init
 */
@Data
@ApiModel(value = "BbctContractSignerVo对象", description = "租户信息表")
public class BbctContractSignerVo extends McpBaseVo implements Serializable {


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateValidatorGroup.class})
    private String signerId;

    /**
     * 关系id
     */
    @ApiModelProperty(value = "关系id")
    private String relationId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 租户方：乙、丙、丁
     */
    @ApiModelProperty(value = "租户方：乙、丙、丁")
    private String type;

    /**
     * 散户或者企业编号
     */
    @ApiModelProperty(value = "散户或者企业编号")
    private String customerNo;

    /**
     * 散户或者企业名称
     */
    @ApiModelProperty(value = "散户或者企业名称")
    private String customerName;

    /**
     * 00:散户  01：企业
     */
    @ApiModelProperty(value = "00:散户  01：企业")
    @McpDictPoint(dictCode = "TENANT_TYPE", overTransCopyTo = "customerTypeName")
    private String customerType;


    @ApiModelProperty(value = "租户类型名称")
    private String customerTypeName;

    public String getCustomerTypeName() {
        return customerTypeName;
    }

    public void setCustomerTypeName(String customerTypeName) {
        this.customerTypeName = customerTypeName;
    }

    /**
     * 租户客商编号
     */
    @ApiModelProperty(value = "租户客商编号")
    private String customerSupplierNo;

    /**
     * 缴费占比（0-1）
     */
    @ApiModelProperty(value = "缴费占比（0-1）")
    private String customerRatio;

    /**
     * 社会统一信息用代码(企业使用)
     */
    @ApiModelProperty(value = "社会统一信息用代码(企业使用)")
    private String customerCreditCode;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    @McpDictPoint(dictCode = "CERTIFICATE_TYPE", overTransCopyTo = "transactionTypeName")
    private String customerIdType;

    public String getCustomerIdTypeName() {
        return customerIdTypeName;
    }

    public void setCustomerIdTypeName(String customerIdTypeName) {
        this.customerIdTypeName = customerIdTypeName;
    }

    @ApiModelProperty(value = "证件类型名称")
    private String customerIdTypeName = "身份证";

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String customerGender;

    /**
     * 单位电话
     */
    @ApiModelProperty(value = "单位电话")
    private String customerWorkTel;

    /**
     * 租户电话
     */
    @ApiModelProperty(value = "租户电话")
    private String customerTel;

    /**
     * 公租房备案号
     */
    @ApiModelProperty(value = "公租房备案号")
    private String customerPublicRecordNo;

    /**
     * 源住址
     */
    @ApiModelProperty(value = "源住址")
    private String customerHouseAddress;

    @ApiModelProperty(value = "开户名称")
    private String bankUserName;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    private String bankName;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNo;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
    private String bankCard;

    /**
     * 开户行手机号
     */
    @ApiModelProperty(value = "开户行手机号")
    private String bankPhone;

    /**
     * 开户银行编码
     */
    @ApiModelProperty(value = "开户银行编码")
    private String bankNameCode;

    /**
     * 是否签署代扣代缴协议（1 是0 否）
     */
    @ApiModelProperty(value = "是否签署代扣代缴协议（1 是0 否）")
    private String bankIsAgreement;

    /**
     * 是否鉴权（1 是0 否）
     */
    @ApiModelProperty(value = "是否鉴权（1 是0 否）")
    private String bankIsAuthentication;

    /**
     * 协议编号
     */
    @ApiModelProperty(value = "协议编号")
    private String bankAgreementNo;

    /**
     * 签署类型(1.手机号开通代扣代缴服务 2.下线已经开通代扣代缴服务)
     */
    @ApiModelProperty(value = "签署类型(1.手机号开通代扣代缴服务 2.下线已经开通代扣代缴服务)")
    private String bankAgreementType;

    /**
     * 支行编码
     */
    @ApiModelProperty(value = "支行编码")
    private String bankSubbranchCode;

    /**
     * 支行名称
     */
    @ApiModelProperty(value = "支行名称")
    private String bankSubbranchName;

    /**
     * 银行分类代码
     */
    @ApiModelProperty(value = "银行分类代码")
    private String bankNccCategoryCode;

    /**
     * 开通代扣代缴服务资料地址
     */
    @ApiModelProperty(value = "开通代扣代缴服务资料地址")
    private String bankDataUrls;

    /**
     * 电子邮箱地址
     */
    @ApiModelProperty(value = "电子邮箱地址")
    private String emailAddress;

    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
    private String mailAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalName;

    /**
     * 法定代表人联系电话
     */
    @ApiModelProperty(value = "法定代表人联系电话")
    private String legalMobile;

    /**
     * 委托代理人姓名
     */
    @ApiModelProperty(value = "委托代理人姓名")
    private String consignorName;

    /**
     * 委托代理人联系电话
     */
    @ApiModelProperty(value = "委托代理人联系电话")
    private String consignorMobile;

    /**
     * 委托人与签约方关系(1.朋友 2.亲属)
     */
    @ApiModelProperty(value = "委托人与签约方关系(1.朋友 2.亲属)")
    private String consignorSignatoryRelationType;

    /**
     * 委托人证件类型(1.身份证 2.户口本)
     */
    @ApiModelProperty(value = "委托人证件类型(1.身份证 2.户口本)")
    private String consignorCertificationType;

    /**
     * 委托人证件号码
     */
    @ApiModelProperty(value = "委托人证件号码")
    private String consignorIdentityCard;

    /**
     * 委托人资料地址
     */
    @ApiModelProperty(value = "委托人资料地址")
    private String consignorDataUrls;

    /**
     * 委托人身份证正面照片
     */
    @ApiModelProperty(value = "委托人身份证正面照片")
    private String consignorIdvoCardFrontPhotoUrl;

    /**
     * 委托人身份证背面面照片
     */
    @ApiModelProperty(value = "委托人身份证背面面照片")
    private String consignorIdvoCardBackPhotoUrl;

    /**
     * 办理人身份证正面照片
     */
    @ApiModelProperty(value = "办理人身份证正面照片")
    private String transactorIdvoCardFrontPhotoUrl;

    /**
     * 办理人身份证背面照片
     */
    @ApiModelProperty(value = "办理人身份证背面照片")
    private String transactorIdvoCardBackPhotoUrl;

    @ApiModelProperty(value = "办理人照片地址")
    private String transactorIdentityCardPhotoUrl;

    @ApiModelProperty(value = "委托人照片地址")
    private String consignorIdentityCardPhotoUrl;

    @ApiModelProperty(value = "客户扩展")
    private String customerExtend;
    /**
     * 扩展1
     */
    @ApiModelProperty(value = "扩展1")
    private String ext1;

    /**
     * 扩展2
     */
    @ApiModelProperty(value = "扩展2")
    private String ext2;

    /**
     * 扩展3
     */
    @ApiModelProperty(value = "扩展3")
    private String ext3;

    /**
     * 扩展4
     */
    @ApiModelProperty(value = "扩展4")
    private String ext4;

    /**
     * 扩展5
     */
    @ApiModelProperty(value = "扩展5")
    private String ext5;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    @ApiModelProperty(value = "租户客户编号")
    private String tenantCustomerNo;

    @ApiModelProperty(value = "租户客商编号")
    private String tenantSupplierNo;

    @ApiModelProperty(value = "租户客商名称")
    private String tenantSupplierName;

    @ApiModelProperty(value = "趸租企业客商编号")
    private String companySupplierNo;

    @ApiModelProperty(value = "趸租企业客商名称")
    private String companySupplierName;

    @ApiModelProperty(value = "趸租企业证照类型")
    private String companyIdType;

    @ApiModelProperty(value = "缴费金额")
    private String customerPaymentAmount;

    @ApiModelProperty(value = "国籍")
    private String customerNationality;

    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 趸租管理协议是否集体变更
     */
    @ApiModelProperty(value = "趸租管理协议是否集体变更(1集体变更 2单独变更)")
    private String isChangeUnitManagement;

    /**
     * rrId
     */
    @ApiModelProperty(value = "rrId")
    private String rrId;

    /**
     * 缴费类型(1.比例 2.金额)
     */
    @ApiModelProperty(value = "缴费类型(1.比例 2.金额)")
    private String payType;

    /**
     * 增值服务费缴费类型(1.比例 2.金额)
     */
    @ApiModelProperty(value = "增值服务费缴费类型(1.比例 2.金额)")
    private String serviceChargePayType;

    /**
     * 增值服务费缴费比例
     */
    @ApiModelProperty(value = "增值服务费缴费比例")
    private String serviceChargePayPercent;

    /**
     * 增值服务费缴费金额
     */
    @ApiModelProperty(value = "增值服务费缴费金额")
    private String serviceChargePayMoney;
}
