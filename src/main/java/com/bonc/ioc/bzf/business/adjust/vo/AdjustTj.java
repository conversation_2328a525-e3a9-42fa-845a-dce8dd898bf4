package com.bonc.ioc.bzf.business.adjust.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdjustTj implements java.io.Serializable{
    @ApiModelProperty(value = "调整待办数量")
    private String tzdb;

    @ApiModelProperty(value = "调整已办数量")
    private String tzyb;

    @ApiModelProperty(value = "审核待办数量")
    private String shdb;

    @ApiModelProperty(value = "审核已办数量")
    private String shyb;
}
