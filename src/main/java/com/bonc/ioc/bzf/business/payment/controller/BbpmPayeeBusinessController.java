package com.bonc.ioc.bzf.business.payment.controller;

import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageVo;
import com.bonc.ioc.bzf.business.payment.service.IBbpmPayeeService;
import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeeVo;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * pos机  收款回传
 *
 * <AUTHOR>
 * @date 2023/1/3 11:19
 * @change 2023/1/3 11:19 by liujian for init
 */
@RestController
@RequestMapping("/v2/business/BbpmPayeeEntity")
@Api(tags = "pos机收款回传")
@Validated
public class BbpmPayeeBusinessController extends McpBaseController {
    @Resource
    private IBbpmPayeeService bbpmPayeeService;

    @PostMapping(value = "payee",produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "liujian")
    @ApiOperation(value = "新增pos机收款回传数据", notes = "pos机收款回传数据",hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply payee(@RequestBody BbpmPayeeVo bbpmPayeeVo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        bbpmPayeeService.savePayee(bbpmPayeeVo);
        return appReply;
    }


    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-01-24
     * @change
     * 2024-01-24 by binghong.tang for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmPayeePageResultVo>>> selectByPageRecord(BbpmPayeePageVo vo){
        AppReply<PageResult<List<BbpmPayeePageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(bbpmPayeeService.selectByPageRecord(vo));
        return appReply;
    }
}
