package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 未存款列表
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@ApiModel(value="BbpmUndepositsVo对象", description="未存款列表")
public class BbpmUndepositsVo extends McpBaseVo implements Serializable{


    /**
     * 存款金额
     */
    @ApiModelProperty(value = "存款金额")
    private BigDecimal paidInAmount;

    /**
     * 收款单数量
     */
    @ApiModelProperty(value = "收款单数量")
    private String collectionDocQty;

    /**
     * 收款单列表
     */
    @ApiModelProperty(value = "收款单列表")
    List<BbpmCollectionPageResultVo> bbpmCollectionVoList;


    public BigDecimal getPaidInAmount() {
        return paidInAmount;
    }

    public void setPaidInAmount(BigDecimal paidInAmount) {
        this.paidInAmount = paidInAmount;
    }

    public String getCollectionDocQty() {
        return collectionDocQty;
    }

    public void setCollectionDocQty(String collectionDocQty) {
        this.collectionDocQty = collectionDocQty;
    }

    public List<BbpmCollectionPageResultVo> getBbpmCollectionVoList() {
        return bbpmCollectionVoList;
    }

    public void setBbpmCollectionVoList(List<BbpmCollectionPageResultVo> bbpmCollectionVoList) {
        this.bbpmCollectionVoList = bbpmCollectionVoList;
    }
}
