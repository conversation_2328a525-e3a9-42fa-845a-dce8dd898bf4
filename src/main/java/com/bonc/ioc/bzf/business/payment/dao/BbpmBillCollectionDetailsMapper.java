package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.BbpmBillCollectionDetailsEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 账单与收款明细表v3.0 Mapper 接口
 *
 * <AUTHOR>
 * @date 2022-12-21
 * @change 2022-12-21 by binghong.tang for init
 */
@Mapper
public interface BbpmBillCollectionDetailsMapper extends McpBaseMapper<BbpmBillCollectionDetailsEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-21
     * @change 2022-12-21 by binghong.tang for init
     */
    List<BbpmBillCollectionDetailsPageResultVo> selectByPageCustom(@Param("vo") BbpmBillCollectionDetailsPageVo vo );

    /**
     * 更具存款单id查询明细信息
     * @param depositId
     * @return
     */
    List<BbpmBillCollectionDetailsVo> selectDetailsByCollectionId(@Param("depositId") String depositId );
}
