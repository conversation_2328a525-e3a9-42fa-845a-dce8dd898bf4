package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 产品类型 枚举类
 *
 * <AUTHOR>
 * @since 2023/6/12
 */
public enum ProductTypeEnum {

    /**
     * 公租房
     */
    PUBLIC_TYPE("01", "公租房"),

    /**
     * 静态停车
     */
    PARK("06", "静态停车"),

    /**
     * 保租房
     */
    PROTECT_TYPE("07", "保租房");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    ProductTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    ProductTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        ProductTypeEnum[] enums = values();
        for (ProductTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
