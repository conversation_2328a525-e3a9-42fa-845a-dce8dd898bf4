package com.bonc.ioc.bzf.business.payment;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * test demo
 *
 * <AUTHOR>
 * @date 2021/5/20 10:35
 * @change: 2021/5/20 10:35 by jin.xu for init
 */
@EnableDiscoveryClient
@EnableFeignClients(basePackages="com.bonc.ioc")
@ComponentScan("com.bonc.ioc")
@MapperScan({"com.bonc.ioc.bzf.business.payment.dao","com.bonc.ioc.bzf.business.invoice.dao","com.bonc.ioc.bzf.business.reminder.dao","com.bonc.ioc.bzf.business.adjust.dao","com.bonc.ioc.bzf.business.supplementary.dao"})
@ServletComponentScan(value = "com.sinovatech.unifiedmanage.client.module.timTask.servlet")
@SpringBootApplication
public class BzfBusinessPaymentApplication {

    public static void main(String[] args) {
        SpringApplication.run(BzfBusinessPaymentApplication.class, args);
    }
}
