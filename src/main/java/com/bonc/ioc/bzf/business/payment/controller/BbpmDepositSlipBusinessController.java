package com.bonc.ioc.bzf.business.payment.controller;

import com.bonc.ioc.bzf.business.payment.service.IBbpmCollectionService;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.bonc.ioc.common.validator.inf.*;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RestController;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.bzf.business.payment.service.IBbpmDepositSlipService;
import io.swagger.annotations.*;

import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 现金盘点存款单表v3.0 前端控制器
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmDepositSlipEntity")
@Api(tags = "现金盘点存款单表v3.0")
@Validated
public class BbpmDepositSlipBusinessController extends McpBaseController {
    @Resource
    private IBbpmDepositSlipService baseService;

    @Resource
    private IBbpmCollectionService iBbpmCollectionService;


    /**
     * selectUndepositsList 生成存款单--列表
     * @param
     * @return  com.bonc.ioc.common.util.AppReply  存款单信息和收款单信息
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectUndepositsList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "binghong.tang")
    @ApiOperation(value = "未存款--所有列表", notes = "查询所有未存款的收款单", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmUndepositsVo> selectUndepositsList(BbpmCollectionVo vo){
        AppReply<BbpmUndepositsVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbpmCollectionService.selectUndepositsList(vo));
        return appReply;
    }



    /**
     * selectByPageRecord 已存款--分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "已存款--分页查询", notes = "已存款--分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmDepositSlipPageResultVo>>> selectByPageRecord(BbpmDepositSlipPageVo vo){
        AppReply<PageResult<List<BbpmDepositSlipPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }


    /**
     * confirmDeposit 生成存款单--确认生成存款单
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/confirmDeposit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "binghong.tang")
    @ApiOperation(value = "生成存款单--确认生成存款单", notes = "点击确认生成存款单按钮,传入：存款金额--paidInAmount,收款单数量--collectionDocQty,收款单id(messageId)--collectionNoList", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/bzf-business-payment/bbpmDepositSlipEntity/confirmDeposit")
    public AppReply<String> confirmDeposit(@ApiParam(value = "现金盘点存款单表v3.0" ,required = false) @RequestBody @Validated({InsertValidatorGroup.class}) BbpmDepositSlipVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.confirmDeposit(vo));
        return appReply;
    }

    /**
     * uploadBankVoucher 根据主键更新 上传银行凭证
     * @param vo 需要更新的现金盘点存款单表v3.0
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/uploadBankVoucher", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 12, author = "binghong.tang")
    @ApiOperation(value = "上传银行凭证", notes = "提交上传银行凭证", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @LogPoint(system = "bzf-business-payment",path = "/bzf-business-payment/bbpmDepositSlipEntity/uploadBankVoucher")
    public AppReply uploadBankVoucher(@ApiParam(value = "需要更新的现金盘点存款单表v3.0" ,required = false) @RequestBody  @Validated(UpdateValidatorGroup.class) BbpmDepositSlipVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.uploadBankVoucher(vo);
        return appReply;
    }

    /**
     * selectByIdRecord 根据存款单id查询存款单表
     * @param depositId 需要查询的主键id
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 13, author = "binghong.tang")
    @ApiOperation(value = "存款单详情--存款单信息 / 上传银行凭证详情", notes = "根据存款单id查询存款单表", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmDepositSlipVo> selectByIdRecord(@ApiParam(value = "需要查询的主键id" ,required = false) @RequestParam(required = true) String depositId){
        AppReply<BbpmDepositSlipVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(depositId));
        return appReply;
    }

    /**
     * selectCollectionByDepositIdList 根据存款单id查询收款单信息
     * @param depositId 存款单id
     * @return  com.bonc.ioc.common.util.AppReply  存款单信息和收款单信息
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectCollectionByDepositIdList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 14, author = "binghong.tang")
    @ApiOperation(value = "存款单详情--收款信息", notes = "根据存款单id查询收款单信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmCollectionPageResultVo>> selectCollectionByDepositIdList(@ApiParam(value = "存款单id" ,required = false) @RequestParam(required = true) String depositId){
        AppReply<List<BbpmCollectionPageResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbpmCollectionService.selectDepositAndCollectionList(depositId));
        return appReply;
    }




}

