package com.bonc.ioc.bzf.business.payment.result.create;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.alibaba.fastjson.JSONObject;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * chargeSubjectList	计费科目列表 是 递增、免租等规则不变就传原来的，变了就传新的
 */
@Data
public class ChargeSubject implements Serializable {

    @ApiModelProperty(value = "计费科目编号")
    private String chargeSubjectNo;	 //计费科目编号 String 是	10 01,房屋租金; 02,押金; 03,家具家电租金; 04,车位租金; 05,仓库租金; 07,物业费; 08,违约金; 09,损坏赔偿; 10,水费; 11,电费; 12,燃气费b;

    @ApiModelProperty(value = "计费科目金额")
    private BigDecimal chargeSubjectAmount;	 //计费科目金额 BigDecimal 否 13,4 物业费和租金为空，有押金传

    @ApiModelProperty(value = "计费科目金额类型")
    private String amountType;	//计费科目金额类型	String 是 01-日 02-月 03-年 计算月租金用

    @ApiModelProperty(value = "计费科目税率")
    private BigDecimal taxRate;	 //计费科目税率 BigDecimal 是 13,4

    @ApiModelProperty(value = "循环计费或单次计费3")
    private String cyclicOrSingle; 	//循环计费或单次计费3 String 是 2 01循环 02单次

    @ApiModelProperty(value = "计费科目收款周期")
    private String chargeSubjectPeriod;	//计费科目收款周期	Int 是 10 01月 02季 03半年 04年 05每2个月 06每4个月 与合同计价周期保持一致

    @ApiModelProperty(value = "押金比例")
    private BigDecimal depositProportion;	//押金比例 BigDecimal 否 13,4 1-合同最后一个月 2-合同最后两个月 3-合同最后三个月 依次类推 3：表示押金为最后三个月租金和

    @ApiModelProperty(value = "计费规则编号")
    private String chargeRuleNo;	//计费规则编号 String 否 10

    @ApiModelProperty(value = "计费规则名称")
    private String chargeRuleName;	//计费规则名称 String	否 200

    @ApiModelProperty(value = "参数列表")
    private JSONObject paramList;	//参数列表 String 否 200 json串，如下图

    @ApiModelProperty(value = "参数值列表")
    private JSONObject paramValueList;	//参数值列表 String 否 200 json串，如下图 计算月租金使用

    @ApiModelProperty(value = "列表，递增规则")
    private List<IncreaseRules> IncreaseRules; //increaseRules递增规则列表否 列表，递增规则

    @ApiModelProperty(value = "列表，优惠规则")
    private List<PreferentRules> PreferentRules; //preferentRules优惠规则列表否 列表，优惠规则




}
