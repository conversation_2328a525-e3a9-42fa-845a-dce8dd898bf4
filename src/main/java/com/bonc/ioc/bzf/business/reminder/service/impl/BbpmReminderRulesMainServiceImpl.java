package com.bonc.ioc.bzf.business.reminder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.FaceMdListResult;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmReminderRulesSubMapper;
import com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesMainEntity;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmReminderRulesMainMapper;
import com.bonc.ioc.bzf.business.reminder.enums.ReminderEnums;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmReminderRulesMainService;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmReminderRulesSubService;
import com.bonc.ioc.bzf.business.reminder.task.TaskPlan;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import org.springframework.beans.BeanUtils;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 缴费提醒规则--主表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmReminderRulesMainServiceImpl extends McpBaseServiceImpl<BbpmReminderRulesMainEntity> implements IBbpmReminderRulesMainService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmReminderRulesMainMapper baseMapper;
    @Resource
    private BbpmReminderRulesSubMapper bbpmReminderRulesSubMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmReminderRulesMainService baseService;

    @Resource
    private IBbpmReminderRulesSubService iBbpmReminderRulesSubService;
    /**
     * Bean 字典转化
     */
    @Resource
    private McpDictSession mcpDictSession;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;



    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmReminderRulesMainVo vo) {
        if(vo == null) {
            return null;
        }
        if(vo.getBbpmReminderRulesSubVoList() == null || vo.getBbpmReminderRulesSubVoList().size() == 0){
            throw new McpException("缴费提醒规则不能为空");
        }

        BbpmReminderRulesMainVo findVo = baseMapper.findByRuleName(vo);
        if(findVo != null && StringUtils.isNotBlank(findVo.getRulesId())){
            throw new McpException("规则名称重复");
        }

        BbpmReminderRulesMainEntity entity = new BbpmReminderRulesMainEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setRulesId(null);
        entity.setDownTime(new Date());
        if(!baseService.insert(entity)) {
            log.error("缴费提醒规则--主表新增失败:" + entity.toString());
            throw new McpException("缴费提醒规则--主表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getRulesId(),1)) {
                log.error("缴费提醒规则--主表新增后保存历史失败:" + entity.toString());
                throw new McpException("缴费提醒规则--主表新增后保存历史失败");
            }
            log.debug("缴费提醒规则--主表新增成功:"+entity.getRulesId());

            //新增子表
            List<BbpmReminderRulesSubVo> bbpmReminderRulesSubVoList = vo.getBbpmReminderRulesSubVoList();
            for(int i=0;i <bbpmReminderRulesSubVoList.size();i++){
                BbpmReminderRulesSubVo bbpmReminderRulesSubVo = bbpmReminderRulesSubVoList.get(i);
                bbpmReminderRulesSubVo.setRulesId(entity.getRulesId());
                bbpmReminderRulesSubVo.setSeqNum(i+1);
            }
            iBbpmReminderRulesSubService.insertBatchRecord(bbpmReminderRulesSubVoList);

            return entity.getRulesId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmReminderRulesMainVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmReminderRulesMainEntity> entityList = new ArrayList<>();
        for (BbpmReminderRulesMainVo item:voList) {
            BbpmReminderRulesMainEntity entity = new BbpmReminderRulesMainEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmReminderRulesMainEntity item:entityList){
            item.setRulesId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("缴费提醒规则--主表新增失败");
            throw new McpException("缴费提醒规则--主表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmReminderRulesMainEntity::getRulesId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("缴费提醒规则--主表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("缴费提醒规则--主表批量新增后保存历史失败");
            }

            log.debug("缴费提醒规则--主表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param rulesId 需要删除的规则编号
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String rulesId) {
        if(!StringUtils.isEmpty(rulesId)) {
            if(!baseService.saveOperationHisById(rulesId,3)) {
                log.error("缴费提醒规则--主表删除后保存历史失败:" + rulesId);
                throw new McpException("缴费提醒规则--主表删除后保存历史失败");
            }

//            if(!baseService.removeById(rulesId)) {
//                log.error("缴费提醒规则--主表删除失败");
//                throw new McpException("缴费提醒规则--主表删除失败"+rulesId);
//            }

            BbpmReminderRulesMainEntity entity = new BbpmReminderRulesMainEntity();
            entity.setRulesId(rulesId);
            entity.setDelFlag(0);
            if(!baseService.updateById(entity)) {
                log.error("缴费提醒规则--主表删除失败");
                throw new McpException("缴费提醒规则--主表删除失败"+rulesId);
            }
            //删除子表
            bbpmReminderRulesSubMapper.updateByRulesId(rulesId);

        } else {
            throw new McpException("缴费提醒规则--主表删除失败规则编号为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rulesIdList 需要删除的规则编号
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> rulesIdList) {
        if(!CollectionUtils.isEmpty(rulesIdList)) {
            int oldSize = rulesIdList.size();
            rulesIdList = rulesIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(rulesIdList) || oldSize != rulesIdList.size()) {
                throw new McpException("缴费提醒规则--主表批量删除失败 存在主键id为空的记录"+StringUtils.join(rulesIdList));
            }

            if(!baseService.saveOperationHisByIds(rulesIdList,3)) {
                log.error("缴费提醒规则--主表批量删除后保存历史失败:" + StringUtils.join(rulesIdList));
                throw new McpException("缴费提醒规则--主表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(rulesIdList)) {
                log.error("缴费提醒规则--主表批量删除失败");
                throw new McpException("缴费提醒规则--主表批量删除失败"+StringUtils.join(rulesIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缴费提醒规则--主表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmReminderRulesMainVo vo) {
        if(vo != null) {
            BbpmReminderRulesMainVo findVo = baseMapper.findByRuleName(vo);
            if(findVo != null && StringUtils.isNotBlank(findVo.getRulesId())){
                throw new McpException("规则名称已存在");
            }

            BbpmReminderRulesMainEntity entity = new BbpmReminderRulesMainEntity();
            BeanUtils.copyProperties(vo, entity);

            entity.setDownTime(new Date());

            if(StringUtils.isEmpty(entity.getRulesId())) {
                throw new McpException("缴费提醒规则--主表更新失败传入规则编号为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("缴费提醒规则--主表更新失败");
                throw new McpException("缴费提醒规则--主表更新失败"+entity.getRulesId());
            } else {
                if(!baseService.saveOperationHisById(entity.getRulesId(),2)) {
                    log.error("缴费提醒规则--主表更新后保存历史失败:" + entity.getRulesId());
                    throw new McpException("缴费提醒规则--主表更新后保存历史失败");
                }

                //先根据主表id删除子表内容，再新增
                bbpmReminderRulesSubMapper.deleteByRulesId(entity.getRulesId());
//                bbpmReminderRulesSubMapper.updateByRulesId(entity.getRulesId());
                //新增子表
                List<BbpmReminderRulesSubVo> bbpmReminderRulesSubVoList = vo.getBbpmReminderRulesSubVoList();
                for(int i=0;i <bbpmReminderRulesSubVoList.size();i++){
                    BbpmReminderRulesSubVo bbpmReminderRulesSubVo = bbpmReminderRulesSubVoList.get(i);
                    bbpmReminderRulesSubVo.setRulesId(entity.getRulesId());
                    bbpmReminderRulesSubVo.setSeqNum(i+1);
                }
                iBbpmReminderRulesSubService.insertBatchRecord(bbpmReminderRulesSubVoList);

            }
        } else {
            throw new McpException("缴费提醒规则--主表更新失败传入为空");
        }
    }

    /**
     *
     * 启用停用
     * @param vo
     */
    @Override
    public void enableDeactivate(BbpmReminderRulesMainVo vo) {
         if(vo != null){
             if(StringUtils.isBlank(vo.getRulesId())){
                 throw new McpException("rulesId不能为空");
             }
             if(StringUtils.isBlank(vo.getEnableStatus())){
                 throw new McpException("enableStatus不能为空");
             }
             BbpmReminderRulesMainEntity entity = new BbpmReminderRulesMainEntity();
             entity.setRulesId(vo.getRulesId());
             entity.setEnableStatus(vo.getEnableStatus());

             if(ReminderEnums.ENABLESTATUS_OK.getCode().equals(vo.getEnableStatus())){
                    entity.setActivationTime(new Date());
             }else {
                    entity.setDownTime(new Date());
             }

             if(!baseService.updateById(entity)) {
                 log.error("缴费提醒规则--主表更新失败");
                 throw new McpException("缴费提醒规则--主表更新失败"+entity.getRulesId());
             }

         }else {
             throw new McpException("传入参数为空");
         }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缴费提醒规则--主表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmReminderRulesMainVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReminderRulesMainEntity> entityList = new ArrayList<>();

            for (BbpmReminderRulesMainVo item:voList){
                BbpmReminderRulesMainEntity entity = new BbpmReminderRulesMainEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getRulesId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("缴费提醒规则--主表批量更新失败 存在规则编号为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("缴费提醒规则--主表批量更新失败");
                throw new McpException("缴费提醒规则--主表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getRulesId())).map(BbpmReminderRulesMainEntity::getRulesId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("缴费提醒规则--主表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("缴费提醒规则--主表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缴费提醒规则--主表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmReminderRulesMainVo vo) {
        if(vo != null) {
            BbpmReminderRulesMainEntity entity = new BbpmReminderRulesMainEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("缴费提醒规则--主表保存失败");
                throw new McpException("缴费提醒规则--主表保存失败"+entity.getRulesId());
            } else {
                if(!baseService.saveOperationHisById(entity.getRulesId(),4)) {
                    log.error("缴费提醒规则--主表保存后保存历史失败:" + entity.getRulesId());
                    throw new McpException("缴费提醒规则--主表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("缴费提醒规则--主表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缴费提醒规则--主表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmReminderRulesMainVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReminderRulesMainEntity> entityList = new ArrayList<>();

            for (BbpmReminderRulesMainVo item:voList){
                BbpmReminderRulesMainEntity entity = new BbpmReminderRulesMainEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("缴费提醒规则--主表批量保存失败");
                throw new McpException("缴费提醒规则--主表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getRulesId())).map(BbpmReminderRulesMainEntity::getRulesId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("缴费提醒规则--主表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("缴费提醒规则--主表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param rulesId 需要查询的规则编号
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmReminderRulesMainVo selectByIdRecord(String rulesId) {
        BbpmReminderRulesMainVo vo = new BbpmReminderRulesMainVo();

        if(!StringUtils.isEmpty(rulesId)) {
            BbpmReminderRulesMainEntity entity = baseService.selectById(rulesId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);

                //查询子表
                BbpmReminderRulesSubVo subVo = new BbpmReminderRulesSubVo();
                subVo.setRulesId(entity.getRulesId());
                List<BbpmReminderRulesSubVo> subVoList = bbpmReminderRulesSubMapper.selectByRulesIdList(subVo);
                vo.setBbpmReminderRulesSubVoList(subVoList);

                //字典转换
                mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmReminderRulesMainPageResultVo>> selectByPageRecord(BbpmReminderRulesMainPageVo vo) {
        List<BbpmReminderRulesMainPageResultVo> result = baseMapper.selectByPageCustom(vo);
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    @Override
    public List<UnpaidBillMainVo> listByPayableDate(ListByPayableDateVo vo) {
        //拼接参数请求工银
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<ListByPayableDateVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        List<UnpaidBillMainVo> unpaidBillMainVoList = new ArrayList<>();

        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.36查询未缴费账单列表接口请求参数json:" + jsonRequest);

//        String responseBody = "{\n" +
//                "    \"busiCode\": \"\",\n" +
//                "    \"code\": \"00000\",\n" +
//                "    \"data\": [\n" +
//                "        {\n" +
//                "            \"contractCode\": \"958S2309040001\",\n" +
//                "            \"unPaidMonthTotal\": 2,\n" +
//                "            \"unPaidMoney\": 3333,\n" +
//                "            \"unPaidBillDetailVOList\": [\n" +
//                "                {\n" +
//                "                    \"billCode\": 1698601613183959040,\n" +
//                "                    \"contractCode\": \"958S2309040001\",\n" +
//                "                    \"tenantName\": \"五一\",\n" +
//                "                    \"certNo\": \"130627199908023222\",\n" +
//                "                    \"tenantMobile\": \"18390659768\",\n" +
//                "                    \"projectName\": \"（土）**********土地储备项目\",\n" +
//                "                    \"buildingNo\": \"003\",\n" +
//                "                    \"unitNo\": \"00\",\n" +
//                "                    \"floorNo\": \"4/28\",\n" +
//                "                    \"roomNo\": \"0404\",\n" +
//                "                    \"chargeSubject\": \"01\",\n" +
//                "                    \"chargeStatus\": \"03\",\n" +
//                "                    \"accountingMonth\": \"2023-09\",\n" +
//                "                    \"chargePeriod\": 1,\n" +
//                "                    \"payableDate\": \"2023-09-04\",\n" +
//                "                    \"payableDateTmp\": \"2023-09-04 00:00:00\",\n" +
//                "                    \"payableMoney\": 5174.745,\n" +
//                "                    \"payedAmount\": 0,\n" +
//                "                    \"toBePaidMoney\": 5174.745,\n" +
//                "                    \"prepaymentAmount\": 0,\n" +
//                "                    \"targetAmount\": 0,\n" +
//                "                    \"changepayableMoney\": 5174.745,\n" +
//                "                    \"chargeStandardCny\": \"CNY\",\n" +
//                "                    \"chargeCycle\": \"01\",\n" +
//                "                    \"isProvision\": \"0\",\n" +
//                "                    \"chargeType\": \"01\",\n" +
//                "                    \"subsidyRadio\": null,\n" +
//                "                    \"subsidyMoney\": \"0.0000\",\n" +
//                "                    \"status\": \"01\",\n" +
//                "                    \"accountStatus\": \"02\",\n" +
//                "                    \"invoicingStatus\": \"02\",\n" +
//                "                    \"invoicingMoney\": 0,\n" +
//                "                    \"primaryChargeCode\": \"\",\n" +
//                "                    \"chargeStartDate\": \"2023-09-04\",\n" +
//                "                    \"chargeEndDate\": \"2023-10-03\",\n" +
//                "                    \"billPayStatus\": \"01\",\n" +
//                "                    \"tenantId\": \"PC-11111\",\n" +
//                "                    \"houseName\": \"香榭8号003号楼00单元0404\",\n" +
//                "                    \"projectId\": \"0123A10005\",\n" +
//                "                    \"deductionMoney\": 0,\n" +
//                "                    \"reletUnitName\": null,\n" +
//                "                    \"houseCount\": 1,\n" +
//                "                    \"payType\": null,\n" +
//                "                    \"payRate\": null,\n" +
//                "                    \"projectFormat\": \"01\",\n" +
//                "                    \"authorizedAgent\": null,\n" +
//                "                    \"authorizedAgentMobile\": null,\n" +
//                "                    \"chargeStandardMoney\": null,\n" +
//                "                    \"invoiceAbility\": \"01\",\n" +
//                "                    \"chargeOwner\": \"02\",\n" +
//                "                    \"payTime\": null,\n" +
//                "                    \"invoiceMoney\": 0,\n" +
//                "                    \"payableInvoiceMoney\": 5174.745,\n" +
//                "                    \"toBeInvoicedMoney\": 5174.745,\n" +
//                "                    \"redFlushMoney\": 0,\n" +
//                "                    \"refundMoney\": 0,\n" +
//                "                    \"orgMobile\": null,\n" +
//                "                    \"orgCertNo\": null,\n" +
//                "                    \"orgName\": null,\n" +
//                "                    \"orgCode\": null,\n" +
//                "                    \"contractBizId\": \"1698601613171376128\",\n" +
//                "                    \"leaseType\": \"01\",\n" +
//                "                    \"serviceMoney\": null\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"billCode\": 1698601613196541952,\n" +
//                "                    \"contractCode\": \"958S2309040001\",\n" +
//                "                    \"tenantName\": \"五一\",\n" +
//                "                    \"certNo\": \"130627199908023222\",\n" +
//                "                    \"tenantMobile\": \"18390659768\",\n" +
//                "                    \"projectName\": \"（土）**********土地储备项目\",\n" +
//                "                    \"buildingNo\": \"003\",\n" +
//                "                    \"unitNo\": \"00\",\n" +
//                "                    \"floorNo\": \"4/28\",\n" +
//                "                    \"roomNo\": \"0404\",\n" +
//                "                    \"chargeSubject\": \"01\",\n" +
//                "                    \"chargeStatus\": \"03\",\n" +
//                "                    \"accountingMonth\": \"2023-10\",\n" +
//                "                    \"chargePeriod\": 2,\n" +
//                "                    \"payableDate\": \"2023-10-04\",\n" +
//                "                    \"payableDateTmp\": \"2023-10-04 00:00:00\",\n" +
//                "                    \"payableMoney\": 5174.745,\n" +
//                "                    \"payedAmount\": 0,\n" +
//                "                    \"toBePaidMoney\": 5174.745,\n" +
//                "                    \"prepaymentAmount\": 0,\n" +
//                "                    \"targetAmount\": 0,\n" +
//                "                    \"changepayableMoney\": 5174.745,\n" +
//                "                    \"chargeStandardCny\": \"CNY\",\n" +
//                "                    \"chargeCycle\": \"01\",\n" +
//                "                    \"isProvision\": \"0\",\n" +
//                "                    \"chargeType\": \"01\",\n" +
//                "                    \"subsidyRadio\": null,\n" +
//                "                    \"subsidyMoney\": \"0.0000\",\n" +
//                "                    \"status\": \"01\",\n" +
//                "                    \"accountStatus\": \"02\",\n" +
//                "                    \"invoicingStatus\": \"02\",\n" +
//                "                    \"invoicingMoney\": 0,\n" +
//                "                    \"primaryChargeCode\": \"\",\n" +
//                "                    \"chargeStartDate\": \"2023-10-04\",\n" +
//                "                    \"chargeEndDate\": \"2023-11-03\",\n" +
//                "                    \"billPayStatus\": \"01\",\n" +
//                "                    \"tenantId\": \"PC-11111\",\n" +
//                "                    \"houseName\": \"香榭8号003号楼00单元0401\",\n" +
//                "                    \"projectId\": \"0123A10005\",\n" +
//                "                    \"deductionMoney\": 0,\n" +
//                "                    \"reletUnitName\": null,\n" +
//                "                    \"houseCount\": 1,\n" +
//                "                    \"payType\": null,\n" +
//                "                    \"payRate\": null,\n" +
//                "                    \"projectFormat\": \"01\",\n" +
//                "                    \"authorizedAgent\": null,\n" +
//                "                    \"authorizedAgentMobile\": null,\n" +
//                "                    \"chargeStandardMoney\": null,\n" +
//                "                    \"invoiceAbility\": \"01\",\n" +
//                "                    \"chargeOwner\": \"02\",\n" +
//                "                    \"payTime\": null,\n" +
//                "                    \"invoiceMoney\": 0,\n" +
//                "                    \"payableInvoiceMoney\": 5174.745,\n" +
//                "                    \"toBeInvoicedMoney\": 5174.745,\n" +
//                "                    \"redFlushMoney\": 0,\n" +
//                "                    \"refundMoney\": 0,\n" +
//                "                    \"orgMobile\": null,\n" +
//                "                    \"orgCertNo\": null,\n" +
//                "                    \"orgName\": null,\n" +
//                "                    \"orgCode\": null,\n" +
//                "                    \"contractBizId\": \"1698601613171376128\",\n" +
//                "                    \"leaseType\": \"01\",\n" +
//                "                    \"serviceMoney\": null\n" +
//                "                }\n" +
//                "            ]\n" +
//                "        }, \n" +
//                "        {\n" +
//                "            \"contractCode\": \"958S2xxxx\",\n" +
//                "            \"unPaidMonthTotal\": 1,\n" +
//                "            \"unPaidMoney\": 1111,\n" +
//                "            \"unPaidBillDetailVOList\": [\n" +
//                "                {\n" +
//                "                    \"billCode\": 1698601613183959040,\n" +
//                "                    \"contractCode\": \"958S2xxxx\",\n" +
//                "                    \"tenantName\": \"五一2\",\n" +
//                "                    \"certNo\": \"130627199908023222\",\n" +
//                "                    \"tenantMobile\": \"18504400471\",\n" +
//                "                    \"projectName\": \"（土）**********土地储备项目\",\n" +
//                "                    \"buildingNo\": \"003\",\n" +
//                "                    \"unitNo\": \"00\",\n" +
//                "                    \"floorNo\": \"4/28\",\n" +
//                "                    \"roomNo\": \"0404\",\n" +
//                "                    \"chargeSubject\": \"01\",\n" +
//                "                    \"chargeStatus\": \"03\",\n" +
//                "                    \"accountingMonth\": \"2023-09\",\n" +
//                "                    \"chargePeriod\": 1,\n" +
//                "                    \"payableDate\": \"2023-09-04\",\n" +
//                "                    \"payableDateTmp\": \"2023-09-04 00:00:00\",\n" +
//                "                    \"payableMoney\": 5174.745,\n" +
//                "                    \"payedAmount\": 0,\n" +
//                "                    \"toBePaidMoney\": 5174.745,\n" +
//                "                    \"prepaymentAmount\": 0,\n" +
//                "                    \"targetAmount\": 0,\n" +
//                "                    \"changepayableMoney\": 5174.745,\n" +
//                "                    \"chargeStandardCny\": \"CNY\",\n" +
//                "                    \"chargeCycle\": \"01\",\n" +
//                "                    \"isProvision\": \"0\",\n" +
//                "                    \"chargeType\": \"01\",\n" +
//                "                    \"subsidyRadio\": null,\n" +
//                "                    \"subsidyMoney\": \"0.0000\",\n" +
//                "                    \"status\": \"01\",\n" +
//                "                    \"accountStatus\": \"02\",\n" +
//                "                    \"invoicingStatus\": \"02\",\n" +
//                "                    \"invoicingMoney\": 0,\n" +
//                "                    \"primaryChargeCode\": \"\",\n" +
//                "                    \"chargeStartDate\": \"2023-09-04\",\n" +
//                "                    \"chargeEndDate\": \"2023-10-03\",\n" +
//                "                    \"billPayStatus\": \"01\",\n" +
//                "                    \"tenantId\": \"PC-222222\",\n" +
//                "                    \"houseName\": \"香榭8号003号楼00单元0404\",\n" +
//                "                    \"projectId\": \"0123A10005\",\n" +
//                "                    \"deductionMoney\": 0,\n" +
//                "                    \"reletUnitName\": null,\n" +
//                "                    \"houseCount\": 1,\n" +
//                "                    \"payType\": null,\n" +
//                "                    \"payRate\": null,\n" +
//                "                    \"projectFormat\": \"01\",\n" +
//                "                    \"authorizedAgent\": null,\n" +
//                "                    \"authorizedAgentMobile\": null,\n" +
//                "                    \"chargeStandardMoney\": null,\n" +
//                "                    \"invoiceAbility\": \"01\",\n" +
//                "                    \"chargeOwner\": \"02\",\n" +
//                "                    \"payTime\": null,\n" +
//                "                    \"invoiceMoney\": 0,\n" +
//                "                    \"payableInvoiceMoney\": 5174.745,\n" +
//                "                    \"toBeInvoicedMoney\": 5174.745,\n" +
//                "                    \"redFlushMoney\": 0,\n" +
//                "                    \"refundMoney\": 0,\n" +
//                "                    \"orgMobile\": null,\n" +
//                "                    \"orgCertNo\": null,\n" +
//                "                    \"orgName\": null,\n" +
//                "                    \"orgCode\": null,\n" +
//                "                    \"contractBizId\": \"1698601613171376128\",\n" +
//                "                    \"leaseType\": \"01\",\n" +
//                "                    \"serviceMoney\": null\n" +
//                "                }\n" +
//                "            ]\n" +
//                "        } \n" +
//                "    ],\n" +
//                "    \"message\": \"查询成功\"\n" +
//                "}";

        String responseBody = "";
        if (yecaiFeign){
            responseBody = bfipChargeFeignClient.listByPayableDate(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/bill/queryUnPaidBillList", parentRequest);
        }

        log.info("调用3.36查询未缴费账单列表接口返回:" + responseBody);

        if (StringUtils.isBlank(responseBody)) {
            return unpaidBillMainVoList;
        }

        FaceMdListResult<UnpaidBillMainVo> faceMdListResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdListResult.class);
        if (!("00000").equals(faceMdListResult.getCode())) {
            throw new McpException("调用3.36查询未缴费账单列表接口*提示:+"+faceMdListResult.getMessage()+",请求参数:[" + jsonRequest + "].*返回:" + responseBody);
        } else {
            if (faceMdListResult.getData() == null || faceMdListResult.getData() == null || faceMdListResult.getData().size() == 0) {
                log.info("调用3.36查询未缴费账单列表接口未查询到数据");
                return unpaidBillMainVoList;
            }

            unpaidBillMainVoList = JSON.parseArray(faceMdListResult.getData().toString(), UnpaidBillMainVo.class);

            for(UnpaidBillMainVo unpaidBillMainVo:unpaidBillMainVoList){
                List<UnpaidBillVo> billList = unpaidBillMainVo.getUnPaidBillDetailVOList();
                if(billList!=null && billList.size()>0){
                    for(UnpaidBillVo bill:billList){
                        bill.setBillCycle("第"+bill.getChargePeriod()+"期("+bill.getChargeStartDate()+"至"+bill.getChargeEndDate()+")");
                    }
                    //字典转换
                    mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(billList);
                }
            }


        }

        return unpaidBillMainVoList;
    }
}
