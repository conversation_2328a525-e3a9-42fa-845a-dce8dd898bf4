package com.bonc.ioc.bzf.business.payment.feign.feign;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.vo.BankRequestVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRuleParamsVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRuleResultVo;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 计费中心
 * @date 2022/12/12 9:52
 */
@FeignClient(contextId = "bzfYecaiCharge" ,value = "bfip-charge", configuration = FeignExceptionConfiguration.class)
public interface YecaiFeignClient {


	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0 查询计费规则列表接口
	 * <AUTHOR>
	 * @Date 2022/12/9
	 */
	@PostMapping(value = "/charge/v1/chargeRule/list")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo<List<ChargeRuleResultVo>> getChargeRuleList(@RequestBody BankRequestVo<ChargeRuleParamsVo> bankRequestVo);


	 /**
	  * @param bankRequestVo
	  * @return
	  * @version 1.0 获取计费结果接口
	  * <AUTHOR>
	  * @Date 2022/12/9
	  */
	@PostMapping(value = "/charge/v1/chargeRule/getChargeResult")
	@LogPoint(system = "bfip-charge")
	ChargeRespondVo getChargeResult(@RequestBody JSONObject bankRequestVo);


}
