package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 状态 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/9
 */
@Data
@ApiModel(value = "状态 vo实体", description = "状态 vo实体")
public class BbpmStatusVo extends McpBaseVo implements Serializable {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空", groups = {
            UpdateValidatorGroup.class
    })
    private String id;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @NotBlank(message = "状态不能为空", groups = {
            UpdateValidatorGroup.class
    })
    private String status;
}
