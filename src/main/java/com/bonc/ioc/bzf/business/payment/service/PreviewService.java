package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.vo.ChangeTrialPreviewBillsParamsVo;
import com.bonc.ioc.bzf.business.payment.vo.ChangeTrialPreviewBillsResultVo;
import com.bonc.ioc.bzf.business.payment.vo.ChangeTrialPreviewBillsResultV2Vo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsParamsVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsResultVo;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

public interface PreviewService {

    /**
     * 3.39. 商业合同账单预览接口
     * @param previewBillsParamsVo
     * @return
     */
    PreviewBillsResultVo getPreviewBills(PreviewBillsParamsVo previewBillsParamsVo);

    /**
     * 3.64. 合同变更试算接口
     * @param changeTrialPreviewBillsParamsVo
     * @return
     */
    List<ChangeTrialPreviewBillsResultV2Vo> changeTrial(ChangeTrialPreviewBillsParamsVo changeTrialPreviewBillsParamsVo);

}
