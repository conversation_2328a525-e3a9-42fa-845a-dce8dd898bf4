package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.vo.BillFivePhaseVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRuleResultVo;

import java.util.List;
import java.util.Map;


/**
 * 五期合同生成账单
 *
 */
public interface IBillFivePhaseService {

    /**
     *  selectRentRuleList 查询计费规则列表接口
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-06-26
     * @change
     * 2022-12-12 by gxp for init
     */
    List<ChargeRuleResultVo> selectChargeRuleList(String serviceType, String chargeSubjectNo);

    /**
     * 对接工银   公租房散租五期合同生成账单接口
     */
    String pubLooseRentFivePhase(BillFivePhaseVo vo);

    /**
     * 对接工银   管理协议候审期生成账单接口
     */
    String agreementPhase(BillFivePhaseVo vo);

    /**
     * 对接工银   趸租大合同候审期生成账单接口
     */
    String singlePhase(BillFivePhaseVo vo);
}
