package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName BaseVo
 * @Description 需要显示操作信息的请继承这个类
 * @AUTHOR 宋鑫
 * @Date 2022-12-08 18:14
 **/
@Data
public class BaseVo extends McpBaseVo implements java.io.Serializable {

        @ApiModelProperty( value = "创建用户" )
        private String createUser;

        @ApiModelProperty( value = "创建用户姓名")
        private String createUserName;

        @ApiModelProperty(value = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;

        @ApiModelProperty(value = "修改人")
        private String modifyUser;

        @ApiModelProperty( value = "修改人姓名" )
        private String modifyUserName;

        @ApiModelProperty(value = "修改时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date modifyTime;

}
