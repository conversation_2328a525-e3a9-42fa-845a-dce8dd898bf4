package com.bonc.ioc.bzf.business.payment.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.util.List;
@ApiModel(value="房屋信息", description="房屋信息")
@Data
public class PreviewBillsParamsRoomVo implements java.io.Serializable{

    @ApiModelProperty(value = "房源名称")
    private String houseName;

    @ApiModelProperty(value = "变更前商铺id")
    private String beforeHouseId;

    @ApiModelProperty(value = "房屋ID")
    private String houseId;

    @ApiModelProperty(value = "计费科目列表")
    private List<PreviewBillParamsRoomChargeSubjectVo> chargeSubjectList;

}
