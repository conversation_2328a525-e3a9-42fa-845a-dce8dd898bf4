package com.bonc.ioc.bzf.business.payment.result.create;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRuleSubParamsVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 计费科目列表 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
@Builder
public class ChargeSubjectParamsRequest {

    @ApiModelProperty(value = "计费科目编号")
    @NotBlank(message = "计费科目编号", groups = {UpdateValidatorGroup.class})
    private String chargeSubjectNo;

    @ApiModelProperty(value = "计费科目金额")
    @NotBlank(message = "计费科目金额", groups = {UpdateValidatorGroup.class})
    private BigDecimal chargeSubjectAmount;

    /**
     * 计费科目金额类型
     */
    @ApiModelProperty(value = "计费科目金额类型")
    private String amountType;

    @ApiModelProperty(value = "计费科目税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "个人计费科目税率")
    private BigDecimal personalTaxRate;


    @ApiModelProperty(value = "循环计费或单次计费")
    @NotBlank(message = "循环计费或单次计费", groups = {UpdateValidatorGroup.class})
    private String cyclicOrSingle;

    @ApiModelProperty(value = "计费科目收款周期")
    @NotBlank(message = "计费科目收款周期", groups = {UpdateValidatorGroup.class})
    private String chargeSubjectPeriod;


    @ApiModelProperty(value = "押金比例 只有02押金才有")
    @NotBlank(message = "押金比例", groups = {UpdateValidatorGroup.class})
    private Integer depositProportion;

    @ApiModelProperty(value = "优惠类别")
    private String preferentialCategory;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal preferentialAmount;

    @ApiModelProperty(value = "优惠比例")
    private BigDecimal preferentialRatio;

    @ApiModelProperty(value = "计费规则编号")
    private String chargeRuleNo;

    @ApiModelProperty(value = "计费规则名称")
    private String chargeRuleName;

    @ApiModelProperty(value = "参数列表")
//    private ChargeRuleSubParamsVo paramList;
    private JSONObject paramList;

    @ApiModelProperty(value = "参数值列表")
//    private ChargeRuleSubParamsVo paramValueList;
    private JSONObject paramValueList;

    @ApiModelProperty(value = "参数值列表")
    @Singular("increaseRulesParamsRequest")
    private List<IncreaseRulesParamsRequest> increaseRules;

    @ApiModelProperty(value = "优惠规则列表")
    @Singular("preferentRulesParamsRequest")
    private List<PreferentRulesParamsRequest> preferentRules;

    @ApiModelProperty(value = "分摊方式")
    private String shareType;
    @ApiModelProperty(value = "企业支付比例")
    private BigDecimal companyRate;
    @ApiModelProperty(value = "个人支付比例")
    private BigDecimal personalRate;
    @ApiModelProperty(value = "企业支付金额")
    private BigDecimal companyAmount;
    @ApiModelProperty(value = "个人支付金额")
    private BigDecimal personalAmount;

    //增值服务费新增字段
    @ApiModelProperty(value = "是否包含增值服务费int0不包含1包含")
    private Integer hasServiceFee;
    @ApiModelProperty(value = "增值服务费计费规则1月租金百分比2增值服务费单价3增值服务费固定值")
    private Integer serviceFeeChargeRule;
    @ApiModelProperty(value = "月租金百分比70.00代表70%")
    private String monthlyRentServiceFeeRatio;
    @ApiModelProperty(value = "增值服务费单价保留两位小数,例如 12.00")
    private String serviceFeePricePerSquareMeter;
    @ApiModelProperty(value = "增值服务费固定值保留两位小数，例如12.01")
    private String fixedServiceFee;

    /**
     * 分摊方式
     */
    @ApiModelProperty(value = "分摊方式")
    private String serviceFeeShareType;

    /**
     * 服务费企业支付金额
     */
    @ApiModelProperty(value = "服务费企业支付金额")
    private String serviceFeeCompanyAmount;

    /**
     * 服务费企业支付比例
     */
    @ApiModelProperty(value = "服务费企业支付比例")
    private String serviceFeeCompanyRate;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date chargeStartDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date chargeEndDate;

    /**
     * 首尾非整账期标识
     */
    @ApiModelProperty(value = "首尾非整账期标识")
    private String noCompleteType;

    @Override
    public String toString() {
        return "ChargeSubjectParamsRequest{" +
                "chargeSubjectNo='" + chargeSubjectNo + '\'' +
                ", chargeSubjectAmount=" + chargeSubjectAmount +
                ", amountType='" + amountType + '\'' +
                ", taxRate=" + taxRate +
                ", cyclicOrSingle='" + cyclicOrSingle + '\'' +
                ", chargeSubjectPeriod='" + chargeSubjectPeriod + '\'' +
                ", depositProportion=" + depositProportion +
                ", preferentialCategory='" + preferentialCategory + '\'' +
                ", preferentialAmount=" + preferentialAmount +
                ", preferentialRatio=" + preferentialRatio +
                ", chargeRuleNo='" + chargeRuleNo + '\'' +
                ", chargeRuleName='" + chargeRuleName + '\'' +
                ", paramList=" + paramList +
                ", paramValueList=" + paramValueList +
                ", increaseRules=" + increaseRules +
                ", preferentRules=" + preferentRules +
                ", shareType='" + shareType + '\'' +
                ", companyRate=" + companyRate +
                ", personalRate=" + personalRate +
                ", companyAmount=" + companyAmount +
                ", personalAmount=" + personalAmount +
                ", hasServiceFee=" + hasServiceFee +
                ", serviceFeeChargeRule=" + serviceFeeChargeRule +
                ", monthlyRentServiceFeeRatio='" + monthlyRentServiceFeeRatio + '\'' +
                ", serviceFeePricePerSquareMeter='" + serviceFeePricePerSquareMeter + '\'' +
                ", fixedServiceFee='" + fixedServiceFee + '\'' +
                ", serviceFeeCompanyAmount=" + serviceFeeCompanyAmount +
                ", serviceFeeCompanyRate=" + serviceFeeCompanyRate +
                '}';
    }
}
