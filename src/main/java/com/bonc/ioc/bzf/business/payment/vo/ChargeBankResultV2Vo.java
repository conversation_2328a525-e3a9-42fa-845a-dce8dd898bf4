package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeBankResultV2Vo {

    @ApiModelProperty(value = "银行支行名称")
    private String branchName;
    @ApiModelProperty(value = "银行总行名称")
    private String bankName;
    @ApiModelProperty(value = "银行账户")
    private String bankAccountNo;
    @ApiModelProperty(value = "银行户名")
    private String bankAccountName;


}
