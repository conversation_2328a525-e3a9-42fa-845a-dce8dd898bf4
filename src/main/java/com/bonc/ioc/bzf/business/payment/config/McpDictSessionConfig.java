package com.bonc.ioc.bzf.business.payment.config;

import com.bonc.ioc.bzf.business.payment.entity.BbpDictEntity;
import com.bonc.ioc.bzf.business.payment.service.IBbpDictService;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * McpDictSession配置
 *
 * <AUTHOR>
 * @date 2021/6/22 11:32
 * @change 2021/6/22 11:32 by qishenghe for init
 */
@Configuration
public class McpDictSessionConfig {

    /**
     * 字典（项目类型编码：PROJECT_TYPE_CODE）
     */
    public static final String DICT_PROJECT_TYPE_CODE = "PROJECT_TYPE_CODE";

    /**
     * 镜像字典（项目类型编码：IMAGE_PROJECT_TYPE_CODE）
     */
    public static final String DICT_IMAGE_PROJECT_TYPE_CODE = "IMAGE_PROJECT_TYPE_CODE";

    /**
     * ServiceDictServiceImpl
     */
    @Resource
    IBbpDictService iBbfuseDictService;


    /**
     * McpDictSession初始化
     *
     * @return McpDictSession
     * <AUTHOR>
     * @date 2021/6/8 16:27
     * @change 2021/6/8 16:27 by <EMAIL> for init
     * @since 1.0.0
     */
    @Bean
    public McpDictSession initMcpDictSession() {

        return McpDictSession.builder()
                .setMcpDictPackInitJob(() -> {
                    List<BbpDictEntity> list = iBbfuseDictService.selectAll();
                    return transFunction(list);
                }, this::transFunctionDictProjectTypeCode)
                .setAutoRefreshCron("0 0 0/10 * * ?")
                .setMcpDictUtilConfig("readOnly", Boolean.TRUE.toString())
                .getOrCreate();
    }

    /**
     * 【封装】处理字典数据集为标准化输入
     *
     * @param list 数据集
     * @return 处理结果
     * <AUTHOR>
     * @date 2021/6/8 13:43
     * @change 2021/6/8 13:43 by <EMAIL> for init
     * @since 1.0.0
     */
    private static List<McpDictEntity> transFunction(List<BbpDictEntity> list) {

        List<McpDictEntity> resultList = new ArrayList<>();
        for (BbpDictEntity single : list) {
            McpDictEntity singleResult = new McpDictEntity();
            singleResult.setDictCode(single.getTypeCode());
            singleResult.setDictName(single.getTypeName());
            singleResult.setSortNum(single.getSeqNum());
            singleResult.setCode(single.getCode());
            singleResult.setMeaning(single.getMeaning());
            singleResult.setExpand(single.getExpand());
            resultList.add(singleResult);
        }

        return resultList;
    }

    /**
     * 【封装】获取特殊字典（项目类型编码）
     */
    private List<McpDictEntity> transFunctionDictProjectTypeCode () {

        return null;
    }

}
