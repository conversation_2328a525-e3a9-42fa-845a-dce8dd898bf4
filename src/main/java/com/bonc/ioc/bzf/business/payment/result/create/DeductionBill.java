package com.bonc.ioc.bzf.business.payment.result.create;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * deductionBillList	抵扣列表
 */
@Data
public class DeductionBill implements Serializable {

    @ApiModelProperty(value = "商铺id")
    private String houseId;	//商铺id	 String	是

    @ApiModelProperty(value = "应缴日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date payableDate;	//应缴日期	LocalDate	是

    @ApiModelProperty(value = "计费科目税率")
    private BigDecimal deductionMoney;	//抵扣金额	BigDecimal	是

    @ApiModelProperty(value = "计费科目")
    private String chargeSubjectNo;	//计费科目String 是 (01租金 02押金 07物业费)


}
