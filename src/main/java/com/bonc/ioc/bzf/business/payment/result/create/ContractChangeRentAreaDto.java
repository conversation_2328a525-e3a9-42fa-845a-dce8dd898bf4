package com.bonc.ioc.bzf.business.payment.result.create;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.util.Date;
import java.util.List;

/**
 * 07 contractChangeRentAreaDTO (租金标准/面积变更)
 */
@Data
@Builder
public class ContractChangeRentAreaDto {

    private String requestDate;//请求时间 格式：YYYY-MM-DD 当前请求时间的下一期账单开始变更
    private String changeAccountingPeriodType;//更改账期类型	String	是		01变更当前账期 02 变更下一账期  目前仅支持散租
    private String agreementCode;//协议号
    private String contractType;//合同类型
    private String contractCode;//合同编码
    private String parentContractCode;//大合同编码
    private String houseId;//房屋Id

    @Singular("chargeSubjecParamsRequest")
    private List<ChargeSubjectParamsRequest> chargeSubjectList;//计费科目列表

    @Singular("furnitureRentalParamsRequest")
    private List<FurnitureRentalParamsRequest> furnitureRentalList;//家具租金列表
}
