package com.bonc.ioc.bzf.business.payment.vo;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 账单管理V2 实体类
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@ApiModel(value="BbpmBillManagementV2Vo对象", description="账单管理V2")
public class BbpmBillManagementVo extends McpBaseVo implements Serializable{


    /**
     * 账单ID
     */
    @ApiModelProperty(value = "账单ID")
    @NotNull(message = "账单ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String billId;

    /**
     * 账单编号
     */
    @ApiModelProperty(value = "账单编号")
                            private String billNo;

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
                            private String contractCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractNo;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
                            private String billCycle;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
                            private String tenantCode;

    /**
     * 租户名称
     */
    @ApiModelProperty(value = "租户名称")
                            private String tenantName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
                            private String certNo;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
                            private String tenantMobile;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 楼栋
     */
    @ApiModelProperty(value = "楼栋")
                            private String buildingNo;

    /**
     * 单元
     */
    @ApiModelProperty(value = "单元")
                            private String unitNo;

    /**
     * 所在楼层
     */
    @ApiModelProperty(value = "所在楼层")
                            private String floorNo;

    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
                            private String roomNo;

    /**
     * 账单对应的收费科目
     */
    @ApiModelProperty(value = "账单对应的收费科目")
                            private String billChargeSubject;

    /**
     * 账单对应的收费科目名称
     */
    @ApiModelProperty(value = "账单对应的收费科目名称")
                            private String billChargeSubjectName;

    /**
     * 计费科目编号(查询条件）
     */
    @ApiModelProperty(value = "计费科目编号(查询条件）")
                            private String chargeSubjectNo;

    /**
     * 账单缴费状态
     */
    @ApiModelProperty(value = "账单缴费状态")
                            private String billStatus;

    /**
     * 账单缴费状态名称
     */
    @ApiModelProperty(value = "账单缴费状态名称")
                            private String billStatusName;

    /**
     * 账单会计月
     */
    @ApiModelProperty(value = "账单会计月")
                            private String accountingMonth;

    /**
     * 账单周期(从1开始递增)
     */
    @ApiModelProperty(value = "账单周期(从1开始递增)")
                            private Integer chargeSubjectPeriod;

    /**
     * 应缴费日期
     */
    @ApiModelProperty(value = "应缴费日期")
                            private String payableDate;

    /**
     * 应缴费开始日期
     */
    @ApiModelProperty(value = "应缴费开始日期")
                            private String payableStartDate;

    /**
     * 应缴费结束日期
     */
    @ApiModelProperty(value = "应缴费结束日期")
                            private String payableEndDate;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
                            private BigDecimal shouldPayAmount;

    /**
     * 实缴金额
     */
    @ApiModelProperty(value = "实缴金额")
                            private BigDecimal payedAmount;

    /**
     * 待缴金额
     */
    @ApiModelProperty(value = "待缴金额")
                            private BigDecimal replacePayAmount;

    /**
     * 预收金额
     */
    @ApiModelProperty(value = "预收金额")
                            private BigDecimal prepaymentAmount;

    /**
     * 调整后应缴金额
     */
    @ApiModelProperty(value = "调整后应缴金额")
                            private BigDecimal changeShouldPayAmount;

    /**
     * 收款标准币种
     */
    @ApiModelProperty(value = "收款标准币种")
                            private String chargeStandardCny;

    /**
     * 收款标准币种名称
     */
    @ApiModelProperty(value = "收款标准币种名称")
                            private String chargeStandardCnyName;

    /**
     * 缴费周期
     */
    @ApiModelProperty(value = "缴费周期")
                            private String chargeCycle;

    /**
     * 缴费周期名称
     */
    @ApiModelProperty(value = "缴费周期名称")
                            private String chargeCycleName;

    /**
     * 是否计提
     */
    @ApiModelProperty(value = "是否计提")
                            private String isProvision;

    /**
     * 是否计提名称
     */
    @ApiModelProperty(value = "是否计提名称")
                            private String isProvisionName;

    /**
     * 是否循环计费
     */
    @ApiModelProperty(value = "是否循环计费")
                            private String chargeType;

    /**
     * 是否循环计费名称
     */
    @ApiModelProperty(value = "是否循环计费名称")
                            private String chargeTypeName;

    /**
     * 补贴比例
     */
    @ApiModelProperty(value = "补贴比例")
                            private String subsidyRadio;

    /**
     * 补贴金额
     */
    @ApiModelProperty(value = "补贴金额")
                            private String subsidyMoney;

    /**
     * 账单状态
     */
    @ApiModelProperty(value = "账单状态")
                            private String status;

    /**
     * 账单状态名称
     */
    @ApiModelProperty(value = "账单状态名称")
                            private String statusName;

    /**
     * 账单对账状态
     */
    @ApiModelProperty(value = "账单对账状态")
                            private String accountStatus;

    /**
     * 账单对账状态名称
     */
    @ApiModelProperty(value = "账单对账状态名称")
                            private String accountStatusName;

    /**
     * 开票状态
     */
    @ApiModelProperty(value = "开票状态")
                            private String invoicingStatus;

    /**
     * 开票状态名称
     */
    @ApiModelProperty(value = "开票状态名称")
                            private String invoicingStatusName;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
                            private BigDecimal invoiceMoney;

    /**
     * 关联收款单
     */
    @ApiModelProperty(value = "关联收款单")
                            private String primaryChargeCode;

    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
                            private String chargeSubjectBeginDate;

    /**
     * 收费科目终止日期
     */
    @ApiModelProperty(value = "收费科目终止日期")
                            private String chargeSubjectEndDate;

    /**
     * 账单支付状态
     */
    @ApiModelProperty(value = "账单支付状态")
                            private String billPayStatus;

    /**
     * 账单支付状态名称
     */
    @ApiModelProperty(value = "账单支付状态名称")
                            private String billPayStatusName;

    /**
     * 是否计算退租当月应退租金
     */
    @ApiModelProperty(value = "是否计算退租当月应退租金")
                            private String calCurrentMonth;

    /**
     * 租户实际退场日期
     */
    @ApiModelProperty(value = "租户实际退场日期")
                            private String exitDate;

    /**
     * 全量分页标识
     */
    @ApiModelProperty(value = "全量分页标识")
                            private String fullPage;

//    /**
//     * page_size
//     */
//    @ApiModelProperty(value = "page_size")
//                            private Integer pageSize;

    /**
     * page_no
     */
    @ApiModelProperty(value = "page_no")
                            private Integer pageNo;

    /**
     * ext1
     */
    @ApiModelProperty(value = "ext1")
                            private String ext1;

    /**
     * ext2
     */
    @ApiModelProperty(value = "ext2")
                            private String ext2;

    /**
     * ext3
     */
    @ApiModelProperty(value = "ext3")
                            private String ext3;

    /**
     * ext4
     */
    @ApiModelProperty(value = "ext4")
                            private String ext4;

    /**
     * ext5
     */
    @ApiModelProperty(value = "ext5")
                            private String ext5;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;
    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String houseName;

    /**
     * 主收款单标识
     */
    @ApiModelProperty(value = "主收款单标识")
    private String primaryChargeCodeFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "抵扣金额")
    private BigDecimal deductionMoney;

    @ApiModelProperty(value = "应退金额--押金条时使用(我们自己计算)")
    private BigDecimal refundableAmount;

    @ApiModelProperty(value = "已退金额--押金条时使用(我们自己计算)")
    private BigDecimal refundedAmount;

    @ApiModelProperty(value = "收费科目结束时间")
    private String chargeEndDate;
    @ApiModelProperty(value = "是否有预收01无预收02有预收")
    private String isHavePreCharge;
    //企业相关
    @ApiModelProperty(value = "个人/企业标识 01企业02个人")
    private String chargeOwner;
    @ApiModelProperty(value = "趸租单位名称")
    private String reletUnitName;
    @ApiModelProperty(value = "签约房源套数")
    private Integer houseCount;
    @ApiModelProperty(value = "分摊方式1 按比例分摊2 按金额分摊3 企业全额支付4 个人全额支付")
    @McpDictPoint(dictCode = "BILLING_YC_PAYTYPE",overTransCopyTo = "payTypeName")
    private String payType;
    @ApiModelProperty(value = "分摊比例")
    private BigDecimal payRate;
    @ApiModelProperty(value = "分摊方式名称")
    private String payTypeName;

    @ApiModelProperty(value = "项目业态")
    @McpDictPoint(dictCode = "BILLING_PROJECT_FORMAT", overTransCopyTo = "projectFormatName")
    private String projectFormat;

    @ApiModelProperty(value = "发票可开具能力01:可开具 02:不可开具")
    private String invoiceAbility;
    @ApiModelProperty(value = "待开票金额")
    private BigDecimal toBeInvoicedMoney;

//    @ApiModelProperty(value = "账单总额")
//    private BigDecimal billTotalMoney;
    @ApiModelProperty(value = "收款标准金额")
    private BigDecimal  chargeStandardMoney	;

    @ApiModelProperty(value = "项目业态名称")
    private String projectFormatName;

    @ApiModelProperty(value = "委托代理人")
    private String authorizedAgent ;
    @ApiModelProperty(value = "委托代理人电话")
    private String  authorizedAgentMobile ;

    @ApiModelProperty(value = "增值服务费")
    private BigDecimal  serviceMoney;

    @ApiModelProperty(value = "收款金额")
    private BigDecimal  targetAmount;

    @ApiModelProperty(value = "租赁类型(合同类型)01散租02趸租大合同03趸租管理协议04其他")
    @McpDictPoint(dictCode = "BILL_LEASETYPE", overTransCopyTo = "leaseTypeName")
    private String  leaseType ;
    @ApiModelProperty(value = "租赁类型名称")
    private String  leaseTypeName ;

    public String getLeaseType() {
        return leaseType;
    }

    public void setLeaseType(String leaseType) {
        this.leaseType = leaseType;
    }

    //手动报盘未缴费账单查询增加的2个字段
    @ApiModelProperty(value = "证件类型")
    @McpDictPoint(dictCode = "WHITELIST_CERTIFICATE_TYPE_CODE")
    private String  certType;
    @ApiModelProperty(value = "未缴金额总计")
    private BigDecimal  totalReplacePayAmount;

    @ApiModelProperty(value = "是否支持代扣0:不支持，1:支持")
    @McpDictPoint(dictCode = "BILLING_IS_WITHHOLD", overTransCopyTo = "isWithholdName")
    private String isWithhold;
    @ApiModelProperty(value = "是否支持代扣名称")
    private String isWithholdName;
    @ApiModelProperty(value = "收款时间")
    private String chargeTime;
    @ApiModelProperty(value = "未对平金额")
    private BigDecimal unevenMoney;

    @ApiModelProperty(value = "收费科目开始时间")
    private String chargeStartDate;
    @ApiModelProperty(value = "是否有增值服务费")
    @McpDictPoint(dictCode = "BILL_SERVICEMONEYFLAG", overTransCopyTo = "serviceMoneyFlagName")
    private String serviceMoneyFlag;
    @ApiModelProperty(value = "是否有增值服务费名称")
    private String serviceMoneyFlagName;

    @ApiModelProperty(value = "APP支付交易流水号")
    private String appReqNo;

    public String getAppReqNo() {
        return appReqNo;
    }

    public void setAppReqNo(String appReqNo) {
        this.appReqNo = appReqNo;
    }

    public String getLeaseTypeName() {
        return leaseTypeName;
    }

    public void setLeaseTypeName(String leaseTypeName) {
        this.leaseTypeName = leaseTypeName;
    }

    public String getServiceMoneyFlagName() {
        return serviceMoneyFlagName;
    }

    public void setServiceMoneyFlagName(String serviceMoneyFlagName) {
        this.serviceMoneyFlagName = serviceMoneyFlagName;
    }

    public String getChargeStartDate() {
        return chargeStartDate;
    }

    public void setChargeStartDate(String chargeStartDate) {
        this.chargeStartDate = chargeStartDate;
    }

    public String getServiceMoneyFlag() {
        return serviceMoneyFlag;
    }

    public void setServiceMoneyFlag(String serviceMoneyFlag) {
        this.serviceMoneyFlag = serviceMoneyFlag;
    }

    public String getIsWithhold() {
        return isWithhold;
    }

    public void setIsWithhold(String isWithhold) {
        this.isWithhold = isWithhold;
    }

    public String getIsWithholdName() {
        return isWithholdName;
    }

    public void setIsWithholdName(String isWithholdName) {
        this.isWithholdName = isWithholdName;
    }

    public String getChargeTime() {
        return chargeTime;
    }

    public void setChargeTime(String chargeTime) {
        this.chargeTime = chargeTime;
    }

    public BigDecimal getUnevenMoney() {
        return unevenMoney;
    }

    public void setUnevenMoney(BigDecimal unevenMoney) {
        this.unevenMoney = unevenMoney;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public BigDecimal getTotalReplacePayAmount() {
        return totalReplacePayAmount;
    }

    public void setTotalReplacePayAmount(BigDecimal totalReplacePayAmount) {
        this.totalReplacePayAmount = totalReplacePayAmount;
    }

    public BigDecimal getTargetAmount() {
        return targetAmount;
    }

    public void setTargetAmount(BigDecimal targetAmount) {
        this.targetAmount = targetAmount;
    }

    public BigDecimal getServiceMoney() {
        return serviceMoney;
    }

    public void setServiceMoney(BigDecimal serviceMoney) {
        this.serviceMoney = serviceMoney;
    }

    public String getAuthorizedAgent() {
        return authorizedAgent;
    }

    public void setAuthorizedAgent(String authorizedAgent) {
        this.authorizedAgent = authorizedAgent;
    }

    public String getAuthorizedAgentMobile() {
        return authorizedAgentMobile;
    }

    public void setAuthorizedAgentMobile(String authorizedAgentMobile) {
        this.authorizedAgentMobile = authorizedAgentMobile;
    }

    public BigDecimal getChargeStandardMoney() {
        return chargeStandardMoney;
    }

    public void setChargeStandardMoney(BigDecimal chargeStandardMoney) {
        this.chargeStandardMoney = chargeStandardMoney;
    }
//    public BigDecimal getBillTotalMoney() {
//        return billTotalMoney;
//    }
//
//    public void setBillTotalMoney(BigDecimal billTotalMoney) {
//        this.billTotalMoney = billTotalMoney;
//    }

    public String getProjectFormatName() {
        return projectFormatName;
    }

    public void setProjectFormatName(String projectFormatName) {
        this.projectFormatName = projectFormatName;
    }

    public BigDecimal getToBeInvoicedMoney() {
        return toBeInvoicedMoney;
    }

    public void setToBeInvoicedMoney(BigDecimal toBeInvoicedMoney) {
        this.toBeInvoicedMoney = toBeInvoicedMoney;
    }
    public String getInvoiceAbility() {
        return invoiceAbility;
    }

    public void setInvoiceAbility(String invoiceAbility) {
        this.invoiceAbility = invoiceAbility;
    }

    public String getProjectFormat() {
        return projectFormat;
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = projectFormat;
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    public String getChargeEndDate() {
        return chargeEndDate;
    }

    public void setChargeEndDate(String chargeEndDate) {
        this.chargeEndDate = chargeEndDate;
    }

    public String getIsHavePreCharge() {
        return isHavePreCharge;
    }

    public void setIsHavePreCharge(String isHavePreCharge) {
        this.isHavePreCharge = isHavePreCharge;
    }

    public String getChargeOwner() {
        return chargeOwner;
    }

    public void setChargeOwner(String chargeOwner) {
        this.chargeOwner = chargeOwner;
    }

    public String getReletUnitName() {
        return reletUnitName;
    }

    public void setReletUnitName(String reletUnitName) {
        this.reletUnitName = reletUnitName;
    }

    public Integer getHouseCount() {
        return houseCount;
    }

    public void setHouseCount(Integer houseCount) {
        this.houseCount = houseCount;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public BigDecimal getPayRate() {
        return payRate;
    }

    public void setPayRate(BigDecimal payRate) {
        this.payRate = payRate;
    }


    public BigDecimal getDeductionMoney() {
        return deductionMoney;
    }

    public void setDeductionMoney(BigDecimal deductionMoney) {
        this.deductionMoney = deductionMoney;
    }

    public BigDecimal getRefundableAmount() {
        return refundableAmount;
    }

    public void setRefundableAmount(BigDecimal refundableAmount) {
        this.refundableAmount = refundableAmount;
    }

    public BigDecimal getRefundedAmount() {
        return refundedAmount;
    }

    public void setRefundedAmount(BigDecimal refundedAmount) {
        this.refundedAmount = refundedAmount;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getPrimaryChargeCodeFlag() {
        return primaryChargeCodeFlag;
    }

    public void setPrimaryChargeCodeFlag(String primaryChargeCodeFlag) {
        this.primaryChargeCodeFlag = primaryChargeCodeFlag;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    /**
     * @return 账单ID
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 账单编号
     */
    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    /**
     * @return 合同ID
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    /**
     * @return 账单周期
     */
    public String getBillCycle() {
        return billCycle;
    }

    public void setBillCycle(String billCycle) {
        this.billCycle = billCycle;
    }

    /**
     * @return 租户ID
     */
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    /**
     * @return 租户名称
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 证件号码
     */
    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    /**
     * @return 联系电话
     */
    public String getTenantMobile() {
        return tenantMobile;
    }

    public void setTenantMobile(String tenantMobile) {
        this.tenantMobile = tenantMobile;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 楼栋
     */
    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    /**
     * @return 单元
     */
    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    /**
     * @return 所在楼层
     */
    public String getFloorNo() {
        return floorNo;
    }

    public void setFloorNo(String floorNo) {
        this.floorNo = floorNo;
    }

    /**
     * @return 门牌号
     */
    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    /**
     * @return 账单对应的收费科目
     */
    public String getBillChargeSubject() {
        return billChargeSubject;
    }

    public void setBillChargeSubject(String billChargeSubject) {
        this.billChargeSubject = billChargeSubject;
    }

    /**
     * @return 账单对应的收费科目名称
     */
    public String getBillChargeSubjectName() {
        return billChargeSubjectName;
    }

    public void setBillChargeSubjectName(String billChargeSubjectName) {
        this.billChargeSubjectName = billChargeSubjectName;
    }

    /**
     * @return 计费科目编号(查询条件）
     */
    public String getChargeSubjectNo() {
        return chargeSubjectNo;
    }

    public void setChargeSubjectNo(String chargeSubjectNo) {
        this.chargeSubjectNo = chargeSubjectNo;
    }

    /**
     * @return 账单缴费状态
     */
    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    /**
     * @return 账单缴费状态名称
     */
    public String getBillStatusName() {
        return billStatusName;
    }

    public void setBillStatusName(String billStatusName) {
        this.billStatusName = billStatusName;
    }

    /**
     * @return 账单会计月
     */
    public String getAccountingMonth() {
        return accountingMonth;
    }

    public void setAccountingMonth(String accountingMonth) {
        this.accountingMonth = accountingMonth;
    }

    /**
     * @return 账单周期(从1开始递增)
     */
    public Integer getChargeSubjectPeriod() {
        return chargeSubjectPeriod;
    }

    public void setChargeSubjectPeriod(Integer chargeSubjectPeriod) {
        this.chargeSubjectPeriod = chargeSubjectPeriod;
    }

    /**
     * @return 应缴费日期
     */
    public String getPayableDate() {
        return payableDate;
    }

    public void setPayableDate(String payableDate) {
        this.payableDate = payableDate;
    }

    /**
     * @return 应缴费开始日期
     */
    public String getPayableStartDate() {
        return payableStartDate;
    }

    public void setPayableStartDate(String payableStartDate) {
        this.payableStartDate = payableStartDate;
    }

    /**
     * @return 应缴费结束日期
     */
    public String getPayableEndDate() {
        return payableEndDate;
    }

    public void setPayableEndDate(String payableEndDate) {
        this.payableEndDate = payableEndDate;
    }

    /**
     * @return 应缴金额
     */
    public BigDecimal getShouldPayAmount() {
        return shouldPayAmount;
    }

    public void setShouldPayAmount(BigDecimal shouldPayAmount) {
        this.shouldPayAmount = shouldPayAmount;
    }

    /**
     * @return 实缴金额
     */
    public BigDecimal getPayedAmount() {
        return payedAmount;
    }

    public void setPayedAmount(BigDecimal payedAmount) {
        this.payedAmount = payedAmount;
    }

    /**
     * @return 待缴金额
     */
    public BigDecimal getReplacePayAmount() {
        return replacePayAmount;
    }

    public void setReplacePayAmount(BigDecimal replacePayAmount) {
        this.replacePayAmount = replacePayAmount;
    }

    /**
     * @return 预收金额
     */
    public BigDecimal getPrepaymentAmount() {
        return prepaymentAmount;
    }

    public void setPrepaymentAmount(BigDecimal prepaymentAmount) {
        this.prepaymentAmount = prepaymentAmount;
    }

    /**
     * @return 调整后应缴金额
     */
    public BigDecimal getChangeShouldPayAmount() {
        return changeShouldPayAmount;
    }

    public void setChangeShouldPayAmount(BigDecimal changeShouldPayAmount) {
        this.changeShouldPayAmount = changeShouldPayAmount;
    }

    /**
     * @return 收款标准币种
     */
    public String getChargeStandardCny() {
        return chargeStandardCny;
    }

    public void setChargeStandardCny(String chargeStandardCny) {
        this.chargeStandardCny = chargeStandardCny;
    }

    /**
     * @return 收款标准币种名称
     */
    public String getChargeStandardCnyName() {
        return chargeStandardCnyName;
    }

    public void setChargeStandardCnyName(String chargeStandardCnyName) {
        this.chargeStandardCnyName = chargeStandardCnyName;
    }

    /**
     * @return 缴费周期
     */
    public String getChargeCycle() {
        return chargeCycle;
    }

    public void setChargeCycle(String chargeCycle) {
        this.chargeCycle = chargeCycle;
    }

    /**
     * @return 缴费周期名称
     */
    public String getChargeCycleName() {
        return chargeCycleName;
    }

    public void setChargeCycleName(String chargeCycleName) {
        this.chargeCycleName = chargeCycleName;
    }

    /**
     * @return 是否计提
     */
    public String getIsProvision() {
        return isProvision;
    }

    public void setIsProvision(String isProvision) {
        this.isProvision = isProvision;
    }

    /**
     * @return 是否计提名称
     */
    public String getIsProvisionName() {
        return isProvisionName;
    }

    public void setIsProvisionName(String isProvisionName) {
        this.isProvisionName = isProvisionName;
    }

    /**
     * @return 是否循环计费
     */
    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    /**
     * @return 是否循环计费名称
     */
    public String getChargeTypeName() {
        return chargeTypeName;
    }

    public void setChargeTypeName(String chargeTypeName) {
        this.chargeTypeName = chargeTypeName;
    }

    /**
     * @return 补贴比例
     */
    public String getSubsidyRadio() {
        return subsidyRadio;
    }

    public void setSubsidyRadio(String subsidyRadio) {
        this.subsidyRadio = subsidyRadio;
    }

    /**
     * @return 补贴金额
     */
    public String getSubsidyMoney() {
        return subsidyMoney;
    }

    public void setSubsidyMoney(String subsidyMoney) {
        this.subsidyMoney = subsidyMoney;
    }

    /**
     * @return 账单状态
     */
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return 账单状态名称
     */
    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    /**
     * @return 账单对账状态
     */
    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    /**
     * @return 账单对账状态名称
     */
    public String getAccountStatusName() {
        return accountStatusName;
    }

    public void setAccountStatusName(String accountStatusName) {
        this.accountStatusName = accountStatusName;
    }

    /**
     * @return 开票状态
     */
    public String getInvoicingStatus() {
        return invoicingStatus;
    }

    public void setInvoicingStatus(String invoicingStatus) {
        this.invoicingStatus = invoicingStatus;
    }

    /**
     * @return 开票状态名称
     */
    public String getInvoicingStatusName() {
        return invoicingStatusName;
    }

    public void setInvoicingStatusName(String invoicingStatusName) {
        this.invoicingStatusName = invoicingStatusName;
    }

    /**
     * @return 开票金额
     */
    public BigDecimal getInvoiceMoney() {
        return invoiceMoney;
    }

    public void setInvoiceMoney(BigDecimal invoiceMoney) {
        this.invoiceMoney = invoiceMoney;
    }

    /**
     * @return 关联收款单
     */
    public String getPrimaryChargeCode() {
        return primaryChargeCode;
    }

    public void setPrimaryChargeCode(String primaryChargeCode) {
        this.primaryChargeCode = primaryChargeCode;
    }

    /**
     * @return 收费科目起始日期
     */
    public String getChargeSubjectBeginDate() {
        return chargeSubjectBeginDate;
    }

    public void setChargeSubjectBeginDate(String chargeSubjectBeginDate) {
        this.chargeSubjectBeginDate = chargeSubjectBeginDate;
    }

    /**
     * @return 收费科目终止日期
     */
    public String getChargeSubjectEndDate() {
        return chargeSubjectEndDate;
    }

    public void setChargeSubjectEndDate(String chargeSubjectEndDate) {
        this.chargeSubjectEndDate = chargeSubjectEndDate;
    }

    /**
     * @return 账单支付状态
     */
    public String getBillPayStatus() {
        return billPayStatus;
    }

    public void setBillPayStatus(String billPayStatus) {
        this.billPayStatus = billPayStatus;
    }

    /**
     * @return 账单支付状态名称
     */
    public String getBillPayStatusName() {
        return billPayStatusName;
    }

    public void setBillPayStatusName(String billPayStatusName) {
        this.billPayStatusName = billPayStatusName;
    }

    /**
     * @return 是否计算退租当月应退租金
     */
    public String getCalCurrentMonth() {
        return calCurrentMonth;
    }

    public void setCalCurrentMonth(String calCurrentMonth) {
        this.calCurrentMonth = calCurrentMonth;
    }

    /**
     * @return 租户实际退场日期
     */
    public String getExitDate() {
        return exitDate;
    }

    public void setExitDate(String exitDate) {
        this.exitDate = exitDate;
    }

    /**
     * @return 全量分页标识
     */
    public String getFullPage() {
        return fullPage;
    }

    public void setFullPage(String fullPage) {
        this.fullPage = fullPage;
    }

//    /**
//     * @return page_size
//     */
//    public Integer getPageSize() {
//        return pageSize;
//    }
//
//    public void setPageSize(Integer pageSize) {
//        this.pageSize = pageSize;
//    }

    /**
     * @return page_no
     */
    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    /**
     * @return ext1
     */
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * @return ext2
     */
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * @return ext3
     */
    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    /**
     * @return ext4
     */
    public String getExt4() {
        return ext4;
    }

    public void setExt4(String ext4) {
        this.ext4 = ext4;
    }

    /**
     * @return ext5
     */
    public String getExt5() {
        return ext5;
    }

    public void setExt5(String ext5) {
        this.ext5 = ext5;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmBillManagementV2Vo{" +
            "billId=" + billId +
            ", billNo=" + billNo +
            ", contractCode=" + contractCode +
            ", contractNo=" + contractNo +
            ", billCycle=" + billCycle +
            ", tenantCode=" + tenantCode +
            ", tenantName=" + tenantName +
            ", certNo=" + certNo +
            ", tenantMobile=" + tenantMobile +
            ", projectName=" + projectName +
            ", buildingNo=" + buildingNo +
            ", unitNo=" + unitNo +
            ", floorNo=" + floorNo +
            ", roomNo=" + roomNo +
            ", billChargeSubject=" + billChargeSubject +
            ", billChargeSubjectName=" + billChargeSubjectName +
            ", chargeSubjectNo=" + chargeSubjectNo +
            ", billStatus=" + billStatus +
            ", billStatusName=" + billStatusName +
            ", accountingMonth=" + accountingMonth +
            ", chargeSubjectPeriod=" + chargeSubjectPeriod +
            ", payableDate=" + payableDate +
            ", payableStartDate=" + payableStartDate +
            ", payableEndDate=" + payableEndDate +
            ", shouldPayAmount=" + shouldPayAmount +
            ", payedAmount=" + payedAmount +
            ", replacePayAmount=" + replacePayAmount +
            ", prepaymentAmount=" + prepaymentAmount +
            ", changeShouldPayAmount=" + changeShouldPayAmount +
            ", chargeStandardCny=" + chargeStandardCny +
            ", chargeStandardCnyName=" + chargeStandardCnyName +
            ", chargeCycle=" + chargeCycle +
            ", chargeCycleName=" + chargeCycleName +
            ", isProvision=" + isProvision +
            ", isProvisionName=" + isProvisionName +
            ", chargeType=" + chargeType +
            ", chargeTypeName=" + chargeTypeName +
            ", subsidyRadio=" + subsidyRadio +
            ", subsidyMoney=" + subsidyMoney +
            ", status=" + status +
            ", statusName=" + statusName +
            ", accountStatus=" + accountStatus +
            ", accountStatusName=" + accountStatusName +
            ", invoicingStatus=" + invoicingStatus +
            ", invoicingStatusName=" + invoicingStatusName +
            ", invoiceMoney=" + invoiceMoney +
            ", primaryChargeCode=" + primaryChargeCode +
            ", chargeSubjectBeginDate=" + chargeSubjectBeginDate +
            ", chargeSubjectEndDate=" + chargeSubjectEndDate +
            ", billPayStatus=" + billPayStatus +
            ", billPayStatusName=" + billPayStatusName +
            ", calCurrentMonth=" + calCurrentMonth +
            ", exitDate=" + exitDate +
            ", fullPage=" + fullPage +
            ", pageNo=" + pageNo +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", ext3=" + ext3 +
            ", ext4=" + ext4 +
            ", ext5=" + ext5 +
            ", delFlag=" + delFlag +
        "}";
    }
}
