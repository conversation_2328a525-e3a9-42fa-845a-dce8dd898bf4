package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCashCollectionVoucherMapper;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCollectionMapper;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.YecaiFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.result.create.ChargeSubjectParamsRequest;
import com.bonc.ioc.bzf.business.payment.service.*;
import com.bonc.ioc.bzf.business.payment.utils.RedisDistributedId;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class BillFivePhaseServiceImpl implements IBillFivePhaseService {

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmCollectionMapper baseMapper;

    @Resource
    private BbpmCashCollectionVoucherMapper bbpmCashCollectionVoucherMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmCollectionService baseService;

    @Resource
    private IBbpmBillCollectionRelationshipService iBbpmBillCollectionRelationshipService;

    @Resource
    private McpDictSession mcpDictSession;

    @Autowired
    RedisDistributedId redisDistributedId;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IBbpmBillCollectionDetailsService iBbpmBillCollectionDetailsService;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Value("${export.maxSize}")
    private Integer exportMaxSize;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private YecaiFeignClient yecaiFeignClient;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    @Resource
    private IBbpmBillManagementService iBbpmBillManagementService;

    @Resource
    private RestTemplateUtil restTemplateUtil ;

    @Override
    public List<ChargeRuleResultVo> selectChargeRuleList(String serviceType, String chargeSubjectNo) {
        ChargeRuleParamsVo chargeRuleParamsVo  = new ChargeRuleParamsVo();
        BankRequestVo<ChargeRuleParamsVo> params= new BankRequestVo();

        chargeRuleParamsVo.setServiceType(serviceType);
        chargeRuleParamsVo.setChargeSubjectNo(chargeSubjectNo);

        params.setData(chargeRuleParamsVo);
        ChargeRespondVo<List<ChargeRuleResultVo>> result ;
        log.info("调用月租金计算规则参数："+params.toString());
        if(yecaiFeign){
            result = yecaiFeignClient.getChargeRuleList(params);
        }else{
            String url=yecaiUrl+"/charge/v1/chargeRule/list";
            result = restTemplateUtil.postJsonDataByVo(url,params);
        }
        if(!("00000").equals(result.getCode()) || result.getData() == null){
            throw new McpException("*调用获取租金结算规则失败");
        }else {
            return result.getData();
        }
    }


    /**
     * 公租房散租五期合同生成账单接口
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String pubLooseRentFivePhase(BillFivePhaseVo vo) {

        String code = null;
        //计费科目列表
        List<ChargeSubjectParamsRequest> chargeSubjectList = new ArrayList<>();

        for(ChargeSubjectVo chargeSubjectVo : vo.getChargeSubjectList()) {

            Map<String,Object> paramsVo = chargeSubjectVo.getParamList();
            Map<String,Object> paramValues = chargeSubjectVo.getParamValueList();
            JSONObject paramList = null;
            JSONObject paramValueList = null;
            if(paramsVo != null){
                paramList = (JSONObject) JSONObject.toJSON(paramsVo);
            }
            if(paramValues != null){
                paramValueList = (JSONObject) JSONObject.toJSON(paramValues);
            }
            ChargeSubjectParamsRequest chargeSubjectParamsRequest = ChargeSubjectParamsRequest.builder()
                    .chargeSubjectNo(chargeSubjectVo.getChargeSubjectNo())
                    .cyclicOrSingle(chargeSubjectVo.getCyclicOrSingle())
                    .chargeSubjectPeriod(chargeSubjectVo.getChargeSubjectPeriod())
                    .chargeRuleNo(chargeSubjectVo.getChargeRuleNo())
                    .chargeRuleName(chargeSubjectVo.getChargeRuleName())
                    .paramList(paramList)
                    .paramValueList(paramValueList)
                    .build();
            chargeSubjectList.add(chargeSubjectParamsRequest);
        }

        //五期实体入参
        FivePhaseRequest fivePhaseRequest = FivePhaseRequest.builder()
                .originalContractId(vo.getOriginalContractId())
                .contractStatus(vo.getContractStatus())
                .periodBeginDate(vo.getPeriodBeginDate())
                .periodEndDate(vo.getPeriodEndDate())
                .projectId(vo.getProjectId())
                .chargeSubjectList(chargeSubjectList)
                .build();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<FivePhaseRequest> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(fivePhaseRequest);

        log.info("公租房散租五期合同生成账单接口:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("公租房散租五期合同生成账单接口json:" + jsonRequest);

        //工银接口暂无
        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipChargeFeignClient.pubLooseRentFivePhase(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/bill/pubLooseRentFivePhase", parentRequest);
        }
        log.info("调用工银公租房散租五期合同生成账单接口返回结果为:" + responseBody);

        FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银公租房散租五期合同生成账单接口失败:"+responseBody);
            throw new McpException(faceMdMapResult.getMessage());
        }

        return faceMdMapResult.getCode();
        //return "00000";
    }

    /**
     * 管理协议候审期生成账单接口
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String agreementPhase(BillFivePhaseVo vo) {

        String code = null;
        //计费科目列表
        List<ChargeSubjectParamsRequest> chargeSubjectList = new ArrayList<>();

        for(ChargeSubjectVo chargeSubjectVo : vo.getChargeSubjectList()) {

            Map<String,Object> paramsVo = chargeSubjectVo.getParamList();
            Map<String,Object> paramValues = chargeSubjectVo.getParamValueList();
            JSONObject paramList = null;
            JSONObject paramValueList = null;
            if(paramsVo != null){
                paramList = (JSONObject) JSONObject.toJSON(paramsVo);
            }
            if(paramValues != null){
                paramValueList = (JSONObject) JSONObject.toJSON(paramValues);
            }
            ChargeSubjectParamsRequest chargeSubjectParamsRequest = ChargeSubjectParamsRequest.builder()
                    .chargeSubjectNo(chargeSubjectVo.getChargeSubjectNo())
                    .cyclicOrSingle(chargeSubjectVo.getCyclicOrSingle())
                    .chargeSubjectPeriod(chargeSubjectVo.getChargeSubjectPeriod())
                    .chargeRuleNo(chargeSubjectVo.getChargeRuleNo())
                    .chargeRuleName(chargeSubjectVo.getChargeRuleName())
                    .paramList(paramList)
                    .paramValueList(paramValueList)
                    .shareType(chargeSubjectVo.getShareType())
                    .companyRate(chargeSubjectVo.getCompanyRate())
                    .personalRate(chargeSubjectVo.getPersonalRate())
                    .companyAmount(chargeSubjectVo.getCompanyAmount())
                    .personalAmount(chargeSubjectVo.getPersonalAmount())
                    .build();
            chargeSubjectList.add(chargeSubjectParamsRequest);
        }

        // 管理协议候审期生成账单 实体入参
        FivePhaseRequest fivePhaseRequest = FivePhaseRequest.builder()
                .parentContractId(vo.getParentContractId())
                .originalContractId(vo.getOriginalContractId())
                .contractStatus(vo.getContractStatus())
                .periodBeginDate(vo.getPeriodBeginDate())
                .periodEndDate(vo.getPeriodEndDate())
                .projectId(vo.getProjectId())
                .houseId(vo.getHouseId())
                .chargeSubjectList(chargeSubjectList)
                .build();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<FivePhaseRequest> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(fivePhaseRequest);

        log.info("管理协议候审期生成账单接口:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("管理协议候审期生成账单接口json:" + jsonRequest);

        //工银接口暂无
        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipChargeFeignClient.agreementPhase(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/bill/agreementPhase", parentRequest);
        }
        log.info("管理协议候审期生成账单接口返回结果为:" + responseBody);

        FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("管理协议候审期生成账单接口失败:"+responseBody);
            throw new McpException(faceMdMapResult.getMessage());
        }

        return faceMdMapResult.getCode();
        //return "00000";
    }



    /**
     * 趸租大合同候审期生成账单
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String singlePhase(BillFivePhaseVo vo) {

        String code = null;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<BillFivePhaseVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        log.info("趸租大合同候审期生成账单接口:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("趸租大合同候审期生成账单接口json:" + jsonRequest);

        //工银接口暂无
        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipChargeFeignClient.singlePhase(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/bill/singlePhase", parentRequest);
        }
        log.info("趸租大合同候审期生成账单接口返回结果为:" + responseBody);

        FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("趸租大合同候审期生成账单接口失败:"+responseBody);
            throw new McpException(faceMdMapResult.getMessage());
        }

        return faceMdMapResult.getCode();
        //return "00000";
    }
}
