package com.bonc.ioc.bzf.business.reminder.service.impl;

import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.reminder.entity.BbpmCollectionWhitelistEntity;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmCollectionWhitelistMapper;
import com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesMainEntity;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmCollectionWhitelistService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 催缴白名单表 服务类实现
 *
 * <AUTHOR>
 * @date 2024-04-24
 * @change 2024-04-24 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmCollectionWhitelistServiceImpl extends McpBaseServiceImpl<BbpmCollectionWhitelistEntity> implements IBbpmCollectionWhitelistService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmCollectionWhitelistMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmCollectionWhitelistService baseService;

    /**
     * Bean 字典转化
     */
    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmCollectionWhitelistVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmCollectionWhitelistEntity entity = new BbpmCollectionWhitelistEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setWhiteId(null);
        if(!baseService.insert(entity)) {
            log.error("催缴白名单表新增失败:" + entity.toString());
            throw new McpException("催缴白名单表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getWhiteId(),1)) {
                log.error("催缴白名单表新增后保存历史失败:" + entity.toString());
                throw new McpException("催缴白名单表新增后保存历史失败");
            }

            log.debug("催缴白名单表新增成功:"+entity.getWhiteId());
            return entity.getWhiteId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmCollectionWhitelistVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmCollectionWhitelistEntity> entityList = new ArrayList<>();
        for (BbpmCollectionWhitelistVo item:voList) {
            BbpmCollectionWhitelistEntity entity = new BbpmCollectionWhitelistEntity();
            BeanUtils.copyProperties(item, entity);

            //根据客户id  或者 项目编号 查
            BbpmCollectionWhitelistVo bbpmCollectionWhitelistVo = new BbpmCollectionWhitelistVo();
            bbpmCollectionWhitelistVo.setType(item.getType());
            if(PaymentEnums.COLLECTION_WHITE_PEOPLE.getCode().equals(item.getType())){
                bbpmCollectionWhitelistVo.setPersonCustomerId(item.getPersonCustomerId());
            }else if(PaymentEnums.COLLECTION_WHITE_PROJECT.getCode().equals(item.getType())){
                bbpmCollectionWhitelistVo.setOperateProjectId(item.getOperateProjectId());
            }else{
                throw new McpException("type不能为空");
            }
            List<BbpmCollectionWhitelistVo> bbpmCollectionWhitelistVoList = baseMapper.selectCustomerOrProject(bbpmCollectionWhitelistVo);
            //有数据就跳过
            if(bbpmCollectionWhitelistVoList != null && bbpmCollectionWhitelistVoList.size()>0){
                continue;
            }

            entityList.add(entity);
        }

        for (BbpmCollectionWhitelistEntity item:entityList){
            item.setWhiteId(null);
        }

        if (entityList == null || entityList.size() == 0) {
            return Collections.emptyList();
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("催缴白名单表新增失败");
            throw new McpException("催缴白名单表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmCollectionWhitelistEntity::getWhiteId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("催缴白名单表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("催缴白名单表批量新增后保存历史失败");
            }

            log.debug("催缴白名单表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param whiteId 需要删除的白名单id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String whiteId) {
        if(!StringUtils.isEmpty(whiteId)) {
            if(!baseService.saveOperationHisById(whiteId,3)) {
                log.error("催缴白名单表删除后保存历史失败:" + whiteId);
                throw new McpException("催缴白名单表删除后保存历史失败");
            }

//            if(!baseService.removeById(whiteId)) {
//                log.error("催缴白名单表删除失败");
//                throw new McpException("催缴白名单表删除失败"+whiteId);
//            }
            BbpmCollectionWhitelistEntity entity = new BbpmCollectionWhitelistEntity();
            entity.setWhiteId(whiteId);
            entity.setDelFlag(0);
            if(!baseService.updateById(entity)) {
                log.error("缴费提醒规则--主表删除失败");
                throw new McpException("缴费提醒规则--主表删除失败"+whiteId);
            }

        } else {
            throw new McpException("催缴白名单表删除失败白名单id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param whiteIdList 需要删除的白名单id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> whiteIdList) {
        if(!CollectionUtils.isEmpty(whiteIdList)) {
            int oldSize = whiteIdList.size();
            whiteIdList = whiteIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(whiteIdList) || oldSize != whiteIdList.size()) {
                throw new McpException("催缴白名单表批量删除失败 存在主键id为空的记录"+StringUtils.join(whiteIdList));
            }

            if(!baseService.saveOperationHisByIds(whiteIdList,3)) {
                log.error("催缴白名单表批量删除后保存历史失败:" + StringUtils.join(whiteIdList));
                throw new McpException("催缴白名单表批量删除后保存历史失败");
            }

            List<BbpmCollectionWhitelistEntity> entityList = new ArrayList<>();
            for(String whiteId:whiteIdList) {
                BbpmCollectionWhitelistEntity entity = new BbpmCollectionWhitelistEntity();
                entity.setWhiteId(whiteId);
                entity.setDelFlag(0);
                entityList.add(entity);
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("催缴白名单表批量删除失败");
                throw new McpException("催缴白名单表批量删除失败"+StringUtils.join(whiteIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的催缴白名单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmCollectionWhitelistVo vo) {
        if(vo != null) {
            BbpmCollectionWhitelistEntity entity = new BbpmCollectionWhitelistEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getWhiteId())) {
                throw new McpException("催缴白名单表更新失败传入白名单id为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("催缴白名单表更新失败");
                throw new McpException("催缴白名单表更新失败"+entity.getWhiteId());
            } else {
                if(!baseService.saveOperationHisById(entity.getWhiteId(),2)) {
                    log.error("催缴白名单表更新后保存历史失败:" + entity.getWhiteId());
                    throw new McpException("催缴白名单表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("催缴白名单表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的催缴白名单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmCollectionWhitelistVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmCollectionWhitelistEntity> entityList = new ArrayList<>();

            for (BbpmCollectionWhitelistVo item:voList){
                BbpmCollectionWhitelistEntity entity = new BbpmCollectionWhitelistEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getWhiteId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("催缴白名单表批量更新失败 存在白名单id为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("催缴白名单表批量更新失败");
                throw new McpException("催缴白名单表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getWhiteId())).map(BbpmCollectionWhitelistEntity::getWhiteId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("催缴白名单表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("催缴白名单表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的催缴白名单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmCollectionWhitelistVo vo) {
        if(vo != null) {
            BbpmCollectionWhitelistEntity entity = new BbpmCollectionWhitelistEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("催缴白名单表保存失败");
                throw new McpException("催缴白名单表保存失败"+entity.getWhiteId());
            } else {
                if(!baseService.saveOperationHisById(entity.getWhiteId(),4)) {
                    log.error("催缴白名单表保存后保存历史失败:" + entity.getWhiteId());
                    throw new McpException("催缴白名单表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("催缴白名单表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的催缴白名单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmCollectionWhitelistVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmCollectionWhitelistEntity> entityList = new ArrayList<>();

            for (BbpmCollectionWhitelistVo item:voList){
                BbpmCollectionWhitelistEntity entity = new BbpmCollectionWhitelistEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("催缴白名单表批量保存失败");
                throw new McpException("催缴白名单表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getWhiteId())).map(BbpmCollectionWhitelistEntity::getWhiteId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("催缴白名单表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("催缴白名单表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param whiteId 需要查询的白名单id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmCollectionWhitelistVo selectByIdRecord(String whiteId) {
        BbpmCollectionWhitelistVo vo = new BbpmCollectionWhitelistVo();

        if(!StringUtils.isEmpty(whiteId)) {
            BbpmCollectionWhitelistEntity entity = baseService.selectById(whiteId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmCollectionWhitelistPageResultVo>> selectByPageRecord(BbpmCollectionWhitelistPageVo vo) {
        List<BbpmCollectionWhitelistPageResultVo> result = baseMapper.selectByPageCustom(vo);
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }
}
