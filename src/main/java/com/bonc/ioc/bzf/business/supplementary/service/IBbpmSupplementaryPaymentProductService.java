package com.bonc.ioc.bzf.business.supplementary.service;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPaymentProductEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 追加账单产品表 服务类
 *
 * <AUTHOR>
 * @date 2025-04-01
 * @change 2025-04-01 by pyj for init
 */
public interface IBbpmSupplementaryPaymentProductService extends IMcpBaseService<BbpmSupplementaryPaymentProductEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbpmSupplementaryPaymentProductVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbpmSupplementaryPaymentProductVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param productId 需要删除的产品id
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String productId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param productIdList 需要删除的产品id
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> productIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的追加账单产品表
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbpmSupplementaryPaymentProductVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的追加账单产品表
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbpmSupplementaryPaymentProductVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的追加账单产品表
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbpmSupplementaryPaymentProductVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的追加账单产品表
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbpmSupplementaryPaymentProductVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param productId 需要查询的产品id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    BbpmSupplementaryPaymentProductVo selectByIdRecord(String productId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbpmSupplementaryPaymentProductPageResultVo>> selectByPageRecord(BbpmSupplementaryPaymentProductPageVo vo);

    /**
     * 根据追加单id删除
     *
     * @param supplementaryId 追加单id
     */
    void deleteBySupplementaryId(String supplementaryId);

    /**
     * 根据上级id删除(逻辑删除)
     *
     * @param parentId 上级id
     */
    void removeByParentId(String parentId);

    /**
     * 根据上级id查询
     *
     * @param parentId 上级id
     * @return 追加账单产品列表
     */
    List<BbpmSupplementaryPaymentProductVo> selectListByParentId(String parentId);
}
