package com.bonc.ioc.bzf.business.reminder.task;

//import com.bonc.ioc.bzf.business.reminder.service.IBbpmScheduledService;
//import com.bonc.ioc.bzf.business.reminder.vo.BbpmScheduledVo;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmScheduledService;
import com.bonc.ioc.bzf.business.reminder.vo.BbpmScheduledVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

/**
 * @Description
 * @ClassName MyTask
 * <AUTHOR>
 * @date 2020.06.07 15:23
 */
//@Slf4j
//@Component
//@EnableScheduling
public class MyTask implements SchedulingConfigurer {

//    @Autowired
//    private IBbpmScheduledService iBbpmScheduledService;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
//        scheduledTaskRegistrar.addTriggerTask(() -> process(),
//                triggerContext -> {
//                    BbpmScheduledVo bbpmScheduledVo = iBbpmScheduledService.selectByIdRecord("1");
//                    String cron = bbpmScheduledVo.getCron();
//                    if (cron.isEmpty()) {
//                        System.out.println("cron is null");
//                    }
//                    return new CronTrigger(cron).nextExecutionTime(triggerContext);
//                });
    }

    private void process() {
        System.out.println("基于接口定时任务");
    }
}
