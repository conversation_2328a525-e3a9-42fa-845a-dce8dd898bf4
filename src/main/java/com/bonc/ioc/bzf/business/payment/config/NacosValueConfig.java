package com.bonc.ioc.bzf.business.payment.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

@Data
@Component
@RefreshScope
public class NacosValueConfig implements Serializable {


//    private boolean insertDb = true;
    @Value("${contractChange.insertDb}")
    private boolean insertDb;

}
