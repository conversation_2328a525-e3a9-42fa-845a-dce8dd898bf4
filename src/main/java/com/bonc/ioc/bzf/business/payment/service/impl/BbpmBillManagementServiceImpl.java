package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.bzf.business.payment.dao.BbpmBillManagementMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmBillManagementEntity;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.*;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.utils.RedisDistributedId;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账单管理(来源业财)v3.0 服务类实现
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmBillManagementServiceImpl extends McpBaseServiceImpl<BbpmBillManagementEntity> implements IBbpmBillManagementService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmBillManagementMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmBillManagementService baseService;

    @Resource
    private BbctContractFeignClient bbctContractFeignClient;

    @Resource
    private BbctCustomerFeignClient bbctCustomerFeignClient;

    /**
     * Bean 字典转化
     */
    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;
    @Resource
    private BfipSettlementFeignClient settlementFeignClient;

    @Resource
    private BbsigningFeignClient bbsigningFeignClient;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Value("${export.maxSize}")
    private Integer exportMaxSize;

    @Value("${export.customerDeptProId}")
    private String customerDeptProId;

    @Resource
    private RestTemplateUtil restTemplateUtil ;
    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    @Autowired
    private RedisDistributedId redisDistributedId;
    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmBillManagementPageResultVo>> selectByPageRecord(BbpmBillManagementPageVo vo) {
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setPageSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setPageNo(Integer.valueOf(vo.getPageNumber()+""));
        vo.setPrimaryChargeCodeFlag("1");
        //默认查询个人列表
        if(StringUtils.isBlank(vo.getChargeOwner())){
            vo.setChargeOwner(PaymentEnums.CHARGEOWNER_PERSON.getCode());
        }
        ParentRequest<BbpmBillManagementPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

         //请求业财接口
        //重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = listByContract(parentRequest);

//        if(pageResult != null && pageResult.getRows() != null){
//            log.info("pageResult.getRows():"+pageResult.getRows().size());
//        }else{
//            log.info("pageResult.getRows():空");
//        }

        return pageResult;
    }

    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public AppReply<String> manualOffer(BbpmBillManagementManualVo vo) {
        //生成批次号
        vo.setBatchNo(redisDistributedId.nextIdV5("manualOffer","0000"));

        //请求业财接口
        ParentRequest<BbpmBillManagementManualVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        log.info("【调用账单手动报盘接口 - 参数】{}",JSONObject.toJSONString(parentRequest, SerializerFeature.WriteMapNullValue));
        String responseBody = null;
        if (yecaiFeign){
            responseBody = settlementFeignClient.manualOffer(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/withhold/manual", parentRequest);
        }
        log.info("【调用账单手动报盘接口 - 返回结果】{}",responseBody);
        JSONObject jsonObject = JSONObject.parseObject(responseBody);
        if(jsonObject != null && jsonObject.get("code") != null && jsonObject.get("code").equals("00000")){
            return new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        }
        if(jsonObject != null && !("00000").equals(jsonObject.get("code"))){
            log.error("调用账单手动报盘接口接口失败:"+responseBody);
            throw new McpException("*提示:"+(String)jsonObject.get("message"));
        }
        return new AppReply<>(AppReply.ERROR_CODE, AppReply.ERROR_MSG, null);
    }

    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmBillManagementPageResultVo> getBillList(BbpmBillManagementPageVo vo) {
        List<BbpmBillManagementPageResultVo> list = new ArrayList<>();
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setPageSize(1);
        vo.setPageNo(1);
        vo.setPrimaryChargeCodeFlag("1");
        if(StringUtils.isBlank(vo.getChargeOwner())){
            vo.setChargeOwner(PaymentEnums.CHARGEOWNER_PERSON.getCode());
        }
        ParentRequest<BbpmBillManagementPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        PageResult<List<BbpmBillManagementPageResultVo>> pageResultCheck = listByContract(parentRequest);
        log.info("【调用账单管理导出接口 - 工银接口返回的总条数】{}",pageResultCheck.getTotal());
        log.info("【调用账单管理导出接口 - 最大限制数】{}",exportMaxSize);
        if(pageResultCheck != null && pageResultCheck.getTotal() > exportMaxSize){
            throw new McpException("导出限制最大条数"+exportMaxSize+"条");
        }
        //全量查
        vo.setFullPage("true");
        vo.setPageSize(null);
        vo.setPageNo(null);
        if(StringUtils.isBlank(vo.getChargeOwner())){
            vo.setChargeOwner(PaymentEnums.CHARGEOWNER_PERSON.getCode());
        }
        parentRequest.setData(vo);
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = listByContract(parentRequest);
        if(pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0){
            list = pageResult.getRows();
        }
        return list;
    }

    /**
     * 根据合同编号查询我的账单
     * @param vo
     * @return
     */
    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmBillManagementPageResultVo> selectMyBillList(BbpmBillManagementVo vo) {
        //拼接请求参数
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");

        if(StringUtils.isBlank(vo.getContractCode())){
            vo.setContractCode(vo.getContractNo());
        }
//        billListRequest.setBillStatus(vo.getBillStatus());
//        billListRequest.setTenantCode(vo.getTenantCode());
        vo.setFullPage("true");
        vo.setPrimaryChargeCodeFlag("1");
        if(StringUtils.isBlank(vo.getChargeOwner())){
            vo.setChargeOwner(PaymentEnums.CHARGEOWNER_PERSON.getCode());
        }
        ParentRequest<BbpmBillManagementVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        //请求业财接口
        //重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = listByContract(parentRequest);
        if(pageResult != null && pageResult.getRows() != null){
            return pageResult.getRows();
        }
        return null;
    }

    /**
     * 查询单个账单
     * @param vo
     * @return
     */
    @Override
    public BbpmBillManagementPageResultVo selectSingleBill(BbpmBillManagementVo vo) {
        //拼接请求参数
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");

//        billListRequest.setBillStatus(vo.getBillStatus());
//        billListRequest.setTenantCode(vo.getTenantCode());
        vo.setFullPage("true");
        vo.setPrimaryChargeCodeFlag("1");
        if(StringUtils.isBlank(vo.getChargeOwner())){
            vo.setChargeOwner(PaymentEnums.CHARGEOWNER_PERSON.getCode());
        }
        ParentRequest<BbpmBillManagementVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        //请求业财接口
        //重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = listByContract(parentRequest);
        if(pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0){
            return pageResult.getRows().get(0);
        }
        return null;
    }

    /**
     * 根据收款单code查询关联的账单
     * @param primaryChargeCode
     * @return
     */
    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmBillManagementPageResultVo> selectBillAndCollectionList(String primaryChargeCode,String projectId,String chargeOwner) {
        //拼接请求参数
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        BbpmBillManagementVo vo = new BbpmBillManagementVo();
        if(StringUtils.isNotBlank(primaryChargeCode)){
            vo.setPrimaryChargeCode(primaryChargeCode);
        }
        vo.setProjectId(projectId);
        vo.setFullPage("true");
        vo.setPrimaryChargeCodeFlag("1");
        if(StringUtils.isBlank(chargeOwner)){
            vo.setChargeOwner(PaymentEnums.CHARGEOWNER_PERSON.getCode());
        }else{
            vo.setChargeOwner(chargeOwner);
        }
        ParentRequest<BbpmBillManagementVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        //请求业财接口
        //重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = listByContract(parentRequest);
        if(pageResult != null && pageResult.getRows() != null){
            return pageResult.getRows();
        }
        return null;
    }

    @Override
    public PageResult<List<BbpmBillManagementPageResultVo>> listByContract(ParentRequest parentRequest){
        log.info("3.4调用工银账单列表接口请求参数:"+JSONObject.toJSONString(parentRequest));
        //请求业财接口
        String responseBody = null;
        if (yecaiFeign){
            responseBody = bfipChargeFeignClient.listByContract(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/bill/listByContract", parentRequest);
        }
        log.debug("3.4调用工银账单列表接口返回:"+responseBody);
        //重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = resetBill(responseBody);
        return pageResult;
    }

    public PageResult<List<BbpmBillManagementPageResultVo>> resetBill(String responseBody){
        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }

        FaceHttpResultTwo<BbpmBillManagementPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            throw new McpException("*提示:"+billListResultFaceHttpResultTwo.getMessage());
        }

        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(null);
        }
        List<BbpmBillManagementPageResultVo> billListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(),BbpmBillManagementPageResultVo.class);

        List<BbpmBillManagementPageResultVo> result = new ArrayList<>();

        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
//        for(BbpmBillManagementPageResultVo bill : billListResultList){
        for(int i=0;i<billListResultList.size();i++){
            BbpmBillManagementPageResultVo bill = billListResultList.get(i);
//            bbpmBillManagementPageResultVo.setBillId(bill.getBillCode()+"");        +"-"+UUID.randomUUID().toString().replace("-", "")
            bill.setBillNo(bill.getBillId());
            bill.setContractNo(bill.getContractCode());
            //账单周期相关  第X期（YYYY/MM/DD - YYYY/MM/DD）
            bill.setBillCycle("第"+bill.getChargeSubjectPeriod()+"期("+bill.getChargeSubjectBeginDate()+"至"+bill.getChargeSubjectEndDate()+")");
           //字典值
            bill.setBillChargeSubjectName(bill.getBillChargeSubject());
            bill.setBillStatusName(bill.getBillStatus());
            bill.setChargeStandardCnyName(bill.getChargeStandardCny());
            bill.setChargeCycleName(bill.getChargeCycle());
            bill.setIsProvisionName(bill.getIsProvision());
            bill.setChargeTypeName(bill.getChargeType());
            bill.setStatusName(bill.getStatus());
            bill.setAccountStatusName(bill.getAccountStatus());
            bill.setInvoicingStatusName(bill.getInvoicingStatus());
            bill.setBillPayStatusName(bill.getBillPayStatus());
            bill.setDeptName(getCustomerDept(bill));
            result.add(bill);
        }

        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
//        log.info("重新设置值后的result大小:"+result.size());
        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(),result);
    }


    public String getCustomerDept(BbpmBillManagementPageResultVo bill){
        String customerNo = "";
        String enterpriseNo = "";
        log.info("获取客户部门开始:配置项目" + customerDeptProId + "账单项目" + bill.getProjectId() + "账单类型" +bill.getLeaseType());
        try {
            if (customerDeptProId.contains(bill.getProjectId())&&bill.getLeaseType().equals(PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode())) {
                log.info("符合客户部门导出权限 ：合同编号" + bill.getContractCode());
                AppReply<BbctContractManagementVo> reply = bbctContractFeignClient.selectByIdNo(bill.getContractCode(), null);
                BbctContractManagementVo bbctContractManagementVo = reply.getData();
                if (bbctContractManagementVo == null || bbctContractManagementVo.getUserList() == null) {
                    return ""; // 防空指针
                }

                // 使用 Java Stream 过滤出 customerType == "00" 的用户
                List<BbctContractSignerVo> userList = bbctContractManagementVo.getUserList().stream()
                        .filter(user -> "00".equals(user.getCustomerType()))
                        .collect(Collectors.toList());
                List<BbctContractSignerVo> enterpriseList = bbctContractManagementVo.getUserList().stream()
                        .filter(user -> "01".equals(user.getCustomerType()))
                        .collect(Collectors.toList());

                if (userList.size() > 0) {
                     customerNo = userList.get(0).getCustomerNo();
                     log.info("获取的个人编号：" + customerNo);
                }else {
                    log.info("获取的个人编号为空");
                    return "";
                }
                if (enterpriseList.size() > 0) {
                     enterpriseNo= enterpriseList.get(0).getCustomerNo();
                     log.info("获取的企业编号：" + enterpriseNo);
                }else {
                    log.info("获取的企业编号为空");
                    return "";
                }
                AppReply<String> deptName = bbctCustomerFeignClient.getDepartmentName(customerNo, enterpriseNo);
                log.info("获取的部门名称：" + deptName.getData());
                return deptName.getData();
            }
        } catch (Exception e) {
            log.error("账单导出查询客户部门异常:"+e.getMessage());
            return "";
        }
        return "";
    }


    /**
     * 查询收款凭证
     * @param vo
     * @return
     */
    @Override
    public CorporateCollectionVo transferInfo(CorporateSearchVo vo) {
        CorporateCollectionVo result = new CorporateCollectionVo();

        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<CorporateSearchVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.25查询趸租收款凭证接口请求参数json:"+jsonRequest);
        String responseBody = "";
        if (yecaiFeign){
            responseBody = bfipSettlementFeignClient.info(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/receipt/transfer/info", parentRequest);
        }
        log.info("3.25查询趸租收款凭证接口返回:"+responseBody);


//        FaceMdMapResultTwo<CorporateCollectionVo> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);
        FaceHttpResultTwo<CorporateCollectionVo> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.25查询趸租收款凭证接口失败:"+responseBody);
            throw new McpException("*提示:"+faceMdMapResult.getMessage());
        }else{
//            JSONObject jsonObject = JSON.parseObject(String.valueOf(faceMdMapResult.getData()));
//            CorporateCollectionVo corporateCollectionVo = jsonObject.toJavaObject(CorporateCollectionVo.class);

            if(faceMdMapResult.getData() == null || faceMdMapResult.getData().getRecords() == null){
               return result;
            }
            List<CorporateCollectionVo> corporateCollectionVoList = JSON.parseArray(faceMdMapResult.getData().getRecords().toString(),CorporateCollectionVo.class);
            if(corporateCollectionVoList != null && corporateCollectionVoList.size() > 0){
                result = corporateCollectionVoList.get(0);
                //recepiter 收款方名称 --》"receiptBankAccountName":"北京保障房中心",
                //recepitAcctNo 收款方账户号 --》"receiptBankAcctNo":"*****************"
                result.setReceiptBankAccountName(result.getRecepiter());
                result.setReceiptBankAcctNo(result.getRecepitAcctNo());

                //按照收款赋值字段对应
                result.setBankName(result.getReceiptBankName());
                result.setBankAccountNo(result.getReceiptBankAcctNo());
                result.setBankAccountName(result.getReceiptBankAccountName());
                result.setBranchName(result.getReceiptBankBranchName());

                 //"transDateStr": "2024/03/27",
                if(StringUtils.isNotBlank(result.getTransDateStr())){
                    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy/MM/dd");
                    SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                    try {
                        Date date = inputFormat.parse(result.getTransDateStr());
                        String outputDate = outputFormat.format(date);
                        result.setTransDate(outputDate);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }

                if(StringUtils.isBlank(result.getMultiProject())){
//                    log.info("工银多项目认款字段为空");
                    result.setMultiProject(PaymentEnums.MULTIPROJECT_NO.getCode());
                }

                if(PaymentEnums.MULTIPROJECT_YES.getCode().equals(result.getMultiProject())){
                    result.setMultiProjectName(PaymentEnums.MULTIPROJECT_YES.getMsg());
                }else if(PaymentEnums.MULTIPROJECT_NO.getCode().equals(result.getMultiProject())){
                    result.setMultiProjectName(PaymentEnums.MULTIPROJECT_NO.getMsg());
                }else {
                    result.setMultiProjectName(result.getMultiProject());
                }
            }
            return result;
        }
    }

    /**
     * 3.58.账单关闭或开启接口
     *
     * @param vo
     * @return
     */
    @Override
    public Boolean closeOrOpenBillAndBillBranks(CloseOrOpenBillAndBillBranksParamVo vo) {
        ParentRequest<CloseOrOpenBillAndBillBranksParamVo> bankRequestVo = new ParentRequest<>();
        bankRequestVo.setData(vo);
        String resultString = null;
        ChargeRespondVo<PreviewBillsResultVo> result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("3.58.账单关闭或开启接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result =  bfipChargeFeignClient.closeOrOpenBillAndBillBranks(bankRequestVo);
        } else {
            String url = yecaiUrl + "charge/v1/bill/closeOrOpenBillAndBillBranks";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("3.58.账单关闭或开启接口,工银返回:"+ JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }
        return true;
    }

    /**
     * 3.59.月账单查询
     *
     * @param vo
     * @return
     */
    @Override
    public PageResult<List<BbpmBillManagementPageResultVo>> queryMonthBillList(BbpmMonthBillManagementPageVo vo) {
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setPageSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setPageNo(Integer.valueOf(vo.getPageNumber()+""));
        vo.setPrimaryChargeCodeFlag("1");
        //默认查询个人列表
        if(StringUtils.isBlank(vo.getChargeOwner())){
            vo.setChargeOwner(PaymentEnums.CHARGEOWNER_PERSON.getCode());
        }
        ParentRequest<BbpmMonthBillManagementPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = monthListByContract(parentRequest);

        return pageResult;
    }

    /**
     * 月度账单查询
     * @param parentRequest
     * @return
     */
    private PageResult<List<BbpmBillManagementPageResultVo>> monthListByContract(ParentRequest<BbpmMonthBillManagementPageVo> parentRequest) {

        log.info("3.60.查询月度账单接口 请求参数:"+JSONObject.toJSONString(parentRequest));
        //请求业财接口
        String responseBody = null;
        if (yecaiFeign){
            responseBody = bfipChargeFeignClient.queryMonthBillList(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/bill/queryMonthBillList", parentRequest);
        }
        log.info("3.60.查询月度账单接口 接口返回:"+responseBody);
        //重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = resetBill(responseBody);
        return pageResult;
    }
}
