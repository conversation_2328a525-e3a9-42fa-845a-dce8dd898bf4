package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpmCollectionEntity;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收款表(部分来源业财)v3.0 服务类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
public interface IBbpmCollectionService extends IMcpBaseService<BbpmCollectionEntity>{

    /**
     * insertRecordAll 单个收款，包括现金、支票、线下转账
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    String insertRecordAll(BbpmCollectionVo vo);


    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmCollectionVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param messageId 需要删除的主键id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void removeByIdRecord(String messageId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param messageIdList 需要删除的主键id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> messageIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的收款表(部分来源业财)v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void updateByIdRecord(BbpmCollectionVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的收款表(部分来源业财)v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmCollectionVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的收款表(部分来源业财)v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void saveByIdRecord(BbpmCollectionVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的收款表(部分来源业财)v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmCollectionVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param messageId 需要查询的主键id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    BbpmCollectionVo selectByIdRecord(String messageId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    PageResult<List<BbpmCollectionPageResultVo>> selectByPageRecord(BbpmCollectionPageVo vo);

    /**
     * 获得收款单所有数据
     * @param vo
     * @return
     */
    List<BbpmCollectionPageResultVo> getReceiptList(BbpmCollectionPageVo vo);


    /**
     * selectByPageUndeposits 未存款--分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    PageResult<List<BbpmCollectionPageResultVo>> selectByPageUndeposits(BbpmCollectionPageVo vo);


    /**
     * 未存款--所有的
     * @param vo
     * @return
     */
    BbpmUndepositsVo selectUndepositsList(BbpmCollectionVo vo);

    /**
     * 根据存款单id查询收款单列表信息
     * @param depositId
     * @return
     */
    List<BbpmCollectionPageResultVo> selectDepositAndCollectionList(String depositId);


    /**
     * insertCollectionBatchRecordAll 批量收款,包括现金、支票、线下转账
     * @param  vo 需要保存的记录
     * @return  String 返回新增后的主键vo
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    String insertCollectionBatchRecordAll(BbpmCollectionBatchVo vo);


    /**
     * updateCertificateStateById 根据主键更新凭证状态
     * @param id 需要更新凭证状态的id
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void updateCertificateStateById(String id);


    BbpmChargeMoneyTotalVo chargeMoneyTotal(BbpmCollectionPageVo vo);
}
