package com.bonc.ioc.bzf.business.invoice.controller;

import com.bonc.ioc.bzf.business.invoice.service.IBbpmInvoiceIssueService;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 开具发票 前端控制器
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmInvoiceIssueEntity")
@Api(tags = "开具发票")
@Validated
public class BbpmInvoiceIssueBusinessController extends McpBaseController {

    @Resource
    private IBbpmInvoiceIssueService baseService;


    /**
     * preCheckBill 账单预校验
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @PostMapping(value = "/preCheckBill", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "binghong.tang")
    @ApiOperation(value = "账单预校验", notes = "新增全表数据", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:成功返回true")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmInvoiceIssueEntity/preCheckBill")
    public AppReply<String> preCheckBill(@ApiParam(value = "开票信息" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmPreCheckBillVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.preCheckBill(vo));
        return appReply;
    }



    /**
     * issueInvoice 开具发票
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @PostMapping(value = "/issueInvoice", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "开具发票", notes = "开具发票", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:成功失败条数")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmInvoiceIssueEntity/issueInvoice")
    public AppReply<String> issueInvoice(@ApiParam(value = "开票信息" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmInvoiceIssueVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.issueInvoice(vo));
        return appReply;
    }

    /**
     * redFlush 发票红冲
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @PostMapping(value = "/redFlush", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "发票红冲", notes = "发票信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:交易编号")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmInvoiceIssueEntity/redFlush")
    public AppReply<String> redFlush(@ApiParam(value = "发票信息" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmInvoiceRedFlushVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.redFlush(vo));
        return appReply;
    }

    /**
     * redFlushBatch 批量发票红冲
     * @param voList 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @PostMapping(value = "/redFlushBatch", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "批量发票红冲", notes = "发票信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:成功失败条数")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmInvoiceIssueEntity/redFlushBatch")
    public AppReply<String> redFlushBatch(@ApiParam(value = "发票信息" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbpmInvoiceRedFlushVo> voList){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.redFlushBatch(voList));
        return appReply;
    }

    /**
     * pushInvoice 发票推送
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @PostMapping(value = "/pushInvoice", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "发票推送", notes = "推送信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:交易编号")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmInvoiceIssueEntity/pushInvoice")
    public AppReply<String> pushInvoice(@ApiParam(value = "推送信息" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmInvoicePushVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.pushInvoice(vo));
        return appReply;
    }

    /**
     * pushInvoiceBatch 批量发票推送
     * @param voList 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @PostMapping(value = "/pushInvoiceBatch", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "批量发票推送", notes = "推送信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:成功失败条数")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmInvoiceIssueEntity/pushInvoiceBatch")
    public AppReply<String> pushInvoiceBatch(@ApiParam(value = "推送信息" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbpmInvoicePushVo> voList){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.pushInvoiceBatch(voList));
        return appReply;
    }



    /**
     * redFlushAndIssue app重开发票
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @PostMapping(value = "/redFlushAndIssue", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "app重开发票", notes = "新增全表数据", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:交易编号")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmInvoiceIssueEntity/redFlushAndIssue")
    public AppReply<String> redFlushAndIssue(@ApiParam(value = "开票信息和历史开票信息" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmInvoiceRedFlushAndIssueVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.redFlushAndIssue(vo));
        return appReply;
    }

}

