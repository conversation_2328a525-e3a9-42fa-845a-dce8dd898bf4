package com.bonc.ioc.bzf.business.reminder.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 未缴账单主实体返回
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@ApiModel(value = "UnpaidBillMainVo", description = "未缴账单主实体")
@Data
public class UnpaidBillMainVo extends McpBaseVo implements Serializable {
    @ApiModelProperty(value = "合同编码")
    private String contractCode;//合同编码	String	是	20
    @ApiModelProperty(value = "合同欠缴月份合计")
    private int unPaidMonthTotal;//合同欠缴月份合计	int	是	10
    @ApiModelProperty(value = "合同欠缴金额")
    private BigDecimal unPaidMoney;//合同欠缴金额	Decimal	是	20,4	欠缴金额+未对平金额
    @ApiModelProperty(value = "催缴账单列表")
    private List<UnpaidBillVo> unPaidBillDetailVOList;//urgePayBillList;//	催缴账单列表	List	是

}
