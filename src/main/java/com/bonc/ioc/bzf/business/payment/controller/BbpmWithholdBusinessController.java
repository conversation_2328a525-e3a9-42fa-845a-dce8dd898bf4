package com.bonc.ioc.bzf.business.payment.controller;


import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCollectionService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmWithholdService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 新手动报盘相关接口
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmWithholdEntity")
@Api(tags = "新手动报盘相关接口")
@Validated
public class BbpmWithholdBusinessController extends McpBaseController {
    @Resource
    private IBbpmWithholdService iBbpmWithholdService;



    /**
     * selectByPageRecord 3.21.1手动代扣-未缴账单列表查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/list", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "3.21.1手动代扣-未缴账单列表查询", notes = "3.21.1手动代扣-未缴账单列表查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> list(@RequestBody BbpmWithholdListPageVo vo){
        AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbpmWithholdService.selectByPageRecordList(vo));
        return appReply;
     }


    /**
     * 3.21.3 手动代扣-报盘记录查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @GetMapping(value = "/record", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "3.21.3手动代扣-报盘记录查询", notes = "3.21.3手动代扣-报盘记录查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmWithholdRecordPageResultVo>>> record(BbpmWithholdRecordPageVo vo){
        AppReply<PageResult<List<BbpmWithholdRecordPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbpmWithholdService.selectByPageRecordRecord(vo));
        return appReply;
    }

    /**
     * 3.21.4 手动代扣-报盘明细查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/detail", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "3.21.4手动代扣-报盘明细查询", notes = "3.21.4手动代扣-报盘明细查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmWithholdDetailPageResultVo>>> detail(BbpmWithholdDetailPageVo vo){
        AppReply<PageResult<List<BbpmWithholdDetailPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbpmWithholdService.selectByPageRecordDetail(vo));
        return appReply;
    }

    //下载直接放签约系统了
//    /**
//     * @description: 3.21.5 手动代扣-报盘明细下载
//     * @date: 2023-02-28 13:24
//     * @param: [id, response]
//     * @return: void
//     * @since 1.0.0
//     **/
//    @GetMapping(value = "/exceldownload", produces = "application/json;charset=UTF-8")
//    @ApiOperationSupport(order = 9, author = "tang")
//    @ApiOperation(value = "3.21.5手动代扣-报盘明细下载", notes = "3.21.5手动代扣-报盘明细下载", hidden = false)
//    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
//    public void exceldownload(ExcelDownloadV2Vo vo, HttpServletResponse response){
//        iBbpmWithholdService.exceldownload(vo,response);
//    }
}

