package com.bonc.ioc.bzf.business.payment.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
@ApiModel(value="计费科目", description="计费科目")
@Data
public class PreviewBillParamsRoomChargeSubjectVo implements java.io.Serializable{

    @ApiModelProperty(value = "计费科目编号  01 房屋租金   02 押金   07 物业费")
    private String chargeSubjectNo;

    @ApiModelProperty(value = "计费科目金额")
    private BigDecimal chargeSubjectAmount;

    @ApiModelProperty(value = "押金比例")
    private BigDecimal depositProportion;

    @ApiModelProperty(value = "计费科目金额类型 01-日 02-月 03-年")
    private String amountType;

    @ApiModelProperty(value = "计费科目税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "循环计费或单次计费 01 循环02 单次")
    private String cyclicOrSingle;

    @ApiModelProperty(value = "计费科目收款周期 01 月 02 季 03 半年 04 年 05 每2个月 06 每4个月")
    private Integer chargeSubjectPeriod;

    @ApiModelProperty(value = "计费规则编号")
    private String chargeRuleNo;

    @ApiModelProperty(value = "计费规则名称")
    private String chargeRuleName;

    @ApiModelProperty(value = "参数列表")
    private JSONObject paramList;

    @ApiModelProperty(value = "参数值列表")
    private JSONObject paramValueList;

    @ApiModelProperty(value = "列表，递增规则")
    private List<PreviewBillparamsRoomChargeIncreaseRule> increaseRules;

    @ApiModelProperty(value = "列表，优惠规则")
    private List<PreviewBillparamsRoomChargePreferentRule> preferentRules;



}
