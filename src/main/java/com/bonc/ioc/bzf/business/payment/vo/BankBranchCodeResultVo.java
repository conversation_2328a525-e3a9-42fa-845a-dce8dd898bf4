package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> 开户行 返回实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class BankBranchCodeResultVo {

	@ApiModelProperty(value = "开户行编码")
	private  String bankCode;

	@ApiModelProperty(value = "开户行名称")
	private String bankName;

	@ApiModelProperty(value = "开户行支行名称")
	private  String bankBranchName;

	@ApiModelProperty(value = "开户行支行编码")
	private String bankBranchCode;

	@ApiModelProperty(value = "行别代码")
	private String clsCode;
}
