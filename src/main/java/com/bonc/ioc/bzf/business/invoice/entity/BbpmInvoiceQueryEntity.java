package com.bonc.ioc.bzf.business.invoice.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 已开发票查询 实体类
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@TableName("bbpm_invoice_query")
@ApiModel(value="BbpmInvoiceQueryEntity对象", description="已开发票查询")
public class BbpmInvoiceQueryEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_FPQQLSH = "fpqqlsh";
    public static final String FIELD_FP_DM = "fp_dm";
    public static final String FIELD_FP_HM = "fp_hm";
    public static final String FIELD_KPRQ = "kprq";
    public static final String FIELD_KPLX = "kplx";
    public static final String FIELD_GMF_NSRSBH = "gmf_nsrsbh";
    public static final String FIELD_GMF_MC = "gmf_mc";
    public static final String FIELD_GMF_YHZH = "gmf_yhzh";
    public static final String FIELD_GMF_YHMC = "gmf_yhmc";
    public static final String FIELD_GMF_DZ = "gmf_dz";
    public static final String FIELD_GMF_DH = "gmf_dh";
    public static final String FIELD_GMF_DZYX = "gmf_dzyx";
    public static final String FIELD_GMF_SJH = "gmf_sjh";
    public static final String FIELD_INVOICE_STATUS = "invoice_status";
    public static final String FIELD_INVOICE_MONEY = "invoice_money";
    public static final String FIELD_EXPENSE_TYPE = "expense_type";
    public static final String FIELD_TITLE_TYPE = "title_type";
    public static final String FIELD_BZ = "bz";
    public static final String FIELD_TENANT_NAME = "tenant_name";
    public static final String FIELD_CHARGE_PAYMENTLIST = "charge_paymentList";
    public static final String FIELD_PDFLJ = "pdflj";
    public static final String FIELD_HOUSE_ADDR = "house_addr";
    public static final String FIELD_XSF_MC = "xsf_mc";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 发票请求流水号
     */
    @ApiModelProperty(value = "发票请求流水号")
                                @TableId(value = "fpqqlsh", type = IdType.ASSIGN_UUID)
                                  private String fpqqlsh;

    /**
     * 发票代码
     */
    @ApiModelProperty(value = "发票代码")
                            private String fpDm;

    /**
     * 发票号码
     */
    @ApiModelProperty(value = "发票号码")
                            private String fpHm;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期")
                            private String kprq;

    /**
     * 开票类型(0：蓝字发票,1：红字发票)
     */
    @ApiModelProperty(value = "开票类型(0：蓝字发票,1：红字发票)")
                            private String kplx;

    /**
     * 购买方纳税人识别号
     */
    @ApiModelProperty(value = "购买方纳税人识别号")
                            private String gmfNsrsbh;

    /**
     * 购买方名称
     */
    @ApiModelProperty(value = "购买方名称")
                            private String gmfMc;

    /**
     * 购买方银行账号
     */
    @ApiModelProperty(value = "购买方银行账号")
                            private String gmfYhzh;

    /**
     * 购买方银行名称
     */
    @ApiModelProperty(value = "购买方银行名称")
                            private String gmfYhmc;

    /**
     * 购买方地址
     */
    @ApiModelProperty(value = "购买方地址")
                            private String gmfDz;

    /**
     * 购买方电话
     */
    @ApiModelProperty(value = "购买方电话")
                            private String gmfDh;

    /**
     * 消费者邮箱号
     */
    @ApiModelProperty(value = "消费者邮箱号")
                            private String gmfDzyx;

    /**
     * 消费者手机号
     */
    @ApiModelProperty(value = "消费者手机号")
                            private String gmfSjh;

    /**
     * 发票状态(01:已开票,02:已红冲)
     */
    @ApiModelProperty(value = "发票状态(01:已开票,02:已红冲)")
                            private String invoiceStatus;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
                            private String invoiceMoney;

    /**
     * 费用类型(01:房屋租金,03:家具家电租金)
     */
    @ApiModelProperty(value = "费用类型(01:房屋租金,03:家具家电租金)")
                            private String expenseType;

    /**
     * 抬头类型(01:企业单位,02:个人/非企业单位)
     */
    @ApiModelProperty(value = "抬头类型(01:企业单位,02:个人/非企业单位)")
                            private String titleType;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
                            private String bz;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
                            private String tenantName;

    /**
     * 主收款单列表
     */
    @ApiModelProperty(value = "主收款单列表")
    @TableField("charge_paymentList")
                            private String chargePaymentlist;

    /**
     * 发票pdf文件路径
     */
    @ApiModelProperty(value = "发票pdf文件路径")
                            private String pdflj;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
                            private String houseAddr;

    /**
     * 销售方名称
     */
    @ApiModelProperty(value = "销售方名称(开票方)")
                            private String xsfMc;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 发票请求流水号
     */
    public String getFpqqlsh() {
        return fpqqlsh;
    }

    public void setFpqqlsh(String fpqqlsh) {
        this.fpqqlsh = fpqqlsh;
    }

    /**
     * @return 发票代码
     */
    public String getFpDm() {
        return fpDm;
    }

    public void setFpDm(String fpDm) {
        this.fpDm = fpDm;
    }

    /**
     * @return 发票号码
     */
    public String getFpHm() {
        return fpHm;
    }

    public void setFpHm(String fpHm) {
        this.fpHm = fpHm;
    }

    /**
     * @return 开票日期
     */
    public String getKprq() {
        return kprq;
    }

    public void setKprq(String kprq) {
        this.kprq = kprq;
    }

    /**
     * @return 开票类型(0：蓝字发票,1：红字发票)
     */
    public String getKplx() {
        return kplx;
    }

    public void setKplx(String kplx) {
        this.kplx = kplx;
    }

    /**
     * @return 购买方纳税人识别号
     */
    public String getGmfNsrsbh() {
        return gmfNsrsbh;
    }

    public void setGmfNsrsbh(String gmfNsrsbh) {
        this.gmfNsrsbh = gmfNsrsbh;
    }

    /**
     * @return 购买方名称
     */
    public String getGmfMc() {
        return gmfMc;
    }

    public void setGmfMc(String gmfMc) {
        this.gmfMc = gmfMc;
    }

    /**
     * @return 购买方银行账号
     */
    public String getGmfYhzh() {
        return gmfYhzh;
    }

    public void setGmfYhzh(String gmfYhzh) {
        this.gmfYhzh = gmfYhzh;
    }

    /**
     * @return 购买方银行名称
     */
    public String getGmfYhmc() {
        return gmfYhmc;
    }

    public void setGmfYhmc(String gmfYhmc) {
        this.gmfYhmc = gmfYhmc;
    }

    /**
     * @return 购买方地址
     */
    public String getGmfDz() {
        return gmfDz;
    }

    public void setGmfDz(String gmfDz) {
        this.gmfDz = gmfDz;
    }

    /**
     * @return 购买方电话
     */
    public String getGmfDh() {
        return gmfDh;
    }

    public void setGmfDh(String gmfDh) {
        this.gmfDh = gmfDh;
    }

    /**
     * @return 消费者邮箱号
     */
    public String getGmfDzyx() {
        return gmfDzyx;
    }

    public void setGmfDzyx(String gmfDzyx) {
        this.gmfDzyx = gmfDzyx;
    }

    /**
     * @return 消费者手机号
     */
    public String getGmfSjh() {
        return gmfSjh;
    }

    public void setGmfSjh(String gmfSjh) {
        this.gmfSjh = gmfSjh;
    }

    /**
     * @return 发票状态(01:已开票,02:已红冲)
     */
    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    /**
     * @return 开票金额
     */
    public String getInvoiceMoney() {
        return invoiceMoney;
    }

    public void setInvoiceMoney(String invoiceMoney) {
        this.invoiceMoney = invoiceMoney;
    }

    /**
     * @return 费用类型(01:房屋租金,03:家具家电租金)
     */
    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    /**
     * @return 抬头类型(01:企业单位,02:个人/非企业单位)
     */
    public String getTitleType() {
        return titleType;
    }

    public void setTitleType(String titleType) {
        this.titleType = titleType;
    }

    /**
     * @return 备注说明
     */
    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    /**
     * @return 客户姓名
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 主收款单列表
     */
    public String getChargePaymentlist() {
        return chargePaymentlist;
    }

    public void setChargePaymentlist(String chargePaymentlist) {
        this.chargePaymentlist = chargePaymentlist;
    }

    /**
     * @return 发票pdf文件路径
     */
    public String getPdflj() {
        return pdflj;
    }

    public void setPdflj(String pdflj) {
        this.pdflj = pdflj;
    }

    /**
     * @return 房源地址
     */
    public String getHouseAddr() {
        return houseAddr;
    }

    public void setHouseAddr(String houseAddr) {
        this.houseAddr = houseAddr;
    }

    /**
     * @return 销售方名称
     */
    public String getXsfMc() {
        return xsfMc;
    }

    public void setXsfMc(String xsfMc) {
        this.xsfMc = xsfMc;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmInvoiceQueryEntity{" +
            "fpqqlsh=" + fpqqlsh +
            ", fpDm=" + fpDm +
            ", fpHm=" + fpHm +
            ", kprq=" + kprq +
            ", kplx=" + kplx +
            ", gmfNsrsbh=" + gmfNsrsbh +
            ", gmfMc=" + gmfMc +
            ", gmfYhzh=" + gmfYhzh +
            ", gmfYhmc=" + gmfYhmc +
            ", gmfDz=" + gmfDz +
            ", gmfDh=" + gmfDh +
            ", gmfDzyx=" + gmfDzyx +
            ", gmfSjh=" + gmfSjh +
            ", invoiceStatus=" + invoiceStatus +
            ", invoiceMoney=" + invoiceMoney +
            ", expenseType=" + expenseType +
            ", titleType=" + titleType +
            ", bz=" + bz +
            ", tenantName=" + tenantName +
            ", chargePaymentlist=" + chargePaymentlist +
            ", pdflj=" + pdflj +
            ", houseAddr=" + houseAddr +
            ", xsfMc=" + xsfMc +
            ", delFlag=" + delFlag +
        "}";
    }
}