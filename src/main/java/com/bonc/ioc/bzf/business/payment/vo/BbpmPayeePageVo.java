package com.bonc.ioc.bzf.business.payment.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * pos机收款回传请求响应数据 实体类
 *
 * <AUTHOR>
 * @date 2024-01-24
 * @change 2024-01-24 by binghong.tang for init
 */
@ApiModel(value="BbpmPayeePageVo对象", description="pos机收款回传请求响应数据")
public class BbpmPayeePageVo extends McpBasePageVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String pospbakId;

    /**
     * 收款回传请求json
     */
    @ApiModelProperty(value = "收款回传请求json")
                            private String request;

    /**
     * 收款回传响应json
     */
    @ApiModelProperty(value = "收款回传响应json")
                            private String result;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 主键
     */
    public String getPospbakId() {
        return pospbakId;
    }

    public void setPospbakId(String pospbakId) {
        this.pospbakId = pospbakId;
    }

    /**
     * @return 收款回传请求json
     */
    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    /**
     * @return 收款回传响应json
     */
    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmPayeePageVo{" +
            "pospbakId=" + pospbakId +
            ", request=" + request +
            ", result=" + result +
            ", delFlag=" + delFlag +
        "}";
    }
}
