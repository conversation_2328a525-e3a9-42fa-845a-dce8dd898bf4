package com.bonc.ioc.bzf.business.supplementary.dao;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmApproveInfoEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.supplementary.vo.*;
import java.util.List;

/**
 * 审批表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Mapper
public interface BbpmApproveInfoMapper extends McpBaseMapper<BbpmApproveInfoEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change 2025-03-26 by pyj for init
     */
    List<BbpmApproveInfoPageResultVo> selectByPageCustom(@Param("vo") BbpmApproveInfoPageVo vo );
}
