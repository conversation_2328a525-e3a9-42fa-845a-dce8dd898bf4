package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpmBillManagementEntity;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * 账单管理(来源业财)v3.0 服务类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
public interface IBbpmBillManagementService extends IMcpBaseService<BbpmBillManagementEntity>{


    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    PageResult<List<BbpmBillManagementPageResultVo>> selectByPageRecord(BbpmBillManagementPageVo vo);

    /**
     * 手动报盘
     * @param vo
     * @return
     */
    AppReply<String> manualOffer(BbpmBillManagementManualVo vo);

    /**
     * 查询全部
     * @param vo
     * @return
     */
    List<BbpmBillManagementPageResultVo> getBillList(BbpmBillManagementPageVo vo);

    /**
     * 查我的账单
     * @param vo
     * @return
     */
    List<BbpmBillManagementPageResultVo> selectMyBillList(BbpmBillManagementVo vo);

    /**
     * 查询单个账单
     * @param vo
     * @return
     */
    BbpmBillManagementPageResultVo selectSingleBill(BbpmBillManagementVo vo);


    /**
     * 根据收款单code查询关联的账单
     * @param primaryChargeCode
     * @return
     */
    List<BbpmBillManagementPageResultVo> selectBillAndCollectionList(String primaryChargeCode,String projectId,String chargeOwner);


    PageResult<List<BbpmBillManagementPageResultVo>> listByContract(ParentRequest parentRequest);

    /**
     * 查询收款凭证
     * @param vo
     * @return
     */
    CorporateCollectionVo transferInfo(CorporateSearchVo vo);

    /**
     * 3.58.账单关闭或开启接口
     * @param vo
     * @return
     */
    Boolean closeOrOpenBillAndBillBranks(CloseOrOpenBillAndBillBranksParamVo vo);

    /**
     * 3.59.月账单查询
     * @param vo
     * @return
     */
    PageResult<List<BbpmBillManagementPageResultVo>> queryMonthBillList(BbpmMonthBillManagementPageVo vo);
}
