package com.bonc.ioc.bzf.business.invoice.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RestController;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.bzf.business.invoice.service.IBbpmInvoiceQueryService;
import io.swagger.annotations.*;

import java.util.List;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 已开发票查询 前端控制器
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmInvoiceQueryEntity")
@Api(tags = "已开发票查询")
@Validated
public class BbpmInvoiceQueryBusinessController extends McpBaseController {
    @Resource
    private IBbpmInvoiceQueryService baseService;


    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-13
     * @change
     * 2023-05-13 by binghong.tang for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "已开票--分页查询", notes = "分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmInvoiceQueryPageResultVo>>> selectByPageRecord(BbpmInvoiceQueryPageVo vo){
        AppReply<PageResult<List<BbpmInvoiceQueryPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }



    /**
     * selectByIdRecord 根据主键查询
     * @param vo 需要查询的发票请求流水号
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-13
     * @change
     * 2023-05-13 by binghong.tang for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "binghong.tang")
    @ApiOperation(value = "已开票--根据主键查询", notes = "根据主键查询表中信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmInvoiceQueryVo> selectByIdRecord(BbpmInvoiceQueryVo vo){
        AppReply<BbpmInvoiceQueryVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(vo));
        return appReply;
    }

}

