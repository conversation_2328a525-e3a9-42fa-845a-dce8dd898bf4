package com.bonc.ioc.bzf.business.payment.result.create;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR> 递增规则列表 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
@Builder
public class IncreaseRulesParamsRequest {

	@ApiModelProperty(value = "递增规则ID")
	@NotBlank(message = "递增规则ID",groups = {UpdateValidatorGroup.class})
	private  Integer increaseRuleId;

	@ApiModelProperty(value = "递增比例")
	private  BigDecimal increaseProportion;


	@ApiModelProperty(value = "递增金额")
	private BigDecimal increaseAmount;

	@ApiModelProperty(value = "递增周期")
	private String increasePeriod;

	@ApiModelProperty(value = "递增顺序")
	private Integer increaseOrder;

	@ApiModelProperty(value = "分摊方式")
	private String shareType;

	/**
	 * 递增类型
	 */
	@ApiModelProperty(value = "递增类型")
	private String increaseType;


	@ApiModelProperty(value = "企业支付比例")
	private  BigDecimal companyRate;
	@ApiModelProperty(value = "个人支付比例")
	private  BigDecimal personalRate;
	@ApiModelProperty(value = "企业支付金额")
	private  BigDecimal companyAmount;
	@ApiModelProperty(value = "个人支付金额")
	private  BigDecimal personalAmount;

	@Override
	public String toString() {
		return "IncreaseRulesParamsVo{" +
				"increaseRuleId=" + increaseRuleId +
				", increaseProportion=" + increaseProportion +
				", increaseAmount=" + increaseAmount +
				", increasePeriod='" + increasePeriod + '\'' +
				", increaseOrder=" + increaseOrder +
				", shareType='" + shareType + '\'' +
				", companyRate=" + companyRate +
				", personalRate=" + personalRate +
				", companyAmount=" + companyAmount +
				", personalAmount=" + personalAmount +
				'}';
	}
}
