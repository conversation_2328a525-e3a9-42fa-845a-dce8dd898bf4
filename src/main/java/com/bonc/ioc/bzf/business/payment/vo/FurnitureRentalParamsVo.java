package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR> 家具租金列表 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class FurnitureRentalParamsVo {

	@ApiModelProperty(value = "家具ID")
	@NotBlank(message = "家具ID",groups = {UpdateValidatorGroup.class})
	private  String furnitureId;


	@ApiModelProperty(value = "家具名称")
	@NotBlank(message = "家具名称",groups = {UpdateValidatorGroup.class})
	private  String furnitureName;


	@ApiModelProperty(value = "家具租金")
	@NotBlank(message = "家具租金",groups = {UpdateValidatorGroup.class})
	private BigDecimal furnitureRental;

	@Override
	public String toString() {
		return "FurnitureRentalParamsVo{" +
				"furnitureId=" + furnitureId +
				", furnitureName='" + furnitureName + '\'' +
				", furnitureRental=" + furnitureRental +
				'}';
	}
}
