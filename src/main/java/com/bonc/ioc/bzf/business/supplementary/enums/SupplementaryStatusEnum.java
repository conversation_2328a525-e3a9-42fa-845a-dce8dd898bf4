package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 追加账单状态 枚举类
 *
 * <AUTHOR>
 * @since 2025/3/26
 */
public enum SupplementaryStatusEnum {

    /**
     * 暂存
     */
    TEMPORARY("1", "暂存"),

    /**
     * 未通过
     */
    NO_PASS("2", "未通过"),

    /**
     * 待审核
     */
    WAIT_AUDIT("3", "待审核"),

    /**
     * 已完成
     */
    SUCCESS("4", "已完成");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    SupplementaryStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    SupplementaryStatusEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(Integer code) {
        SupplementaryStatusEnum[] enums = values();
        for (SupplementaryStatusEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
