package com.bonc.ioc.bzf.business.payment.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 站内信与短信实时发送接口参数内容-本系统接口使用的参数
 *
 * <AUTHOR>
 * @date 2022-06-10 12:04
 * @change 2022-06-10 12:04 by sqj for init
 */
@Data
public class MessageInterfaceParamVo {

    @ApiModelProperty(value = "模板Id", required = true)
    private String templateId;

    @ApiModelProperty(value = "消息标题站内信实时接口使用")
    private String title;

    @ApiModelProperty(value = "信息内容；如果有多个通知就是多条信息，最多10条")
    private List<MessageInterfaceParamContentVo> messageContent;

    @ApiModelProperty(value = "定时任务-群发接口任务起始时间，格式：yyyy-MM-dd HH:mm:ss；最大日期取值是当前时间+7天")
    private String startDateTime;

    @ApiModelProperty(value = "定时任务-群发接口任务结束时间，格式：yyyy-MM-dd HH:mm:ss；最大日期取值是在开始日期基础上加30天")
    private String endDateTime;
}
