package com.bonc.ioc.bzf.business.payment.controller;


import com.bonc.ioc.bzf.business.payment.result.ChangeContractBusinessRequest;
import com.bonc.ioc.bzf.business.payment.result.ChangeContractPaymentDateRequest;
import com.bonc.ioc.bzf.business.payment.result.ChangeContractRequestV2;
import com.bonc.ioc.bzf.business.payment.result.ChangeContractStartEndDateRequest;
import com.bonc.ioc.bzf.business.payment.result.create.BillCreateParamsRequest;
import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangeStartDTO;
import com.bonc.ioc.bzf.business.payment.service.IBillCreateService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 账单管理(来源业财)v3.0 前端控制器
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmBillCreateEntity")
@Api(tags = "生成账单")
@Validated
public class BbpmBillCreateBusinessController extends McpBaseController {

    @Autowired
    IBillCreateService iBillCreateService;

    /**
     * billCreateAll
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/billCreateAll", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "000-合同签订生成账单接口", notes = "合同签订生成账单接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/billCreateAll")
    public AppReply<String> billCreateAll(@ApiParam(value = "生成账单需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbctPushInfoVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBillCreateService.billCreateAll(vo));
        return appReply;
    }

    /**
     * billVacancyFeeCreate
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/billVacancyFeeCreate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "binghong.tang")
    @ApiOperation(value = "001-3.30空置费生成账单接口", notes = "3.30空置费生成账单接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/billAgreementCreate")
    public AppReply<String> billVacancyFeeCreate(@ApiParam(value = "生成账单需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BillVacancyFeeCreateParamsVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBillCreateService.billVacancyFeeCreate(vo));
        return appReply;
    }



    /**
     * billVacancyFeeCreate
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/billOverdue", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "fem")
    @ApiOperation(value = "项目下合同欠费状态查询", notes = "项目下合同欠费状态查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/billAgreementCreate")
    public AppReply<Object> billOverdue(@ApiParam(value = "欠费状态查询需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) ChargeArrearsVo vo){
        AppReply<Object> appReply = new AppReply<Object>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBillCreateService.billOverdue(vo));
        return appReply;
    }

    /**
     * billReletCreate
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/billCreate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "binghong.tang")
    @ApiOperation(value = "(不用)3.3合同签订生成账单接口", notes = "3.3.合同签订生成账单接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/billCreate")
    public AppReply<String> billCreate(@ApiParam(value = "生成账单需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BillCreateParamsVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(null);
        return appReply;
    }

    /**
     * billReletCreate
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/billReletCreate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "binghong.tang")
    @ApiOperation(value = "(不用)3.26趸租大合同生成账单接口", notes = "3.26趸租大合同生成账单接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/billReletCreate")
    public AppReply<String> billReletCreate(@ApiParam(value = "生成账单需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BillReletCreateParamsVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(null);
        return appReply;
    }


    /**
     * billReletCreate
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/billAgreementCreate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "binghong.tang")
    @ApiOperation(value = "(不用)3.27趸租管理协议生成账单接口", notes = "3.27. 趸租管理协议生成账单接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/billAgreementCreate")
    public AppReply<String> billAgreementCreate(@ApiParam(value = "生成账单需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BillAgreementCreateParamsVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(null);
        return appReply;
    }



    @PostMapping(value = "/businessContractChange", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "商业承租人变更", notes = "商业承租人变更", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 500,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/businessContractChange")
    public AppReply<String> businessContractChange(@ApiParam(value = "变更需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) ChangeContractBusinessRequest vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBillCreateService.businessContractChange(vo));
        return appReply;
    }


    @PostMapping(value = "/businessContractChangePaymentDate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "商业账单缴费日变更", notes = "商业账单缴费日变更", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 500,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/businessContractChangePaymentDate")
    public AppReply<String> businessContractChangePaymentDate(@ApiParam(value = "变更需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) ChangeContractPaymentDateRequest vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBillCreateService.businessContractChangePaymentDate(vo));
        return appReply;
    }


    @PostMapping(value = "/businessContractChangeStart", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "商业起租日变更", notes = "商业起租日变更", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 500,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/businessContractChangeStart")
    public AppReply<String> businessContractChangeStart(@ApiParam(value = "变更需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) ChangeContractStartEndDateRequest vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBillCreateService.businessContractChangeStart(vo));
        return appReply;
    }


    @PostMapping(value = "/businessContractChange091011", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "09商业账单缴费日期变更，10商业合同起租日变更，11商业合同租金标准、缩租", notes = "09商业账单缴费日期变更，10商业合同起租日变更，11商业合同租金标准、缩租", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 500,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbpmBillCreateEntity/businessContractChange091011")
    public AppReply<String> businessContractChange091011(@ApiParam(value = "变更需要的信息" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) ChangeContractRequestV2 vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBillCreateService.businessContractChange091011(vo));
        return appReply;
    }

    /**
     * 364-合同变更试算
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/changeTrialPreviewBills", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "364-合同变更试算", notes = "364-合同变更试算", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ChangeTrialPreviewBillsResultVo>> changeTrialPreviewBills(@ApiParam(value = "生成账单需要的信息", required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbctPushInfoVo vo) {
        AppReply<List<ChangeTrialPreviewBillsResultVo>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iBillCreateService.changeTrialPreviewBills(vo));
        return appReply;
    }
}

