package com.bonc.ioc.bzf.business.reminder.dao;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendSubLogEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import java.util.List;

/**
 * 催缴规则消息发送日志--子表  Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@Mapper
public interface BbpmMessageSendSubLogMapper extends McpBaseMapper<BbpmMessageSendSubLogEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change 2023-08-04 by binghong.tang for init
     */
    List<BbpmMessageSendSubLogPageResultVo> selectByPageCustom(@Param("vo") BbpmMessageSendSubLogPageVo vo );
}
