package com.bonc.ioc.bzf.business.reminder.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 缴费提醒规则--主表 实体类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@ApiModel(value="BbpmReminderRulesMainAndSubVo对象", description="缴费提醒合并表")
@Data
public class BbpmReminderRulesMainAndSubVo extends McpBaseVo implements Serializable{


    /**
     * 规则编号
     */
    @ApiModelProperty(value = "规则编号")
    @NotBlank(message = "规则编号不能为空",groups = {UpdateValidatorGroup.class})
                                  private String rulesId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    @NotBlank(message = "规则名称不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String ruleName;

    /**
     * 合同类型:01散租合同,02趸租合同,03趸租管理协议
     */
    @ApiModelProperty(value = "合同类型:01散租合同,02趸租合同,03趸租管理协议")
    @McpDictPoint(dictCode = "CALL_CONTRACT_TYPE", overTransCopyTo = "contractTypeName")
    private String contractType;


    /**
     * 业务类型:01公租房,02保租房
     */
    @ApiModelProperty(value = "业务类型:01公租房,02保租房")
    @McpDictPoint(dictCode = "CALL_BUSINESS_TYPE", overTransCopyTo = "businessTypeName")
    private String businessType;


    /**
     * 启用状态:01停用,02启用
     */
    @ApiModelProperty(value = "启用状态:01停用,02启用")
    @McpDictPoint(dictCode = "CALL_ENABLE_STATUS", overTransCopyTo = "enableStatusName")
    private String enableStatus;


    /**
     * 启用时间
     */
    @ApiModelProperty(value = "启用时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date activationTime;

    /**
     * 停用时间
     */
    @ApiModelProperty(value = "停用时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date downTime;
    /**
     * 唯一标识符
     */
    @ApiModelProperty(value = "唯一标识符")
    @NotBlank(message = "唯一标识符不能为空",groups = {UpdateValidatorGroup.class})
    private String rulesSubId;

    /**
     * 账单应缴日期前后:01前,02后
     */
    @ApiModelProperty(value = "账单应缴日期前后:01前,02后")
    private String billDueAround;

    /**
     * 账单应缴日期天数
     */
    @ApiModelProperty(value = "账单应缴日期天数")
    private String billDueDays;

    /**
     * 短信消息模板id
     */
    @ApiModelProperty(value = "短信消息模板id")
    private String textMessageTemplateId;

    /**
     * 站内消息模板id
     */
    @ApiModelProperty(value = "站内消息模板id")
    private String stationMessageTemplateId;

    @ApiModelProperty(value = "催缴原因(类型)")
    private String collectionReason;
}
