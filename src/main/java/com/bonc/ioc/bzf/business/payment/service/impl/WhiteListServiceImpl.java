package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCashPledgeMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmCashPledgeEntity;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCashPledgeService;
import com.bonc.ioc.bzf.business.payment.service.IWhiteListService;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 押金条 服务类实现
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by binghong.tang for init
 */
@Slf4j
@Service
public class WhiteListServiceImpl extends McpBaseServiceImpl<BbpmCashPledgeEntity> implements IWhiteListService {

    /**
     * 每次推送限制10条
     */
    private static final Integer MAX_SEND = 10;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Override
    public JSONObject whiteList(List<WhiteListVo> whiteListVoList) {

        if(whiteListVoList == null || whiteListVoList.size() == 0){
            return null;
        }
        JSONObject message = new JSONObject();
        int limit = countStep(whiteListVoList.size());
        List<List<WhiteListVo>> splitList = Stream.iterate(0, n -> n + 1).limit(limit).parallel().map(
                a -> whiteListVoList.stream().skip((long) a * MAX_SEND).limit(MAX_SEND).parallel().collect(Collectors.toList())).collect(Collectors.toList());

        for (List<WhiteListVo> whiteListVos : splitList){
            List<WhiteListRequest> whiteListRequestList = new ArrayList<>();
            for(WhiteListVo whiteListVo : whiteListVos){
                WhiteListRequest whiteListRequest = WhiteListRequest.builder()
                        .tenantId(whiteListVo.getPersonCustomerId())
                        .publicRecordNo(whiteListVo.getPublicRentalFilingNum())
                        .tenantName(whiteListVo.getName())
                        .tenantNo(whiteListVo.getCertificateNum())
//                        .withholdRatio(BigDecimal.valueOf(whiteListVo.getPublicRentalSubsidyRatio()!=null?(whiteListVo.getPublicRentalSubsidyRatio()/100):0).setScale(2,BigDecimal.ROUND_HALF_UP))
                        .withholdRatio(BigDecimal.valueOf(StringUtils.isNotBlank(whiteListVo.getRealityRentalSubsidyRatio()) ?(Double.parseDouble(whiteListVo.getRealityRentalSubsidyRatio())/100):0).setScale(4,BigDecimal.ROUND_HALF_UP))
                        .operateType(whiteListVo.getOperateType())
                        .projectId(whiteListVo.getProjectId())
                        .build();
                whiteListRequestList.add(whiteListRequest);
            }
            //请求工银
            message = sendWhiteList(whiteListRequestList);
        }

        return message;
    }

    @Override
    public JSONObject whiteListSendGy(List<WhiteListRequestVo> whiteListRequestList) {
        List<WhiteListRequest> requests =  new ArrayList<>();
        for(WhiteListRequestVo whiteListRequestVo : whiteListRequestList){
            WhiteListRequest request = WhiteListRequest.builder()
                    .tenantId(whiteListRequestVo.getTenantId())
                    .publicRecordNo(whiteListRequestVo.getPublicRecordNo())
                    .tenantName(whiteListRequestVo.getTenantName())
                    .tenantNo(whiteListRequestVo.getTenantNo())
                    .withholdRatio(whiteListRequestVo.getWithholdRatio())
                    .operateType(whiteListRequestVo.getOperateType())
                    .projectId(whiteListRequestVo.getProjectId())
                    .build();
            requests.add(request);
        }
        return sendWhiteList(requests);
    }

    /**
     * 3.9.接收白名单推送接口
     * @param whiteListRequestList
     * @return
     */

    public JSONObject sendWhiteList(List<WhiteListRequest> whiteListRequestList){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<List<WhiteListRequest>> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(whiteListRequestList);

        log.info("3.9接收白名单推送接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.9接收白名单推送接口请求参数json:" + jsonRequest);

        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipChargeFeignClient.whiteList(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/whiteList", parentRequest);
        }
        log.info("调用工银3.9接收白名单推送接口返回结果为:" + responseBody);
//        String responseBody = "{\n" +
//                "    \"busiCode\":\"\",\n" +
//                "    \"code\":\"00000\",\n" +
//                "    \"data\":{\n" +
//                "        \"RY000000271289\":\"ID为RY000000271289的租户白名单创建失败！\",\n" +
//                "        \"PC-fed5acba81224540a07fcbeb5a7c2736\":\"ID为PC-fed5acba81224540a07fcbeb5a7c2736的租户白名单创建失败！\"\n" +
//                "    },\n" +
//                "    \"message\":\"提交成功\"\n" +
//                "}";

        if(StringUtils.isBlank(responseBody)){
            throw new McpException("调用*3.9接收白名单推送接口失败,*返回结果为:" + responseBody);
        }

        FaceMdMapResultTwo<JSONObject> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);

        //创建失败也是00000，触发工银接口异常才会走这个if
        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.9接收白名单推送接口失败:"+responseBody);
            throw new McpException("*提示:"+faceMdMapResult.getMessage());
//            if(faceMdMapResult.getData()!=null && faceMdMapResult.getData().get("Message") != null && StringUtils.isNotBlank(faceMdMapResult.getData().get("Message").toString())){
//                throw new McpException("*提示:"+faceMdMapResult.getData().get("Message").toString());
//            }else{
//                throw new McpException("*提示:"+faceMdMapResult.getMessage());
//            }
        }

        return faceMdMapResult.getData();
    }

    /**
     * 计算切分次数
     */
    private static Integer countStep(Integer size) {
        return (size + MAX_SEND - 1) / MAX_SEND;
    }

}
