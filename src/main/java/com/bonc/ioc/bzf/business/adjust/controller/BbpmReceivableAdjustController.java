package com.bonc.ioc.bzf.business.adjust.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import com.bonc.ioc.common.validator.inf.*;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RestController;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.bzf.business.adjust.service.IBbpmReceivableAdjustService;
import io.swagger.annotations.*;
import com.bonc.ioc.bzf.business.adjust.entity.*;
import java.util.List;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 应收调整 前端控制器
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@RestController
@RequestMapping("/v2/business/bbpmReceivableAdjustEntity")
@Api(tags = "应收调整")
@Validated
public class BbpmReceivableAdjustController extends McpBaseController {
    @Resource
    private IBbpmReceivableAdjustService baseService;


    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "yuanxuesong")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "需要删除的主键" ,required = false) @RequestBody String id){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdRecord(id);
        return appReply;
     }


    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应收调整
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @PostMapping(value = "/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "yuanxuesong")
    @ApiOperation(value = "根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> saveByIdRecord(@ApiParam(value = "需要更新或新增的应收调整" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmReceivableAdjustVo vo){
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.saveByIdRecord(vo));
        return appReply;
     }

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "yuanxuesong")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmReceivableAdjustVo> selectByIdRecord(@ApiParam(value = "需要查询的主键" ,required = false) @RequestParam(required = false) String id){
        AppReply<BbpmReceivableAdjustVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(id));
        return appReply;
     }

    /**
     * selectByCcid 根据商业合同变更主键查询
     * @param ccid 商业合同变更主键
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @GetMapping(value = "/selectByCcid", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "yuanxuesong")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmReceivableAdjustVo> selectByCcid(@ApiParam(value = "需要查询的主键" ,required = false) @RequestParam(required = false) String ccid){
        AppReply<BbpmReceivableAdjustVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByCcid(ccid));
        return appReply;
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "yuanxuesong")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmReceivableAdjustPageResultVo>>> selectByPageRecord(BbpmReceivableAdjustPageVo vo){
        AppReply<PageResult<List<BbpmReceivableAdjustPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }

    /**
     * selectByExportList 导出查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @GetMapping(value = "/selectByExportList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "yuanxuesong")
    @ApiOperation(value = "导出查询", notes = "导出查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<AdjustBillExportVO>> selectByExportList(BbpmReceivableAdjustPageVo vo){
        AppReply<List<AdjustBillExportVO>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByExportList(vo));
        return appReply;
    }


    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应收调整
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @PostMapping(value = "/updateForTk", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "yuanxuesong")
    @ApiOperation(value = "修改退款状态", notes = "修改退款状态", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateForTk(@ApiParam(value = "需要更新或新增的应收调整" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) AdjustGxVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateForTk(vo);
        return appReply;
    }


    /**
     * selectForTj 统计接口
     * @return  com.bonc.ioc.common.util.AppReply 统计数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @GetMapping(value = "/selectForTj", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "yuanxuesong")
    @ApiOperation(value = "统计接口", notes = "统计接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<AdjustTj> selectForTj(){
        AppReply<AdjustTj> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectForTj());
        return appReply;
    }

    @GetMapping(value = "/selectBillForAdjust", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "yuanxuesong")
    @ApiOperation(value = "根据调整单id查询账单", notes = "根据调整单id查询账单", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmBillForAdjustPageVo>> selectBillForAdjust(@ApiParam(value = "需要查询的主键" ,required = false) @RequestParam(required = false) String id){
        AppReply<List<BbpmBillForAdjustPageVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectBillForAdjust(id));
        return appReply;
    }

    /**
     * selectContractForAdjustId 查询应收调整合同编号接口
     * @return  com.bonc.ioc.common.util.AppReply 应收调整合同编号
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @GetMapping(value = "/selectContractForAdjustId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "yuanxuesong")
    @ApiOperation(value = "查询应收调整合同编号接口", notes = "查询应收调整合同编号接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String[]> selectContractForAdjustId(@RequestParam(required = false) String id){
        AppReply<String[]> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectContractForAdjustId(id));
        return appReply;
    }

    @PostMapping(value = "/updateAdjustStatus", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "sx")
    @ApiOperation(value = "修改追加账单状态接口", notes = "修改追加账单状态接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateAdjustStatus(@RequestParam(required = true)  String id, @RequestParam(required = true) String status){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateAdjustStatus(id,status);
        return appReply;
    }

    @PostMapping(value = "/adjustmentImport", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "yuanxuesong")
    @ApiOperation(value = "导入应收调整", notes = "导入应收调整", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> adjustmentImport(@ApiParam(value = "需要更新或新增的应收调整" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmReceivableAdjustVo vo){
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.adjustmentImport(vo));
        return appReply;
    }

}

