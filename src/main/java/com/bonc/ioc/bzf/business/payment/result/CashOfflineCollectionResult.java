package com.bonc.ioc.bzf.business.payment.result;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 现金线下收款 返回结果
 *
 * 工银返回的字段就是开头大写
 */
@Data
public class CashOfflineCollectionResult {

//    private Long receipt;//收款单ID	Long	20

     private String Message;
     private String chargeId;
//     private Long BillId;
     private String BillId;

     //2024年12月冲刺增加
     private BigDecimal amount;//	本次消费金额	BigDecimal	是
}
