package com.bonc.ioc.bzf.business.supplementary.service;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPaymentEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.supplementary.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 追加账单表 服务类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
public interface IBbpmSupplementaryPaymentService extends IMcpBaseService<BbpmSupplementaryPaymentEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    String insertRecord(BbpmSupplementaryPaymentVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    List<String> insertBatchRecord(List<BbpmSupplementaryPaymentVo> voList);

    /**
     * 批量新增并且新增产品信息
     *
     * @param voList 需要保存的记录
     * @return 主键id列表
     */
    List<String> insertBatchAndProduct(List<BbpmSupplementaryPaymentVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param paymentId 需要删除的追加账单id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void removeByIdRecord(String paymentId);

    /**
     * 根据上级id删除(逻辑删除)
     *
     * @param parentId 上级id
     */
    void removeByParentId(String parentId);

    /**
     * 根据上级id删除(物理删除)
     *
     * @param parentId 上级id
     */
    void deleteByParentId(String parentId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param paymentIdList 需要删除的追加账单id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void removeByIdsRecord(List<String> paymentIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的追加账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void updateByIdRecord(BbpmSupplementaryPaymentVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的追加账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void updateBatchByIdRecord(List<BbpmSupplementaryPaymentVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的追加账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void saveByIdRecord(BbpmSupplementaryPaymentVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的追加账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void saveBatchByIdRecord(List<BbpmSupplementaryPaymentVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param paymentId 需要查询的追加账单id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    BbpmSupplementaryPaymentVo selectByIdRecord(String paymentId);

    /**
     * 根据上级id查询追加账单列表
     *
     * @param parentId 上级id
     * @return 追加账单列表
     */
    List<BbpmSupplementaryPaymentVo> selectListByParentId(String parentId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    PageResult<List<BbpmSupplementaryPaymentPageResultVo>> selectByPageRecord(BbpmSupplementaryPaymentPageVo vo);
}
