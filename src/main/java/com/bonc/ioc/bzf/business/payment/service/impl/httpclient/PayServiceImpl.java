package com.bonc.ioc.bzf.business.payment.service.impl.httpclient;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.dao.BbpDictMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpDictEntity;
import com.bonc.ioc.bzf.business.payment.feign.service.IBfipSettlementFeignService;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.service.IBbpDictService;
import com.bonc.ioc.bzf.business.payment.service.IPayService;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import lombok.Data;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付模块服务层接口httpclient方式实现类
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023/3/4 22:23
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "yecai",name = "feign",havingValue = "false")
public class PayServiceImpl implements IPayService {

    @Autowired
    private IBfipSettlementFeignService  bfipSettlementFeignService;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Override
    public PayResultVo pay(PayParmsVo vo) {
        if(vo == null){
            throw new McpException("参数对象不能为空");
        }

        PayDataParmsVo data = vo.getData();
        if(data == null){
            throw new McpException("请求参数对象不能为空");
        }

        String returnUrl = data.getReturnUrl();
        if(returnUrl != null){
            if(returnUrl.length() > 100 ){
                throw new McpException("returnUrl长度不能超过100");
            }
        }
        List<PayDataBillInfoParmsVo> billInfos = data.getBillInfo();
        if(billInfos == null || billInfos.size() == 0){
            throw new McpException("至少有一个主账单");
        }

        for (PayDataBillInfoParmsVo billInfo:billInfos){
            if(billInfo == null){
                throw new McpException("主账单信息参数对象不能为空");
            }

            Long billId = billInfo.getBillId();
            if(billId == null){
                throw new McpException("billId不能为空");
            }

            BigDecimal paidMoney = billInfo.getPaidMoney();
            if(paidMoney == null){
                throw new McpException("paidMoney不能为空");
            }

            String paidMoneyStr = paidMoney.toString();
            if(paidMoneyStr == null || "".equals(paidMoneyStr)){
                throw new McpException("paidMoney不能为空");
            }

            int index = paidMoneyStr.indexOf(".") + 1;
            if((paidMoneyStr.length() - index) > 4){
                throw new McpException("paidMoney小数点后只能有位");
            }
        }

        String result = new RestTemplateUtil<PayParmsVo>().post(yecaiUrl+"/settlement/v1/aggregatepayment/pay", vo);

        log.info("【调用工银支付接口 - 请求参数】{}",vo.toString());
        log.info("【调用工银支付接口 - 返回结果】{}",result);

        if (StringUtils.isBlank(result)){
            log.info("【调用工银支付接口】未获取到响应结果");
            throw new McpException("系统异常");
        }

        return JSONObject.parseObject(result, PayResultVo.class);
    }

    @Override
    public PayUnlockResultVo payUnlock(PayUnlockParmsVo vo) {

        String result = new RestTemplateUtil<PayUnlockParmsVo>().post(yecaiUrl+"/settlement/v1/aggregatepayment/pay/unlock", vo);
        log.info("【调用工银支付解锁接口 - 请求参数】{}",vo.toString());
        log.info("【调用工银支付解锁接口 - 返回结果】{}",result);

        if (StringUtils.isBlank(result)){
            log.info("【调用工银支付解锁接口】未获取到响应结果");
            throw new McpException("系统异常");
        }

        return JSONObject.parseObject(result, PayUnlockResultVo.class);
    }
}
