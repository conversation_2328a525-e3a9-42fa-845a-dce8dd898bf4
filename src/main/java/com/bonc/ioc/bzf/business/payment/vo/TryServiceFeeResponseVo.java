package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 3.45 增值服务费试算接口 返回参数
 */
@Data
public class TryServiceFeeResponseVo {

    @ApiModelProperty(value = "增值服务费试算结果列表")
    private List<TryResultListVo> subTryServiceFeeList;

    @ApiModelProperty(value = "增值服务费总额")
    private BigDecimal totalAmount;

}

