package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 缴费类型 枚举类
 *
 * <AUTHOR>
 * @since 2023/5/6
 */
public enum PayTypeEnum {

    /**
     * 通过
     */
    PERCENT("1", "比例"),

    /**
     * 待签约
     */
    AMOUNT("2", "金额");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    PayTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    PayTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        PayTypeEnum[] enums = values();
        for (PayTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
