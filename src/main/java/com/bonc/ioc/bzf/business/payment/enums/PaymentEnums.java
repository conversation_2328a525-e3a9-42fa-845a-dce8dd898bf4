package com.bonc.ioc.bzf.business.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum PaymentEnums {

    COLLECTION_CHANNEL_CARD("02","POS刷卡"),
    COLLECTION_CHANNEL_QR("03","POS二维码"),
    COLLECTION_CHANNEL_CASH("04","现金"),
    COLLECTION_CHANNEL_OFFLINE("05","线下转账"),
    COLLECTION_CHANNEL_CHEQUE("06","支票"),

    PAYMENTTYPE_OFFLINE("0","线下转账"),
    PAYMENTTYPE_CHEQUE("1","支票"),

    TYPE_OF_CONTRACT_LOOSE("01","散租"),
    TYPE_OF_CONTRACT_SINGLERENT("02","趸租合同"),
    TYPE_OF_CONTRACT_MANAGER("03","趸租管理协议"),
    TYPE_OF_CONTRACT_BUSINESS("07","商业"),

    CHARGE_SUBJECT_NO_ONE("01", "房屋租金"),
    CHARGE_SUBJECT_NO_TWO("02", "押金"),
    CHARGE_SUBJECT_NO_THREE("03", "家具家电租金"),
    CHARGE_SUBJECT_NO_FOUR("04", "车位租金"),
    CHARGE_SUBJECT_NO_FIVE("05", "仓库租金"),
    CHARGE_SUBJECT_NO_SEVEN("07", "物业费"),
    CHARGE_SUBJECT_NO_FIFTEEN("15", "保证金"),

    WITHHOLDING_NO("0","否"),
    WITHHOLDING_YES("1","是"),

    CHARGEOWNER_ENTERPRISE("01","企业"),
    CHARGEOWNER_PERSON("02","个人"),

    SIGNATORYTYPE_ENTERPRISE("01","企业"),
    SIGNATORYTYPE_PERSON("00","租户"),

    CONTRACTSOURCETYPE_RENEWAL("3","续租"),
    CONTRACTSOURCETYPE_MAIN_LESSEE("7","主承租人合同变更合同"),
    CONTRACTSOURCETYPE_PURCHASE_AGREEMENT("8","购房补充协议"),
    CONTRACTSOURCETYPE_RENT_AREA("11","租金标准/面积变更"),
    CONTRACTSOURCETYPE_CAR("12","静态停车合同"),
    CONTRACTSOURCETYPE_STORAGE("13","仓储合同"),
    CONTRACTSOURCETYPE_BUSINESS_LESSEE("14","商业承租人变更"),
    CONTRACTSOURCETYPE_PAYMENT_CYCLE_CHANGE("22","缴费周期变更"),


    GY_CHANGETYPE_MAIN_LESSEE("01","主承租人变更"),
    GY_CHANGETYPE_PURCHASE_AGREEMENT("03","购房签订补充协"),
    GY_RENT_AREA("07","租金标准/面积变更"),
    GY_BUSINESS_LESSEE("08","商业承租人变更"),
    GY_PAYMENT_CYCLE_CHANGE("16","缴费周期变更"),

    PROJECTFORMAT_GZ("01","公租房"),
    PROJECTFORMAT_BZ("02","保租房"),
    PROJECTFORMAT_SY("03","商业"),

    //签字状态
    SIGN_STATUS_NO("01", "未签字"),
    SIGN_STATUS_OK("02", "已签字"),
    SIGN_STATUS_WITHOUT("03", "不用签字"),

    STATUS_VOIDED("02","已作废"),

    //账单管理-现金盘点-状态
    BILLING_CASH_STATUS_THREE("3","已退回"),

    BACKSTATUS_ONE("01","已回退"),
    BACKSTATUS_TWO("02","未回退"),

    MULTIPROJECT_YES("01","是"),
    MULTIPROJECT_NO("02","否"),


    COLLECTION_WHITE_PEOPLE("01", "人员白名单"),
    COLLECTION_WHITE_PROJECT("02", "项目白名单"),

    TENANTRY_ONE("01","所有租户"),
    TENANTRY_TWO("02","企业"),
    TENANTRY_THREE("03","个人"),

    HISTORY_NO("0","不是历史数据"),
    HISTORY_YES("1","是历史数据");

    private final String code;
    private final String msg;

}

