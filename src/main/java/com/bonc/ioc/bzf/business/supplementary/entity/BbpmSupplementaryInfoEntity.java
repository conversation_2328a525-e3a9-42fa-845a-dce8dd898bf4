package com.bonc.ioc.bzf.business.supplementary.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 追加单表 实体类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Data
@TableName("bbpm_supplementary_info")
@ApiModel(value = "BbpmSupplementaryInfoEntity对象", description = "追加单表")
public class BbpmSupplementaryInfoEntity extends McpBaseEntity implements Serializable {

    /**
     * 追加单id
     */
    @ApiModelProperty(value = "追加单id")
    @TableId(value = "supplementary_id", type = IdType.ASSIGN_UUID)
    private String supplementaryId;

    /**
     * 追加单号
     */
    @ApiModelProperty(value = "追加单号")
    private String supplementaryCode;

    /**
     * 追加单状态(1.暂存 2.未通过 3.待审核 4.已完成)
     */
    @ApiModelProperty(value = "追加单状态(1.暂存 2.未通过 3.待审核 4.已完成)")
    private String supplementaryStatus;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 签约类型(01.散租 02.趸租 03.管理协议)
     */
    @ApiModelProperty(value = "签约类型(01.散租 02.趸租 03.管理协议)")
    private String signType;

    /**
     * 产品类型(01.公租房 07.保租房)
     */
    @ApiModelProperty(value = "产品类型(01.公租房 07.保租房)")
    private String productType;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 社会统一信息用代码
     */
    @ApiModelProperty(value = "社会统一信息用代码")
    private String customerCreditCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;

    /**
     * 追加账单依据
     */
    @ApiModelProperty(value = "追加账单依据")
    private String supplementaryFile;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    private String remark;

    /**
     * 缴费周期
     */
    @ApiModelProperty(value = "缴费周期")
    private String paymentCycleCode;

}
