package com.bonc.ioc.bzf.business.payment.config;

import com.bonc.ioc.common.constant.XssWhiteInfo;
import com.bonc.ioc.common.security.XssWhiteListAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class XssWhiteListImpl extends XssWhiteListAbstract {

    /**
     * getReleaseUris 针对特殊字符注入拦截器的放行接口
     *
     * @return  java.util.List<com.bonc.ioc.common.constant.XssWhiteInfo>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2021/12/02 11:09:38
     * @change
     * 2021/12/02 11:09:38 by jin.xu for init
     */
    @Override
    public List<XssWhiteInfo> getReleaseUris() {
        List<XssWhiteInfo> list = new ArrayList<>();
        XssWhiteInfo xssWhiteInfo = new XssWhiteInfo();
        xssWhiteInfo.setUri("/bzf-business-payment/springboot/timTask");
        list.add(xssWhiteInfo);
        return list;
    }
}
