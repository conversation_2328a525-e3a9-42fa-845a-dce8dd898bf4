package com.bonc.ioc.bzf.business.payment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 报盘记录查询 实体类
 *
 * <AUTHOR>
 * @date 2024-02-23
 * @change 2024-02-23 by binghong.tang for init
 */
@TableName("bbpm_offline")
@ApiModel(value="BbpmOffLineEntity对象", description="现金存款单回退信息")
public class BbpmOffLineEntity extends McpBaseEntity implements Serializable{

    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "凭证号")
    private String summaryNo;
    @ApiModelProperty(value = "交易流水号")
    private String receiptNo;
    @ApiModelProperty(value = "存款时间开始日期")
    private String startDate;
    @ApiModelProperty(value = "存款时间结束日期")
    private String endDate;
    @ApiModelProperty(value = "回退状态")
    private String backStatus;

    @ApiModelProperty(value = "回退原因")
    private String backReason;
    @ApiModelProperty(value = "操作人id")
    private String backOperator	;
    @ApiModelProperty(value = "回退时间")
    private String backTime	;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getSummaryNo() {
        return summaryNo;
    }

    public void setSummaryNo(String summaryNo) {
        this.summaryNo = summaryNo;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getBackStatus() {
        return backStatus;
    }

    public void setBackStatus(String backStatus) {
        this.backStatus = backStatus;
    }

    public String getBackReason() {
        return backReason;
    }

    public void setBackReason(String backReason) {
        this.backReason = backReason;
    }

    public String getBackOperator() {
        return backOperator;
    }

    public void setBackOperator(String backOperator) {
        this.backOperator = backOperator;
    }

    public String getBackTime() {
        return backTime;
    }

    public void setBackTime(String backTime) {
        this.backTime = backTime;
    }
}