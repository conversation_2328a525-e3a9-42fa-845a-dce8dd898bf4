package com.bonc.ioc.bzf.business.payment.service.impl.feign;

import com.bonc.ioc.bzf.business.payment.feign.service.IBfipSettlementFeignService;
import com.bonc.ioc.bzf.business.payment.service.IPayService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付模块服务层接口feign方式实现类
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023/3/4 22:23
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "yecai",name = "feign",havingValue = "true")
public class PayServiceImpl implements IPayService {

    @Autowired
    private IBfipSettlementFeignService  bfipSettlementFeignService;

    @Override
    public PayResultVo pay(PayParmsVo vo) {
        if(vo == null){
            throw new McpException("参数对象不能为空");
        }

        PayDataParmsVo data = vo.getData();
        if(data == null){
            throw new McpException("请求参数对象不能为空");
        }

        String returnUrl = data.getReturnUrl();
        if(returnUrl != null){
            if(returnUrl.length() > 100 ){
                throw new McpException("returnUrl长度不能超过100");
            }
        }
        List<PayDataBillInfoParmsVo> billInfos = data.getBillInfo();
        if(billInfos == null || billInfos.size() == 0){
            throw new McpException("至少有一个主账单");
        }

        for (PayDataBillInfoParmsVo billInfo:billInfos){
            if(billInfo == null){
                throw new McpException("主账单信息参数对象不能为空");
            }

            Long billId = billInfo.getBillId();
            if(billId == null){
                throw new McpException("billId不能为空");
            }

            BigDecimal paidMoney = billInfo.getPaidMoney();
            if(paidMoney == null){
                throw new McpException("paidMoney不能为空");
            }

            String paidMoneyStr = paidMoney.toString();
            if(paidMoneyStr == null || "".equals(paidMoneyStr)){
                throw new McpException("paidMoney不能为空");
            }

            int index = paidMoneyStr.indexOf(".") + 1;
            if((paidMoneyStr.length() - index) > 4){
                throw new McpException("paidMoney小数点后只能有位");
            }
        }

        return bfipSettlementFeignService.pay(vo);
    }

    @Override
    public PayUnlockResultVo payUnlock(PayUnlockParmsVo vo) {
        return bfipSettlementFeignService.payUnlock(vo);
    }
}
