package com.bonc.ioc.bzf.business.payment.result;

import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangePaymentDateDTO;
import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangeStartDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 合同变更 商业起租日变更
 */
@Data
public class ChangeContractStartEndDateRequest implements Serializable {
    @ApiModelProperty(value = "变更类型")
    private String changeType;
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "商业起租日变更")
    private BusinessContractChangeStartDTO businessContractChangeStartDTO;//(商业起租日变更)



}
