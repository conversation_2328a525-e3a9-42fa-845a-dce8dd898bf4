package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.entity.OfferEntity;
import com.bonc.ioc.bzf.business.payment.dao.OfferMapper;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.FaceMdMapResult;
import com.bonc.ioc.bzf.business.payment.result.FaceMdMapResultTwo;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.service.IOfferService;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import feign.Response;
import org.apache.kafka.common.protocol.types.Field;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.payment.vo.*;
import org.springframework.beans.BeanUtils;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 报盘记录 服务类实现
 *
 * <AUTHOR>
 * @date 2023-10-20
 * @change 2023-10-20 by binghong.tang for init
 */
@Slf4j
@Service
public class OfferServiceImpl extends McpBaseServiceImpl<OfferEntity> implements IOfferService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private OfferMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IOfferService baseService;

    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;


    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;




    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<OfferPageResultVo>> selectByPageRecord(OfferPageVo vo) {

        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setCurrent(Integer.valueOf(vo.getPageNumber()+""));
        ParentRequest<OfferPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        //请求业财接口
        //重新set值到分页实体、转换
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.40报盘文件分页查询接口请求参数json:"+jsonRequest);
        String responseBody = null;
        if (yecaiFeign){
            responseBody = bfipSettlementFeignClient.page(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/chargestatement/page", parentRequest);
        }
        log.info("3.40报盘文件分页查询接口返回:"+responseBody);

        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }
        FaceHttpResultTwo<OfferPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            throw new McpException("*提示:"+billListResultFaceHttpResultTwo.getMessage());
        }

        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(null);
        }
        List<OfferPageResultVo> collectionListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(),OfferPageResultVo.class);

        List<OfferPageResultVo> result = new ArrayList<>();

        for(OfferPageResultVo collection : collectionListResultList){

            result.add(collection);
        }

        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);

        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(),result);

    }


    @Override
    public void exceldownload(ExcelDownloadVo vo, HttpServletResponse httpServletResponse) {
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<ExcelDownloadVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        log.info("3.41报盘文件下载接口请求参数json:"+JSONObject.toJSONString(parentRequest));
        Response response = bfipSettlementFeignClient.exceldownload(parentRequest);
        log.info("3.41报盘文件下载接口请求返回状态:"+response.status());

        InputStream inputStream = null;
        OutputStream out = null;
        try {
            Map<String, Collection<String>> header = response.headers();
            header.forEach((key,values)->{
                log.info("3.41报盘文件下载接口header信息"+key+":"+values);
            });

            Collection<String> contentDispositions  = header.get("content-disposition");
            Collection<String> contentTypes = header.get("content-type");
            String contentType = "";
            if(contentTypes!=null && contentTypes.size() >0){
                contentType =  contentTypes.toArray()[0].toString();
            }

            if(!contentType.contains("application/vnd.ms-excel")){
                String body = response.body().toString();
                if(StringUtils.isBlank(body)){
                    throw new McpException("body为空,content-length:"+header.get("content-length").toArray()[0].toString());
                }
                log.info("3.41报盘文件下载接口下载失败后body信息:"+body);
                FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(body), FaceMdMapResult.class);
                throw new McpException(faceMdMapResult!=null ? ("*提示:"+ faceMdMapResult.getMessage()) : "body为空");
            }

            httpServletResponse.setContentType(contentType);
            if(contentDispositions!=null && contentDispositions.size()>0) {
                String _tmp = contentDispositions.toArray()[0].toString();
                httpServletResponse.setHeader("Content-Disposition", _tmp);
            }
            out = httpServletResponse.getOutputStream();  // new FileOutputStream(new File("e:\\test.jpg"));
            inputStream =response.body().asInputStream();
            byte[] buffer = new byte[2048];
            int i = -1;
            while ((i = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, i);
            }
            out.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            if(out!=null){
                try {
                    out.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            if(inputStream!=null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(OfferVo vo) {
        if(vo == null) {
            return null;
        }

        OfferEntity entity = new OfferEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setReqno(null);
        if(!baseService.insert(entity)) {
            log.error("报盘记录新增失败:" + entity.toString());
            throw new McpException("报盘记录新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getReqno(),1)) {
                log.error("报盘记录新增后保存历史失败:" + entity.toString());
                throw new McpException("报盘记录新增后保存历史失败");
            }

            log.debug("报盘记录新增成功:"+entity.getReqno());
            return entity.getReqno();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<OfferVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<OfferEntity> entityList = new ArrayList<>();
        for (OfferVo item:voList) {
            OfferEntity entity = new OfferEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (OfferEntity item:entityList){
            item.setReqno(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("报盘记录新增失败");
            throw new McpException("报盘记录新增失败");
        }else{
            List<String> kidList = entityList.stream().map(OfferEntity::getReqno).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("报盘记录批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("报盘记录批量新增后保存历史失败");
            }

            log.debug("报盘记录新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param reqno 需要删除的请求编号
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String reqno) {
        if(!StringUtils.isEmpty(reqno)) {
            if(!baseService.saveOperationHisById(reqno,3)) {
                log.error("报盘记录删除后保存历史失败:" + reqno);
                throw new McpException("报盘记录删除后保存历史失败");
            }

            if(!baseService.removeById(reqno)) {
                log.error("报盘记录删除失败");
                throw new McpException("报盘记录删除失败"+reqno);
            }
        } else {
            throw new McpException("报盘记录删除失败请求编号为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param reqnoList 需要删除的请求编号
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> reqnoList) {
        if(!CollectionUtils.isEmpty(reqnoList)) {
            int oldSize = reqnoList.size();
            reqnoList = reqnoList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(reqnoList) || oldSize != reqnoList.size()) {
                throw new McpException("报盘记录批量删除失败 存在主键id为空的记录"+StringUtils.join(reqnoList));
            }

            if(!baseService.saveOperationHisByIds(reqnoList,3)) {
                log.error("报盘记录批量删除后保存历史失败:" + StringUtils.join(reqnoList));
                throw new McpException("报盘记录批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(reqnoList)) {
                log.error("报盘记录批量删除失败");
                throw new McpException("报盘记录批量删除失败"+StringUtils.join(reqnoList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的报盘记录
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(OfferVo vo) {
        if(vo != null) {
            OfferEntity entity = new OfferEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getReqno())) {
                throw new McpException("报盘记录更新失败传入请求编号为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("报盘记录更新失败");
                throw new McpException("报盘记录更新失败"+entity.getReqno());
            } else {
                if(!baseService.saveOperationHisById(entity.getReqno(),2)) {
                    log.error("报盘记录更新后保存历史失败:" + entity.getReqno());
                    throw new McpException("报盘记录更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("报盘记录更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的报盘记录
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<OfferVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<OfferEntity> entityList = new ArrayList<>();

            for (OfferVo item:voList){
                OfferEntity entity = new OfferEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getReqno())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("报盘记录批量更新失败 存在请求编号为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("报盘记录批量更新失败");
                throw new McpException("报盘记录批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getReqno())).map(OfferEntity::getReqno).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("报盘记录批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("报盘记录批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的报盘记录
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(OfferVo vo) {
        if(vo != null) {
            OfferEntity entity = new OfferEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("报盘记录保存失败");
                throw new McpException("报盘记录保存失败"+entity.getReqno());
            } else {
                if(!baseService.saveOperationHisById(entity.getReqno(),4)) {
                    log.error("报盘记录保存后保存历史失败:" + entity.getReqno());
                    throw new McpException("报盘记录保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("报盘记录保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的报盘记录
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<OfferVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<OfferEntity> entityList = new ArrayList<>();

            for (OfferVo item:voList){
                OfferEntity entity = new OfferEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("报盘记录批量保存失败");
                throw new McpException("报盘记录批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getReqno())).map(OfferEntity::getReqno).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("报盘记录批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("报盘记录批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param reqno 需要查询的请求编号
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-10-20
     * @change
     * 2023-10-20 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public OfferVo selectByIdRecord(String reqno) {
        OfferVo vo = new OfferVo();

        if(!StringUtils.isEmpty(reqno)) {
            OfferEntity entity = baseService.selectById(reqno);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }


    public static void main(String[] args){
        String message = "{\"busiCode\":\"\",\"code\":\"10100\",\"data\":null,\"message\":\"无数据！\"}";
        String[] bodyArray = message.split(",");
        if(bodyArray!=null && bodyArray.length >0){
            for(String filed : bodyArray){
                if(filed!=null && filed.contains("message")){
                    String[] filedArray = filed.split(":");
                    message = filedArray[1].replace("}","");
                    System.out.println(message);
                }
            }
        }
    }

}
