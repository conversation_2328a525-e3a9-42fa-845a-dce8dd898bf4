package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpmWithholdRecordEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 报盘记录查询 服务类
 *
 * <AUTHOR>
 * @date 2024-02-23
 * @change 2024-02-23 by binghong.tang for init
 */
public interface IBbpmWithholdRecordService extends IMcpBaseService<BbpmWithholdRecordEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    String insertRecord(BbpmWithholdRecordVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmWithholdRecordVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param batchNo 需要删除的申请批次
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    void removeByIdRecord(String batchNo);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param batchNoList 需要删除的申请批次
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> batchNoList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的报盘记录查询
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    void updateByIdRecord(BbpmWithholdRecordVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的报盘记录查询
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmWithholdRecordVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的报盘记录查询
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    void saveByIdRecord(BbpmWithholdRecordVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的报盘记录查询
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmWithholdRecordVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param batchNo 需要查询的申请批次
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    BbpmWithholdRecordVo selectByIdRecord(String batchNo);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    PageResult<List<BbpmWithholdRecordPageResultVo>> selectByPageRecord(BbpmWithholdRecordPageVo vo);
}
