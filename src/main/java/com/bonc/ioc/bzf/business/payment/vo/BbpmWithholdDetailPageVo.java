package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 报盘明细查询
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@ApiModel(value="BbpmWithholdDetailPageVo对象", description="报盘明细查询")
@Data
public class BbpmWithholdDetailPageVo extends McpBasePageVo implements Serializable{

    @ApiModelProperty(value = "申请批次")
    private String batchNo;
    @ApiModelProperty(value = "承租人")
    private String tenantName;
    @ApiModelProperty(value = "证件号码")
    private String certNo;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "房源地址")
    private String houseName;

    @ApiModelProperty(value = "全量分页标识Y:是,N:否")
    private String fullPage;

    @ApiModelProperty(value = "当前页码")
    private Integer current;

    @ApiModelProperty(value = "每页最大数目")
    private Integer size;

    @ApiModelProperty(value = "公租房备案号")
    private String publicRecordNo;
}
