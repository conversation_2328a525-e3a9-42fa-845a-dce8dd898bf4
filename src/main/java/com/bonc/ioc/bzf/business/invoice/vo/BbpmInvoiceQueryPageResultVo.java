package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

import com.bonc.ioc.common.validator.inf.*;

import javax.validation.constraints.*;

/**
 * 已开发票查询 实体类
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value = "BbpmInvoiceQueryPageResultVo对象", description = "已开发票查询")
public class BbpmInvoiceQueryPageResultVo extends McpBasePageVo implements Serializable {


    /**
     * 发票请求流水号
     */
    @ApiModelProperty(value = "发票请求流水号")
    @NotBlank(message = "发票请求流水号不能为空", groups = {UpdateValidatorGroup.class})
    private String fpqqlsh;

    /**
     * 发票代码
     */
    @ApiModelProperty(value = "发票代码")
    private String fpDm;

    /**
     * 发票号码
     */
    @ApiModelProperty(value = "发票号码")
    private String fpHm;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期")
    private String kprq;

    @ApiModelProperty(value = "开票开始日期")
    private String kprqStartDate;
    @ApiModelProperty(value = "开票结束日期")
    private String kprqEndDate;

    /**
     * 开票类型(0：蓝字发票,1：红字发票)
     */
    @ApiModelProperty(value = "开票类型(0：蓝字发票,1：红字发票)")
    @McpDictPoint(dictCode = "INVOICE_KPLX", overTransCopyTo = "kplxName")
    private String kplx;

    @ApiModelProperty(value = "开票类型名称")
    private String kplxName;


    /**
     * 发票类型81：专票82：普票
     */
    @ApiModelProperty(value = "发票类型81：专票82：普票")
    @McpDictPoint(dictCode = "INVOICE_FPLX", overTransCopyTo = "fplxdmName")
    private String fplxdm;

    @ApiModelProperty(value = "开票类型名称")
    private String fplxdmName;

    /**
     * 购买方纳税人识别号
     */
    @ApiModelProperty(value = "购买方纳税人识别号")
    private String gmfNsrsbh;

    /**
     * 购买方名称
     */
    @ApiModelProperty(value = "购买方名称")
    private String gmfMc;

    /**
     * 购买方银行账号
     */
    @ApiModelProperty(value = "购买方银行账号")
    private String gmfYhzh;

    /**
     * 购买方银行名称
     */
    @ApiModelProperty(value = "购买方银行名称")
    private String gmfYhmc;

    /**
     * 购买方地址
     */
    @ApiModelProperty(value = "购买方地址")
    private String gmfDz;

    /**
     * 购买方电话
     */
    @ApiModelProperty(value = "购买方电话")
    private String gmfDh;

    /**
     * 消费者邮箱号
     */
    @ApiModelProperty(value = "消费者邮箱号")
    private String gmfDzyx;

    /**
     * 消费者手机号
     */
    @ApiModelProperty(value = "消费者手机号")
    private String gmfSjh;

    /**
     * 发票状态(01:已开票,02:已红冲)
     */
    @ApiModelProperty(value = "发票状态(01:已开票,02:已红冲)")
    @McpDictPoint(dictCode = "INVOICE_STATUS", overTransCopyTo = "invoiceStatusName")
    private String invoiceStatus;

    @ApiModelProperty(value = "发票状态名称")
    private String invoiceStatusName;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private String invoiceMoney;

    /**
     * 费用类型(01:房屋租金,03:家具家电租金)
     */
    @ApiModelProperty(value = "费用类型(01:房屋租金,03:家具家电租金)")
    @McpDictPoint(dictCode = "BILLING_YC_BILLCHARGESUBJECT", overTransCopyTo = "expenseTypeName")
    private String expenseType;

    @ApiModelProperty(value = "费用类型名称")
    private String expenseTypeName;

    /**
     * 抬头类型(01:企业单位,02:个人/非企业单位)
     */
    @ApiModelProperty(value = "抬头类型(01:企业单位,02:个人/非企业单位)")
    @McpDictPoint(dictCode = "INVOICE_TYPE", overTransCopyTo = "titleTypeName")
    private String titleType;

    @ApiModelProperty(value = "抬头类型名称")
    private String titleTypeName;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String bz;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
    private String tenantName;

//    /**
//     * 主收款单列表
//     */
//    @ApiModelProperty(value = "主收款单列表")
//    private List<ChargePaymentVo> chargePaymentlist;
    @ApiModelProperty(value = "开具发票对应主账单唯一标识")
    private String invoicedBillCode;

    @ApiModelProperty(value = "账单列表")
    private List<BbpmBillManagementPageResultVo> billList;

    /**
     * 发票pdf文件路径
     */
    @ApiModelProperty(value = "发票pdf文件路径")
    private String pdflj;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String houseAddr;

    /**
     * 销售方名称
     */
    @ApiModelProperty(value = "销售方名称(开票方)")
    private String xsfMc;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "收款单数量")
    private String orderNum;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer current;

    /**
     * 每页显示多少条
     */
    @ApiModelProperty(value = "每页显示多少条")
    private Integer size;

    /**
     * 全量分页标识
     */
    @ApiModelProperty(value = "全量分页标识")
    private String fullPage;

    @ApiModelProperty(value = "红冲原因 01:退租,02:换开")
    @McpDictPoint(dictCode = "INVOICE_REDFLUSH", overTransCopyTo = "reasonName")
    private String reason;

    @ApiModelProperty(value = "红冲原因名称")
    private String reasonName;

    @ApiModelProperty(value = "租户ID")
    private String tenantCode;

    @ApiModelProperty(value = "委托代理人")
    private String authorizedAgent ;
    @ApiModelProperty(value = "委托代理人电话")
    private String authorizedAgentMobile;
    @ApiModelProperty(value = "小区/楼宇名称")
    private String residentialQuartersName;
    @ApiModelProperty(value = "项目业态")
    @McpDictPoint(dictCode = "BILLING_PROJECT_FORMAT", overTransCopyTo = "projectFormatName")
    private String projectFormat;

    @ApiModelProperty(value = "项目业态名称")
    private String projectFormatName;

    @ApiModelProperty(value = "红冲发票PDF文件路径,多个之间以英文逗号分隔")
    private String redFlushPdflj;

    @ApiModelProperty(value = "合同唯一识别码")
    private String contractCode;

    @ApiModelProperty(value = "发票pdf附件ID")
    private String pdfAttId;
    @ApiModelProperty(value = "红冲发票pdf附件ID")
    private String redFlushPdfAttId;

    @ApiModelProperty(value = "申请日期")
    private String applyDate;

    public String getFplxdm() {
        return fplxdm;
    }

    public void setFplxdm(String fplxdm) {
        this.fplxdm = fplxdm;
    }

    public String getFplxdmName() {
        return fplxdmName;
    }

    public void setFplxdmName(String fplxdmName) {
        this.fplxdmName = fplxdmName;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public String getPdfAttId() {
        return pdfAttId;
    }

    public void setPdfAttId(String pdfAttId) {
        this.pdfAttId = pdfAttId;
    }

    public String getRedFlushPdfAttId() {
        return redFlushPdfAttId;
    }

    public void setRedFlushPdfAttId(String redFlushPdfAttId) {
        this.redFlushPdfAttId = redFlushPdfAttId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getProjectFormatName() {
        return projectFormatName;
    }

    public void setProjectFormatName(String projectFormatName) {
        this.projectFormatName = projectFormatName;
    }

    public String getRedFlushPdflj() {
        return redFlushPdflj;
    }

    public void setRedFlushPdflj(String redFlushPdflj) {
        this.redFlushPdflj = redFlushPdflj;
    }

    public String getAuthorizedAgent() {
        return authorizedAgent;
    }

    public void setAuthorizedAgent(String authorizedAgent) {
        this.authorizedAgent = authorizedAgent;
    }

    public String getAuthorizedAgentMobile() {
        return authorizedAgentMobile;
    }

    public void setAuthorizedAgentMobile(String authorizedAgentMobile) {
        this.authorizedAgentMobile = authorizedAgentMobile;
    }

    public String getResidentialQuartersName() {
        return residentialQuartersName;
    }

    public void setResidentialQuartersName(String residentialQuartersName) {
        this.residentialQuartersName = residentialQuartersName;
    }

    public String getProjectFormat() {
        return projectFormat;
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = projectFormat;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getReasonName() {
        return reasonName;
    }

    public void setReasonName(String reasonName) {
        this.reasonName = reasonName;
    }

    public String getExpenseTypeName() {
        return expenseTypeName;
    }

    public void setExpenseTypeName(String expenseTypeName) {
        this.expenseTypeName = expenseTypeName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getFullPage() {
        return fullPage;
    }

    public void setFullPage(String fullPage) {
        this.fullPage = fullPage;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 发票请求流水号
     */
    public String getFpqqlsh() {
        return fpqqlsh;
    }

    public void setFpqqlsh(String fpqqlsh) {
        this.fpqqlsh = fpqqlsh;
    }

    /**
     * @return 发票代码
     */
    public String getFpDm() {
        return fpDm;
    }

    public void setFpDm(String fpDm) {
        this.fpDm = fpDm;
    }

    /**
     * @return 发票号码
     */
    public String getFpHm() {
        return fpHm;
    }

    public void setFpHm(String fpHm) {
        this.fpHm = fpHm;
    }

    /**
     * @return 开票日期
     */
    public String getKprq() {
        return kprq;
    }

    public void setKprq(String kprq) {
        this.kprq = kprq;
    }

    /**
     * @return 开票类型(0 ： 蓝字发票, 1 ： 红字发票)
     */
    public String getKplx() {
        return kplx;
    }

    public void setKplx(String kplx) {
        this.kplx = kplx;
    }

    /**
     * @return 购买方纳税人识别号
     */
    public String getGmfNsrsbh() {
        return gmfNsrsbh;
    }

    public void setGmfNsrsbh(String gmfNsrsbh) {
        this.gmfNsrsbh = gmfNsrsbh;
    }

    /**
     * @return 购买方名称
     */
    public String getGmfMc() {
        return gmfMc;
    }

    public void setGmfMc(String gmfMc) {
        this.gmfMc = gmfMc;
    }

    /**
     * @return 购买方银行账号
     */
    public String getGmfYhzh() {
        return gmfYhzh;
    }

    public void setGmfYhzh(String gmfYhzh) {
        this.gmfYhzh = gmfYhzh;
    }

    /**
     * @return 购买方银行名称
     */
    public String getGmfYhmc() {
        return gmfYhmc;
    }

    public void setGmfYhmc(String gmfYhmc) {
        this.gmfYhmc = gmfYhmc;
    }

    /**
     * @return 购买方地址
     */
    public String getGmfDz() {
        return gmfDz;
    }

    public void setGmfDz(String gmfDz) {
        this.gmfDz = gmfDz;
    }

    /**
     * @return 购买方电话
     */
    public String getGmfDh() {
        return gmfDh;
    }

    public void setGmfDh(String gmfDh) {
        this.gmfDh = gmfDh;
    }

    /**
     * @return 消费者邮箱号
     */
    public String getGmfDzyx() {
        return gmfDzyx;
    }

    public void setGmfDzyx(String gmfDzyx) {
        this.gmfDzyx = gmfDzyx;
    }

    /**
     * @return 消费者手机号
     */
    public String getGmfSjh() {
        return gmfSjh;
    }

    public void setGmfSjh(String gmfSjh) {
        this.gmfSjh = gmfSjh;
    }

    /**
     * @return 发票状态(01 : 已开票, 02 : 已红冲)
     */
    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    /**
     * @return 开票金额
     */
    public String getInvoiceMoney() {
        return invoiceMoney;
    }

    public void setInvoiceMoney(String invoiceMoney) {
        this.invoiceMoney = invoiceMoney;
    }

    /**
     * @return 费用类型(01 : 房屋租金, 03 : 家具家电租金)
     */
    public String getExpenseType() {
        return expenseType;
    }

    public void setExpenseType(String expenseType) {
        this.expenseType = expenseType;
    }

    /**
     * @return 抬头类型(01 : 企业单位, 02 : 个人 / 非企业单位)
     */
    public String getTitleType() {
        return titleType;
    }

    public void setTitleType(String titleType) {
        this.titleType = titleType;
    }

    /**
     * @return 备注说明
     */
    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    /**
     * @return 客户姓名
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

//    /**
//     * @return 主收款单列表
//     */
//    public List<ChargePaymentVo> getChargePaymentlist() {
//        return chargePaymentlist;
//    }
//
//    public void setChargePaymentlist(List<ChargePaymentVo> chargePaymentlist) {
//        this.chargePaymentlist = chargePaymentlist;
//    }

    public String getInvoicedBillCode() {
        return invoicedBillCode;
    }

    public void setInvoicedBillCode(String invoicedBillCode) {
        this.invoicedBillCode = invoicedBillCode;
    }

    /**
     * @return 发票pdf文件路径
     */
    public String getPdflj() {
        return pdflj;
    }

    public void setPdflj(String pdflj) {
        this.pdflj = pdflj;
    }

    /**
     * @return 房源地址
     */
    public String getHouseAddr() {
        return houseAddr;
    }

    public void setHouseAddr(String houseAddr) {
        this.houseAddr = houseAddr;
    }

    /**
     * @return 销售方名称
     */
    public String getXsfMc() {
        return xsfMc;
    }

    public void setXsfMc(String xsfMc) {
        this.xsfMc = xsfMc;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getKplxName() {
        return kplxName;
    }

    public void setKplxName(String kplxName) {
        this.kplxName = kplxName;
    }

    public String getInvoiceStatusName() {
        return invoiceStatusName;
    }

    public void setInvoiceStatusName(String invoiceStatusName) {
        this.invoiceStatusName = invoiceStatusName;
    }

    public String getTitleTypeName() {
        return titleTypeName;
    }

    public void setTitleTypeName(String titleTypeName) {
        this.titleTypeName = titleTypeName;
    }

    public List<BbpmBillManagementPageResultVo> getBillList() {
        return billList;
    }

    public void setBillList(List<BbpmBillManagementPageResultVo> billList) {
        this.billList = billList;
    }

    public String getKprqStartDate() {
        return kprqStartDate;
    }

    public void setKprqStartDate(String kprqStartDate) {
        this.kprqStartDate = kprqStartDate;
    }

    public String getKprqEndDate() {
        return kprqEndDate;
    }

    public void setKprqEndDate(String kprqEndDate) {
        this.kprqEndDate = kprqEndDate;
    }

    @Override
    public String toString() {
        return "BbpmInvoiceQueryPageResultVo{" +
                "fpqqlsh=" + fpqqlsh +
                ", fpDm=" + fpDm +
                ", fpHm=" + fpHm +
                ", kprq=" + kprq +
                ", kplx=" + kplx +
                ", gmfNsrsbh=" + gmfNsrsbh +
                ", gmfMc=" + gmfMc +
                ", gmfYhzh=" + gmfYhzh +
                ", gmfYhmc=" + gmfYhmc +
                ", gmfDz=" + gmfDz +
                ", gmfDh=" + gmfDh +
                ", gmfDzyx=" + gmfDzyx +
                ", gmfSjh=" + gmfSjh +
                ", invoiceStatus=" + invoiceStatus +
                ", invoiceMoney=" + invoiceMoney +
                ", expenseType=" + expenseType +
                ", titleType=" + titleType +
                ", bz=" + bz +
                ", tenantName=" + tenantName +

                ", pdflj=" + pdflj +
                ", houseAddr=" + houseAddr +
                ", xsfMc=" + xsfMc +
                ", delFlag=" + delFlag +
                "}";
    }
}
