package com.bonc.ioc.bzf.business.adjust.vo;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 应收调整 实体类
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@ApiModel(value="BbpmReceivableAdjustPageVo对象", description="应收调整")
public class BbpmReceivableAdjustPageVo extends McpBasePageVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String id;

    /**
     * 调整单编号
     */
    @ApiModelProperty(value = "调整单编号")
                            private String adjustNo;

    /**
     * 产品地址
     */
    @ApiModelProperty(value = "产品地址")
                            private String productAddress;

    /**
     * 承租人姓名
     */
    @ApiModelProperty(value = "承租人姓名")
                            private String customerName;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
                            private String customerIdType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
                            private String customerIdNumber;

    /**
     * 趸租单位
     */
    @ApiModelProperty(value = "趸租单位")
                            private String company;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
                            private String customerCreditCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractNo;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
                            private String contractType;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date contractBeginTime;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date contractEndTime;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 调整单状态
     */
    @ApiModelProperty(value = "调整单状态")
                            private String adjustStatus;

    /**
     * 退款状态
     */
    @ApiModelProperty(value = "退款状态")
                            private String refundStatus;

    /**
     * 退款失败原因
     */
    @ApiModelProperty(value = "退款失败原因")
                            private String failureReason;

    /**
     * 调整金额（元）
     */
    @ApiModelProperty(value = "调整金额（元）")
                            private BigDecimal adjustAmount;

    /**
     * 调整应收依据文件
     */
    @ApiModelProperty(value = "调整应收依据文件")
                            private String adjustFiles;

    /**
     * 应退金额处理类型
     */
    @ApiModelProperty(value = "应退金额处理类型")
                            private String amountHandle;

    /**
     * 退款路径类型
     */
    @ApiModelProperty(value = "退款路径类型")
                            private String refundType;

    /**
     * 退回路径
     */
    @ApiModelProperty(value = "退回路径")
                            private String refundPath;

    /**
     * 开户人姓名
     */
    @ApiModelProperty(value = "开户人姓名")
                            private String accountHolder;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
                            private String bankCard;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
                            private String openingBank;

    /**
     * 开户行所在省
     */
    @ApiModelProperty(value = "开户行所在省")
                            private String openingProvince;

    /**
     * 开户行所在市
     */
    @ApiModelProperty(value = "开户行所在市")
                            private String openingCity;

    /**
     * 备用字段1
     */
    @ApiModelProperty(value = "备用字段1")
                            private String ext1;

    /**
     * 备用字段2
     */
    @ApiModelProperty(value = "备用字段2")
                            private String ext2;

    /**
     * 备用字段3
     */
    @ApiModelProperty(value = "备用字段3")
                            private String ext3;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
                            private String createUserName;

    /**
     * 以逗号分割的项目编号
     */
    @ApiModelProperty(value = "以逗号分割的项目编号")
    private String projectIdStr;

    @ApiModelProperty(value = "查询参数，创建开始时间")
    private String createTimeStart;

    @ApiModelProperty(value = "查询参数，创建结束时间")
    private String createTimeEnd;

    @ApiModelProperty(value = "查询参数，修改开始时间")
    private String modifyTimeStart;

    @ApiModelProperty(value = "查询参数，修改结束时间")
    private String modifyTimeEnd;

    @ApiModelProperty(value = "查询类型（1：应收调整待办，2：应收调整已办，3：应收调整审核待办，4：应收调整审核已办）")
    private String type;

    @ApiModelProperty(value = "企业退款原因")
    private String qyFailureReason;

    @ApiModelProperty(value = "工银试算结果")
    private String calculationResults;

    public String getCalculationResults() {
        return calculationResults;
    }

    public void setCalculationResults(String calculationResults) {
        this.calculationResults = calculationResults;
    }

    public String getModifyTimeStart() {
        return modifyTimeStart;
    }

    public void setModifyTimeStart(String modifyTimeStart) {
        this.modifyTimeStart = modifyTimeStart;
    }

    public String getModifyTimeEnd() {
        return modifyTimeEnd;
    }

    public void setModifyTimeEnd(String modifyTimeEnd) {
        this.modifyTimeEnd = modifyTimeEnd;
    }

    public String getQyFailureReason() {
        return qyFailureReason;
    }

    public void setQyFailureReason(String qyFailureReason) {
        this.qyFailureReason = qyFailureReason;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public String getProjectIdStr() {
        return projectIdStr;
    }

    public void setProjectIdStr(String projectIdStr) {
        this.projectIdStr = projectIdStr;
    }

    /**
     * @return 主键
     */
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return 调整单编号
     */
    public String getAdjustNo() {
        return adjustNo;
    }

    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo;
    }

    /**
     * @return 产品地址
     */
    public String getProductAddress() {
        return productAddress;
    }

    public void setProductAddress(String productAddress) {
        this.productAddress = productAddress;
    }

    /**
     * @return 承租人姓名
     */
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * @return 证件类型
     */
    public String getCustomerIdType() {
        return customerIdType;
    }

    public void setCustomerIdType(String customerIdType) {
        this.customerIdType = customerIdType;
    }

    /**
     * @return 证件号码
     */
    public String getCustomerIdNumber() {
        return customerIdNumber;
    }

    public void setCustomerIdNumber(String customerIdNumber) {
        this.customerIdNumber = customerIdNumber;
    }

    /**
     * @return 趸租单位
     */
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    /**
     * @return 统一社会信用代码
     */
    public String getCustomerCreditCode() {
        return customerCreditCode;
    }

    public void setCustomerCreditCode(String customerCreditCode) {
        this.customerCreditCode = customerCreditCode;
    }

    /**
     * @return 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    /**
     * @return 合同类型
     */
    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    /**
     * @return 合同开始日期
     */
    public Date getContractBeginTime(){
        if(contractBeginTime!=null){
            return (Date)contractBeginTime.clone();
        }else{
            return null;
        }
    }

    public void setContractBeginTime(Date contractBeginTime) {
        if(contractBeginTime==null){
            this.contractBeginTime = null;
        }else{
            this.contractBeginTime = (Date)contractBeginTime.clone();
        }
    }

    /**
     * @return 合同结束日期
     */
    public Date getContractEndTime(){
        if(contractEndTime!=null){
            return (Date)contractEndTime.clone();
        }else{
            return null;
        }
    }

    public void setContractEndTime(Date contractEndTime) {
        if(contractEndTime==null){
            this.contractEndTime = null;
        }else{
            this.contractEndTime = (Date)contractEndTime.clone();
        }
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 调整单状态
     */
    public String getAdjustStatus() {
        return adjustStatus;
    }

    public void setAdjustStatus(String adjustStatus) {
        this.adjustStatus = adjustStatus;
    }

    /**
     * @return 退款状态
     */
    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    /**
     * @return 退款失败原因
     */
    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    /**
     * @return 调整金额（元）
     */
    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    /**
     * @return 调整应收依据文件
     */
    public String getAdjustFiles() {
        return adjustFiles;
    }

    public void setAdjustFiles(String adjustFiles) {
        this.adjustFiles = adjustFiles;
    }

    /**
     * @return 应退金额处理类型
     */
    public String getAmountHandle() {
        return amountHandle;
    }

    public void setAmountHandle(String amountHandle) {
        this.amountHandle = amountHandle;
    }

    /**
     * @return 退款路径类型
     */
    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    /**
     * @return 退回路径
     */
    public String getRefundPath() {
        return refundPath;
    }

    public void setRefundPath(String refundPath) {
        this.refundPath = refundPath;
    }

    /**
     * @return 开户人姓名
     */
    public String getAccountHolder() {
        return accountHolder;
    }

    public void setAccountHolder(String accountHolder) {
        this.accountHolder = accountHolder;
    }

    /**
     * @return 银行卡号
     */
    public String getBankCard() {
        return bankCard;
    }

    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    /**
     * @return 开户行
     */
    public String getOpeningBank() {
        return openingBank;
    }

    public void setOpeningBank(String openingBank) {
        this.openingBank = openingBank;
    }

    /**
     * @return 开户行所在省
     */
    public String getOpeningProvince() {
        return openingProvince;
    }

    public void setOpeningProvince(String openingProvince) {
        this.openingProvince = openingProvince;
    }

    /**
     * @return 开户行所在市
     */
    public String getOpeningCity() {
        return openingCity;
    }

    public void setOpeningCity(String openingCity) {
        this.openingCity = openingCity;
    }

    /**
     * @return 备用字段1
     */
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * @return 备用字段2
     */
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * @return 备用字段3
     */
    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 创建人姓名
     */
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

      @Override
    public String toString() {
        return "BbpmReceivableAdjustPageVo{" +
            "id=" + id +
            ", adjustNo=" + adjustNo +
            ", productAddress=" + productAddress +
            ", customerName=" + customerName +
            ", customerIdType=" + customerIdType +
            ", customerIdNumber=" + customerIdNumber +
            ", company=" + company +
            ", customerCreditCode=" + customerCreditCode +
            ", contractNo=" + contractNo +
            ", contractType=" + contractType +
            ", contractBeginTime=" + contractBeginTime +
            ", contractEndTime=" + contractEndTime +
            ", projectId=" + projectId +
            ", projectName=" + projectName +
            ", adjustStatus=" + adjustStatus +
            ", refundStatus=" + refundStatus +
            ", failureReason=" + failureReason +
            ", adjustAmount=" + adjustAmount +
            ", adjustFiles=" + adjustFiles +
            ", amountHandle=" + amountHandle +
            ", refundType=" + refundType +
            ", refundPath=" + refundPath +
            ", accountHolder=" + accountHolder +
            ", bankCard=" + bankCard +
            ", openingBank=" + openingBank +
            ", openingProvince=" + openingProvince +
            ", openingCity=" + openingCity +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", ext3=" + ext3 +
            ", delFlag=" + delFlag +
            ", createUserName=" + createUserName +
        "}";
    }
}
