package com.bonc.ioc.bzf.business.supplementary.dao;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPreviewBillEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.supplementary.vo.*;

import java.util.List;

/**
 * 追加单试算表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 * @change 2025-04-08 by pyj for init
 */
@Mapper
public interface BbpmSupplementaryPreviewBillMapper extends McpBaseMapper<BbpmSupplementaryPreviewBillEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    List<BbpmSupplementaryPreviewBillPageResultVo> selectByPageCustom(@Param("vo") BbpmSupplementaryPreviewBillPageVo vo);

    /**
     * 根据上级id查询列表
     *
     * @param parentId 上级id
     * @return 查询结果
     */
    List<BbpmSupplementaryPreviewBillVo> selectListByParentId(@Param("parentId") String parentId);

    /**
     * 获取导出数据
     *
     * @return 导出数据
     */
    List<BbpmSupplementaryExportVo> selectExportData(@Param("vo") BbpmSupplementaryInfoPageVo vo);
}
