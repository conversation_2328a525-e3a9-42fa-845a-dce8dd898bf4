package com.bonc.ioc.bzf.business.payment.result.create;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR> 递增规则列表 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
@Builder
public class PreferentRulesParamsRequest {


	/**
	 * 优惠方式
	 */
	@ApiModelProperty(value = "优惠方式")
	private String preferentialType;

	/**
	 * 优惠开始日期
	 */
	@ApiModelProperty(value = "优惠开始日期")
	private String preferentialBeginDate;

	/**
	 * 优惠结束日期
	 */
	@ApiModelProperty(value = "优惠结束日期")
	private String preferentialEndDate;

	/**
	 * 优惠规则ID
	 */
	@ApiModelProperty(value = "优惠规则ID")
	private Integer preferentRuleId;

	@ApiModelProperty(value = "优惠金额")
	private BigDecimal preferentialAmount;
	@ApiModelProperty(value = "优惠比例")
	private BigDecimal preferentialRatio;


}
