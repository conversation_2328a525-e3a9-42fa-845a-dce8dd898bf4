package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@ApiModel(value="账单预览结果实体", description="账单预览结果实体")
@Data
public class PreviewBillsResultVo implements java.io.Serializable{


    @ApiModelProperty(value = "押金列表")
    private List<PreviewBillsResultDataVo> depositList;

    @ApiModelProperty(value = "物业费列表")
    private List<PreviewBillsResultDataVo> propertyList;

    @ApiModelProperty(value = "租金列表")
    private List<PreviewBillsResultDataVo> rentList;

    @ApiModelProperty(value = "月租金列表")
    private List<PreviewBillsResultDataVo> monthRentList;

    @ApiModelProperty(value = "可抵扣账单列表(押金、租金)")
    private List<PreviewBillsResultDataVo>  deductibleBillList;

    @ApiModelProperty(value = "可抵扣物业费列表")
    private List<PreviewBillsResultDataVo>  deductiblePropertyBillList;

    @ApiModelProperty(value = "抵扣当期后盈余金额列表")
    private List<PreviewBillsResultDeductedAmount> deductedAmountList;

}
