package com.bonc.ioc.bzf.business.payment.result;

import lombok.Data;

import java.util.List;
import org.apache.commons.collections4.map.CaseInsensitiveMap;

@Data
public class FaceSubHttpResultTwo<T> {

    List<T> records;
    private Integer total;
    private Integer size;
    private Integer current;
    private List<CaseInsensitiveMap<String, String>> orders;//orders": [],
    private Boolean optimizeCountSql;
    private Boolean hitCount;
    private Boolean searchCount;
    private Integer pages;

    // List<CaseInsensitiveMap<String, String>> data;
    //List<CaseInsensitiveMap<String, String>> items;
}


