package com.bonc.ioc.bzf.business.payment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
@ApiModel(value="3.59.商业合同变更退款生成付款单接口Param", description="3.59.商业合同变更退款生成付款单接口Param")
public class BusinessGeneratePaymentByGXParamVo implements java.io.Serializable{

    @ApiModelProperty(value = "合同唯一识别码 合同编码")
    private String contractCode;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "发起申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestDate;

    @ApiModelProperty(value = "更改账期类型：01合同开始日， 02 当期的第一天， 03 下一个账期的第一天")
    private String changeAccountingPeriodType;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号")
    private String certNo;

    @ApiModelProperty(value = "银行账号")
    private String bankAccountNo;

    @ApiModelProperty(value = "开户行省份名称")
    private String bankProvinceName;

    @ApiModelProperty(value = "开户行城市名称")
    private String bankCityName;

    @ApiModelProperty(value = "总行行名")
    private String bankName;

    @ApiModelProperty(value = "总行行号")
    private String bankCode;

    @ApiModelProperty(value = "开户行网点名称")
    private String bankBranchName;

    @ApiModelProperty(value = "开户行网点行号")
    private String bankBranchCode;

    @ApiModelProperty(value = "是否重新生成付款单 0第一次生成，1重新生成")
    private String regenerate;

    @ApiModelProperty(value = "退款路径 01，原路，02非原路")
    private String isOriginReturn;

    @ApiModelProperty(value = "付款单唯一标识")
    private String paymentCode;

    @ApiModelProperty(value = "1、变更生成，2、收款调整生成")
    private String source;

    @ApiModelProperty(value = "退款类型")
    private String owner;

}
