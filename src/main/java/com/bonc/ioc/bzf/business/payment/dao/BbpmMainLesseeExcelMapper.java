package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.BbpmMainLesseeExcelEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 合同变更-主承租人导入参数日志表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-11-01
 * @change 2023-11-01 by binghong.tang for init
 */
@Mapper
public interface BbpmMainLesseeExcelMapper extends McpBaseMapper<BbpmMainLesseeExcelEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change 2023-11-01 by binghong.tang for init
     */
    List<BbpmMainLesseeExcelPageResultVo> selectByPageCustom(@Param("vo") BbpmMainLesseeExcelPageVo vo );
}
