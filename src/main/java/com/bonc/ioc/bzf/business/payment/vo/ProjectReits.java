package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "reits变更项目", description = "reits变更项目")
public class ProjectReits {
    @ApiModelProperty(value = "老项目ID")
    private String beforeProjectId;//老项目ID	String	否
    @ApiModelProperty(value = "新项目ID")
    private String projectId;//新项目ID	String	否
}
