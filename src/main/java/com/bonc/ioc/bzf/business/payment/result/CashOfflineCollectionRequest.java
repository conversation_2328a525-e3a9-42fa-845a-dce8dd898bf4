package com.bonc.ioc.bzf.business.payment.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 现金线下收款请求 主类
 */
@Data
public class CashOfflineCollectionRequest {

    private String summaryNo;//	存款汇总单号	String	否	20	是存款明细单号的汇总单，只传存款明细单的时候存款汇总单号为空，该单号会在去银行存款时让柜员写到附言里面
    private String receiptNoUrl;//	纸质回单文件中心ID 	String	是	300	照片在文件中心的ID
    private String receiptNo;	//纸质回单编号	String	否	50	对应电子回单编号
    private String depositor;	//存款人	String	是	50	对应存款业务员姓名
    private String depositDate;	//存款时间	Date	是	10	yyyy/MM/dd存款日期
    private String uploader;	//上传人	String	是	50	对应上传业务员姓名
    private String  uploadDate;	//上传时间	Date	是	10	yyyy/MM/dd上传日期
    private String bankCode; //银行总行联行号
    private String projectId; //项目ID
    List<CashOfflineCollectionSubRequest> offlineList;	//存款明细单号列表				列表


//    private String receiptBankName;//收款银行名称	String	否	59	支付方式为现金
    private String receiptBankAcct;//收款银行账户	String	否	59	支付方式为现金
    private String receiptBankCode;//收款银行编码	String	       否	59	支付方式为现金
    private BigDecimal totalCashAmount;//实际存款金额	BigDecimal	否

    //收款银行名称
    private String receiptBankName;
    //收款银行账号
    private String receiptBankAcctNo;
    //收款银行户名
    private String receiptBankAccountName;
    //收款银行支行名称
    private String receiptBankBranchName;

    //是否是多项目认款01是02否
    private String  multiProject;

    //2024年12月冲刺增加
    private BigDecimal amount;//	本次消费金额	BigDecimal	是     这2个字段收款就要。与工银沟通过--第二次上传凭证不要这个字段
    private BigDecimal amountReceivable;//本次账单待缴金额	BigDecimal  这2个字段收款就要。与工银沟通过--第二次上传凭证不要这个字段

    private BigDecimal actualTotalAmount;//	收款总金额	BigDecimal   这个字段上传银行凭证时对应“存款总金额	”paidInAmount



}
