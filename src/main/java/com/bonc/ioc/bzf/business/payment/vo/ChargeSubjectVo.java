package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 计费科目列表 参数实体
 * @version 1.0
 * @date 2023/06/26 9:41
 */
@Data
public class ChargeSubjectVo {

	@ApiModelProperty(value = "计费科目编号")
	@NotBlank(message = "计费科目编号",groups = {UpdateValidatorGroup.class})
	private  String chargeSubjectNo;

	@ApiModelProperty(value = "计费科目金额")
	private  BigDecimal chargeSubjectAmount;


	@ApiModelProperty(value = "计费科目税率")
	private BigDecimal taxRate;

	@ApiModelProperty(value = "循环计费或单次计费")
	@NotBlank(message = "循环计费或单次计费",groups = {UpdateValidatorGroup.class})
	private String cyclicOrSingle;

	@ApiModelProperty(value = "计费科目收款周期")
	@NotBlank(message = "计费科目收款周期",groups = {UpdateValidatorGroup.class})
	private String chargeSubjectPeriod;


	@ApiModelProperty(value = "押金比例 只有02押金才有")
	private Integer depositProportion;

	@ApiModelProperty(value = "优惠类别")
	private String preferentialCategory;

	@ApiModelProperty(value = "优惠金额")
	private BigDecimal preferentialAmount;

	@ApiModelProperty(value = "优惠比例")
	private BigDecimal preferentialRatio;

	@ApiModelProperty(value = "计费规则编号")
	private String chargeRuleNo;

	@ApiModelProperty(value = "计费规则名称")
	private String chargeRuleName;

	@ApiModelProperty(value = "参数列表")
	private Map<String, Object> paramList;

	@ApiModelProperty(value = "参数值列表")
	private Map<String, Object> paramValueList;

	@ApiModelProperty(value = "分摊方式")
	private String shareType;

	@ApiModelProperty(value = "企业支付比例")
	private BigDecimal companyRate;

	@ApiModelProperty(value = "个人支付比例")
	private BigDecimal personalRate;

	@ApiModelProperty(value = "企业支付金额")
	private BigDecimal companyAmount;

	@ApiModelProperty(value = "个人支付金额")
	private BigDecimal personalAmount;

	/*@ApiModelProperty(value = "参数值列表")
	private List<IncreaseRulesParamsVo> increaseRules;*/

	@Override
	public String toString() {
		return "ChargeSubjecParamsVo{" +
				"chargeSubjectNo='" + chargeSubjectNo + '\'' +
				", chargeSubjectAmount=" + chargeSubjectAmount +
				", taxRate=" + taxRate +
				", cyclicOrSingle='" + cyclicOrSingle + '\'' +
				", chargeSubjectPeriod='" + chargeSubjectPeriod + '\'' +
				", depositProportion=" + depositProportion +
				", preferentialCategory='" + preferentialCategory + '\'' +
				", preferentialAmount=" + preferentialAmount +
				", preferentialRatio=" + preferentialRatio +
				", chargeRuleNo='" + chargeRuleNo + '\'' +
				", chargeRuleName='" + chargeRuleName + '\'' +
				", paramList='" + paramList + '\'' +
				", paramValueList='" + paramValueList + '\'' +
				/*", increaseRules=" + increaseRules +*/
				'}';
	}
}
