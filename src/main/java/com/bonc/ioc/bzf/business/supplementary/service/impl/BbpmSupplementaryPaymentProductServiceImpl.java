package com.bonc.ioc.bzf.business.supplementary.service.impl;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPaymentProductEntity;
import com.bonc.ioc.bzf.business.supplementary.dao.BbpmSupplementaryPaymentProductMapper;
import com.bonc.ioc.bzf.business.supplementary.enums.DelFlagEnum;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmSupplementaryPaymentProductService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

/**
 * 追加账单产品表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-04-01
 * @change 2025-04-01 by pyj for init
 */
@Slf4j
@Service
public class BbpmSupplementaryPaymentProductServiceImpl extends McpBaseServiceImpl<BbpmSupplementaryPaymentProductEntity> implements IBbpmSupplementaryPaymentProductService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmSupplementaryPaymentProductMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmSupplementaryPaymentProductService baseService;

    /**
     * 字典相关 session实例
     */
    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbpmSupplementaryPaymentProductVo vo) {
        if (vo == null) {
            return null;
        }

        BbpmSupplementaryPaymentProductEntity entity = new BbpmSupplementaryPaymentProductEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setProductId(null);
        if (!baseService.insert(entity)) {
            log.error("追加账单产品表新增失败:" + entity.toString());
            throw new McpException("追加账单产品表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getProductId(), 1)) {
                log.error("追加账单产品表新增后保存历史失败:" + entity.toString());
                throw new McpException("追加账单产品表新增后保存历史失败");
            }

            log.debug("追加账单产品表新增成功:" + entity.getProductId());
            return entity.getProductId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmSupplementaryPaymentProductVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmSupplementaryPaymentProductEntity> entityList = new ArrayList<>();
        for (BbpmSupplementaryPaymentProductVo item : voList) {
            BbpmSupplementaryPaymentProductEntity entity = new BbpmSupplementaryPaymentProductEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmSupplementaryPaymentProductEntity item : entityList) {
            item.setProductId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("追加账单产品表新增失败");
            throw new McpException("追加账单产品表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbpmSupplementaryPaymentProductEntity::getProductId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("追加账单产品表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("追加账单产品表批量新增后保存历史失败");
            }

            log.debug("追加账单产品表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param productId 需要删除的产品id
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String productId) {
        if (!StringUtils.isEmpty(productId)) {
            if (!baseService.saveOperationHisById(productId, 3)) {
                log.error("追加账单产品表删除后保存历史失败:" + productId);
                throw new McpException("追加账单产品表删除后保存历史失败");
            }

            if (!baseService.removeById(productId)) {
                log.error("追加账单产品表删除失败");
                throw new McpException("追加账单产品表删除失败" + productId);
            }
        } else {
            throw new McpException("追加账单产品表删除失败产品id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param productIdList 需要删除的产品id
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> productIdList) {
        if (!CollectionUtils.isEmpty(productIdList)) {
            int oldSize = productIdList.size();
            productIdList = productIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(productIdList) || oldSize != productIdList.size()) {
                throw new McpException("追加账单产品表批量删除失败 存在主键id为空的记录" + StringUtils.join(productIdList));
            }

            if (!baseService.saveOperationHisByIds(productIdList, 3)) {
                log.error("追加账单产品表批量删除后保存历史失败:" + StringUtils.join(productIdList));
                throw new McpException("追加账单产品表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(productIdList)) {
                log.error("追加账单产品表批量删除失败");
                throw new McpException("追加账单产品表批量删除失败" + StringUtils.join(productIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的追加账单产品表
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmSupplementaryPaymentProductVo vo) {
        if (vo != null) {
            BbpmSupplementaryPaymentProductEntity entity = new BbpmSupplementaryPaymentProductEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getProductId())) {
                throw new McpException("追加账单产品表更新失败传入产品id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("追加账单产品表更新失败");
                throw new McpException("追加账单产品表更新失败" + entity.getProductId());
            } else {
                if (!baseService.saveOperationHisById(entity.getProductId(), 2)) {
                    log.error("追加账单产品表更新后保存历史失败:" + entity.getProductId());
                    throw new McpException("追加账单产品表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("追加账单产品表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的追加账单产品表
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmSupplementaryPaymentProductVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmSupplementaryPaymentProductEntity> entityList = new ArrayList<>();

            for (BbpmSupplementaryPaymentProductVo item : voList) {
                BbpmSupplementaryPaymentProductEntity entity = new BbpmSupplementaryPaymentProductEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getProductId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("追加账单产品表批量更新失败 存在产品id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("追加账单产品表批量更新失败");
                throw new McpException("追加账单产品表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getProductId())).map(BbpmSupplementaryPaymentProductEntity::getProductId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("追加账单产品表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("追加账单产品表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的追加账单产品表
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmSupplementaryPaymentProductVo vo) {
        if (vo != null) {
            BbpmSupplementaryPaymentProductEntity entity = new BbpmSupplementaryPaymentProductEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("追加账单产品表保存失败");
                throw new McpException("追加账单产品表保存失败" + entity.getProductId());
            } else {
                if (!baseService.saveOperationHisById(entity.getProductId(), 4)) {
                    log.error("追加账单产品表保存后保存历史失败:" + entity.getProductId());
                    throw new McpException("追加账单产品表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("追加账单产品表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的追加账单产品表
     * @return void
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmSupplementaryPaymentProductVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmSupplementaryPaymentProductEntity> entityList = new ArrayList<>();

            for (BbpmSupplementaryPaymentProductVo item : voList) {
                BbpmSupplementaryPaymentProductEntity entity = new BbpmSupplementaryPaymentProductEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("追加账单产品表批量保存失败");
                throw new McpException("追加账单产品表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getProductId())).map(BbpmSupplementaryPaymentProductEntity::getProductId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("追加账单产品表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("追加账单产品表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param productId 需要查询的产品id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmSupplementaryPaymentProductVo selectByIdRecord(String productId) {
        BbpmSupplementaryPaymentProductVo vo = new BbpmSupplementaryPaymentProductVo();

        if (!StringUtils.isEmpty(productId)) {
            BbpmSupplementaryPaymentProductEntity entity = baseService.selectById(productId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmSupplementaryPaymentProductPageResultVo>> selectByPageRecord(BbpmSupplementaryPaymentProductPageVo vo) {
        List<BbpmSupplementaryPaymentProductPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据追加单id删除
     *
     * @param supplementaryId 追加单id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void deleteBySupplementaryId(String supplementaryId) {
        baseMapper.deleteBySupplementaryId(supplementaryId);
    }

    /**
     * 根据上级id删除(逻辑删除)
     *
     * @param parentId 上级id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByParentId(String parentId) {
        baseMapper.updateDelFlagByParentId(parentId, DelFlagEnum.DELETED.getCode());
    }

    /**
     * 根据上级id查询
     *
     * @param parentId 上级id
     * @return 追加账单产品列表
     */
    @Override
    public List<BbpmSupplementaryPaymentProductVo> selectListByParentId(String parentId) {
        List<BbpmSupplementaryPaymentProductVo> productList = baseMapper.selectListByParentId(parentId);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(productList);
        return productList;
    }
}
