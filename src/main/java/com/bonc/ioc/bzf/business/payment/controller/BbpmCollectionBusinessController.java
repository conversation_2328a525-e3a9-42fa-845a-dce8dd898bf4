package com.bonc.ioc.bzf.business.payment.controller;

import com.bonc.ioc.bzf.business.payment.service.IBbpmCollectionService;
import com.bonc.ioc.bzf.business.payment.vo.BbpmChargeMoneyTotalVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionPageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCollectionVo;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 收款表(部分来源业财)v3.0 前端控制器
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmCollectionEntity")
@Api(tags = "收款表(部分来源业财)v3.0")
@Validated
public class BbpmCollectionBusinessController extends McpBaseController {
    @Resource
    private IBbpmCollectionService baseService;



    /**
     * selectByPageRecord 收款单管理--分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "收款单管理--分页查询", notes = "收款单管理--分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmCollectionPageResultVo>>> selectByPageRecord(BbpmCollectionPageVo vo){
        AppReply<PageResult<List<BbpmCollectionPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }

    /**
     * 收款单--查询全部
     * @param vo
     */
    @GetMapping(value = "/getReceiptList", produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "收款单管理--查询全部", notes = "收款单管理--查询全部", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmCollectionPageResultVo>> getReceiptList(BbpmCollectionPageVo vo){
        AppReply<List<BbpmCollectionPageResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getReceiptList(vo));
        return appReply;
    }

    /**
     * updateCertificateStateById 根据主键更新凭证状态
     * @param id 需要更新凭证状态的id
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/updateCertificateStateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "binghong.tang")
    @ApiOperation(value = "根据主键更新凭证状态", notes = "根据主键更新凭证状态", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @LogPoint(system = "bzf-business-payment",path = "/bzf-business-payment/bbpmCollectionEntity/updateCertificateStateById")
    public AppReply updateCertificateStateById(@ApiParam(value = "需要更新凭证状态的idv3.0" ,required = true) @RequestParam String id){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateCertificateStateById(id);
        return appReply;
    }


    /**
     * chargeMoneyTotal 收款单管理--分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/chargeMoneyTotal", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "3.11.1收款单查询统计接口", notes = "3.11.1收款单查询统计接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmChargeMoneyTotalVo> chargeMoneyTotal(BbpmCollectionPageVo vo){
        AppReply<BbpmChargeMoneyTotalVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.chargeMoneyTotal(vo));
        return appReply;
    }

}

