package com.bonc.ioc.bzf.business.payment.controller;


import com.bonc.ioc.bzf.business.payment.service.IBbsiRuleInfoService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 3.45 增值服务费试算接口
 */
@RestController
@RequestMapping("/v2/business/bbsiTryServiceFeeEntity")
@Api(tags = "试算增值服务费")
@Validated
public class BbsiTryServiceFeeBusinessController extends McpBaseController {

    @Resource
    private IBbsiRuleInfoService baseService;

    @PostMapping(value = "/tryServiceFee", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "增值服务费试算接口", notes = "增值服务费试算接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bbsiTryServiceFeeEntity/tryServiceFee")
    public AppReply<TryServiceFeeResponseVo> tryServiceFee(@RequestBody TryServiceFeeMainParamsVo vo){
        AppReply<TryServiceFeeResponseVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.tryServiceFee(vo));
        return appReply;
    }

}

