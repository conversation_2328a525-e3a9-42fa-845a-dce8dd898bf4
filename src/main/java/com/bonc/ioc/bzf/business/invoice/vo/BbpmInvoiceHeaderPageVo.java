package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 常用发票抬头信息表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-05
 * @change 2023-05-05 by binghong.tang for init
 */
@ApiModel(value = "BbpmInvoiceHeaderPageVo对象", description = "常用发票抬头信息表")
public class BbpmInvoiceHeaderPageVo extends McpBasePageVo implements Serializable {


    /**
     * 唯一标识符
     */
    @ApiModelProperty(value = "唯一标识符")
    @NotBlank(message = "唯一标识符不能为空", groups = {UpdateValidatorGroup.class})
    private String invoiceHeaderId;

    /**
     * 抬头类型，01表示企业单位，02表示个人/非企业单位
     */
    @ApiModelProperty(value = "抬头类型，01表示企业单位，02表示个人/非企业单位")
    @NotBlank(message = "抬头类型，01表示企业单位，02表示个人/非企业单位不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String type;

    /**
     * 抬头名称
     */
    @ApiModelProperty(value = "抬头名称")
    @NotBlank(message = "抬头名称不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String name;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    private String taxNumber;

    /**
     * 公司注册地址
     */
    @ApiModelProperty(value = "公司注册地址")
    private String registerAddr;

    /**
     * 公司注册电话
     */
    @ApiModelProperty(value = "公司注册电话")
    private String registerPhone;

    /**
     * 公司开户行
     */
    @ApiModelProperty(value = "公司开户行")
    private String bankName;

    /**
     * 开户行账号
     */
    @ApiModelProperty(value = "开户行账号")
    private String bankAccount;

    /**
     * 是否为默认发票抬头,0否,1是
     */
    @ApiModelProperty(value = "是否为默认发票抬头,0否,1是")
    private String isDefault;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * @return 唯一标识符
     */
    public String getInvoiceHeaderId() {
        return invoiceHeaderId;
    }

    public void setInvoiceHeaderId(String invoiceHeaderId) {
        this.invoiceHeaderId = invoiceHeaderId;
    }

    /**
     * @return 抬头类型，01表示企业单位，02表示个人/非企业单位
     */
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return 抬头名称
     */
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return 公司税号
     */
    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    /**
     * @return 公司注册地址
     */
    public String getRegisterAddr() {
        return registerAddr;
    }

    public void setRegisterAddr(String registerAddr) {
        this.registerAddr = registerAddr;
    }

    /**
     * @return 公司注册电话
     */
    public String getRegisterPhone() {
        return registerPhone;
    }

    public void setRegisterPhone(String registerPhone) {
        this.registerPhone = registerPhone;
    }

    /**
     * @return 公司开户行
     */
    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    /**
     * @return 开户行账号
     */
    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    /**
     * @return 是否为默认发票抬头, 0否, 1是
     */
    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * @return 备注
     */
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "BbpmInvoiceHeaderPageVo{" +
                "invoiceHeaderId=" + invoiceHeaderId +
                ", type=" + type +
                ", name=" + name +
                ", taxNumber=" + taxNumber +
                ", registerAddr=" + registerAddr +
                ", registerPhone=" + registerPhone +
                ", bankName=" + bankName +
                ", bankAccount=" + bankAccount +
                ", isDefault=" + isDefault +
                ", remarks=" + remarks +
                ", delFlag=" + delFlag +
                "}";
    }
}
