package com.bonc.ioc.bzf.business.payment.service.impl;

import com.bonc.ioc.bzf.business.payment.entity.BbpDictEntity;
import com.bonc.ioc.bzf.business.payment.dao.BbpDictMapper;
import com.bonc.ioc.bzf.business.payment.service.IBbpDictService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.payment.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 字典表 服务类实现
 *
 * <AUTHOR>
 * @date 2022-09-22
 * @change 2022-09-22 by wtl for init
 */
@Slf4j
@Service
public class BbpDictServiceImpl extends McpBaseServiceImpl<BbpDictEntity> implements IBbpDictService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpDictMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpDictService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpDictVo vo) {
        if(vo == null) {
            return null;
        }

        BbpDictEntity entity = new BbpDictEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setDictId(null);
        if(!baseService.insert(entity)) {
            log.error("字典表新增失败:" + entity.toString());
            throw new McpException("字典表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getDictId(),1)) {
                log.error("字典表新增后保存历史失败:" + entity.toString());
                throw new McpException("字典表新增后保存历史失败");
            }

            log.debug("字典表新增成功:"+entity.getDictId());
            return entity.getDictId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpDictVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpDictEntity> entityList = new ArrayList<>();
        for (BbpDictVo item:voList) {
            BbpDictEntity entity = new BbpDictEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpDictEntity item:entityList){
            item.setDictId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("字典表新增失败");
            throw new McpException("字典表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpDictEntity::getDictId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("字典表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("字典表批量新增后保存历史失败");
            }

            log.debug("字典表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param dictId 需要删除的字典表主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String dictId) {
        if(!StringUtils.isEmpty(dictId)) {
            if(!baseService.saveOperationHisById(dictId,3)) {
                log.error("字典表删除后保存历史失败:" + dictId);
                throw new McpException("字典表删除后保存历史失败");
            }

            if(!baseService.removeById(dictId)) {
                log.error("字典表删除失败");
                throw new McpException("字典表删除失败"+dictId);
            }
        } else {
            throw new McpException("字典表删除失败字典表主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param dictIdList 需要删除的字典表主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> dictIdList) {
        if(!CollectionUtils.isEmpty(dictIdList)) {
            int oldSize = dictIdList.size();
            dictIdList = dictIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(dictIdList) || oldSize != dictIdList.size()) {
                throw new McpException("字典表批量删除失败 存在主键id为空的记录"+StringUtils.join(dictIdList));
            }

            if(!baseService.saveOperationHisByIds(dictIdList,3)) {
                log.error("字典表批量删除后保存历史失败:" + StringUtils.join(dictIdList));
                throw new McpException("字典表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(dictIdList)) {
                log.error("字典表批量删除失败");
                throw new McpException("字典表批量删除失败"+StringUtils.join(dictIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的字典表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpDictVo vo) {
        if(vo != null) {
            BbpDictEntity entity = new BbpDictEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getDictId())) {
                throw new McpException("字典表更新失败传入字典表主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("字典表更新失败");
                throw new McpException("字典表更新失败"+entity.getDictId());
            } else {
                if(!baseService.saveOperationHisById(entity.getDictId(),2)) {
                    log.error("字典表更新后保存历史失败:" + entity.getDictId());
                    throw new McpException("字典表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("字典表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的字典表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpDictVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpDictEntity> entityList = new ArrayList<>();

            for (BbpDictVo item:voList){
                BbpDictEntity entity = new BbpDictEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getDictId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("字典表批量更新失败 存在字典表主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("字典表批量更新失败");
                throw new McpException("字典表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getDictId())).map(BbpDictEntity::getDictId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("字典表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("字典表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的字典表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpDictVo vo) {
        if(vo != null) {
            BbpDictEntity entity = new BbpDictEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("字典表保存失败");
                throw new McpException("字典表保存失败"+entity.getDictId());
            } else {
                if(!baseService.saveOperationHisById(entity.getDictId(),4)) {
                    log.error("字典表保存后保存历史失败:" + entity.getDictId());
                    throw new McpException("字典表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("字典表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的字典表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpDictVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpDictEntity> entityList = new ArrayList<>();

            for (BbpDictVo item:voList){
                BbpDictEntity entity = new BbpDictEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("字典表批量保存失败");
                throw new McpException("字典表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getDictId())).map(BbpDictEntity::getDictId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("字典表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("字典表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param dictId 需要查询的字典表主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpDictVo selectByIdRecord(String dictId) {
        BbpDictVo vo = new BbpDictVo();

        if(!StringUtils.isEmpty(dictId)) {
            BbpDictEntity entity = baseService.selectById(dictId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpDictPageResultVo>> selectByPageRecord(BbpDictPageVo vo) {
        List<BbpDictPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
