package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 确认开票接收实体
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@ApiModel(value="BbpmCashCollectionVo", description="确认开票接收实体")
public class BbpmCashCollectionVo extends McpBaseVo implements Serializable{
    @ApiModelProperty("pc还是app调用")
    private String isPcOrApp;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal paidInAmount;

    /**
     * 收款单信息列表
     */
    @ApiModelProperty(value = "收款单信息列表")
    List<BbpmCollectionPageResultVo> bbpmCollectionVoList;

    @ApiModelProperty("签章人印章")
    private String sealImage;

    public String getSealImage() {
        return sealImage;
    }

    public void setSealImage(String sealImage) {
        this.sealImage = sealImage;
    }
    /**
     * 凭证文件地址
     */
//    @ApiModelProperty(value = "凭证文件地址--不用传")
//    private String credentialFileAddress;
//
//
//    @ApiModelProperty(value = "收款凭证编号--不用传")
//    private String VoucherNumber;
//
//    @ApiModelProperty(value = "签字前请求合同模板接口参数--不用传")
//    private String requestParams;

//    public String getRequestParams() {
//        return requestParams;
//    }
//
//    public void setRequestParams(String requestParams) {
//        this.requestParams = requestParams;
//    }
//
//    public String getVoucherNumber() {
//        return VoucherNumber;
//    }
//
//    public void setVoucherNumber(String voucherNumber) {
//        VoucherNumber = voucherNumber;
//    }

    public String getIsPcOrApp() {
        return isPcOrApp;
    }

    public void setIsPcOrApp(String isPcOrApp) {
        this.isPcOrApp = isPcOrApp;
    }

    public BigDecimal getPaidInAmount() {
        return paidInAmount;
    }

    public void setPaidInAmount(BigDecimal paidInAmount) {
        this.paidInAmount = paidInAmount;
    }

    public List<BbpmCollectionPageResultVo> getBbpmCollectionVoList() {
        return bbpmCollectionVoList;
    }

    public void setBbpmCollectionVoList(List<BbpmCollectionPageResultVo> bbpmCollectionVoList) {
        this.bbpmCollectionVoList = bbpmCollectionVoList;
    }

//    public String getCredentialFileAddress() {
//        return credentialFileAddress;
//    }
//
//    public void setCredentialFileAddress(String credentialFileAddress) {
//        this.credentialFileAddress = credentialFileAddress;
//    }
}
