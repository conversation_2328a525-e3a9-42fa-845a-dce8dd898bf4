package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpmDepositCollectionRelationshipEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 存款与收款对应关系表v3.0 服务类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
public interface IBbpmDepositCollectionRelationshipService extends IMcpBaseService<BbpmDepositCollectionRelationshipEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    String insertRecord(BbpmDepositCollectionRelationshipVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmDepositCollectionRelationshipVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param billCollectionId 需要删除的主键id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void removeByIdRecord(String billCollectionId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param billCollectionIdList 需要删除的主键id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> billCollectionIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的存款与收款对应关系表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void updateByIdRecord(BbpmDepositCollectionRelationshipVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的存款与收款对应关系表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmDepositCollectionRelationshipVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的存款与收款对应关系表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void saveByIdRecord(BbpmDepositCollectionRelationshipVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的存款与收款对应关系表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmDepositCollectionRelationshipVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param billCollectionId 需要查询的主键id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    BbpmDepositCollectionRelationshipVo selectByIdRecord(String billCollectionId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    PageResult<List<BbpmDepositCollectionRelationshipPageResultVo>> selectByPageRecord(BbpmDepositCollectionRelationshipPageVo vo);
}
