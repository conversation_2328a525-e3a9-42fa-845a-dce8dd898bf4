package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 审批明细表 实体类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Data
@ApiModel(value = "BbpmApproveDetailInfoVo对象", description = "审批明细表")
public class BbpmApproveDetailInfoVo extends McpBaseVo implements Serializable {

    /**
     * 审批明细id
     */
    @ApiModelProperty(value = "审批明细id")
    @NotBlank(message = "审批明细id不能为空", groups = {UpdateValidatorGroup.class})
    private String approveDetailId;

    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    private String approveId;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 审批人角色id
     */
    @ApiModelProperty(value = "审批人角色id")
    private String approverRoleId;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverUserId;

    /**
     * 审批评论
     */
    @ApiModelProperty(value = "审批评论")
    private String approveRemark;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String createUser;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 设置审批信息
     *
     * @param vo 审批 vo实体
     */
    public void setApproveInfo(BbpmApproveInfoVo vo) {
        this.approveId = vo.getApproveId();
        this.approveStatus = vo.getApproveStatus();
    }
}
