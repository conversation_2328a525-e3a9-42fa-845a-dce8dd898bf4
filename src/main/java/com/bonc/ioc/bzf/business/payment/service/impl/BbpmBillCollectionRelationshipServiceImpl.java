package com.bonc.ioc.bzf.business.payment.service.impl;

import com.bonc.ioc.bzf.business.payment.entity.BbpmBillCollectionRelationshipEntity;
import com.bonc.ioc.bzf.business.payment.dao.BbpmBillCollectionRelationshipMapper;
import com.bonc.ioc.bzf.business.payment.service.IBbpmBillCollectionRelationshipService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.payment.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 账单与收款对应关系表v3.0 服务类实现
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmBillCollectionRelationshipServiceImpl extends McpBaseServiceImpl<BbpmBillCollectionRelationshipEntity> implements IBbpmBillCollectionRelationshipService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmBillCollectionRelationshipMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmBillCollectionRelationshipService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmBillCollectionRelationshipVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmBillCollectionRelationshipEntity entity = new BbpmBillCollectionRelationshipEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setBillCollectionId(null);
        if(!baseService.insert(entity)) {
            log.error("账单与收款对应关系表v3.0新增失败:" + entity.toString());
            throw new McpException("账单与收款对应关系表v3.0新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getBillCollectionId(),1)) {
                log.error("账单与收款对应关系表v3.0新增后保存历史失败:" + entity.toString());
                throw new McpException("账单与收款对应关系表v3.0新增后保存历史失败");
            }

            log.debug("账单与收款对应关系表v3.0新增成功:"+entity.getBillCollectionId());
            return entity.getBillCollectionId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmBillCollectionRelationshipVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmBillCollectionRelationshipEntity> entityList = new ArrayList<>();
        for (BbpmBillCollectionRelationshipVo item:voList) {
            BbpmBillCollectionRelationshipEntity entity = new BbpmBillCollectionRelationshipEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmBillCollectionRelationshipEntity item:entityList){
            item.setBillCollectionId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("账单与收款对应关系表v3.0新增失败");
            throw new McpException("账单与收款对应关系表v3.0新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmBillCollectionRelationshipEntity::getBillCollectionId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("账单与收款对应关系表v3.0批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("账单与收款对应关系表v3.0批量新增后保存历史失败");
            }

            log.debug("账单与收款对应关系表v3.0新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param billCollectionId 需要删除的主键id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String billCollectionId) {
        if(!StringUtils.isEmpty(billCollectionId)) {
            if(!baseService.saveOperationHisById(billCollectionId,3)) {
                log.error("账单与收款对应关系表v3.0删除后保存历史失败:" + billCollectionId);
                throw new McpException("账单与收款对应关系表v3.0删除后保存历史失败");
            }

            if(!baseService.removeById(billCollectionId)) {
                log.error("账单与收款对应关系表v3.0删除失败");
                throw new McpException("账单与收款对应关系表v3.0删除失败"+billCollectionId);
            }
        } else {
            throw new McpException("账单与收款对应关系表v3.0删除失败主键id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param billCollectionIdList 需要删除的主键id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> billCollectionIdList) {
        if(!CollectionUtils.isEmpty(billCollectionIdList)) {
            int oldSize = billCollectionIdList.size();
            billCollectionIdList = billCollectionIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(billCollectionIdList) || oldSize != billCollectionIdList.size()) {
                throw new McpException("账单与收款对应关系表v3.0批量删除失败 存在主键id为空的记录"+StringUtils.join(billCollectionIdList));
            }

            if(!baseService.saveOperationHisByIds(billCollectionIdList,3)) {
                log.error("账单与收款对应关系表v3.0批量删除后保存历史失败:" + StringUtils.join(billCollectionIdList));
                throw new McpException("账单与收款对应关系表v3.0批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(billCollectionIdList)) {
                log.error("账单与收款对应关系表v3.0批量删除失败");
                throw new McpException("账单与收款对应关系表v3.0批量删除失败"+StringUtils.join(billCollectionIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的账单与收款对应关系表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmBillCollectionRelationshipVo vo) {
        if(vo != null) {
            BbpmBillCollectionRelationshipEntity entity = new BbpmBillCollectionRelationshipEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getBillCollectionId())) {
                throw new McpException("账单与收款对应关系表v3.0更新失败传入主键id为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("账单与收款对应关系表v3.0更新失败");
                throw new McpException("账单与收款对应关系表v3.0更新失败"+entity.getBillCollectionId());
            } else {
                if(!baseService.saveOperationHisById(entity.getBillCollectionId(),2)) {
                    log.error("账单与收款对应关系表v3.0更新后保存历史失败:" + entity.getBillCollectionId());
                    throw new McpException("账单与收款对应关系表v3.0更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("账单与收款对应关系表v3.0更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的账单与收款对应关系表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmBillCollectionRelationshipVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmBillCollectionRelationshipEntity> entityList = new ArrayList<>();

            for (BbpmBillCollectionRelationshipVo item:voList){
                BbpmBillCollectionRelationshipEntity entity = new BbpmBillCollectionRelationshipEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getBillCollectionId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("账单与收款对应关系表v3.0批量更新失败 存在主键id为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("账单与收款对应关系表v3.0批量更新失败");
                throw new McpException("账单与收款对应关系表v3.0批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getBillCollectionId())).map(BbpmBillCollectionRelationshipEntity::getBillCollectionId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("账单与收款对应关系表v3.0批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("账单与收款对应关系表v3.0批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的账单与收款对应关系表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmBillCollectionRelationshipVo vo) {
        if(vo != null) {
            BbpmBillCollectionRelationshipEntity entity = new BbpmBillCollectionRelationshipEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("账单与收款对应关系表v3.0保存失败");
                throw new McpException("账单与收款对应关系表v3.0保存失败"+entity.getBillCollectionId());
            } else {
                if(!baseService.saveOperationHisById(entity.getBillCollectionId(),4)) {
                    log.error("账单与收款对应关系表v3.0保存后保存历史失败:" + entity.getBillCollectionId());
                    throw new McpException("账单与收款对应关系表v3.0保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("账单与收款对应关系表v3.0保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的账单与收款对应关系表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmBillCollectionRelationshipVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmBillCollectionRelationshipEntity> entityList = new ArrayList<>();

            for (BbpmBillCollectionRelationshipVo item:voList){
                BbpmBillCollectionRelationshipEntity entity = new BbpmBillCollectionRelationshipEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("账单与收款对应关系表v3.0批量保存失败");
                throw new McpException("账单与收款对应关系表v3.0批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getBillCollectionId())).map(BbpmBillCollectionRelationshipEntity::getBillCollectionId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("账单与收款对应关系表v3.0批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("账单与收款对应关系表v3.0批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param billCollectionId 需要查询的主键id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmBillCollectionRelationshipVo selectByIdRecord(String billCollectionId) {
        BbpmBillCollectionRelationshipVo vo = new BbpmBillCollectionRelationshipVo();

        if(!StringUtils.isEmpty(billCollectionId)) {
            BbpmBillCollectionRelationshipEntity entity = baseService.selectById(billCollectionId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmBillCollectionRelationshipPageResultVo>> selectByPageRecord(BbpmBillCollectionRelationshipPageVo vo) {
        List<BbpmBillCollectionRelationshipPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
