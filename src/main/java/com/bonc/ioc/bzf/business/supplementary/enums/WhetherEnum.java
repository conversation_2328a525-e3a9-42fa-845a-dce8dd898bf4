package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 是否 枚举类
 *
 * <AUTHOR>
 * @since 2023/5/12
 */
public enum WhetherEnum {

    /**
     * 否
     */
    NO("0", "否"),

    /**
     * 是
     */
    YES("1", "是");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    WhetherEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    WhetherEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        WhetherEnum[] enums = values();
        for (WhetherEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
