package com.bonc.ioc.bzf.business.payment.feign.feign;


import com.alibaba.fastjson.JSONObject;


import com.bonc.ioc.bzf.business.payment.entity.SystemLocaIdEntity;
import com.bonc.ioc.bzf.business.payment.feign.fallback.BmsFeignClientFallback;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.sinovatech.rd.bms.api.location.vo.*;
import com.sinovatech.rd.bms.api.menu.vo.MenuRpcServiceGetMenuByUserIdReq;
import com.sinovatech.rd.bms.api.menu.vo.MenuRpcServiceGetMenuByUserIdResp;
import com.sinovatech.rd.bms.api.role.vo.BmsRoleRpcServiceGetOneRoleReq;
import com.sinovatech.rd.bms.api.role.vo.BmsRoleRpcServiceGetOneRoleResp;
import com.sinovatech.rd.bms.api.role.vo.BmsRoleRpcServiceQueryUsersByLocationsAndRoleReq;
import com.sinovatech.rd.bms.api.role.vo.BmsRoleRpcServiceQueryUsersByLocationsAndRoleResp;
import com.sinovatech.rd.bms.api.user.vo.*;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 描述内容
 *
 * <AUTHOR>
 * @date 2022/2/21 16:55
 * @change 2022/2/21 16:55 by <EMAIL> for init
 */
@FeignClient(value = "bphc-bms-service", fallback = BmsFeignClientFallback.class,
        configuration = FeignExceptionConfiguration.class)
public interface BmsFeignClient {


    /**
     * userLogin 用户登录
     *
     * @param req
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserLoginResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/4/19 17:42
     * @change 2022/4/19 17:42 by <EMAIL> for init
     */
    @PostMapping(value = "/bmsUser/userLogin")
    @LogPoint(system = "bphc-bms-service")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceUserLoginResp> userLogin(Request<BmsUserRpcServiceUserLoginReq> req);


    /**
     * getOneUser 单个用户基本信息
     *
     * @param req
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/4/19 17:42
     * @change 2022/4/19 17:42 by <EMAIL> for init
     */
    @PostMapping(value = "/bmsUser/getOneUser")
    @FeignExceptionCheck
    @LogPoint(system = "bphc-bms-service")
    Response<BmsUserRpcServiceGetOneUserResp> getOneUser(Request<BmsUserRpcServiceGetOneUserReq> req);

    /**
     * getMenuByUserId 根据用户Id获取菜单权限
     *
     * @param req
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.menu.vo.MenuRpcServiceGetMenuByUserIdResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/4/19 17:43
     * @change 2022/4/19 17:43 by <EMAIL> for init
     */
    @PostMapping(value = "/menu/getMenuByUserId")
    @FeignExceptionCheck
    Response<MenuRpcServiceGetMenuByUserIdResp> getMenuByUserId(Request<MenuRpcServiceGetMenuByUserIdReq> req);

    /**
     * userLogOut 用户登出
     *
     * @param req
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserLogOutResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/4/19 17:59
     * @change 2022/4/19 17:59 by <EMAIL> for init
     */
    @PostMapping(value = "/bmsUser/userLogOut")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceUserLogOutResp> userLogOut(Request<BmsUserRpcServiceUserLogOutReq> req);

    /**
     * selectUserListTwo 分页查询用户中心用户
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by wxf for init
     */
    @PostMapping(value = "/bmsUser/getAllUsers")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceGetAllUsersResp> selectUserListTwo(Request<BmsUserRpcServiceGetAllUsersReq> req);



    /**
     * selectUserListAll 分页查询用户中心用户
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by wxf for init
     */
    @PostMapping(value = "/bmsUser/getAllUserForPage")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceGetAllUserForPageResp> getAllUserForPage(@RequestBody Request<BmsUserRpcServiceGetAllUserForPageReq> req);

    /**
     * selectUserListAll 分页查询用户中心用户
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by wxf for init
     */
    @PostMapping(value = "/bmsLocation/getAll")
    @FeignExceptionCheck
    Response<BmsLocationRpcServiceGetAllResp> selectDeptAll(Request<BmsLocationRpcServiceGetAllReq> req);


    /**
     * 修改密码
     *
     * @param req:
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUpdUserPwdResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/28 9:38
     * @change 2022/3/28 9:38 by <EMAIL> for init
     */
    @PostMapping(value = "/bmsUser/updUserPwd")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceUpdUserPwdResp> updUserPwd(Request<BmsUserRpcServiceUpdUserPwdReq> req);

    /**
     * 发送验证码
     *
     * @param req: 请求体
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceSendCodeResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/28 17:38
     * @change 2022/3/28 17:38 by <EMAIL> for init
     */
    @PostMapping(value = "/bmsUser/sendCode")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceSendCodeResp> sendCode(Request<BmsUserRpcServiceSendCodeReq> req);

    /**
     * 手机验证码登录
     *
     * @param req: 请求体
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServicePhoneLoginResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/29 9:24
     * @change 2022/3/29 9:24 by <EMAIL> for init
     */
    @PostMapping(value = "/bmsUser/phoneLogin")
    @FeignExceptionCheck
    Response<BmsUserRpcServicePhoneLoginResp> phoneLogin(Request<BmsUserRpcServicePhoneLoginReq> req);

    /**
     * 用户中心新的手机验证码登录
     *
     * @param req: 请求体
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.user.vo.BmsUserMRpcServiceLoginByPhoneCodeResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/5/5 14:30
     * @change 2022/5/5 14:30 by <EMAIL> for init
     */
    @PostMapping(value = "/bmsUserM/loginByPhoneCode")
    @FeignExceptionCheck
    Response<BmsUserMRpcServiceLoginByPhoneCodeResp> loginByPhoneCode(Request<BmsUserMRpcServiceLoginByPhoneCodeReq> req);

    /**
     * 根据token获取用户信息
     *
     * @param req: 请求体
     * @return  com.sinovatech.saas.base.spec.bean.Response<com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserAuthResp>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/4/21 15:11
     * @change 2022/4/21 15:11 by <EMAIL> for init
     */
    @PostMapping(value = "/bmsUser/userAuth")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceUserAuthResp> userAuth(Request<BmsUserRpcServiceUserAuthReq> req);

    @PostMapping(value = "/bmsUserM/loginWithOutPwd")
    @FeignExceptionCheck
    Response<BmsUserMRpcServiceLoginWithOutPwdResp> loginWithOutPwd(Request<BmsUserMRpcServiceLoginWithOutPwdReq> req);

    /**
     * 用户根据手机号验证码修改密码
     *
     * @param req
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/4/29 15:03
     * @change 2022/4/29 15:03 by wxf for init
     */
    @PostMapping(value = "/bmsUser/updUserPwdAndPhon")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceUpdUserPwdAndPhonResp> updUserPwdAndPhon(Request<BmsUserRpcServiceUpdUserPwdAndPhonReq> req);

    /**
     * getOneRole  获取用户角色信息
     *
     * @param req
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     */
    @PostMapping(value = "/bmsRole/getOneRole")
    @FeignExceptionCheck
    Response<BmsRoleRpcServiceGetOneRoleResp> getOneRole(Request<BmsRoleRpcServiceGetOneRoleReq> req);

    /**
     * getLocUsers  获取部门下用户列表
     *
     * @param req
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     */
    @PostMapping(value = "/bmsUser/getLocUsers")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceGetLocUsersResp> getLocUsers(Request<BmsUserRpcServiceGetLocUsersReq> req);

    /**
     * selectOneOrg 获取单个部门信息
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by yql for init
     */
    @PostMapping(value = "/bmsLocation/getOne")
    @FeignExceptionCheck
    Response<BmsLocationRpcServiceGetOneResp> selectOneOrg(Request<BmsLocationRpcServiceGetOneReq> req);


    @PostMapping(value = "/bmsLocation/getOne")
    @FeignExceptionCheck
    @LogPoint(system = "bphc-bms-service")
    Response<JSONObject> selectOne1(Request<BmsLocationRpcServiceGetOneReq> req);
    /**
     * selectAllSubOrg 获取下属部门信息
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by yql for init
     */
    @PostMapping(value = "/bmsLocation/getAllSubLoc")
    @FeignExceptionCheck
    Response<BmsLocationRpcServiceGetAllSubLocResp> selectAllSubOrg(Request<BmsLocationRpcServiceGetAllSubLocReq> req);

    /**
     * getUserRoles 获取用户角色信息
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by yql for init
     */
    @PostMapping(value = "/bmsUser/getUserRoles")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceGetUserRolesResp> getUserRoles(Request<BmsUserRpcServiceGetUserRolesReq> req);

    /**
     * getUserByLocationId 获取部门下用户列表
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by yql for init
     */
    @PostMapping(value = "/bmsLocation/queryUserByLocationId")
    @FeignExceptionCheck
    Response<BmsLocationRpcServiceQueryUserByLocationIdResp> getUserByLocationId(Request<BmsLocationRpcServiceQueryUserByLocationIdReq> req);

    /**
     * selectUserAll 查询用户中心用户列表
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/8/12315:42
     */
    @PostMapping(value = "/bmsUser/getAll")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceGetAllResp> selectUserAll(Request<BmsUserRpcServiceGetAllReq> req);

    /**
     * getUserByIds 根据id串获取用户列表
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by yql for init
     */
    @PostMapping(value = "/bmsUser/getUsersByIds")
    @FeignExceptionCheck
    Response<BmsUserRpcServiceGetUsersByIdsResp> getUserByIds(Request<BmsUserRpcServiceGetUsersByIdsReq> req);

    /**
     * getUserCountByLocationId 根据部门id获取用户数量，不传部门id会获取所有部门数量
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by yql for init
     */
    @PostMapping(value = "/bmsLocation/getUserCountByLocationId")
    @FeignExceptionCheck
    Response<BmsLocationRpcServiceGetUserCountByLocationIdResp> getUserCountByLocationId(Request<BmsLocationRpcServiceGetUserCountByLocationIdReq> req);

    /**
     * getQueryUsersByLocationsAndRole 根据部门id集合获取用户列表，其中包括用户角色信息，并且可以通过用户真实姓名或电话号筛选
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by yql for init
     */
    @PostMapping(value = "/bmsRole/queryUsersByLocationsAndRole")
    @FeignExceptionCheck
    Response<BmsRoleRpcServiceQueryUsersByLocationsAndRoleResp> getQueryUsersByLocationsAndRole(Request<BmsRoleRpcServiceQueryUsersByLocationsAndRoleReq> req);

    /**
     * getSystemLocaIdByLocationId 获取运营单位编号
     *
     * @param
     * @return  null
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/3/16 15:42
     * @change 2022/3/16 15:42 by yql for init
     */
    @PostMapping(value = "/bmsLocation/getSystemLocaIdByLocationId")
    @FeignExceptionCheck
    @LogPoint(system = "bphc-bms-service")
    Response<SystemLocaIdEntity> getSystemLocaIdByLocationId(Request<SystemLocaIdEntity> req);
}
