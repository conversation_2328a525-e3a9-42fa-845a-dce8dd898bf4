package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCashCollectionVoucherMapper;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCollectionMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmCollectionEntity;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.service.*;
import com.bonc.ioc.bzf.business.payment.utils.RedisDistributedId;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收款表v3.0 服务类实现
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmCollectionServiceImpl extends McpBaseServiceImpl<BbpmCollectionEntity> implements IBbpmCollectionService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmCollectionMapper baseMapper;

    @Resource
    private BbpmCashCollectionVoucherMapper bbpmCashCollectionVoucherMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmCollectionService baseService;

    @Resource
    private IBbpmBillCollectionRelationshipService iBbpmBillCollectionRelationshipService;

    @Resource
    private McpDictSession mcpDictSession;

    @Autowired
    RedisDistributedId redisDistributedId;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IBbpmBillCollectionDetailsService iBbpmBillCollectionDetailsService;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;


    @Value("${export.maxSize}")
    private Integer exportMaxSize;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    @Resource
    private IBbpmBillManagementService iBbpmBillManagementService;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private ISendCollectionService iSendCollectionService;


    /**
     * insertRecordAll 单个收款，包括现金、支票、线下转账
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    public String insertRecordAll(BbpmCollectionVo vo) {
        log.info("单个收款前端入参：{}",JSONObject.toJSONString(vo));
        if (vo == null) {
            throw new McpException("参数为空");
        }
        if(StringUtils.isBlank(vo.getCollectionChannel())){
            throw new McpException("收款渠道不能为空");
        }
        //收款回退时不为空
        String chargeCode = vo.getChargeCode();

        if(StringUtils.isBlank(chargeCode)){
            String billNo = vo.getBillNo();
            String contractCode = vo.getContractCode();
            if (StringUtils.isBlank(billNo)) {
                throw new McpException("账单编号不能为空");
            }
            if (StringUtils.isBlank(contractCode)) {
                throw new McpException("合同号不能为空");
            }
            String lockKey = "lock:" + contractCode;
            RLock disLock = redissonClient.getLock(lockKey);

            if(disLock.tryLock()){
                try {
                    //业务处理
                    if(PaymentEnums.COLLECTION_CHANNEL_CASH.getCode().equals(vo.getCollectionChannel())){
                        //现金收款
                        return iSendCollectionService.collectionSingleCash(vo);
                    }else if(PaymentEnums.COLLECTION_CHANNEL_OFFLINE.getCode().equals(vo.getCollectionChannel())){
                        //线下转账
                        return iSendCollectionService.collectionSingleOffline(vo);
                    }else if(PaymentEnums.COLLECTION_CHANNEL_CHEQUE.getCode().equals(vo.getCollectionChannel())){
                        //支票
                        return iSendCollectionService.collectionSingleCheque(vo);
                    }else{
                        throw new McpException(vo.getCollectionChannel()+" 收款渠道不存在");
                    }
                }finally {
                    if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                        disLock.unlock();
                    }
                }
            }else{
                log.error("收款失败，合同"+contractCode+"下的账单已在收款中");
                throw new McpException("收款失败，合同"+contractCode+"下的账单已在收款中");
            }
        }else{
            //收款回退时
            String lockKey = "lock:" + chargeCode;
            RLock disLock = redissonClient.getLock(lockKey);
            if(disLock.tryLock()){
                try {
                    //业务处理
                    if(PaymentEnums.COLLECTION_CHANNEL_OFFLINE.getCode().equals(vo.getCollectionChannel())){
                        //线下转账
                        return iSendCollectionService.collectionSingleOffline(vo);
                    }else if(PaymentEnums.COLLECTION_CHANNEL_CHEQUE.getCode().equals(vo.getCollectionChannel())){
                        //支票
                        return iSendCollectionService.collectionSingleCheque(vo);
                    }else{
                        throw new McpException("收款回退编辑不支持该渠道:"+vo.getCollectionChannel());
                    }
                }finally {
                    if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                        disLock.unlock();
                    }
                }
            }else{
                log.error("收款单唯一标识码为"+chargeCode+"的收款单正在提交中");
                throw new McpException("收款单唯一标识码为"+chargeCode+"的收款单正在提交中");
            }
        }
    }


    /**
     * insertCollectionBatchRecordAll 批量收款,包括现金、支票、线下转账
     * @param  vo 需要保存的记录
     * @return  String 返回新增后的主键vo
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    public String insertCollectionBatchRecordAll(BbpmCollectionBatchVo vo) {
        log.info("批量收款前端入参：{}",JSONObject.toJSONString(vo));
        if (vo == null || vo.getBbpmBillManagementVoList() == null || vo.getBbpmBillManagementVoList().size() == 0) {
            throw new McpException("参数为空");
        }
        String billNo = vo.getBbpmBillManagementVoList().get(0).getBillNo();
        String contractCode = vo.getBbpmBillManagementVoList().get(0).getContractCode();
        if (StringUtils.isBlank(billNo)) {
            throw new McpException("账单编号不能为空");
        }
        if (StringUtils.isBlank(contractCode)) {
            throw new McpException("合同号不能为空");
        }
        String lockKey = "lock:" + contractCode;
        RLock disLock = redissonClient.getLock(lockKey);

        if(disLock.tryLock()){
            try {
                //业务处理
                if(PaymentEnums.COLLECTION_CHANNEL_CASH.getCode().equals(vo.getCollectionChannel())){
                    //现金收款
                    return iSendCollectionService.collectionBatchCash(vo);
                }else if(PaymentEnums.COLLECTION_CHANNEL_OFFLINE.getCode().equals(vo.getCollectionChannel())){
                    //线下转账
                    return iSendCollectionService.collectionBatchOffline(vo);
                }else if(PaymentEnums.COLLECTION_CHANNEL_CHEQUE.getCode().equals(vo.getCollectionChannel())){
                    //支票
                    return iSendCollectionService.collectionBatchCheque(vo);
                }else{
                    throw new McpException(vo.getCollectionChannel()+" 收款渠道不存在");
                }
            }finally {
                if (disLock.isLocked() && disLock.isHeldByCurrentThread()) {
                    disLock.unlock();
                }
            }
        }else{
            log.error("收款失败，合同"+contractCode+"下的账单已在收款中");
            throw new McpException("收款失败，合同"+contractCode+"下的账单已在收款中");
        }
    }


    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmCollectionVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmCollectionEntity> entityList = new ArrayList<>();
        for (BbpmCollectionVo item : voList) {
            BbpmCollectionEntity entity = new BbpmCollectionEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmCollectionEntity item : entityList) {
            item.setCollectionId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("收款表v3.0新增失败");
            throw new McpException("收款表v3.0新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbpmCollectionEntity::getCollectionId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("收款表v3.0批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("收款表v3.0批量新增后保存历史失败");
            }

            log.debug("收款表v3.0新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param messageId 需要删除的主键id
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String messageId) {
        if (!StringUtils.isEmpty(messageId)) {
            if (!baseService.saveOperationHisById(messageId, 3)) {
                log.error("收款表v3.0删除后保存历史失败:" + messageId);
                throw new McpException("收款表v3.0删除后保存历史失败");
            }

            if (!baseService.removeById(messageId)) {
                log.error("收款表v3.0删除失败");
                throw new McpException("收款表v3.0删除失败" + messageId);
            }
        } else {
            throw new McpException("收款表v3.0删除失败主键id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param messageIdList 需要删除的主键id
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> messageIdList) {
        if (!CollectionUtils.isEmpty(messageIdList)) {
            int oldSize = messageIdList.size();
            messageIdList = messageIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(messageIdList) || oldSize != messageIdList.size()) {
                throw new McpException("收款表v3.0批量删除失败 存在主键id为空的记录" + StringUtils.join(messageIdList));
            }

            if (!baseService.saveOperationHisByIds(messageIdList, 3)) {
                log.error("收款表v3.0批量删除后保存历史失败:" + StringUtils.join(messageIdList));
                throw new McpException("收款表v3.0批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(messageIdList)) {
                log.error("收款表v3.0批量删除失败");
                throw new McpException("收款表v3.0批量删除失败" + StringUtils.join(messageIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的收款表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmCollectionVo vo) {
        if (vo != null) {
            BbpmCollectionEntity entity = new BbpmCollectionEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getCollectionId())) {
                throw new McpException("收款表v3.0更新失败传入主键id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("收款表v3.0更新失败");
                throw new McpException("收款表v3.0更新失败" + entity.getCollectionId());
            } else {
                if (!baseService.saveOperationHisById(entity.getCollectionId(), 2)) {
                    log.error("收款表v3.0更新后保存历史失败:" + entity.getCollectionId());
                    throw new McpException("收款表v3.0更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("收款表v3.0更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的收款表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmCollectionVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmCollectionEntity> entityList = new ArrayList<>();

            for (BbpmCollectionVo item : voList) {
                BbpmCollectionEntity entity = new BbpmCollectionEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getCollectionId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("收款表v3.0批量更新失败 存在主键id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("收款表v3.0批量更新失败");
                throw new McpException("收款表v3.0批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getCollectionId())).map(BbpmCollectionEntity::getCollectionId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("收款表v3.0批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("收款表v3.0批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的收款表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmCollectionVo vo) {
        if (vo != null) {
            BbpmCollectionEntity entity = new BbpmCollectionEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("收款表v3.0保存失败");
                throw new McpException("收款表v3.0保存失败" + entity.getCollectionId());
            } else {
                if (!baseService.saveOperationHisById(entity.getCollectionId(), 4)) {
                    log.error("收款表v3.0保存后保存历史失败:" + entity.getCollectionId());
                    throw new McpException("收款表v3.0保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("收款表v3.0保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的收款表v3.0
     * @return void
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmCollectionVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmCollectionEntity> entityList = new ArrayList<>();

            for (BbpmCollectionVo item : voList) {
                BbpmCollectionEntity entity = new BbpmCollectionEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("收款表v3.0批量保存失败");
                throw new McpException("收款表v3.0批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getCollectionId())).map(BbpmCollectionEntity::getCollectionId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("收款表v3.0批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("收款表v3.0批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param messageId 需要查询的主键id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public BbpmCollectionVo selectByIdRecord(String messageId) {
        BbpmCollectionVo vo = new BbpmCollectionVo();

        if (!StringUtils.isEmpty(messageId)) {
            BbpmCollectionEntity entity = baseService.selectById(messageId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmCollectionPageResultVo>> selectByPageRecord(BbpmCollectionPageVo vo) {
        if(StringUtils.isNotBlank(vo.getProjectFormats())){
            List<String> projectFormatList = Arrays.stream(vo.getProjectFormats().split(","))
                    .collect(Collectors.toList());
            vo.setProjectFormatList(projectFormatList);
        }

        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setCurrent(Integer.valueOf(vo.getPageNumber()+""));
        ParentRequest<BbpmCollectionPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        //请求业财接口
        //重新set值到分页实体、转换
        PageResult<List<BbpmCollectionPageResultVo>> pageResult = listByBill(parentRequest);

        return pageResult;
    }

    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmCollectionPageResultVo> getReceiptList(BbpmCollectionPageVo vo) {
        if(StringUtils.isNotBlank(vo.getProjectFormats())){
            List<String> projectFormatList = Arrays.stream(vo.getProjectFormats().split(","))
                    .collect(Collectors.toList());
            vo.setProjectFormatList(projectFormatList);
        }

        List<BbpmCollectionPageResultVo> list = new ArrayList<>();
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setSize(1);
        vo.setCurrent(1);
        vo.setOptType("02");//optType 01:详情  02:列表，默认详情
        ParentRequest<BbpmCollectionPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        PageResult<List<BbpmCollectionPageResultVo>> pageResultCheck = listByBill(parentRequest);
        log.info("【调用账单管理导出接口 - 工银接口返回的总条数】{}",pageResultCheck.getTotal());
        if(pageResultCheck != null && pageResultCheck.getTotal() > exportMaxSize){
            throw new McpException("导出限制最大条数"+exportMaxSize+"条");
        }
        //全量查
        vo.setFullPage("true");
        vo.setSize(null);
        vo.setCurrent(null);
        parentRequest.setData(vo);
        PageResult<List<BbpmCollectionPageResultVo>> pageResult = listByBill(parentRequest);
        if(pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0){
            list = pageResult.getRows();
        }
        return list;
    }


    public PageResult<List<BbpmCollectionPageResultVo>> listByBill(ParentRequest parentRequest){

        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("收款单查询接口请求参数json:"+jsonRequest);

        String responseBody = null;
        if (yecaiFeign){
            responseBody = bfipChargeFeignClient.listByBill(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v2/receipt/listByBill", parentRequest);
        }

        log.debug("调用工银收款单列表接口返回:"+responseBody);

        //重新set值到分页实体、转换
        PageResult<List<BbpmCollectionPageResultVo>> pageResult = resetCollection(responseBody);

        return pageResult;
    }


    public PageResult<List<BbpmCollectionPageResultVo>> resetCollection(String responseBody){
        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }

        FaceHttpResultTwo<BbpmCollectionPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            throw new McpException("*提示:"+billListResultFaceHttpResultTwo.getMessage());
        }

        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(null);
        }
        List<BbpmCollectionPageResultVo> collectionListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(),BbpmCollectionPageResultVo.class);

        List<BbpmCollectionPageResultVo> result = new ArrayList<>();

        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        for(BbpmCollectionPageResultVo collection : collectionListResultList){
            collection.setCollectionId(collection.getChargeCode());
            collection.setCollectionNo(collection.getChargeCode());
            collection.setReceiptNo(collection.getChargeCode());
            collection.setBillId(collection.getBillCode());
            collection.setBillNo(collection.getBillCode());

            collection.setElectronicVoucher("1");
            //查询是否开具电子凭证
            if(StringUtils.isNotBlank(collection.getChargeCode())){
                BbpmCashCollectionVoucherVo bbpmCashCollectionVoucherVo = new BbpmCashCollectionVoucherVo();
                bbpmCashCollectionVoucherVo.setCollectionNo(collection.getChargeCode());
                //已签字的算开具完了
                bbpmCashCollectionVoucherVo.setSignStatus(PaymentEnums.SIGN_STATUS_OK.getCode());
                bbpmCashCollectionVoucherVo = bbpmCashCollectionVoucherMapper.selectByCollectionNo(bbpmCashCollectionVoucherVo);
                if(bbpmCashCollectionVoucherVo != null && StringUtils.isNotBlank(bbpmCashCollectionVoucherVo.getVoucherNumber())){
                    collection.setElectronicVoucher("2");
                }
            }

            result.add(collection);
        }

        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);

        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(),result);
    }



    /**
     * selectByPageUndeposits 现金盘点--未存款--分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    public PageResult<List<BbpmCollectionPageResultVo>> selectByPageUndeposits(BbpmCollectionPageVo vo) {
        //未存款
        vo.setDepositStatus("1");
        //现金
//        vo.setCollectionChannel("04");

        List<BbpmCollectionPageResultVo> result = baseMapper.selectByPageCustom(vo);

//        //计算当前页未存款总额
//        BigDecimal totalUndeposits = BigDecimal.ZERO;
//        for (BbpmCollectionPageResultVo bbpmCollectionPageResultVo : result) {
//            totalUndeposits = totalUndeposits.add(bbpmCollectionPageResultVo.getPaidInAmount());
//        }
//        for (BbpmCollectionPageResultVo pageResultVo : result) {
//            pageResultVo.setTotalUndeposits(totalUndeposits);
//        }

        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }


    /**
     * 现金盘点--未存款--所有的
     *
     * @param vo
     * @return
     */
    @Override
    public BbpmUndepositsVo selectUndepositsList(BbpmCollectionVo vo) {
        BbpmUndepositsVo result = new BbpmUndepositsVo();
        //未存款
        vo.setDepositStatus("1");
//        //现金
//        vo.setCollectionChannel("04");

        //收款单唯一识别码
        String chargeCode = vo.getChargeCode();
        //项目名称
        String projectId = vo.getProjectId();
        //房源地址
        String houseName = vo.getHouseName();
        //客户姓名
        String tenantName = vo.getTenantName();
        //证件号码
        String certNo = vo.getCertNo();
        //合同编号
        String contractId = vo.getContractId();
        //收款日期开始
        String chargeDateStart = vo.getChargeDateStart();
        //收款日期结束
        String chargeDateEnd = vo.getChargeDateEnd();

        List<BbpmCollectionPageResultVo> resultVoList = new ArrayList<>();

        //没有查询条件，就先查本地，再拿收款单查工银拿具体信息；  如果有查询条件，就先查工银接口，然后和本地列表对比，根据收款单号筛选 ;
        if (StringUtils.isBlank(chargeCode)   && StringUtils.isBlank(houseName) && StringUtils.isBlank(tenantName)
                && StringUtils.isBlank(certNo) && StringUtils.isBlank(contractId) && StringUtils.isBlank(chargeDateStart) && StringUtils.isBlank(chargeDateEnd)) {

            //查本地
            vo.setDepositStatus("1");//1未存款
            List<BbpmCollectionPageResultVo> bbpmCollectionVoList = baseMapper.selectUndepositsList(vo);

            if(bbpmCollectionVoList != null && bbpmCollectionVoList.size() > 0){
                //拼接请求参数
                SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
                BbpmCollectionVo bbpmCollectionVo = new BbpmCollectionVo();
                bbpmCollectionVo.setSize(1);
                bbpmCollectionVo.setCurrent(1);
                bbpmCollectionVo.setOptType("02");//optType 01:详情  02:列表，默认详情
                bbpmCollectionVo.setProjectId(projectId);
                bbpmCollectionVo.setChargeStatus("01");//收款单状态 正常
                ParentRequest<BbpmCollectionVo> parentRequest = new ParentRequest<>();
                parentRequest.setTime(sdf.format(new Date()));
                parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
                parentRequest.setData(bbpmCollectionVo);

                for(int i = 0;i< bbpmCollectionVoList.size();i++){
                    //主键
                    String collectionId = bbpmCollectionVoList.get(i).getCollectionId();
                    //存款状态
                    String depositStatus = bbpmCollectionVoList.get(i).getDepositStatus();
                    //收款后工银返回的收款单号
                    String receiptNo = bbpmCollectionVoList.get(i).getReceiptNo();

                    //设置收款单号查询条件
                    bbpmCollectionVo.setChargeCode(receiptNo);
                    //查询工银接口 重新set值到分页实体、转换
                    PageResult<List<BbpmCollectionPageResultVo>> pageResult = listByBill(parentRequest);
                    if(pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0){
                        BbpmCollectionPageResultVo bbpmCollectionPageResultVo = pageResult.getRows().get(0);
                        bbpmCollectionPageResultVo.setCollectionId(collectionId);
                        bbpmCollectionPageResultVo.setDepositStatus(depositStatus);
                        resultVoList.add(bbpmCollectionPageResultVo);
                    }
                }
            }
        } else {

            //拼接请求参数
            SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
            //全量查
            vo.setFullPage("true");
            vo.setOptType("02");//optType 01:详情  02:列表，默认详情
            vo.setPaymentType("04");//04-现金支付
            vo.setChargeStatus("01");//收款单状态 正常
            ParentRequest<BbpmCollectionVo> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(vo);
            //请求工银接口 重新set值到分页实体、转换
            PageResult<List<BbpmCollectionPageResultVo>> pageResult = listByBill(parentRequest);
            if(pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0){
                for(BbpmCollectionPageResultVo bbpmCollectionPageResultVo : pageResult.getRows()){

                    BbpmCollectionVo collectionVo = new BbpmCollectionVo();
                    collectionVo.setReceiptNo(bbpmCollectionPageResultVo.getChargeCode());
                    //查本地 按理说只有一条
                    collectionVo.setDepositStatus("1");//1未存款
                    List<BbpmCollectionPageResultVo> bbpmCollectionVoList = baseMapper.selectUndepositsList(collectionVo);
                    if(bbpmCollectionVoList != null && bbpmCollectionVoList.size() >0){
                        bbpmCollectionPageResultVo.setCollectionId(bbpmCollectionVoList.get(0).getCollectionId());
                        bbpmCollectionPageResultVo.setDepositStatus(bbpmCollectionVoList.get(0).getDepositStatus());
                        resultVoList.add(bbpmCollectionPageResultVo);
                    }
                }
            }

        }

        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(resultVoList);
        result.setBbpmCollectionVoList(resultVoList);
        //计算数量和总金额
        BigDecimal sum = new BigDecimal(0);
        if(resultVoList != null && resultVoList.size() > 0){
            result.setCollectionDocQty(resultVoList.size() + "");
            for (BbpmCollectionPageResultVo resultVo : resultVoList) {
                sum = sum.add(resultVo.getChargeMoney());
            }
            result.setPaidInAmount(sum);
        }else{
            result.setCollectionDocQty("0");
            result.setPaidInAmount(sum);
        }

        return result;
    }

    /**
     * 根据存款单id查询收款单列表信息
     *
     * @param depositId
     * @return
     */
    @Override
//    @Transactional(rollbackFor = {Exception.class})
    public List<BbpmCollectionPageResultVo> selectDepositAndCollectionList(String depositId) {
        List<BbpmCollectionVo> bbpmCollectionVoList = baseMapper.selectDepositAndCollectionList(depositId);
        List<BbpmCollectionPageResultVo> resultVoList = new ArrayList<>();
        if(bbpmCollectionVoList != null && bbpmCollectionVoList.size() > 0){
            //拼接请求参数
            SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
            BbpmCollectionVo bbpmCollectionVo = new BbpmCollectionVo();
            bbpmCollectionVo.setSize(1);
            bbpmCollectionVo.setCurrent(1);
            ParentRequest<BbpmCollectionVo> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(bbpmCollectionVo);

            for(int i = 0;i< bbpmCollectionVoList.size();i++){
                //主键
                String collectionId = bbpmCollectionVoList.get(i).getCollectionId();
                //存款状态
                String depositStatus = bbpmCollectionVoList.get(i).getDepositStatus();
                String projectId = bbpmCollectionVoList.get(i).getProjectId();
                //收款后工银返回的收款单号
                String receiptNo = bbpmCollectionVoList.get(i).getReceiptNo();

                if(StringUtils.isBlank(receiptNo)){
                    throw new McpException("数据异常,收款单号receiptNo为空");
                }

                bbpmCollectionVo.setChargeCode(receiptNo);
                bbpmCollectionVo.setProjectId(projectId);
                //查询工银接口 重新set值到分页实体、转换
                PageResult<List<BbpmCollectionPageResultVo>> pageResult = listByBill(parentRequest);
                if(pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0){
                    BbpmCollectionPageResultVo bbpmCollectionPageResultVo = pageResult.getRows().get(0);
                    bbpmCollectionPageResultVo.setCollectionId(collectionId);
                    bbpmCollectionPageResultVo.setDepositStatus(depositStatus);
                    resultVoList.add(bbpmCollectionPageResultVo);
                }
            }
        }

        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(resultVoList);
        return resultVoList;
    }


    /**
     * updateCertificateStateById 根据主键更新凭证状态
     *
     * @param id 需要更新凭证状态的id
     * @return com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * <AUTHOR>
     * @date 2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateCertificateStateById(String id) {
        baseMapper.updateCertificateStateById(id);
    }


    @Override
    public BbpmChargeMoneyTotalVo chargeMoneyTotal(BbpmCollectionPageVo vo) {
        if(StringUtils.isNotBlank(vo.getProjectFormats())){
            List<String> projectFormatList = Arrays.stream(vo.getProjectFormats().split(","))
                    .collect(Collectors.toList());
            vo.setProjectFormatList(projectFormatList);
        }

        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setCurrent(Integer.valueOf(vo.getPageNumber()+""));
        //全量查
        vo.setFullPage("true");
//        vo.setFrontFlag("01");
        ParentRequest<BbpmCollectionPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.11.1收款单查询统计接口请求参数json:{}",jsonRequest);

        String responseBody = null;
        if (yecaiFeign){
            responseBody = bfipChargeFeignClient.chargeMoneyTotal(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/receipt/chargeMoneyTotal", parentRequest);
        }
        log.info("调用工银3.11.1收款单查询统计接口返回: {}", responseBody);

        FaceMdMapResultTwo<BbpmChargeMoneyTotalVo> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);
        if (faceMdMapResult != null) {
            if(!("00000").equals(faceMdMapResult.getCode())){
                log.error("调用工银3.11.1收款单查询统计接口失败:"+responseBody);
                throw new McpException("*提示:"+faceMdMapResult.getMessage());
            }else{
//    {"busiCode":"","code":"00000","data":{"preChargeMoneyTotal":735452.0000,"actualChargeMoneyTotal":12896136.4100,"totalChargeMoney":13631588.4100},"message":"查询成功"}
                BbpmChargeMoneyTotalVo bbpmChargeMoneyTotalVo = new BbpmChargeMoneyTotalVo();
                if(faceMdMapResult.getData()!=null){
                   JSONObject jsonObject = JSON.parseObject(String.valueOf(faceMdMapResult.getData()));
                   bbpmChargeMoneyTotalVo = jsonObject.toJavaObject(BbpmChargeMoneyTotalVo.class);
                }else{
                    log.info("3.11.1收款单查询统计接口返回为空");
                }
                return bbpmChargeMoneyTotalVo;
            }
        }else{
            throw new McpException("*3.11.1收款单查询统计接口返回为"+responseBody);
        }
    }

}
