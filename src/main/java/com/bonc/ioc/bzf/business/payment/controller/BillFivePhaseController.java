package com.bonc.ioc.bzf.business.payment.controller;


import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCollectionService;
import com.bonc.ioc.bzf.business.payment.service.IBillFivePhaseService;
import com.bonc.ioc.bzf.business.payment.vo.BillFivePhaseVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRuleResultVo;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 五期合同 前端控制器
 *
 * <AUTHOR>
 * @date 2023-06-28
 * @change 2023-06-28 by gxp for init
 */
@RestController
@RequestMapping("/v2/business/billFivePhaseEntity")
@Api(tags = "五期合同")
@Validated
public class BillFivePhaseController extends McpBaseController {
    @Resource
    private IBbpmBillManagementService baseService;

    @Resource
    private IBillFivePhaseService iBillFivePhaseService;


    /**
     *  selectChargeRuleList 查询计费规则列表接口
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    @GetMapping(value = "/selectChargeRuleList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "gxp")
    @ApiOperation(value = "查询计费规则列表接口", notes = "查询计费规则列表接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ChargeRuleResultVo>> selectChargeRuleList(@RequestParam("serviceType") String serviceType, @RequestParam("chargeSubjectNo") String chargeSubjectNo){
        AppReply<List<ChargeRuleResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBillFivePhaseService.selectChargeRuleList(serviceType,chargeSubjectNo));
        return appReply;
    }


    /**
     * changeContractPersonal 公租房散租五期合同生成账单
     * @param vo 公租房散租五期合同生成账单
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-06-27
     * @change
     * 2023-05-23 by gxp for init
     */
    @PostMapping(value = "/pubLooseRentFivePhase", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "gxp")
    @ApiOperation(value = "公租房散租五期合同生成账单", notes = "公租房散租五期合同生成账单", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply pubLooseRentFivePhase(@ApiParam(value = "公租房散租五期合同生成账单" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BillFivePhaseVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        iBillFivePhaseService.pubLooseRentFivePhase(vo);
        return appReply;
    }

    /**
     * changeContractPersonal 管理协议候审期生成账单
     * @param vo 管理协议候审期生成账单
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-06-27
     * @change
     * 2023-05-23 by gxp for init
     */
    @PostMapping(value = "/agreementPhase", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "gxp")
    @ApiOperation(value = "管理协议候审期生成账单", notes = "管理协议候审期生成账单", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply agreementPhase(@ApiParam(value = "管理协议候审期生成账单" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BillFivePhaseVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        iBillFivePhaseService.agreementPhase(vo);
        return appReply;
    }


    /**
     * changeContractPersonal 趸租大合同候审期生成账单
     * @param vo 趸租大合同候审期生成账单
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-06-27
     * @change
     * 2023-05-23 by gxp for init
     */
    @PostMapping(value = "/singlePhase", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "gxp")
    @ApiOperation(value = "趸租大合同候审期生成账单", notes = "趸租大合同候审期生成账单", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply singlePhase(@ApiParam(value = "趸租大合同候审期生成账单" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BillFivePhaseVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        iBillFivePhaseService.singlePhase(vo);
        return appReply;
    }
}

