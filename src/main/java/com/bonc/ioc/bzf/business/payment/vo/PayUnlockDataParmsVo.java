package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付解锁数据参数对象
 *
 * <AUTHOR>
 * @date 2022-03-04
 * @change 2022-03-04 by 姚春雨 for init
 */
@Data
@ApiModel(value = "PayUnlockDataParmsVo对象", description = "支付解锁数据参数对象")
public class PayUnlockDataParmsVo extends McpBaseVo implements Serializable {

    @ApiModelProperty(value = "订单交易流水号")
    private String orderTrxid;

}
