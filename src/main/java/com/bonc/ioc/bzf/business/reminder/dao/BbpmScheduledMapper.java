package com.bonc.ioc.bzf.business.reminder.dao;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmScheduledEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import java.util.List;

/**
 * 缴费定时器 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-08-09
 * @change 2023-08-09 by binghong.tang for init
 */
@Mapper
public interface BbpmScheduledMapper extends McpBaseMapper<BbpmScheduledEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change 2023-08-09 by binghong.tang for init
     */
    List<BbpmScheduledPageResultVo> selectByPageCustom(@Param("vo") BbpmScheduledPageVo vo );
}
