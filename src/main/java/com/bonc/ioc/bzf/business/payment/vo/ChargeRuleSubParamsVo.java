package com.bonc.ioc.bzf.business.payment.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR> 获取计费结果接口 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeRuleSubParamsVo {
	@JSONField(name = "PARAMPRICE")
	@ApiModelProperty(value = "单价")
	private  String PARAMPRICE;
	@JSONField(name = "PARAMAREA")
	@ApiModelProperty(value = "面积")
	private String PARAMAREA;
	@J<PERSON>NField(name = "PARAMFLOOR")
	@ApiModelProperty(value = "楼层")
	private  String PARAMFLOOR;
	@JSONField(name = "PARAMHOUSETYPE")
	@ApiModelProperty(value = "户型")
	private String PARAMHOUSETYPE;
	@JSONField(name = "PARAMORIENTATION")
	@ApiModelProperty(value = "朝向")
	private String PARAMORIENTATION;
	@J<PERSON>NField(name = "PARAMDEPOSIT")
	@ApiModelProperty(value = "押金")
	private String PARAMDEPOSIT;
	@JSONField(name = "PARAMTOTALMONTHS")
	@ApiModelProperty(value = "总月数")
	private String PARAMTOTALMONTHS;

	@JSONField(name = "PERCENTAGE")
	@ApiModelProperty(value = "百分比")
	private String PERCENTAGE;

}
