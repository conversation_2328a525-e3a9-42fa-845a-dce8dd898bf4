package com.bonc.ioc.bzf.business.payment.feign.fallback;


import com.bonc.ioc.bzf.business.payment.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.business.payment.vo.BbctContractManagementVo;
import com.bonc.ioc.common.util.AppReply;

/**
 * @ClassName BbctContractManagementFeignClientFallback
 * @Date 2022-12-12 8:52
 **/
public class BbctContractFeignClientFallback implements BbctContractFeignClient {
    @Override
    public AppReply<BbctContractManagementVo> selectByIdNo(String contractNo, String contractId) {
        return null;
    }

}
