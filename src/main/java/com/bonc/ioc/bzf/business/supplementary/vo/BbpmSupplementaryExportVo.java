package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 追加单导出 vo实体类
 *
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
@ApiModel(value = "追加单导出 vo实体", description = "追加单导出 vo实体")
public class BbpmSupplementaryExportVo extends McpBaseVo implements Serializable {

    /**
     * 追加单号
     */
    @ApiModelProperty(value = "追加单号")
    private String supplementaryCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 社会统一信息用代码
     */
    @ApiModelProperty(value = "社会统一信息用代码")
    private String customerCreditCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;

    /**
     * 租凭期限
     */
    @ApiModelProperty(value = "租凭期限")
    private String contractTimeStr;

    /**
     * 签约类型(01.散租 02.趸租 03.管理协议)
     */
    @ApiModelProperty(value = "签约类型(01.散租 02.趸租 03.管理协议)")
    @McpDictPoint(dictCode = "SIGN_TYPE", overTransCopyTo = "signTypeName")
    private String signType;

    /**
     * 签约类型名称
     */
    @ApiModelProperty(value = "签约类型名称")
    private String signTypeName;

    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date chargeSubjectBeginDate;

    /**
     * 收费科目结束日期
     */
    @ApiModelProperty(value = "收费科目结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date chargeSubjectEndDate;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
    private Integer chargeSubjectPeriod;

    /**
     * 账单周期字符串
     */
    @ApiModelProperty(value = "账单周期字符串")
    private String chargeSubjectPeriodStr;

    /**
     * 费用项目
     */
    @ApiModelProperty(value = "费用项目")
    @McpDictPoint(dictCode = "ALL_EXPENSE_ITEM", overTransCopyTo = "chargeSubjectName")
    private String chargeSubject;

    /**
     * 费用项目名称
     */
    @ApiModelProperty(value = "费用项目名称")
    private String chargeSubjectName;

    /**
     * 账单类型(01.个人 02.企业)
     */
    @ApiModelProperty(value = "账单类型(01.个人 02.企业)")
    @McpDictPoint(dictCode = "BILL_OWNER", overTransCopyTo = "billOwnerName")
    private String billOwner;

    /**
     * 账单类型名称
     */
    @ApiModelProperty(value = "账单类型名称")
    private String billOwnerName;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String roomAddress;

    /**
     * 应缴费日期
     */
    @ApiModelProperty(value = "应缴费日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payableDate;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
    private String payableMoney;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private String taxRate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private String noTaxMoney;

    /**
     * 增值税额
     */
    @ApiModelProperty(value = "增值税额")
    private String rateMoney;

    /**
     * 计费方式(01.循环 02.单次)
     */
    @ApiModelProperty(value = "计费方式(01.循环 02.单次)")
    @McpDictPoint(dictCode = "CYCLIC_OR_SINGLE", overTransCopyTo = "cyclicOrSingleName")
    private String cyclicOrSingle;

    /**
     * 计费方式名称
     */
    @ApiModelProperty(value = "计费方式名称")
    private String cyclicOrSingleName;
}
