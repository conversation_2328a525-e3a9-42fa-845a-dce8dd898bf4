package com.bonc.ioc.bzf.business.payment.feign.fallback;

import com.bonc.ioc.bzf.business.adjust.vo.CalculationParamVo;
import com.bonc.ioc.bzf.business.adjust.vo.CalculationResultVo;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.result.create.*;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmAppendBillVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 描述内容
 *
 * <AUTHOR>
 * @date 2022/2/21 16:55
 * @change 2022/2/21 16:55 by z<PERSON><PERSON><PERSON>@bonc.com.cn for init
 */
public class BfipChargeFeignClientFallback implements BfipChargeFeignClient {


    @Override
    public String listByContract(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String listByBill(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo billCreate(BankRequestVo<BillCreateParamsRequest> bankRequestVo) {
        return null;
    }

    @Override
    public ChargeRespondVo looseRentRelet(BankRequestVo<BillLooseRentReletCreateParamsRequest> bankRequestVo) {
        return null;
    }

    @Override
    public ChargeRespondVo billReletCreate(BankRequestVo<BillReletCreateParamsRequest> bankRequestVo) {
        return null;
    }

    @Override
    public ChargeRespondVo singleRelet(BankRequestVo<BillSingleReletParamsRequest> bankRequestVo) {
        return null;
    }

    @Override
    public ChargeRespondVo billAgreementCreate(BankRequestVo<BillAgreementCreateParamsRequest> bankRequestVo) {
        return null;
    }

    @Override
    public ChargeRespondVo agreementRelet(BankRequestVo<BillAgreementReletCreateParamsRequest> bankRequestVo) {
        return null;
    }

    @Override
    public String updateByContract(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo<List<ChangeTrialPreviewBillsResultVo>> changeTrial(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo billVacancyFeeCreate(BankRequestVo<BillVacancyFeeCreateParamsVo> bankRequestVo) {
        return null;
    }

    @Override
    public ChargeRespondVo billOverdue(BankRequestVo<ChargeArrearsVo> bankRequestVo) {
        return null;
    }

    @Override
    public String whiteList(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String listByPayableDate(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo billBusinessCreate(BankRequestVo<BillBusinessCreateParamsRequest> bankRequestVo) {
        return null;
    }

    @Override
    public String tryServiceFee(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo billBusinessRelet(BankRequestVo<BillBusinessCreateParamsRequest> bankRequestVo) {
        return null;
    }

    @Override
    public String submit(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String chargeMoneyTotal(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo<PreviewBillsResultVo> storageRelet(BankRequestVo<BillStorageReletCreateParamsRequest> bankRequestVo) {
        return null;
    }

    /**
     * 3.39. 商业合同账单预览接口
     *
     * @param parentRequest
     * @return
     */
    @Override
    public ChargeRespondVo<PreviewBillsResultVo> getPreviewBills(ParentRequest<PreviewBillsParamsVo> parentRequest) {
        return null;
    }

    @Override
    public String getPreviewBillsString(ParentRequest<PreviewBillsParamsVo> parentRequest) {
        return null;
    }

    /**
     * 3.58.账单关闭或开启接口
     *
     * @param parentRequest
     * @return
     */
    @Override
    public ChargeRespondVo closeOrOpenBillAndBillBranks(ParentRequest<CloseOrOpenBillAndBillBranksParamVo> parentRequest) {
        return null;
    }

    @Override
    public String pubLooseRentFivePhase(@RequestBody ParentRequest parentRequest) {return null;}

    @Override
    public String agreementPhase(@RequestBody ParentRequest parentRequest) {return null;}

    @Override
    public String singlePhase(@RequestBody ParentRequest parentRequest) {return null;}

    /**
     * 3.4查询账单列表接口
     *
     * @param parentRequest
     * @return
     */
    @Override
    public String queryMonthBillList(ParentRequest parentRequest) {
        return "";
    }

    /**
     * 3.61.应收调整试算接口
     *
     * @param parentRequest
     * @return
     */
    @Override
    public ChargeRespondVo<CalculationResultVo> calculation(ParentRequest<CalculationParamVo> parentRequest) {
        return null;
    }

    @Override
    public String calculationString(ParentRequest<CalculationParamVo> parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo updateBill(ParentRequest<CalculationParamVo> parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo tryAppendBill(@RequestBody ParentRequest<BbpmAppendBillVo> parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo appendBill(@RequestBody ParentRequest<BbpmAppendBillVo> parentRequest) {
        return null;
    }
}
