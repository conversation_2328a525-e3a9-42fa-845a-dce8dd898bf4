package com.bonc.ioc.bzf.business.payment.controller;


import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCollectionService;
import com.bonc.ioc.bzf.business.payment.service.IChangeContractService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同变更 前端控制器
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmChangeContractEntity")
@Api(tags = "合同变更")
@Validated
@RefreshScope
public class BbpmChangeContractController extends McpBaseController {
    @Resource
    private IBbpmBillManagementService baseService;

    @Resource
    private IBbpmCollectionService iBbpmCollectionService;

    @Resource
    private IChangeContractService iChangeContractService;


    /**
     * changeContractPersonal 合同变更
     * @param vo 收款相关信息
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-105-23
     * @change
     * 2023-05-23 by gxp for init
     */
    @PostMapping(value = "/changeContractPersonal", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "gxp")
    @ApiOperation(value = "合同变更", notes = "合同变更", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    //@ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    //@LogPoint(system = "bzf-business-payment",path = "/v2/business/bzf-business-payment/bbpmChangeContractEntity/changeContractPersonal")
    public AppReply changeContractPersonal(@ApiParam(value = "合同变更" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbsChangeRecordVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iChangeContractService.changeContractPersonal(vo));
        return appReply;
    }

    /**
     * 3.59.商业合同变更退款生成付款单接口
     * @param vo
     * @return
     */
    @PostMapping(value = "/businessGeneratePaymentByGX", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "gxp")
    @ApiOperation(value = "3.59.商业合同变更退款生成付款单接口", notes = "3.59.商业合同变更退款生成付款单接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BusinessGeneratePaymentByGXResultVo> businessGeneratePaymentByGX(@RequestBody BusinessGeneratePaymentByGXParamVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iChangeContractService.businessGeneratePaymentByGX(vo));
        return appReply;
    }

}

