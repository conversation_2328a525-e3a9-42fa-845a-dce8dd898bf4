package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.BbpmCashCollectionVoucherEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 现金收缴凭证表v3.0 Mapper 接口
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Mapper
public interface BbpmCashCollectionVoucherMapper extends McpBaseMapper<BbpmCashCollectionVoucherEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     */
    List<BbpmCashCollectionVoucherPageResultVo> selectByPageCustom(@Param("vo") BbpmCashCollectionVoucherPageVo vo );

    /**
     * 根据收款单号查询
     * @param vo
     * @return
     */
    BbpmCashCollectionVoucherVo selectByCollectionNo(@Param("vo") BbpmCashCollectionVoucherVo vo);

}
