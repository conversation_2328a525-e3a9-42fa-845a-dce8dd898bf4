package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> 开户行 返回实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeBankResultVo {

	@ApiModelProperty(value = "开户行编码")
	private  String bankCode;

	@ApiModelProperty(value = "开户行名称")
	private String bankName;

	@ApiModelProperty(value = "开户行支行名称")
	private  String bankBranchName;

	@ApiModelProperty(value = "开户行支行编码")
	private String bankBranchCode;

	@ApiModelProperty(value = "NCC银行类别编码")
	private String nccBankCategoryCode;

	@ApiModelProperty(value = "银行账户")
	private String bankAccountNo;
	@ApiModelProperty(value = "银行户名")
	private String bankAccountName;

	@ApiModelProperty(value = "是否发送鉴权短信0：否1：是")
	private String isSendAuthMessage;


	@ApiModelProperty(value = "代收项目编号")
	private String withholdProjectNo;
	@ApiModelProperty(value = "代收合约编号")
	private String withholdContractNo;
	@ApiModelProperty(value = "业务种类代码")
	private String withholdBusiTypeCode;


}
