package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 支付方式 枚举类
 *
 * <AUTHOR>
 * @since 2023/10/19
 */
public enum PayModeEnum {

    /**
     * 1个月
     */
    COMPANY_PAY("1", "企业付"),

    /**
     * 2个月
     */
    PERSONAGE_PAY("2", "个人付"),

    /**
     * 3个月
     */
    PERCENT_PAY("3", "比例支付");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    PayModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    PayModeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        PayModeEnum[] enums = values();
        for (PayModeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
