package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbsiRuleInfoEntity;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 签约规则信息表 服务类
 *
 * <AUTHOR>
 * @date 2022-11-29
 * @change 2022-11-29 by ly for init
 */
public interface IBbsiRuleInfoService extends IMcpBaseService<BbsiRuleInfoEntity>{
    /**
     *  selectRentRuleList 租金计费规则下拉框
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    List<ChargeRuleResultVo> selectRentRuleList(Map<String, Object> vo);

    /**
     *  selectRentRuleList 租金计费规则下拉框
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    List<ChargeRuleResultVo> selectRentRuleListV2(ChargeRuleParamsVo chargeRuleParamsVo);

    /**
     * @return
     * @version 1.0 获取计费结果接口
     * <AUTHOR>
     * @Date 2022/12/9
     */
    ChargeRespondVo getChargeResult(ChargeResultParamsVo chargeResultParamsVo);


    /**
     *  getBankByPorjectId 根据项目id获取银行
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    List<ChargeBankResultVo> getBankByPorjectId(String projectId,String bankCode,String bankAccountNo);


    /**
     *  getBankByPorjectId 根据项目id获取银行
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    List<ChargeBankResultV2Vo> getBankByPorjectIdV2(String projectId);

    PageResult getListBankBranchCode(BankBranchCodeParamsVo bankBranchCodeParamsVo);
    /**
     * 增值服务费试算接口
     * @param vo
     * @return
     */
    TryServiceFeeResponseVo tryServiceFee(TryServiceFeeMainParamsVo vo);


    /**
     * 3.38 查询税率配置列表
     * @param requestVo
     * @return
     */
    TaxRateResultVo getTaxRateList(TaxRateVo requestVo);

    /**
     * 收款单调整接口
     * @param adjustmentCollectionVo
     * @return
     */
    String chargePaymentAdjustSubmit(AdjustmentCollectionVo adjustmentCollectionVo);
}
