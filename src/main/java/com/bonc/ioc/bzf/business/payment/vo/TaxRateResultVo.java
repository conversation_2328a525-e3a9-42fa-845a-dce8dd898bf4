package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TaxRateResultVo extends McpBasePageVo implements Serializable {
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目业态" +
            "01公租房" +
            "02:保租房" +
            "03:商业" +
            "04:共有产权房" +
            "05:仓储" +
            "06:车位")
    private String yeTai;

    @ApiModelProperty(value = "收费项目" +
            "01:房屋租金" +
            "02:押金" +
            "03:家具家电租金" +
            "04:车位租金" +
            "05:仓库租金" +
            "06:能源费" +
            "07:物业费" +
            "08:违约金" +
            "09:损坏赔偿" +
            "10:水费" +
            "11:电费" +
            "12:燃气费" +
            "13:空置费" +
            "14:增值服务费")
    private String chargeItemId;

    @ApiModelProperty(value = "承租方" +
            "01:所有租户" +
            "02:企业" +
            "03:个人")
    private String tenantry;

    @ApiModelProperty(value = "企业id")
    private String companyId;

    @ApiModelProperty(value = "个人税率")
    private String personTaxRate;

    @ApiModelProperty(value = "企业税率")
    private String companyTaxRate;
}
