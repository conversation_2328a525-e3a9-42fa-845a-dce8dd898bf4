package com.bonc.ioc.bzf.business.payment.result.create;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * PreferentRules	优惠规则列表
 */
@Data
public class PreferentRules implements Serializable {

    @ApiModelProperty(value = "优惠方式")
    private String preferentialType;	//优惠方式 String 否 2 01-免租（目前只有免租） 02-折扣

    @ApiModelProperty(value = "优惠开始日期")
    private String preferentialBeginDate;	//优惠开始日期 String	否 格式：YYYY-MM-DD

    @ApiModelProperty(value = "优惠结束日期")
    private String preferentialEndDate;	 //优惠结束日期 String 否 格式：YYYY-MM-DD

    @ApiModelProperty(value = "优惠规则ID")
    private Integer preferentRuleId;	//优惠规则ID Int 否 从1开始递增




}
