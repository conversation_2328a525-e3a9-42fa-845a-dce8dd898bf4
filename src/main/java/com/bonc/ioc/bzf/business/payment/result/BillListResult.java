package com.bonc.ioc.bzf.business.payment.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 业财账单列表返回实体
 */
@Data
public class BillListResult {
    private Long billId;//账单ID
    private Date chargeSubjectBeginDate;//收费科目起始日期	Date
    private Date chargeSubjectEndDate;//收费科目终止日期	Date
    private String billStatus;//账单缴费状态(账单列表状态) 01 已缴足额支付 02 已缴部分支付 03 未缴
    private String billCloseStatus	;//账单关闭状态 0 已关闭 1未关闭
    private String billChargeSubject;//账单对应的收费科目 01 房屋租金02 押金03 家具家电租金04 车位租金05 仓库租金06 能源费07 物业费08 违约金
    private Integer chargeSubjectPeriod;//收费科目当期期次 从1开始递增
    private BigDecimal shouldPayAmount;//应缴金额
    private BigDecimal payedAmount;//实缴金额	BigDecimal
    private BigDecimal replacePayAmount;//待缴金额	BigDecimal
    private BigDecimal prepaymentAmount;//预收金额	BigDecimal
    private BigDecimal changeShouldPayAmount;//调整后应缴金额
    private BigDecimal deductAmount;//抵扣金额	BigDecimal
    private BigDecimal afterDeductShouldPayAmount;//抵扣后待缴金额
    private String billPayStatus;//账单支付状态	String 01 待支付 02 支付中03 部分支付04 支付完成（足额支付）  05 支付失败
    private Date chargeTime;//收费时间	Time
    private String payChannel;//支付方式	01 银行卡代扣02 pos机03 app支付04 现金支付05 对公转账
    private String contractCode;//合同ID	String
    private String tenantCode;//租户ID	String
    private String accountingMonth;


}
