package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value="AdjustmentFileVo对象", description="收款调整-附件信息")
@Data
public class AdjustmentFileVo extends McpBasePageVo implements Serializable {

    @ApiModelProperty(value = "文件中心附件Id")
    private String fileId;
    @ApiModelProperty(value = "附件名称")
    private String fileName;


}
