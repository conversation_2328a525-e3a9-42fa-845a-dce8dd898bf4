package com.bonc.ioc.bzf.business.reminder.controller;

import com.bonc.ioc.bzf.business.reminder.task.TaskPlan;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import com.bonc.ioc.common.validator.inf.*;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RestController;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmReminderRulesMainService;
import io.swagger.annotations.*;
import com.bonc.ioc.bzf.business.reminder.entity.*;
import java.util.List;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 缴费提醒规则--主表 前端控制器
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/atomic/bbpmReminderRulesMainEntity")
@Api(tags = "缴费提醒规则--主表")
@Validated
public class BbpmReminderRulesMainController extends McpBaseController {
    @Resource
    private IBbpmReminderRulesMainService baseService;
    @Resource
    private TaskPlan taskPlan;


    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmReminderRulesMainPageResultVo>>> selectByPageRecord(BbpmReminderRulesMainPageVo vo){
        AppReply<PageResult<List<BbpmReminderRulesMainPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param rulesId 需要查询的规则编号
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "binghong.tang")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmReminderRulesMainVo> selectByIdRecord(@ApiParam(value = "需要查询的规则编号" ,required = false) @RequestParam(required = false) String rulesId){
        AppReply<BbpmReminderRulesMainVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(rulesId));
        return appReply;
    }

    /**
     * insertRecord 新增
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "新增", notes = "新增全表数据", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "缴费提醒规则--主表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmReminderRulesMainVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
     }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缴费提醒规则--主表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "binghong.tang")
    @ApiOperation(value = "编辑", notes = "根据主键更新表中信息 更新全部信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateByIdRecord(@ApiParam(value = "需要更新的缴费提醒规则--主表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbpmReminderRulesMainVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateByIdRecord(vo);
        return appReply;
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param rulesId 需要删除的规则编号
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "binghong.tang")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "需要删除的规则编号" ,required = true) @RequestParam(required = true) String rulesId){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdRecord(rulesId);
        return appReply;
    }

    /**
     *  enableDeactivate 启用停用
     * @param vo 需要更新的缴费提醒规则--主表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/enableDeactivate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "binghong.tang")
    @ApiOperation(value = "启用停用", notes = "启用停用", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply enableDeactivate(@ApiParam(value = "需要更新的缴费提醒规则--主表" ,required = false) @RequestBody BbpmReminderRulesMainVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.enableDeactivate(vo);
        return appReply;
    }



    @PostMapping(value = "/task", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "binghong.tang")
    @ApiOperation(value = "调用催缴定时任务--自己测试用", notes = "调用催缴定时任务", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply task(@ApiParam(value = "调用催缴定时任务" ,required = false) @RequestBody BbpmReminderRulesMainVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(taskPlan.reminderRulesSendScheduledTasks());
        return appReply;
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @GetMapping(value = "/listByPayableDate", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "3.36查询未缴费账单列表", notes = "3.36查询未缴费账单列表", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<UnpaidBillMainVo>> listByPayableDate(ListByPayableDateVo vo){
        AppReply<List<UnpaidBillMainVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.listByPayableDate(vo));
        return appReply;
    }














    /**
     * insertBatchRecord 批量新增
     * @param voList 需要新增的记录 列表
     * @return  com.bonc.ioc.common.util.AppReply<List<String>> 返回新增后的主键 列表
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "binghong.tang")
    @ApiOperation(value = "批量新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键集合")})
    public AppReply<List<String>> insertBatchRecord(@ApiParam(value = "缴费提醒规则--主表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbpmReminderRulesMainVo> voList){
        AppReply<List<String>> appReply = new AppReply<List<String>>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertBatchRecord(voList));
        return appReply;
     }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rulesIdList 需要删除的规则编号集合
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/batch/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "binghong.tang")
    @ApiOperation(value = "根据主键删除-批量", notes = "根据主键批量删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdsRecord(@ApiParam(value = "需要删除的规则编号" ,required = false) @RequestBody List<String> rulesIdList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdsRecord(rulesIdList);
        return appReply;
     }


    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缴费提醒规则--主表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/batch/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "binghong.tang")
    @ApiOperation(value = "根据主键更新-批量", notes = "根据主键批量更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateBatchByIdRecord(@ApiParam(value = "需要更新的缴费提醒规则--主表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) List<BbpmReminderRulesMainVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缴费提醒规则--主表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "binghong.tang")
    @ApiOperation(value = "根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveByIdRecord(@ApiParam(value = "需要更新或新增的缴费提醒规则--主表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmReminderRulesMainVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveByIdRecord(vo);
        return appReply;
     }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缴费提醒规则--主表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @PostMapping(value = "/batch/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "binghong.tang")
    @ApiOperation(value = "根据主键更新或新增-批量", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveBatchByIdRecord(@ApiParam(value = "需要更新或新增的缴费提醒规则--主表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbpmReminderRulesMainVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveBatchByIdRecord(voList);
        return appReply;
     }





}

