package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付解锁结果数据对象
 *
 * <AUTHOR>
 * @date 2022-03-04
 * @change 2022-03-04 by 姚春雨 for init
 */
@Data
@ApiModel(value = "PayUnlockResultDataVo对象", description = "支付解锁结果数据对象")
public class PayUnlockResultDataVo extends McpBaseVo implements Serializable {

    @ApiModelProperty(value = "订单交易流水号")
    private String orderTrxid;

    @ApiModelProperty(value = "解锁是否成功")
    private String success;

    @ApiModelProperty(value = "失败原因")
    private String errorMsg;

}
