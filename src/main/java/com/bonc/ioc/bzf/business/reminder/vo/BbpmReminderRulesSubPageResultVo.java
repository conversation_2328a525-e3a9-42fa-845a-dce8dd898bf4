package com.bonc.ioc.bzf.business.reminder.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 缴费提醒规则--子表 实体类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@ApiModel(value="BbpmReminderRulesSubPageResultVo对象", description="缴费提醒规则--子表")
public class BbpmReminderRulesSubPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 唯一标识符
     */
    @ApiModelProperty(value = "唯一标识符")
    @NotBlank(message = "唯一标识符不能为空",groups = {UpdateValidatorGroup.class})
                                  private String rulesSubId;

    /**
     * 规则编号
     */
    @ApiModelProperty(value = "规则编号")
    @NotBlank(message = "规则编号不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String rulesId;

    /**
     * 账单应缴日期前后:01前,02后
     */
    @ApiModelProperty(value = "账单应缴日期前后:01前,02后")
                            private String billDueAround;

    /**
     * 账单应缴日期天数
     */
    @ApiModelProperty(value = "账单应缴日期天数")
                            private String billDueDays;

    /**
     * 短信消息模板id
     */
    @ApiModelProperty(value = "短信消息模板id")
                            private String textMessageTemplateId;

    /**
     * 站内消息模板id
     */
    @ApiModelProperty(value = "站内消息模板id")
                            private String stationMessageTemplateId;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;
    @ApiModelProperty(value = "顺序号")
    private Integer seqNum;
    @ApiModelProperty(value = "催缴原因(类型)")
    private String collectionReason;

    public String getCollectionReason() {
        return collectionReason;
    }

    public void setCollectionReason(String collectionReason) {
        this.collectionReason = collectionReason;
    }

    public Integer getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(Integer seqNum) {
        this.seqNum = seqNum;
    }
    /**
     * @return 唯一标识符
     */
    public String getRulesSubId() {
        return rulesSubId;
    }

    public void setRulesSubId(String rulesSubId) {
        this.rulesSubId = rulesSubId;
    }

    /**
     * @return 规则编号
     */
    public String getRulesId() {
        return rulesId;
    }

    public void setRulesId(String rulesId) {
        this.rulesId = rulesId;
    }

    /**
     * @return 账单应缴日期前后:01前,02后
     */
    public String getBillDueAround() {
        return billDueAround;
    }

    public void setBillDueAround(String billDueAround) {
        this.billDueAround = billDueAround;
    }

    /**
     * @return 账单应缴日期天数
     */
    public String getBillDueDays() {
        return billDueDays;
    }

    public void setBillDueDays(String billDueDays) {
        this.billDueDays = billDueDays;
    }

    /**
     * @return 短信消息模板id
     */
    public String getTextMessageTemplateId() {
        return textMessageTemplateId;
    }

    public void setTextMessageTemplateId(String textMessageTemplateId) {
        this.textMessageTemplateId = textMessageTemplateId;
    }

    /**
     * @return 站内消息模板id
     */
    public String getStationMessageTemplateId() {
        return stationMessageTemplateId;
    }

    public void setStationMessageTemplateId(String stationMessageTemplateId) {
        this.stationMessageTemplateId = stationMessageTemplateId;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmReminderRulesSubPageResultVo{" +
            "rulesSubId=" + rulesSubId +
            ", rulesId=" + rulesId +
            ", billDueAround=" + billDueAround +
            ", billDueDays=" + billDueDays +
            ", textMessageTemplateId=" + textMessageTemplateId +
            ", stationMessageTemplateId=" + stationMessageTemplateId +
            ", delFlag=" + delFlag +
        "}";
    }
}
