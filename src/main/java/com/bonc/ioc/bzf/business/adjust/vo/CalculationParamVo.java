package com.bonc.ioc.bzf.business.adjust.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="应收调整工银实体", description="应收调整")
public class CalculationParamVo implements java.io.Serializable{

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "应收调整传14（试算时可不传）")
    private String changeType;

    @ApiModelProperty(value = "应收调整DTO")
    private ChargeableAdjustDTO chargeableAdjustDTO;

}
