package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.BbpmWithholdRecordEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 报盘记录查询 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-02-23
 * @change 2024-02-23 by binghong.tang for init
 */
@Mapper
public interface BbpmWithholdRecordMapper extends McpBaseMapper<BbpmWithholdRecordEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change 2024-02-23 by binghong.tang for init
     */
    List<BbpmWithholdRecordPageResultVo> selectByPageCustom(@Param("vo") BbpmWithholdRecordPageVo vo );
}
