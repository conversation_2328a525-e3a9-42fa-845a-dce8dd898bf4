package com.bonc.ioc.bzf.business.payment.controller;


import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCollectionService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 账单管理(来源业财)v3.0 前端控制器
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmBillManagementEntity")
@Api(tags = "账单管理(来源业财)v3.0")
@Validated
@RefreshScope
public class BbpmBillManagementBusinessController extends McpBaseController {
    @Resource
    private IBbpmBillManagementService baseService;

    @Resource
    private IBbpmCollectionService iBbpmCollectionService;



    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "账单管理--分页查询", notes = "账单管理--分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> selectByPageRecord(BbpmBillManagementPageVo vo){
        AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }

    /**
     * 手动报盘
     * @param vo
     * @return
     */
    @PostMapping(value = "/manualOffer", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "账单管理--手动报盘", notes = "账单管理--手动报盘", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bzf-business-payment/bbpmBillManagementEntity/manualOffer")
    public AppReply<String> manualOffer(@RequestBody BbpmBillManagementManualVo vo){
        return baseService.manualOffer(vo);
    }

    /**
     * 账单管理--查询全部
     * @param vo
     * @return
     */
    @GetMapping(value = "/getBillList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "账单管理--查询全部", notes = "账单管理--查询全部", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmBillManagementPageResultVo>> getBillList(BbpmBillManagementPageVo vo){
        AppReply<List<BbpmBillManagementPageResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getBillList(vo));
        return appReply;
    }

    /**
     * selectByPageRecord 根据合同编号查询我的账单
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectMyBillList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "根据合同编号查询我的账单(不分页)", notes = "根据合同编号查询我的账单(不分页)", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmBillManagementPageResultVo>> selectMyBillList(BbpmBillManagementVo vo){
        AppReply<List<BbpmBillManagementPageResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectMyBillList(vo));
        return appReply;
    }


    /**
     * insertCollectionRecord 单个收款
     * @param vo 收款相关信息
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/insertCollectionRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "binghong.tang")
    @ApiOperation(value = "单个收款", notes = "新增收款信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bzf-business-payment/bbpmBillManagementEntity/insertCollectionRecord")
    public AppReply<String> insertCollectionRecord(@ApiParam(value = "收款表(部分来源业财)v3.0" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmCollectionVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbpmCollectionService.insertRecordAll(vo));
        return appReply;
    }

    /**
     * insertCollectionBatchRecord 批量收款
     * @param vo 需要新增的记录 列表
     * @return  com.bonc.ioc.common.util.AppReply<List<String>> 返回新增后的主键 列表
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/insertCollectionBatchRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 12, author = "binghong.tang")
    @ApiOperation(value = "批量收款", notes = "新增收款数据", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键集合")})
    @LogPoint(system = "bzf-business-payment",path = "/v2/business/bzf-business-payment/bbpmBillManagementEntity/insertCollectionBatchRecord")
    public AppReply<String> insertCollectionBatchRecord(@ApiParam(value = "收款表(部分来源业财)v3.0" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmCollectionBatchVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(iBbpmCollectionService.insertCollectionBatchRecordAll(vo));
        return appReply;
    }



    /**
     * selectCollectionByBillNoList 根据收款单code查询关联的账单
     * @param chargeCode 收款单code
     * @return  com.bonc.ioc.common.util.AppReply   账单信息
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectCollectionByBillNoList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 14, author = "binghong.tang")
    @ApiOperation(value = "根据收款单code查询关联的账单", notes = "根据收款单code查询关联的账单", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmBillManagementPageResultVo>> selectCollectionByBillNoList(@ApiParam(value = "收款单code" ,required = false) @RequestParam(required = true) String chargeCode,@ApiParam(value = "项目id" ,required = false) @RequestParam(required = true) String projectId,@ApiParam(value = "个人企业标识" ,required = false) @RequestParam(required = true) String chargeOwner){
        AppReply<List<BbpmBillManagementPageResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectBillAndCollectionList(chargeCode,projectId,chargeOwner));
        return appReply;
    }


    /**
     * transferInfo 3.25. 查询收款凭证接口
     * @return  com.bonc.ioc.common.util.AppReply   账单信息
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/transferInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 14, author = "binghong.tang")
    @ApiOperation(value = "查询线下转账信息", notes = "查询线下转账信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<CorporateCollectionVo> transferInfo(CorporateSearchVo vo){
        AppReply<CorporateCollectionVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.transferInfo(vo));
        return appReply;
    }


    /**
     * 3.58.账单关闭或开启接口
     * @param vo
     * @return
     */
    @PostMapping(value = "/closeOrOpenBillAndBillBranks", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 12, author = "binghong.tang")
    @ApiOperation(value = "3.58.账单关闭或开启接口", notes = "3.58.账单关闭或开启接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键集合")})
    public AppReply closeOrOpenBillAndBillBranks(@RequestBody CloseOrOpenBillAndBillBranksParamVo vo){
        AppReply<CorporateCollectionVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.closeOrOpenBillAndBillBranks(vo);
        return appReply;
    }

    /**
     * 3.60.查询月度账单接口
     * @param vo
     * @return
     */
    @GetMapping(value = "/queryMonthBillList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "3.60.查询月度账单接口", notes = "账单管理--分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> queryMonthBillList(BbpmMonthBillManagementPageVo vo){
        AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.queryMonthBillList(vo));
        return appReply;
    }
}

