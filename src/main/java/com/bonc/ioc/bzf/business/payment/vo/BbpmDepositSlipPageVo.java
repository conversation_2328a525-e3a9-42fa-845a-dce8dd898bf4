package com.bonc.ioc.bzf.business.payment.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.List;

import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 现金盘点存款单表v3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@ApiModel(value="BbpmDepositSlipPageVo对象", description="现金盘点存款单表v3.0")
public class BbpmDepositSlipPageVo extends McpBasePageVo implements Serializable{


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String depositId;

    /**
     * 存款单号
     */
    @ApiModelProperty(value = "存款单号")
                            private String depositSlipNo;

    /**
     * 存款单号
     */
    @ApiModelProperty(value = "存款单号--后台使用")
    private String depositSlipNoV2;

    /**
     * 存款单号
     */
    @ApiModelProperty(value = "存款单号List")
    private List<String> depositSlipNoList;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
                            private String depositStatus;

    /**
     * 存款金额
     */
    @ApiModelProperty(value = "存款金额")
                            private BigDecimal paidInAmount;

    /**
     * 收款单数量
     */
    @ApiModelProperty(value = "收款单数量")
                            private String collectionDocQty;

    /**
     * 交易单号
     */
    @ApiModelProperty(value = "交易单号")
                            private String transactionNo;

    /**
     * 存款时间
     */
    @ApiModelProperty(value = "存款时间")
                    private String depositTime;

    /**
     * 存款人
     */
    @ApiModelProperty(value = "存款人")
                            private String depositor;

    /**
     * 凭证上传状态--无用
     */
    @ApiModelProperty(value = "凭证上传状态--无用")
                            private String voucherUploadStatus;

    /**
     * 银行凭证
     */
    @ApiModelProperty(value = "银行凭证")
                            private String bankVoucher;

    /**
     * 上传人id
     */
    @ApiModelProperty(value = "上传人id")
                            private String uploaderId;

    /**
     * 上传人name
     */
    @ApiModelProperty(value = "上传人name")
                            private String uploaderName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date uploaderDate;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;
    /**
     * 业财返回的收款单ID
     */
    @ApiModelProperty(value = "业财返回的收款单ID")
    private String receiptNo;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "收款银行账户")
    private String receiptBankAcct;
    @ApiModelProperty(value = "实际存款金额")
    private BigDecimal totalCashAmount;

    @ApiModelProperty(value = "银行名称")
    private String bankName;
    @ApiModelProperty(value = "银行支行名称")
    private String branchName;
    @ApiModelProperty(value = "银行账户")
    private String bankAccountNo;
    @ApiModelProperty(value = "银行户名")
    private String bankAccountName;

    @ApiModelProperty(value = "存款时间开始日期")
    private String startDate;
    @ApiModelProperty(value = "存款时间结束日期")
    private String endDate;

    @ApiModelProperty(value = "回退原因")
    private String backReason;
    @ApiModelProperty(value = "操作人id")
    private String backOperator	;
    @ApiModelProperty(value = "回退时间")
    private String backTime	;

    @ApiModelProperty(value = "回退状态")
    private String backStatus;

    @ApiModelProperty(value ="是否是多项目认款01是02否")
    @McpDictPoint(dictCode = "PAYMENT_YES_NO", overTransCopyTo = "multiProjectName")
    private String  multiProject;

    @ApiModelProperty(value ="是否是多项目认款名称")
    private String  multiProjectName;

    public String getMultiProjectName() {
        return multiProjectName;
    }

    public void setMultiProjectName(String multiProjectName) {
        this.multiProjectName = multiProjectName;
    }

    public String getMultiProject() {
        return multiProject;
    }

    public void setMultiProject(String multiProject) {
        this.multiProject = multiProject;
    }
    public String getBackStatus() {
        return backStatus;
    }

    public void setBackStatus(String backStatus) {
        this.backStatus = backStatus;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getBackReason() {
        return backReason;
    }

    public void setBackReason(String backReason) {
        this.backReason = backReason;
    }

    public String getBackOperator() {
        return backOperator;
    }

    public void setBackOperator(String backOperator) {
        this.backOperator = backOperator;
    }

    public String getBackTime() {
        return backTime;
    }

    public void setBackTime(String backTime) {
        this.backTime = backTime;
    }

    public String getDepositSlipNoV2() {
        return depositSlipNoV2;
    }

    public void setDepositSlipNoV2(String depositSlipNoV2) {
        this.depositSlipNoV2 = depositSlipNoV2;
    }

    public List<String> getDepositSlipNoList() {
        return depositSlipNoList;
    }

    public void setDepositSlipNoList(List<String> depositSlipNoList) {
        this.depositSlipNoList = depositSlipNoList;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getReceiptBankAcct() {
        return receiptBankAcct;
    }

    public void setReceiptBankAcct(String receiptBankAcct) {
        this.receiptBankAcct = receiptBankAcct;
    }

    public BigDecimal getTotalCashAmount() {
        return totalCashAmount;
    }

    public void setTotalCashAmount(BigDecimal totalCashAmount) {
        this.totalCashAmount = totalCashAmount;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }
    /**
     * @return 主键id
     */
    public String getDepositId() {
        return depositId;
    }

    public void setDepositId(String depositId) {
        this.depositId = depositId;
    }

    /**
     * @return 存款单号
     */
    public String getDepositSlipNo() {
        return depositSlipNo;
    }

    public void setDepositSlipNo(String depositSlipNo) {
        this.depositSlipNo = depositSlipNo;
    }

    /**
     * @return 状态
     */
    public String getDepositStatus() {
        return depositStatus;
    }

    public void setDepositStatus(String depositStatus) {
        this.depositStatus = depositStatus;
    }

    /**
     * @return 存款金额
     */
    public BigDecimal getPaidInAmount() {
        return paidInAmount;
    }

    public void setPaidInAmount(BigDecimal paidInAmount) {
        this.paidInAmount = paidInAmount;
    }

    /**
     * @return 收款单数量
     */
    public String getCollectionDocQty() {
        return collectionDocQty;
    }

    public void setCollectionDocQty(String collectionDocQty) {
        this.collectionDocQty = collectionDocQty;
    }

    /**
     * @return 交易单号
     */
    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getDepositTime() {
        return depositTime;
    }

    public void setDepositTime(String depositTime) {
        this.depositTime = depositTime;
    }

    /**
     * @return 存款人
     */
    public String getDepositor() {
        return depositor;
    }

    public void setDepositor(String depositor) {
        this.depositor = depositor;
    }

    /**
     * @return 凭证上传状态--无用
     */
    public String getVoucherUploadStatus() {
        return voucherUploadStatus;
    }

    public void setVoucherUploadStatus(String voucherUploadStatus) {
        this.voucherUploadStatus = voucherUploadStatus;
    }

    /**
     * @return 银行凭证
     */
    public String getBankVoucher() {
        return bankVoucher;
    }

    public void setBankVoucher(String bankVoucher) {
        this.bankVoucher = bankVoucher;
    }

    /**
     * @return 上传人id
     */
    public String getUploaderId() {
        return uploaderId;
    }

    public void setUploaderId(String uploaderId) {
        this.uploaderId = uploaderId;
    }

    /**
     * @return 上传人name
     */
    public String getUploaderName() {
        return uploaderName;
    }

    public void setUploaderName(String uploaderName) {
        this.uploaderName = uploaderName;
    }

    /**
     * @return 上传时间
     */
    public Date getUploaderDate(){
        if(uploaderDate!=null){
            return (Date)uploaderDate.clone();
        }else{
            return null;
        }
    }

    public void setUploaderDate(Date uploaderDate) {
        if(uploaderDate==null){
            this.uploaderDate = null;
        }else{
            this.uploaderDate = (Date)uploaderDate.clone();
        }
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmDepositSlipPageVo{" +
            "depositId=" + depositId +
            ", depositSlipNo=" + depositSlipNo +
            ", depositStatus=" + depositStatus +
            ", paidInAmount=" + paidInAmount +
            ", collectionDocQty=" + collectionDocQty +
            ", transactionNo=" + transactionNo +
            ", depositTime=" + depositTime +
            ", depositor=" + depositor +
            ", voucherUploadStatus=" + voucherUploadStatus +
            ", bankVoucher=" + bankVoucher +
            ", uploaderId=" + uploaderId +
            ", uploaderName=" + uploaderName +
            ", uploaderDate=" + uploaderDate +
            ", delFlag=" + delFlag +
        "}";
    }
}
