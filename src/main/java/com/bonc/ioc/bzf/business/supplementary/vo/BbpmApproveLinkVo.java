package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 审批调用链 vo实体类
 *
 * <AUTHOR>
 * @since 2023/6/27
 */
@Data
@ApiModel(value = "审批调用链 vo实体", description = "审批调用链 vo实体")
public class BbpmApproveLinkVo extends McpBaseVo implements Serializable {

    /**
     * 审批信息(旧)
     */
    @ApiModelProperty(value = "审批信息(旧)")
    private BbpmApproveInfoVo oldApproveInfoVo;

    /**
     * 审批信息(新)
     */
    @ApiModelProperty(value = "审批信息(新)")
    private BbpmApproveInfoVo newApproveInfoVo;
}
