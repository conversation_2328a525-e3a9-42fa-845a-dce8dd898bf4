package com.bonc.ioc.bzf.business.reminder.service.impl;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendSubLogEntity;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmMessageSendSubLogMapper;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmMessageSendSubLogService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 催缴规则消息发送日志--子表  服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmMessageSendSubLogServiceImpl extends McpBaseServiceImpl<BbpmMessageSendSubLogEntity> implements IBbpmMessageSendSubLogService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmMessageSendSubLogMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmMessageSendSubLogService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmMessageSendSubLogVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmMessageSendSubLogEntity entity = new BbpmMessageSendSubLogEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setNoticeId(null);
        if(!baseService.insert(entity)) {
            log.error("催缴规则消息发送日志--子表 新增失败:" + entity.toString());
            throw new McpException("催缴规则消息发送日志--子表 新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getNoticeId(),1)) {
                log.error("催缴规则消息发送日志--子表 新增后保存历史失败:" + entity.toString());
                throw new McpException("催缴规则消息发送日志--子表 新增后保存历史失败");
            }

            log.debug("催缴规则消息发送日志--子表 新增成功:"+entity.getNoticeId());
            return entity.getNoticeId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmMessageSendSubLogVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmMessageSendSubLogEntity> entityList = new ArrayList<>();
        for (BbpmMessageSendSubLogVo item:voList) {
            BbpmMessageSendSubLogEntity entity = new BbpmMessageSendSubLogEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmMessageSendSubLogEntity item:entityList){
            item.setNoticeId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("催缴规则消息发送日志--子表 新增失败");
            throw new McpException("催缴规则消息发送日志--子表 新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmMessageSendSubLogEntity::getNoticeId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("催缴规则消息发送日志--子表 批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("催缴规则消息发送日志--子表 批量新增后保存历史失败");
            }

            log.debug("催缴规则消息发送日志--子表 新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param noticeId 需要删除的通知ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String noticeId) {
        if(!StringUtils.isEmpty(noticeId)) {
            if(!baseService.saveOperationHisById(noticeId,3)) {
                log.error("催缴规则消息发送日志--子表 删除后保存历史失败:" + noticeId);
                throw new McpException("催缴规则消息发送日志--子表 删除后保存历史失败");
            }

            if(!baseService.removeById(noticeId)) {
                log.error("催缴规则消息发送日志--子表 删除失败");
                throw new McpException("催缴规则消息发送日志--子表 删除失败"+noticeId);
            }
        } else {
            throw new McpException("催缴规则消息发送日志--子表 删除失败通知ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param noticeIdList 需要删除的通知ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> noticeIdList) {
        if(!CollectionUtils.isEmpty(noticeIdList)) {
            int oldSize = noticeIdList.size();
            noticeIdList = noticeIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(noticeIdList) || oldSize != noticeIdList.size()) {
                throw new McpException("催缴规则消息发送日志--子表 批量删除失败 存在主键id为空的记录"+StringUtils.join(noticeIdList));
            }

            if(!baseService.saveOperationHisByIds(noticeIdList,3)) {
                log.error("催缴规则消息发送日志--子表 批量删除后保存历史失败:" + StringUtils.join(noticeIdList));
                throw new McpException("催缴规则消息发送日志--子表 批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(noticeIdList)) {
                log.error("催缴规则消息发送日志--子表 批量删除失败");
                throw new McpException("催缴规则消息发送日志--子表 批量删除失败"+StringUtils.join(noticeIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmMessageSendSubLogVo vo) {
        if(vo != null) {
            BbpmMessageSendSubLogEntity entity = new BbpmMessageSendSubLogEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getNoticeId())) {
                throw new McpException("催缴规则消息发送日志--子表 更新失败传入通知ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("催缴规则消息发送日志--子表 更新失败");
                throw new McpException("催缴规则消息发送日志--子表 更新失败"+entity.getNoticeId());
            } else {
                if(!baseService.saveOperationHisById(entity.getNoticeId(),2)) {
                    log.error("催缴规则消息发送日志--子表 更新后保存历史失败:" + entity.getNoticeId());
                    throw new McpException("催缴规则消息发送日志--子表 更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("催缴规则消息发送日志--子表 更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmMessageSendSubLogVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmMessageSendSubLogEntity> entityList = new ArrayList<>();

            for (BbpmMessageSendSubLogVo item:voList){
                BbpmMessageSendSubLogEntity entity = new BbpmMessageSendSubLogEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getNoticeId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("催缴规则消息发送日志--子表 批量更新失败 存在通知ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("催缴规则消息发送日志--子表 批量更新失败");
                throw new McpException("催缴规则消息发送日志--子表 批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getNoticeId())).map(BbpmMessageSendSubLogEntity::getNoticeId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("催缴规则消息发送日志--子表 批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("催缴规则消息发送日志--子表 批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmMessageSendSubLogVo vo) {
        if(vo != null) {
            BbpmMessageSendSubLogEntity entity = new BbpmMessageSendSubLogEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("催缴规则消息发送日志--子表 保存失败");
                throw new McpException("催缴规则消息发送日志--子表 保存失败"+entity.getNoticeId());
            } else {
                if(!baseService.saveOperationHisById(entity.getNoticeId(),4)) {
                    log.error("催缴规则消息发送日志--子表 保存后保存历史失败:" + entity.getNoticeId());
                    throw new McpException("催缴规则消息发送日志--子表 保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("催缴规则消息发送日志--子表 保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmMessageSendSubLogVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmMessageSendSubLogEntity> entityList = new ArrayList<>();

            for (BbpmMessageSendSubLogVo item:voList){
                BbpmMessageSendSubLogEntity entity = new BbpmMessageSendSubLogEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("催缴规则消息发送日志--子表 批量保存失败");
                throw new McpException("催缴规则消息发送日志--子表 批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getNoticeId())).map(BbpmMessageSendSubLogEntity::getNoticeId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("催缴规则消息发送日志--子表 批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("催缴规则消息发送日志--子表 批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param noticeId 需要查询的通知ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmMessageSendSubLogVo selectByIdRecord(String noticeId) {
        BbpmMessageSendSubLogVo vo = new BbpmMessageSendSubLogVo();

        if(!StringUtils.isEmpty(noticeId)) {
            BbpmMessageSendSubLogEntity entity = baseService.selectById(noticeId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmMessageSendSubLogPageResultVo>> selectByPageRecord(BbpmMessageSendSubLogPageVo vo) {
        List<BbpmMessageSendSubLogPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
