package com.bonc.ioc.bzf.business.reminder.dao;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmCollectionWhitelistEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import java.util.List;

/**
 * 催缴白名单表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-04-24
 * @change 2024-04-24 by binghong.tang for init
 */
@Mapper
public interface BbpmCollectionWhitelistMapper extends McpBaseMapper<BbpmCollectionWhitelistEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change 2024-04-24 by binghong.tang for init
     */
    List<BbpmCollectionWhitelistPageResultVo> selectByPageCustom(@Param("vo") BbpmCollectionWhitelistPageVo vo );

    List<BbpmCollectionWhitelistVo> selectCustomerOrProject(@Param("vo") BbpmCollectionWhitelistVo vo);

}
