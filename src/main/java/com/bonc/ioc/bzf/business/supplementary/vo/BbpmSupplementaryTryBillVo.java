package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 追加单试算结果 vo实体类
 *
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
@ApiModel(value = "追加单试算结果 vo实体", description = "追加单试算结果 vo实体")
public class BbpmSupplementaryTryBillVo extends McpBaseVo implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 合同bizId
     */
    @ApiModelProperty(value = "合同bizId")
    private String contractBizId;

    /**
     * 承租人名称
     */
    @ApiModelProperty(value = "承租人名称")
    private String tenantName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String orgName;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String houseAddr;

    /**
     * 租金标准
     */
    @ApiModelProperty(value = "租金标准")
    private String rentStandard;

    /**
     * 计租面积
     */
    @ApiModelProperty(value = "计租面积")
    private String rentPrice;

    /**
     * 试算后的账单列表
     */
    @ApiModelProperty(value = "试算后的账单列表")
    private List<BbpmSupplementAppendBillVo> appendBillList;
}
