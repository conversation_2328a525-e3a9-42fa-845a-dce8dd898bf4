package com.bonc.ioc.bzf.business.invoice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceQueryEntity;
import com.bonc.ioc.bzf.business.invoice.dao.BbpmInvoiceQueryMapper;
import com.bonc.ioc.bzf.business.invoice.enums.InvoiceEnums;
import com.bonc.ioc.bzf.business.invoice.service.IBbpmInvoiceQueryService;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.bonc.ioc.bzf.business.invoice.vo.*;
import org.springframework.beans.BeanUtils;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 已开发票查询 服务类实现
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmInvoiceQueryServiceImpl extends McpBaseServiceImpl<BbpmInvoiceQueryEntity> implements IBbpmInvoiceQueryService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmInvoiceQueryMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmInvoiceQueryService baseService;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    /**
     * Bean 字典转化
     */
    @Resource
    private McpDictSession mcpDictSession;


    @Resource
    private IBbpmBillManagementService iBbpmBillManagementService;



    /**
     * selectByIdRecord 根据主键查询
     * @param vo 需要查询的发票请求流水号
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-13
     * @change
     * 2023-05-13 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmInvoiceQueryVo selectByIdRecord(BbpmInvoiceQueryVo vo) {
        if(vo == null){
            return  null;
        }
        String fpqqlsh = vo.getFpqqlsh();
        String projectId = vo.getProjectId();
        String titleType = vo.getTitleType();

        if(StringUtils.isBlank(fpqqlsh)){
            throw new McpException("fpqqlsh为空");
        }
        if(StringUtils.isBlank(projectId)){
            throw new McpException("projectId为空");
        }
        if(StringUtils.isBlank(titleType)){
            throw new McpException("titleType为空");
        }

        BbpmInvoiceQueryPageVo queryPageVo = new BbpmInvoiceQueryPageVo();
        queryPageVo.setFpqqlsh(fpqqlsh);
        queryPageVo.setProjectId(projectId);
        queryPageVo.setTitleType(titleType);

        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        queryPageVo.setSize(1);
        queryPageVo.setCurrent(1);
        ParentRequest<BbpmInvoiceQueryPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(queryPageVo);
        PageResult<List<BbpmInvoiceQueryPageResultVo>> pageResult = queryInvoice(parentRequest,projectId,InvoiceEnums.REQUEST_TYPE_DETAIL.getCode(),titleType);
        if(pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0){
            BbpmInvoiceQueryVo bbpmInvoiceQueryVo = new BbpmInvoiceQueryVo();
            BeanUtils.copyProperties(pageResult.getRows().get(0), bbpmInvoiceQueryVo);
            return bbpmInvoiceQueryVo;
        }
        return null;
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-13
     * @change
     * 2023-05-13 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmInvoiceQueryPageResultVo>> selectByPageRecord(BbpmInvoiceQueryPageVo vo) {
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setCurrent(Integer.valueOf(vo.getPageNumber()+""));
        ParentRequest<BbpmInvoiceQueryPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        String projectId = vo.getProjectId();
        String titleType = vo.getTitleType();

        //请求业财接口
        //重新set值到分页实体、转换
        PageResult<List<BbpmInvoiceQueryPageResultVo>> pageResult = queryInvoice(parentRequest,projectId, InvoiceEnums.REQUEST_TYPE_LIST.getCode(),titleType);

        return pageResult;
    }


    public PageResult<List<BbpmInvoiceQueryPageResultVo>> queryInvoice(ParentRequest parentRequest,String projectId,String type,String titleType){

        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("发票查询接口请求参数json:"+jsonRequest);

        String responseBody = "";
        if (yecaiFeign){
            responseBody = bfipSettlementFeignClient.queryInvoice(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/einvoice/queryInvoice", parentRequest);
        }

        log.debug("调用发票查询接口返回:"+responseBody);

        //重新set值到分页实体、转换
        PageResult<List<BbpmInvoiceQueryPageResultVo>> pageResult = resetCollection(responseBody,projectId,type,titleType);

        return pageResult;
    }


    public PageResult<List<BbpmInvoiceQueryPageResultVo>> resetCollection(String responseBody,String projectId,String type,String titleType){
        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }

        FaceHttpResultTwo<BbpmInvoiceQueryPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);
        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(null);
        }
        List<BbpmInvoiceQueryPageResultVo> invoiceListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(),BbpmInvoiceQueryPageResultVo.class);

        List<BbpmInvoiceQueryPageResultVo> result = new ArrayList<>();

        for(BbpmInvoiceQueryPageResultVo invoice : invoiceListResultList){
//            //收款单数量
//            invoice.setOrderNum(String.valueOf(invoice.getChargePaymentlist().size()));
            String billCodes = invoice.getInvoicedBillCode();
            if(StringUtils.isBlank(billCodes)){
                throw new McpException("invoicedBillCode为空");
            }
            List<String> billCodeList = Arrays.stream(billCodes.split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
            //账单数量
            invoice.setOrderNum(String.valueOf(billCodeList.size()));
            List<BbpmBillManagementPageResultVo> billList = new ArrayList<>();
            //详情
            if(InvoiceEnums.REQUEST_TYPE_DETAIL.getCode().equals(type)){
                //循环查询账单列表
                for(String billId : billCodeList){
                    BbpmBillManagementVo billManagementVo = new BbpmBillManagementVo();
                    billManagementVo.setProjectId(projectId);
                    billManagementVo.setBillId(billId);
                    billManagementVo.setChargeOwner(titleType);
                    BbpmBillManagementPageResultVo resultVo = iBbpmBillManagementService.selectSingleBill(billManagementVo);
                    billList.add(resultVo);
                }
            }
            invoice.setBillList(billList);

            //费用类型翻译
            String expenseType = invoice.getExpenseType();
            String expenseTypeNew = "";
            if(StringUtils.isNotBlank(expenseType)){
                String[] arrays = expenseType.split(",");
                for(String bt : arrays){
                    McpDictEntity mcpDictEntity = mcpDictSession.getMcpDictUtil().getDictInfoByCode("BILLING_YC_BILLCHARGESUBJECT",bt);
                    if(mcpDictEntity != null){
                        expenseTypeNew= expenseTypeNew+mcpDictEntity.getMeaning()+",";
                    }
                }
            }
            invoice.setExpenseTypeName(StringUtils.isBlank(expenseTypeNew)?"":expenseTypeNew.substring(0,expenseTypeNew.length()-1));

            result.add(invoice);
        }
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);

        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(),result);
    }


}
