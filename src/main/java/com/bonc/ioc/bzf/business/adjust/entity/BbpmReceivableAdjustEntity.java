package com.bonc.ioc.bzf.business.adjust.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.List;

import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 应收调整 实体类
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@TableName("bbpm_receivable_adjust")
@ApiModel(value="BbpmReceivableAdjustEntity对象", description="应收调整")
public class BbpmReceivableAdjustEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_ID = "id";
    public static final String FIELD_ADJUST_NO = "adjust_no";
    public static final String FIELD_PRODUCT_ADDRESS = "product_address";
    public static final String FIELD_CUSTOMER_NAME = "customer_name";
    public static final String FIELD_CUSTOMER_ID_TYPE = "customer_id_type";
    public static final String FIELD_CUSTOMER_ID_NUMBER = "customer_id_number";
    public static final String FIELD_COMPANY = "company";
    public static final String FIELD_CUSTOMER_CREDIT_CODE = "customer_credit_code";
    public static final String FIELD_CONTRACT_NO = "contract_no";
    public static final String FIELD_CONTRACT_TYPE = "contract_type";
    public static final String FIELD_CONTRACT_BEGIN_TIME = "contract_begin_time";
    public static final String FIELD_CONTRACT_END_TIME = "contract_end_time";
    public static final String FIELD_PROJECT_ID = "project_id";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_ADJUST_STATUS = "adjust_status";
    public static final String FIELD_REFUND_STATUS = "refund_status";
    public static final String FIELD_FAILURE_REASON = "failure_reason";
    public static final String FIELD_ADJUST_AMOUNT = "adjust_amount";
    public static final String FIELD_ADJUST_FILES = "adjust_files";
    public static final String FIELD_AMOUNT_HANDLE = "amount_handle";
    public static final String FIELD_REFUND_TYPE = "refund_type";
    public static final String FIELD_REFUND_PATH = "refund_path";
    public static final String FIELD_ACCOUNT_HOLDER = "account_holder";
    public static final String FIELD_BANK_CARD = "bank_card";
    public static final String FIELD_OPENING_BANK = "opening_bank";
    public static final String FIELD_OPENING_PROVINCE = "opening_province";
    public static final String FIELD_OPENING_CITY = "opening_city";
    public static final String FIELD_EXT1 = "ext1";
    public static final String FIELD_EXT2 = "ext2";
    public static final String FIELD_EXT3 = "ext3";
    public static final String FIELD_DEL_FLAG = "del_flag";
    public static final String FIELD_CREATE_USER_NAME = "create_user_name";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
                                @TableId(value = "id", type = IdType.ASSIGN_UUID)
                                  private String id;

    /**
     * 调整单编号
     */
    @ApiModelProperty(value = "调整单编号")
                            private String adjustNo;

    /**
     * 产品地址
     */
    @ApiModelProperty(value = "产品地址")
                            private String productAddress;

    /**
     * 承租人姓名
     */
    @ApiModelProperty(value = "承租人姓名")
                            private String customerName;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
                            private String customerIdType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
                            private String customerIdNumber;

    /**
     * 趸租单位
     */
    @ApiModelProperty(value = "趸租单位")
                            private String company;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
                            private String customerCreditCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractNo;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
                            private String contractType;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date contractBeginTime;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date contractEndTime;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 调整单状态
     */
    @ApiModelProperty(value = "调整单状态")
                            private String adjustStatus;

    /**
     * 退款状态
     */
    @ApiModelProperty(value = "退款状态")
                            private String refundStatus;

    /**
     * 退款失败原因
     */
    @ApiModelProperty(value = "退款失败原因")
                            private String failureReason;

    /**
     * 调整金额（元）
     */
    @ApiModelProperty(value = "调整金额（元）")
                            private BigDecimal adjustAmount;

    /**
     * 调整应收依据文件
     */
    @ApiModelProperty(value = "调整应收依据文件")
                            private String adjustFiles;

    /**
     * 应退金额处理类型
     */
    @ApiModelProperty(value = "应退金额处理类型")
                            private String amountHandle;

    /**
     * 退款路径类型
     */
    @ApiModelProperty(value = "退款路径类型")
                            private String refundType;

    /**
     * 退回路径
     */
    @ApiModelProperty(value = "个人退回路径")
                            private String refundPath;

    /**
     * 开户人姓名
     */
    @ApiModelProperty(value = "个人开户人姓名")
                            private String accountHolder;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "个人银行卡号")
                            private String bankCard;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "个人开户行")
                            private String openingBank;

    /**
     * 开户行所在省
     */
    @ApiModelProperty(value = "个人开户行所在省")
                            private String openingProvince;

    /**
     * 开户行所在市
     */
    @ApiModelProperty(value = "个人开户行所在市")
                            private String openingCity;

    /**
     * 退回路径
     */
    @ApiModelProperty(value = "企业退回路径")
    private String qyRefundPath;

    /**
     * 开户人姓名
     */
    @ApiModelProperty(value = "企业开户人姓名")
    private String qyAccountHolder;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "企业银行卡号")
    private String qyBankCard;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "企业开户行")
    private String qyOpeningBank;

    /**
     * 开户行所在省
     */
    @ApiModelProperty(value = "企业开户行所在省")
    private String qyOpeningProvince;

    /**
     * 开户行所在市
     */
    @ApiModelProperty(value = "企业开户行所在市")
    private String qyOpeningCity;

    /**
     * 备用字段1
     */
    @ApiModelProperty(value = "备用字段1")
                            private String ext1;

    /**
     * 备用字段2
     */
    @ApiModelProperty(value = "备用字段2")
                            private String ext2;

    /**
     * 备用字段3
     */
    @ApiModelProperty(value = "备用字段3")
                            private String ext3;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
                            private String createUserName;

    @ApiModelProperty(value = "个人修改银行原因")
    private String changeBankReason;

    @ApiModelProperty(value = "个人开户人身份文件")
    private String bankFiles;

    @ApiModelProperty(value = "个人退款证件号码")
    private String bankIdNumber;

    @ApiModelProperty(value = "企业修改银行原因")
    private String qyChangeBankReason;

    @ApiModelProperty(value = "企业开户人身份文件")
    private String qyBankFiles;

    @ApiModelProperty(value = "企业退款证件号码")
    private String qyBankIdNumber;

    @ApiModelProperty(value = "个人开户行网点行号")
    private String bankBranchCode;

    @ApiModelProperty(value = "企业开户行网点行号")
    private String qyBankBranchCode;

    @ApiModelProperty(value = "企业退款原因")
    private String qyFailureReason;

    @ApiModelProperty(value = "个人开户行名称")
    private String openingBankName;
    @ApiModelProperty(value = "个人开户行网点名称")
    private String bankBranchName;
    @ApiModelProperty(value = "个人开户行所在省名称")
    private String openingProvinceName;
    @ApiModelProperty(value = "个人开户行所在市名称")
    private String openingCityName;
    @ApiModelProperty(value = "企业开户行名称")
    private String qyOpeningBankName;
    @ApiModelProperty(value = "企业开户行网点名称")
    private String qyBankBranchName;
    @ApiModelProperty(value = "企业开户行所在省名称")
    private String qyOpeningProvinceName;
    @ApiModelProperty(value = "企业开户行所在市名称")
    private String qyOpeningCityName;

    @ApiModelProperty(value = "工银试算结果")
    private String calculationResults;

    @ApiModelProperty(value = "补充协议文件")
    private String agreementFile;

    @ApiModelProperty(value = "变更id")
    private String changeId;

    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    public String getAgreementFile() {
        return agreementFile;
    }

    public void setAgreementFile(String agreementFile) {
        this.agreementFile = agreementFile;
    }

    public String getCalculationResults() {
        return calculationResults;
    }

    public void setCalculationResults(String calculationResults) {
        this.calculationResults = calculationResults;
    }

    public String getOpeningBankName() {
        return openingBankName;
    }

    public void setOpeningBankName(String openingBankName) {
        this.openingBankName = openingBankName;
    }

    public String getBankBranchName() {
        return bankBranchName;
    }

    public void setBankBranchName(String bankBranchName) {
        this.bankBranchName = bankBranchName;
    }

    public String getOpeningProvinceName() {
        return openingProvinceName;
    }

    public void setOpeningProvinceName(String openingProvinceName) {
        this.openingProvinceName = openingProvinceName;
    }

    public String getOpeningCityName() {
        return openingCityName;
    }

    public void setOpeningCityName(String openingCityName) {
        this.openingCityName = openingCityName;
    }

    public String getQyOpeningBankName() {
        return qyOpeningBankName;
    }

    public void setQyOpeningBankName(String qyOpeningBankName) {
        this.qyOpeningBankName = qyOpeningBankName;
    }

    public String getQyBankBranchName() {
        return qyBankBranchName;
    }

    public void setQyBankBranchName(String qyBankBranchName) {
        this.qyBankBranchName = qyBankBranchName;
    }

    public String getQyOpeningProvinceName() {
        return qyOpeningProvinceName;
    }

    public void setQyOpeningProvinceName(String qyOpeningProvinceName) {
        this.qyOpeningProvinceName = qyOpeningProvinceName;
    }

    public String getQyOpeningCityName() {
        return qyOpeningCityName;
    }

    public void setQyOpeningCityName(String qyOpeningCityName) {
        this.qyOpeningCityName = qyOpeningCityName;
    }

    public String getQyFailureReason() {
        return qyFailureReason;
    }

    public void setQyFailureReason(String qyFailureReason) {
        this.qyFailureReason = qyFailureReason;
    }

    public String getBankBranchCode() {
        return bankBranchCode;
    }

    public void setBankBranchCode(String bankBranchCode) {
        this.bankBranchCode = bankBranchCode;
    }

    public String getQyBankBranchCode() {
        return qyBankBranchCode;
    }

    public void setQyBankBranchCode(String qyBankBranchCode) {
        this.qyBankBranchCode = qyBankBranchCode;
    }

    public String getChangeBankReason() {
        return changeBankReason;
    }

    public void setChangeBankReason(String changeBankReason) {
        this.changeBankReason = changeBankReason;
    }

    public String getBankFiles() {
        return bankFiles;
    }

    public void setBankFiles(String bankFiles) {
        this.bankFiles = bankFiles;
    }

    public String getBankIdNumber() {
        return bankIdNumber;
    }

    public void setBankIdNumber(String bankIdNumber) {
        this.bankIdNumber = bankIdNumber;
    }

    public String getQyChangeBankReason() {
        return qyChangeBankReason;
    }

    public void setQyChangeBankReason(String qyChangeBankReason) {
        this.qyChangeBankReason = qyChangeBankReason;
    }

    public String getQyBankFiles() {
        return qyBankFiles;
    }

    public void setQyBankFiles(String qyBankFiles) {
        this.qyBankFiles = qyBankFiles;
    }

    public String getQyBankIdNumber() {
        return qyBankIdNumber;
    }

    public void setQyBankIdNumber(String qyBankIdNumber) {
        this.qyBankIdNumber = qyBankIdNumber;
    }

    public String getQyRefundPath() {
        return qyRefundPath;
    }

    public void setQyRefundPath(String qyRefundPath) {
        this.qyRefundPath = qyRefundPath;
    }

    public String getQyAccountHolder() {
        return qyAccountHolder;
    }

    public void setQyAccountHolder(String qyAccountHolder) {
        this.qyAccountHolder = qyAccountHolder;
    }

    public String getQyBankCard() {
        return qyBankCard;
    }

    public void setQyBankCard(String qyBankCard) {
        this.qyBankCard = qyBankCard;
    }

    public String getQyOpeningBank() {
        return qyOpeningBank;
    }

    public void setQyOpeningBank(String qyOpeningBank) {
        this.qyOpeningBank = qyOpeningBank;
    }

    public String getQyOpeningProvince() {
        return qyOpeningProvince;
    }

    public void setQyOpeningProvince(String qyOpeningProvince) {
        this.qyOpeningProvince = qyOpeningProvince;
    }

    public String getQyOpeningCity() {
        return qyOpeningCity;
    }

    public void setQyOpeningCity(String qyOpeningCity) {
        this.qyOpeningCity = qyOpeningCity;
    }


    /**
     * @return 主键
     */
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return 调整单编号
     */
    public String getAdjustNo() {
        return adjustNo;
    }

    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo;
    }

    /**
     * @return 产品地址
     */
    public String getProductAddress() {
        return productAddress;
    }

    public void setProductAddress(String productAddress) {
        this.productAddress = productAddress;
    }

    /**
     * @return 承租人姓名
     */
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * @return 证件类型
     */
    public String getCustomerIdType() {
        return customerIdType;
    }

    public void setCustomerIdType(String customerIdType) {
        this.customerIdType = customerIdType;
    }

    /**
     * @return 证件号码
     */
    public String getCustomerIdNumber() {
        return customerIdNumber;
    }

    public void setCustomerIdNumber(String customerIdNumber) {
        this.customerIdNumber = customerIdNumber;
    }

    /**
     * @return 趸租单位
     */
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    /**
     * @return 统一社会信用代码
     */
    public String getCustomerCreditCode() {
        return customerCreditCode;
    }

    public void setCustomerCreditCode(String customerCreditCode) {
        this.customerCreditCode = customerCreditCode;
    }

    /**
     * @return 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    /**
     * @return 合同类型
     */
    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    /**
     * @return 合同开始日期
     */
    public Date getContractBeginTime(){
        if(contractBeginTime!=null){
            return (Date)contractBeginTime.clone();
        }else{
            return null;
        }
    }

    public void setContractBeginTime(Date contractBeginTime) {
        if(contractBeginTime==null){
            this.contractBeginTime = null;
        }else{
            this.contractBeginTime = (Date)contractBeginTime.clone();
        }
    }

    /**
     * @return 合同结束日期
     */
    public Date getContractEndTime(){
        if(contractEndTime!=null){
            return (Date)contractEndTime.clone();
        }else{
            return null;
        }
    }

    public void setContractEndTime(Date contractEndTime) {
        if(contractEndTime==null){
            this.contractEndTime = null;
        }else{
            this.contractEndTime = (Date)contractEndTime.clone();
        }
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 调整单状态
     */
    public String getAdjustStatus() {
        return adjustStatus;
    }

    public void setAdjustStatus(String adjustStatus) {
        this.adjustStatus = adjustStatus;
    }

    /**
     * @return 退款状态
     */
    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    /**
     * @return 退款失败原因
     */
    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    /**
     * @return 调整金额（元）
     */
    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    /**
     * @return 调整应收依据文件
     */
    public String getAdjustFiles() {
        return adjustFiles;
    }

    public void setAdjustFiles(String adjustFiles) {
        this.adjustFiles = adjustFiles;
    }

    /**
     * @return 应退金额处理类型
     */
    public String getAmountHandle() {
        return amountHandle;
    }

    public void setAmountHandle(String amountHandle) {
        this.amountHandle = amountHandle;
    }

    /**
     * @return 退款路径类型
     */
    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    /**
     * @return 退回路径
     */
    public String getRefundPath() {
        return refundPath;
    }

    public void setRefundPath(String refundPath) {
        this.refundPath = refundPath;
    }

    /**
     * @return 开户人姓名
     */
    public String getAccountHolder() {
        return accountHolder;
    }

    public void setAccountHolder(String accountHolder) {
        this.accountHolder = accountHolder;
    }

    /**
     * @return 银行卡号
     */
    public String getBankCard() {
        return bankCard;
    }

    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    /**
     * @return 开户行
     */
    public String getOpeningBank() {
        return openingBank;
    }

    public void setOpeningBank(String openingBank) {
        this.openingBank = openingBank;
    }

    /**
     * @return 开户行所在省
     */
    public String getOpeningProvince() {
        return openingProvince;
    }

    public void setOpeningProvince(String openingProvince) {
        this.openingProvince = openingProvince;
    }

    /**
     * @return 开户行所在市
     */
    public String getOpeningCity() {
        return openingCity;
    }

    public void setOpeningCity(String openingCity) {
        this.openingCity = openingCity;
    }

    /**
     * @return 备用字段1
     */
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * @return 备用字段2
     */
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * @return 备用字段3
     */
    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 创建人姓名
     */
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

      @Override
    public String toString() {
        return "BbpmReceivableAdjustEntity{" +
            "id=" + id +
            ", adjustNo=" + adjustNo +
            ", productAddress=" + productAddress +
            ", customerName=" + customerName +
            ", customerIdType=" + customerIdType +
            ", customerIdNumber=" + customerIdNumber +
            ", company=" + company +
            ", customerCreditCode=" + customerCreditCode +
            ", contractNo=" + contractNo +
            ", contractType=" + contractType +
            ", contractBeginTime=" + contractBeginTime +
            ", contractEndTime=" + contractEndTime +
            ", projectId=" + projectId +
            ", projectName=" + projectName +
            ", adjustStatus=" + adjustStatus +
            ", refundStatus=" + refundStatus +
            ", failureReason=" + failureReason +
            ", adjustAmount=" + adjustAmount +
            ", adjustFiles=" + adjustFiles +
            ", amountHandle=" + amountHandle +
            ", refundType=" + refundType +
            ", refundPath=" + refundPath +
            ", accountHolder=" + accountHolder +
            ", bankCard=" + bankCard +
            ", openingBank=" + openingBank +
            ", openingProvince=" + openingProvince +
            ", openingCity=" + openingCity +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", ext3=" + ext3 +
            ", delFlag=" + delFlag +
            ", createUserName=" + createUserName +
        "}";
    }
}