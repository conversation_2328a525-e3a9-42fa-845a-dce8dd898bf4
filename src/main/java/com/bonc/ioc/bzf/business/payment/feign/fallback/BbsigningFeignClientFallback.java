package com.bonc.ioc.bzf.business.payment.feign.fallback;

import com.bonc.ioc.bzf.business.payment.feign.feign.BbsigningFeignClient;
import com.bonc.ioc.bzf.business.payment.vo.BbsiRuleVo;
import com.bonc.ioc.common.util.AppReply;

public class BbsigningFeignClientFallback implements BbsigningFeignClient {
    @Override
    public AppReply<BbsiRuleVo> selectByIdRecord(String ruleId) {
        return null;
    }
}
