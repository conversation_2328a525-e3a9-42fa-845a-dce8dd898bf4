package com.bonc.ioc.bzf.business.payment.utils;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

public class DateComparisonUtils {
    public static boolean dateComparison(String dateString){
        String formatStr = "yyyy-MM-dd HH:mm:ss";
        if(!dateString.contains("-")){
            formatStr = "yyyyMMddHHmmss";
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatStr);
        LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);
        LocalDateTime now = LocalDateTime.now();

        Period period = Period.between(dateTime.toLocalDate(), now.toLocalDate());
        //超过6个月
        if (period.getMonths() >= 6) {
           return true;
        }
        return false;
    }

    public static void main(String[] args){
//        System.out.println(dateComparison("20191024112128"));

    }

}
