package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@ApiModel(value="递增规则", description="递增规则")
@Data
public class PreviewBillparamsRoomChargeIncreaseRule implements java.io.Serializable{

    @ApiModelProperty(value = "递增规则ID")
    private Integer increaseRuleId;

    @ApiModelProperty(value = "递增比例")
    private BigDecimal increaseProportion;

    @ApiModelProperty(value = "递增金额")
    private BigDecimal increaseAmount;

    @ApiModelProperty(value = "递增周期 01 月02 季03 半年04 年")
    private String increasePeriod;

    @ApiModelProperty(value = "递增顺序 如果上面的递增周期是年，则   1 表示第一年   2表示第二年   3表示第三年\n")
    private Integer increaseOrder;

    @ApiModelProperty(value = "递增类型 01每 02第")
    private String increaseType;
}
