package com.bonc.ioc.bzf.business.reminder.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 催缴规则消息发送日志--子表  实体类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@ApiModel(value="BbpmMessageSendSubLogPageVo对象", description="催缴规则消息发送日志--子表 ")
public class BbpmMessageSendSubLogPageVo extends McpBasePageVo implements Serializable{


    /**
     * 通知ID
     */
    @ApiModelProperty(value = "通知ID")
    @NotBlank(message = "通知ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String noticeId;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @NotBlank(message = "主表ID不能为空",groups = {UpdateValidatorGroup.class})
                            private String mainId;

    /**
     * 规则编号
     */
    @ApiModelProperty(value = "规则编号")
    @NotBlank(message = "规则编号不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String rulesId;

    /**
     * 规则子表
     */
    @ApiModelProperty(value = "规则子表")
    @NotBlank(message = "规则子表不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String rulesSubId;

    /**
     * 消息模板id
     */
    @ApiModelProperty(value = "消息模板id")
                            private String messageTemplateId;

    /**
     * 通知方式:1站内信,2短信
     */
    @ApiModelProperty(value = "通知方式:1站内信,2短信")
    @NotBlank(message = "通知方式:1站内信,2短信不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String noticeMethod;

    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
                            private String noticeContent;

    /**
     * 通知结果:1成功,2失败
     */
    @ApiModelProperty(value = "通知结果:1成功,2失败")
                            private String noticeResult;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date sendTime;

    /**
     * 发送次数
     */
    @ApiModelProperty(value = "发送次数")
                            private Integer sendNum;

    /**
     * 消息中心请求报文
     */
    @ApiModelProperty(value = "消息中心请求报文")
                            private String request;

    /**
     * 消息中心响应报文
     */
    @ApiModelProperty(value = "消息中心响应报文")
                            private String response;

    /**
     * @return 通知ID
     */
    public String getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(String noticeId) {
        this.noticeId = noticeId;
    }

    /**
     * @return 主表ID
     */
    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    /**
     * @return 规则编号
     */
    public String getRulesId() {
        return rulesId;
    }

    public void setRulesId(String rulesId) {
        this.rulesId = rulesId;
    }

    /**
     * @return 规则子表
     */
    public String getRulesSubId() {
        return rulesSubId;
    }

    public void setRulesSubId(String rulesSubId) {
        this.rulesSubId = rulesSubId;
    }

    /**
     * @return 消息模板id
     */
    public String getMessageTemplateId() {
        return messageTemplateId;
    }

    public void setMessageTemplateId(String messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    /**
     * @return 通知方式:1站内信,2短信
     */
    public String getNoticeMethod() {
        return noticeMethod;
    }

    public void setNoticeMethod(String noticeMethod) {
        this.noticeMethod = noticeMethod;
    }

    /**
     * @return 通知内容
     */
    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    /**
     * @return 通知结果:1成功,2失败
     */
    public String getNoticeResult() {
        return noticeResult;
    }

    public void setNoticeResult(String noticeResult) {
        this.noticeResult = noticeResult;
    }

    /**
     * @return 发送时间
     */
    public Date getSendTime(){
        if(sendTime!=null){
            return (Date)sendTime.clone();
        }else{
            return null;
        }
    }

    public void setSendTime(Date sendTime) {
        if(sendTime==null){
            this.sendTime = null;
        }else{
            this.sendTime = (Date)sendTime.clone();
        }
    }

    /**
     * @return 发送次数
     */
    public Integer getSendNum() {
        return sendNum;
    }

    public void setSendNum(Integer sendNum) {
        this.sendNum = sendNum;
    }

    /**
     * @return 消息中心请求报文
     */
    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    /**
     * @return 消息中心响应报文
     */
    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

      @Override
    public String toString() {
        return "BbpmMessageSendSubLogPageVo{" +
            "noticeId=" + noticeId +
            ", mainId=" + mainId +
            ", rulesId=" + rulesId +
            ", rulesSubId=" + rulesSubId +
            ", messageTemplateId=" + messageTemplateId +
            ", noticeMethod=" + noticeMethod +
            ", noticeContent=" + noticeContent +
            ", noticeResult=" + noticeResult +
            ", sendTime=" + sendTime +
            ", sendNum=" + sendNum +
            ", request=" + request +
            ", response=" + response +
        "}";
    }
}
