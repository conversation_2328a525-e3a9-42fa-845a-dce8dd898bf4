package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="3.58.账单关闭或开启接口Param", description="3.58.账单关闭或开启接口Param")
public class CloseOrOpenBillAndBillBranksParamVo {

    @ApiModelProperty(value = "合同唯一识别码 合同编码")
    private String contractCode;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "关闭或开启   0，关闭 1，开启")
    private String closeOrOpen;

}
