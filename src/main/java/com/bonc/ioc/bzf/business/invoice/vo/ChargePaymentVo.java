package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 已开发票--收款单信息
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value="chargePaymentVo对象", description="已开发票--收款单信息")
@Data
public class ChargePaymentVo extends McpBaseVo implements Serializable{

    @ApiModelProperty(value = "收款单唯一标识码")
    private String chargeCode;
    @ApiModelProperty(value = "客户姓名(租户姓名)")
    private String tenantName;
    @ApiModelProperty(value = "合同编号")
    private String contractCode	;
    @ApiModelProperty(value = "房源地址")
    private String houseAddr;
    @ApiModelProperty(value = "客户联系电话(租户手机号)")
    private String tenantMobile;
    @ApiModelProperty(value = "费用类型")
    private String expenseType;
    @ApiModelProperty(value = "收款金额")
    private String chargeMoney;
    @ApiModelProperty(value = "收款时间(收款单生成时间)")
    private String chargeDate;

}
