package com.bonc.ioc.bzf.business.payment.result;

import com.bonc.ioc.bzf.business.payment.result.create.ChargeSubjectParamsRequest;
import com.bonc.ioc.bzf.business.payment.result.create.FurnitureRentalParamsRequest;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 3.9.接收白名单推送接口
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
@Builder
public class WhiteListRequest {

	private String tenantId;//	租户ID	String	是	64
	private String publicRecordNo;//	备案家庭号	String	是	20
	private String tenantName;//	姓名	String	是	20
	private String tenantNo;//	身份证证件号	String	是	30
	private BigDecimal withholdRatio;//	代扣比例	BigDecimal	是	13,2	样例：0.7（含义为70%）
	private String operateType;//	操作类型	String	是	10	01 新增 02 删除 03更新
	private String projectId;//	项目ID	String	是	64	业务中台ID


}
