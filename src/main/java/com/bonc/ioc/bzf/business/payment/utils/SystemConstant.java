package com.bonc.ioc.bzf.business.payment.utils;

/**
 * @ClassName SystemConstant
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2022-12-08 11:50
 **/
public class SystemConstant {
    /**
     * @description: 合同中心
     * @author: 宋鑫
     * @date: 2022-12-08 11:52
     * @param:
     * @return:
     * @since 1.0.0
     **/
    public static final String APPLICATION_NAME_CONTRACT = "bzf-business-contract-1230";

    /**
     * @description: v2合同中心
     * @author: fem
     * @date: 2023-05-10 11:52
     * @param:
     * @return:
     * @since 1.0.0
     **/
    public static final String APPLICATION_NAME_CONTRACT_V2 = "bzf-business-contract-630";

    /**
     * @description: 签约中心
     * @author: 宋鑫
     * @date: 2022-12-08 11:59
     * @param:
     * @return:
     * @since 1.0.0
     **/
    public static final String APPLICATION_NAME_SIGNING = "bzf-business-signing-1230";

    /**
     * @description: 消息中心
     * @author: 许多
     * @date: 2022-12-08 11:59
     * @param:
     * @return:
     * @since 1.0.0bzf-business-message
     **/
    public static final String APPLICATION_NAME_MESSAGE = "bzf-business-message";

    /**
     * @description: 缴费中心
     * @author: 宋鑫
     * @date: 2022-12-08 11:59
     * @param:
     * @return:
     * @since 1.0.0
     **/
    public static final String APPLICATION_NAME_PAYMENT = "bzf-business-payment-630";

    /**
     * @description: 附件中心
     * @author: 宋鑫
     * @date: 2022-12-08 11:59
     * @param:
     * @return:
     * @since 1.0.0
     **/
    public static final String APPLICATION_NAME_FILE = "mcp-file";

    /**
     * @description: 退租中心
     * @author:
     * @date: 2022-12-08 11:59
     * @param:
     * @return:
     * @since 1.0.0bzf-system-withdrawal
     **/
    public static final String APPLICATION_NAME_WITHDRAWAL = "bzf-system-withdrawal";

    /**
     * @description: 房态中心
     * @author:
     * @date: 2022-12-08 11:59
     * @param:
     * @return:
     * @since 1.0.0bzf-business-housing
     **/
    public static final String APPLICATION_NAME_HOUSING = "bzf-business-housing";

    /**
     * @description: 客户中心
     * @author:
     * @date: 2022-12-08 11:59
     * @param:
     * @return:
     * @since 1.0.0bzf-business-customer
     **/
    public static final String APPLICATION_NAME_CUSTOMER = "bzf-business-customer";

    /**
     * @description: 用户中心
     * @author: 宋鑫
     * @date: 2023-01-03 20:03
     * @param:
     * @return:
     * @since 1.0.0
     **/
    public static final String APPLICATION_NAME_USER ="bphc-bms-service";
    /**
     * @description: 计费中心
     * @author:
     * @date: 2022-12-08 11:59
     * @param:
     * @return:
     * @since 1.0.0bzf-business-customer
     **/
    public static final String APPLICATION_NAME_BFIP_CHARGE = "bfip-charge";

    /**
     * 分享中心
     */
    public static final String APPLICATION_NAME_SHARE = "bzf-business-share";

    /**
     * 签约中心v2
     */
    public static final String APPLICATION_NAME_SIGNING_V2 = "bzf-business-signing-630";


    /**
     * 融合中心
     */
    //public static final String APPLICATION_NAME_FUSE_V2 = "bzf-business-fuse-1230";
    public static final String APPLICATION_NAME_FUSE_V2 = "bzf-business-fuse-v2";

    /**
     * 业财
     */
    public static final String APPLICATION_YC = "yc";

    /**
     * 客户中心v2
     */
    public static final String APPLICATION_NAME_CUSTOMER_V2 = "bzf-business-customer";

    /**
     * @description: 入住中心v2
     **/
    public static final String APPLICATION_NAME_CHECKIN_V2 = "bzf-business-checkin-630";

    /**
     * 房态中心v2
     */
    public static final String APPLICATION_NAME_HOUSING_V2 = "bzf-business-housing-630";


    /**
     * 调查中心
     */
    public static final String APPLICATION_NAME_SURVEY = "bzf-business-survey";

    /**
     * 租户信息采集
     */
    public static final String APPLICATION_NAME_TENANT_INFO_V2 = "bzf-business-tenant-collection";

    /**
     * 客户管理系统
     */
    public static final String APPLICATION_NAME_SYSTEM_CUSTOMER = "bzf-system-customer-630";


    /**
     * 文件中心·
     */
    public static final String APPLICATION_NAME_File_V2 = "bzf-business-file";

    /**
     * 调换房中心
     */
    public static final String APPLICATION_NAME_HOUSING_EXCHANGE = "bzf-business-housing-exchange";

    /**
     * 商业系统
     */
    public static final String APPLICATION_NAME_BUSISIGNING = "bzf-system-busisigning-915";

    /**
     * 家庭中心
     */
    public static final String APPLICATION_FILING_FAMILY = "bzf-business-filing-family";


    public static final String APPLICATION_NAME_BCA = "bca-bzf-agent";

    /**
     * 公积金对接系统
     */
    public static final String APPLICATION_PROVIDENT_FUND = "bzf-provident-fund-docking-system";


    public static final String APPLICATION_WORKFLOW = "bzf-business-fw-workflow";
}
