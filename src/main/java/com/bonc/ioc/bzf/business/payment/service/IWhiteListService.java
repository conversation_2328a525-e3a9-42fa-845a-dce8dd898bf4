package com.bonc.ioc.bzf.business.payment.service;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.entity.BbpmCashPledgeEntity;
import com.bonc.ioc.bzf.business.payment.result.WhiteListRequest;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;
import java.util.Map;

/**
 * 押金条 服务类
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by binghong.tang for init
 */
public interface IWhiteListService extends IMcpBaseService<BbpmCashPledgeEntity>{
    JSONObject whiteList(List<WhiteListVo> whiteListVoList);

    JSONObject whiteListSendGy(List<WhiteListRequestVo> whiteListRequestList);
}
