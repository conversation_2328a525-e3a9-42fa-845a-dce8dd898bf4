package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 面积类型 枚举类
 *
 * <AUTHOR>
 * @since 2024/2/2
 */
public enum AreaTypeEnum {

    /**
     * 签约计划
     */
    HOUSE_STRUCT_AREA("1", "建筑面积"),

    /**
     * 合同
     */
    INNER_SLEEVE_AREA("2", "套内建筑面积");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    AreaTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    AreaTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        AreaTypeEnum[] enums = values();
        for (AreaTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述信息查询编号
     *
     * @param desc 描述信息
     * @return 编号
     */
    public static String getCodeByDesc(String desc) {
        AreaTypeEnum[] enums = values();
        for (AreaTypeEnum en : enums) {
            if (en.getDesc().equals(desc)) {
                return en.getCode();
            }
        }
        return null;
    }
}
