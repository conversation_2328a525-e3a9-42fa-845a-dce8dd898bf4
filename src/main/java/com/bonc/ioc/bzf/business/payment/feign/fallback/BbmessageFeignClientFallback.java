package com.bonc.ioc.bzf.business.payment.feign.fallback;



import com.bonc.ioc.bzf.business.payment.feign.feign.BbmessageFeignClient;
import com.bonc.ioc.bzf.business.payment.result.MessageInterfaceParamVo;
import com.bonc.ioc.bzf.business.payment.result.MessageInterfaceResponseVo;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * MssFeignClientFallback
 *
 * <AUTHOR>
 * @date 2021/5/20 11:57
 * @change: 2021/5/20 11:57 by wtl for init
 */
public class BbmessageFeignClientFallback implements BbmessageFeignClient {

    /**
     * realTimeMessagePush 站内信实时接口
     *
     * @param paramVo 发送的通知里面的内容
     * @return com.bonc.ioc.common.util.AppReply<java.lang.String>
     * <AUTHOR>
     * @date 2022/06/10 18:01:59
     * @change 2022/06/10 18:01:59 by sqj for init
     * @since 1.0.0
     */
    @Override
    public AppReply<MessageInterfaceResponseVo> realTimeInternalMessagePush(MessageInterfaceParamVo paramVo) {
        return null;
    }

    @Override
    public AppReply<MessageInterfaceResponseVo> realTimemailMessagePush(MessageInterfaceParamVo paramVo) {
        return null;
    }

    @Override
    public AppReply<MessageInterfaceResponseVo> realTimemailMessagePushv2(MessageInterfaceParamVo paramVo) {
        return null;
    }

    @Override
    public AppReply<PageResult<List<MessTemplateInfoPageResultVo>>> selectByPageTemplateList(MessTemplateInfoPageVo vo) {
        return null;
    }

    @Override
    public AppReply<List<MessTemplateTypeVo>> selectTempTypeList(MessTemplateTypeVo vo) {
        return null;
    }

    @Override
    public AppReply<List<MessTemplateParamVo>> selectParamByTemplateTypeId(String templateTypeId) {
        return null;
    }

    @Override
    public AppReply saveByIdRecord(MessTemplateInfoVo vo) {
        return null;
    }

    @Override
    public AppReply<MessTemplateInfoVo> selectTemplateById(String messTemplateId) {
        return null;
    }

    @Override
    public AppReply removeTemplateByIdRecord(String messTemplateId) {
        return null;
    }

    @Override
    public AppReply<List<MessTemplateInfoPageResultVo>> selectAllTemplate(String messageType, String systemType, String templateTypeCode) {
        return null;
    }

    /**
     * getTemplateInfo 获取模板信息
     *
     * @param sendMessageTypeList 消息类型
     */
    @Override
    public AppReply<List<TemplateInfoContentVo>> getTemplateInfo(List<String> sendMessageTypeList) {
        return null;
    }
}
