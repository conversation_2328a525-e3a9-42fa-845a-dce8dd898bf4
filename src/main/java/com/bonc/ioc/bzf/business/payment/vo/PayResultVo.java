package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付结果对象
 *
 * <AUTHOR>
 * @date 2022-03-04
 * @change 2022-03-04 by 姚春雨 for init
 */
@Data
@ApiModel(value = "PayResultVo对象", description = "支付结果对象")
public class PayResultVo extends McpBaseVo implements Serializable {

    @ApiModelProperty(hidden = true)
    private String busiCode;

    @ApiModelProperty(value = "支付返回编码（00000:成功；其他:失败）")
    private String code;

    @ApiModelProperty(value = "返回消息")
    private String message;

    @ApiModelProperty(value = "返回数据")
    private PayResultDataVo data;


}
