package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.result.ChangeContractBusinessRequest;
import com.bonc.ioc.bzf.business.payment.result.ChangeContractPaymentDateRequest;
import com.bonc.ioc.bzf.business.payment.result.ChangeContractRequestV2;
import com.bonc.ioc.bzf.business.payment.result.ChangeContractStartEndDateRequest;
import com.bonc.ioc.bzf.business.payment.result.create.BillCreateParamsRequest;
import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangeStartDTO;
import com.bonc.ioc.bzf.business.payment.vo.*;

import java.util.List;

/**
 * 创建订单
 *
 */
public interface IBillCreateService {

    /**
     * 合同生成账单  所有类型
     * @param vo
     * @return
     */
    String billCreateAll(BbctPushInfoVo vo);

    /**
     * 对接工银    3.3合同签订生成账单接口
     */
    String billCreate(BbctPushInfoVo vo);

    /**
     * 对接工银 3.32散租续签生成账单
     */
    String looseRentRelet(BbctPushInfoVo vo);

    /**
     *对接工银 3.26. 趸租大合同生成账单接口
     */
    String billReletCreate(BbctPushInfoVo vo);

    /**
     * 对接工银 3.28 趸租大合同续租生成账单接口
     */
    String singleRelet(BbctPushInfoVo vo);

    /**
     *对接工银 3.27. 趸租管理协议生成账单接口和独立管理协议接口
     */
    String billAgreementCreate(BbctPushInfoVo vo);

    /**
     * 对接工银 3.34. 管理协议续签生成账单
     */
    String agreementRelet(BbctPushInfoVo vo);

    /**
     * 3.30空置费账单
     * @param billVacancyFeeCreateParamsVo
     * @return
     */
    String billVacancyFeeCreate(BillVacancyFeeCreateParamsVo billVacancyFeeCreateParamsVo);

    /**
     * 欠费状态查询
     * @param vo
     * @return
     */
    Object billOverdue(ChargeArrearsVo vo);

    /**
     * 3.37. 商业合同生成账单接口
     */
    String businessCreate(BbctPushInfoVo vo);

    /**
     * 对接工银  3.5.合同变更单据更新接口
     */
    String updateByContractAll(BbctPushInfoVo vo);

    /**
     * 3.48 商业合同续签生成账单
     */
    String businessRelet(BbctPushInfoVo vo);

    String businessContractChange(ChangeContractBusinessRequest vo);

    String storageRelet(BbctPushInfoVo vo);

    String businessContractChangePaymentDate(ChangeContractPaymentDateRequest vo);

    String businessContractChangeStart(ChangeContractStartEndDateRequest vo);


    String businessContractChange091011(ChangeContractRequestV2 vo);

    List<ChangeTrialPreviewBillsResultVo> changeTrialPreviewBills(BbctPushInfoVo vo);

    /**
     * 对接工银  3.64.合同变更试算接口
     */
    List<ChangeTrialPreviewBillsResultVo> changeTrial(BbctPushInfoVo vo);
}
