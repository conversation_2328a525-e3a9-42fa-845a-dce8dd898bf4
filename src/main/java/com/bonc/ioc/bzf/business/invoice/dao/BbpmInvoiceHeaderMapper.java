package com.bonc.ioc.bzf.business.invoice.dao;

import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceHeaderEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import java.util.List;

/**
 * 常用发票抬头信息表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-05-05
 * @change 2023-05-05 by binghong.tang for init
 */
@Mapper
public interface BbpmInvoiceHeaderMapper extends McpBaseMapper<BbpmInvoiceHeaderEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change 2023-05-05 by binghong.tang for init
     */
    List<BbpmInvoiceHeaderPageResultVo> selectByPageCustom(@Param("vo") BbpmInvoiceHeaderPageVo vo );

    BbpmInvoiceHeaderVo selectByIsDefault(@Param("isDefault") String isDefault,@Param("invoiceHeaderId") String invoiceHeaderId);
}
