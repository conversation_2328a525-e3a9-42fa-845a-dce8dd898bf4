package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ChargeArrearsContractVo implements Serializable {

    @ApiModelProperty("项目编号")
    private String contractId;

    @ApiModelProperty("项目业态")
    private String projectFormat;

    @ApiModelProperty("返回参数 1 存在欠费 0 不存在欠费")
    private String arrearsStatus;

}
