package com.bonc.ioc.bzf.business.payment.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 报盘记录 实体类
 *
 * <AUTHOR>
 * @date 2023-10-20
 * @change 2023-10-20 by binghong.tang for init
 */
@ApiModel(value="OfferPageResultVo对象", description="报盘记录")
public class OfferPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 请求编号
     */
    @ApiModelProperty(value = "请求编号")
    @NotBlank(message = "请求编号不能为空",groups = {UpdateValidatorGroup.class})
                                  private String reqno;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @McpDictPoint(dictCode = "BILLING_OFFER_STATUS",overTransCopyTo = "statusName")
                            private String status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @McpDictPoint(dictCode ="BILLING_OFFER_TYPE",overTransCopyTo = "typeName")
                            private String type;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    public Date getCreateTime(){
        if(createTime!=null){
            return (Date)createTime.clone();
        }else{
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if(createTime==null){
            this.createTime = null;
        }else{
            this.createTime = (Date)createTime.clone();
        }
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    /**
     * @return 请求编号
     */
    public String getReqno() {
        return reqno;
    }

    public void setReqno(String reqno) {
        this.reqno = reqno;
    }

    /**
     * @return 状态
     */
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return 类型
     */
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

      @Override
    public String toString() {
        return "OfferPageResultVo{" +
            "reqno=" + reqno +
            ", status=" + status +
            ", type=" + type +
            ", projectId=" + projectId +
        "}";
    }
}
