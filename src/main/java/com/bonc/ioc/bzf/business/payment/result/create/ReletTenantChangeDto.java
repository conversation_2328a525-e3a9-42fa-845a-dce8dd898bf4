package com.bonc.ioc.bzf.business.payment.result.create;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 01 主承租人变更,备案家庭不变(散租和管理协议人员信息变更详见tenantChangeDto，趸租大合同企业信息变更详见reletTenantChangeDto)
 */
@Data
@Builder
public class ReletTenantChangeDto {

    private String companyId;//趸租企业ID	String	是
    private String companyIDType;//趸租企业证类型	String	是
    private String socialCreditCode;//	趸租企业社会信用代码	String	是
    private String companyName;//趸租企业名称	String	是
    private String companySupplierNo;//趸租企业客商编号	String	是
    private String companySupplierName;//趸租企业客商名称	String	是
    private String companyCustomerNo;//趸租企业客户编号	String	是
    private String authorizedAgentMobile;//	企业委托代理人电话	String	是
    private String authorizedAgent;//企业委托代理人	String	是
    private String companyTaxNo;//	企业纳税识别号	String	是
    private String companyBankName;//	企业开户总行名称	String	是
    private String companyBankCode;//企业开户总行编码	String	是
    private String companyBankBranchName;//企业开户支行名称	String	是
    private String companyBankBranchCode;//企业开户支行编码	String	是
    private String companyBankAccountName;//企业银行卡户名	String	是
    private String companyBankAccountNo;//企业银行卡卡号	String	是
    private String beforeContractCode;//原合同code	String	是
    private String contractCode;//	新合同code	String	是
    private String contractStatus;//合同状态	String	是
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginDate;//合同起始日期	LocalDate	是
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;//	合同终止日期	LocalDate	是
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractSignTime;//合同签订时间	LocalDate	是
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractCommencementDate;//合同起租日	LocalDate	是


}
