package com.bonc.ioc.bzf.business.payment.controller;

import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import com.bonc.ioc.common.validator.inf.*;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RestController;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCashCollectionVoucherService;
import io.swagger.annotations.*;
import com.bonc.ioc.bzf.business.payment.entity.*;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 现金收缴凭证表v3.0 前端控制器
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/atomic/bbpmCashCollectionVoucherEntity")
@Api(tags = "现金收缴凭证表v3.0")
@Validated
public class BbpmCashCollectionVoucherController extends McpBaseController {
    @Resource
    private IBbpmCashCollectionVoucherService baseService;

    /**
     * insertBatchRecord 批量新增
     * @param vo 需要新增的记录 列表
     * @return  com.bonc.ioc.common.util.AppReply<List<String>> 返回新增后的主键 列表
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @PostMapping(value = "/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "binghong.tang")
    @ApiOperation(value = "新增凭证", notes = "新增凭证", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/bzf-business-payment/v2/atomic/bbpmCashCollectionVoucherEntity/insertBatchRecord")
    public AppReply<String> insertBatchRecord(@ApiParam(value = "现金收缴凭证表v3.0" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmCashCollectionVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertRecordV2(vo));
        return appReply;
    }


    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmCashCollectionVoucherPageResultVo>>> selectByPageRecord(BbpmCashCollectionVoucherPageVo vo){
        AppReply<PageResult<List<BbpmCashCollectionVoucherPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }


    /**
     * selectByPageRecord 根据条件查询单个
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/selectByCollectionNo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "根据条件查询单个", notes = "根据条件查询单个", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmCashCollectionVoucherVo> selectByPageRecord(BbpmCashCollectionVoucherVo vo){
        AppReply<BbpmCashCollectionVoucherVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByCollectionNo(vo));
        return appReply;
    }


    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的押金条
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "binghong.tang")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @LogPoint(system = "bzf-business-payment",path = "/bzf-business-payment/v2/atomic/bbpmCashCollectionVoucherEntity/updateById")
    public AppReply updateByIdRecord(@ApiParam(value = "凭证信息" ,required = false) @RequestBody   BbpmCashCollectionVoucherVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateByIdRecord(vo);
        return appReply;
    }


    /**
     * insertRecord 新增
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "binghong.tang")
    @ApiOperation(value = "新增", notes = "新增全表数据", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    @LogPoint(system = "bzf-business-payment",path = "/bzf-business-payment/v2/atomic/bbpmCashCollectionVoucherEntity/insertRecord")
    public AppReply<String> insertRecord(@ApiParam(value = "凭证信息" ,required = false) @RequestBody   BbpmCashCollectionVoucherVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
    }

}

