package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.bzf.business.payment.result.CorporateCollectionRequest;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 批量收款实体
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@ApiModel(value="BbpmCollectionBatchVo对象", description="批量收款实体")
public class BbpmCollectionBatchVo extends McpBaseVo implements Serializable{

    /**
     * 账单列表
     */
    @ApiModelProperty(value = "账单列表")
    @NotNull(message = "账单列表不能为空", groups = {InsertValidatorGroup.class})
    @Size(min=1,message = "账单列表不能为空" )
    List<BbpmBillManagementVo> bbpmBillManagementVoList;


    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    @NotNull(message = "应收金额不能为空",groups = {InsertValidatorGroup.class})
    private BigDecimal amountReceivable;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    @NotNull(message = "实收金额不能为空",groups = {InsertValidatorGroup.class})
    private BigDecimal paidInAmount;

    /**
     * 剩余应缴金额
     */
    @ApiModelProperty(value = "剩余应缴金额")
    private BigDecimal remainingAmountPayable;

    /**
     * 收款渠道
     */
    @ApiModelProperty(value = "收款渠道")
    private String collectionChannel;
    @ApiModelProperty(value = "支票和线下转账需要传的字段")
    private CorporateCollectionVo corporateCollectionVo;

    @ApiModelProperty(value ="是否是多项目认款01是02否")
    private String  multiProject;

    public String getMultiProject() {
        return multiProject;
    }

    public void setMultiProject(String multiProject) {
        this.multiProject = multiProject;
    }



    public CorporateCollectionVo getCorporateCollectionVo() {
        return corporateCollectionVo;
    }

    public void setCorporateCollectionVo(CorporateCollectionVo corporateCollectionVo) {
        this.corporateCollectionVo = corporateCollectionVo;
    }

    public List<BbpmBillManagementVo> getBbpmBillManagementVoList() {
        return bbpmBillManagementVoList;
    }

    public void setBbpmBillManagementVoList(List<BbpmBillManagementVo> bbpmBillManagementVoList) {
        this.bbpmBillManagementVoList = bbpmBillManagementVoList;
    }

    public BigDecimal getAmountReceivable() {
        return amountReceivable;
    }

    public void setAmountReceivable(BigDecimal amountReceivable) {
        this.amountReceivable = amountReceivable;
    }

    public BigDecimal getPaidInAmount() {
        return paidInAmount;
    }

    public void setPaidInAmount(BigDecimal paidInAmount) {
        this.paidInAmount = paidInAmount;
    }

    public BigDecimal getRemainingAmountPayable() {
        return remainingAmountPayable;
    }

    public void setRemainingAmountPayable(BigDecimal remainingAmountPayable) {
        this.remainingAmountPayable = remainingAmountPayable;
    }

    public String getCollectionChannel() {
        return collectionChannel;
    }

    public void setCollectionChannel(String collectionChannel) {
        this.collectionChannel = collectionChannel;
    }
}
