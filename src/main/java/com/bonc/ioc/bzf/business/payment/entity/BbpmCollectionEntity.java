package com.bonc.ioc.bzf.business.payment.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 收款表V2 实体类
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@TableName("bbpm_collection")
@ApiModel(value="BbpmCollectionV2Entity对象", description="收款表V2")
public class BbpmCollectionEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_COLLECTION_ID = "collection_id";
    public static final String FIELD_COLLECTION_NO = "collection_no";
    public static final String FIELD_RECEIPT_NO = "receipt_no";
    public static final String FIELD_ELECTRONIC_VOUCHER = "electronic_voucher";
    public static final String FIELD_DEPOSIT_STATUS = "deposit_status";
    public static final String FIELD_BILL_NO = "bill_no";
    public static final String FIELD_BILL_CYCLE = "bill_cycle";
    public static final String FIELD_REMAINING_AMOUNT_PAYABLE = "remaining_amount_payable";
    public static final String FIELD_TOTAL_UNDEPOSITS = "total_undeposits";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_RESIDENTIAL_QUARTERS_NAME = "residential_quarters_name";
    public static final String FIELD_BUILDING_NO = "building_no";
    public static final String FIELD_UNIT_NO = "unit_no";
    public static final String FIELD_FLOOR_NO = "floor_no";
    public static final String FIELD_ROOM_NO = "room_no";
    public static final String FIELD_ADDRESS = "address";
    public static final String FIELD_TENANT_NAME = "tenant_name";
    public static final String FIELD_CERT_NO = "cert_no";
    public static final String FIELD_TENANT_MOBILE = "tenant_mobile";
    public static final String FIELD_OPERATOR_NAME = "operator_name";
    public static final String FIELD_CHARGE_CODE = "charge_code";
    public static final String FIELD_CONTRACT_CODE = "contract_code";
    public static final String FIELD_CHARGE_DATE = "charge_date";
    public static final String FIELD_CHARGE_DATE_START = "charge_date_start";
    public static final String FIELD_CHARGE_DATE_END = "charge_date_end";
    public static final String FIELD_CHARGE_STATUS = "charge_status";
    public static final String FIELD_BANK_RECEIPT_NO = "bank_receipt_no";
    public static final String FIELD_BANK_RECEIPT_DATE = "bank_receipt_date";
    public static final String FIELD_CHARGE_SUBJECT = "charge_subject";
    public static final String FIELD_PAYMENT_TYPE = "payment_type";
    public static final String FIELD_CHARGE_MONEY = "charge_money";
    public static final String FIELD_SER_FEE = "ser_fee";
    public static final String FIELD_RECONCILIATION_STATUS = "reconciliation_status";
    public static final String FIELD_IS_PRE_PAYMENT_OFFSET = "is_pre_payment_offset";
    public static final String FIELD_BANK = "bank";
    public static final String FIELD_BANK_BRANCH = "bank_branch";
    public static final String FIELD_INTER_BANK_NO = "inter_bank_no";
    public static final String FIELD_BANK_ACCOUNT_NAME = "bank_account_name";
    public static final String FIELD_BANK_ACCOUNT_NO = "bank_account_no";
    public static final String FIELD_CNY = "cny";
    public static final String FIELD_BANK_ACCOUNT_TYPE = "bank_account_type";
    public static final String FIELD_PAYMENT_CHANNEL = "payment_channel";
    public static final String FIELD_PAYER_NAME = "payer_name";
    public static final String FIELD_PAYMENT_BANK = "payment_bank";
    public static final String FIELD_PAYMENT_BANK_ACCOUNT_NO = "payment_bank_account_no";
    public static final String FIELD_PAYMENT_BANK_BRANCH_CODE = "payment_bank_branch_code";
    public static final String FIELD_RECEIPT = "receipt";
    public static final String FIELD_BILL_CHARGE_SUBJECT = "bill_charge_subject";
    public static final String FIELD_CHARGE_SUBJECT_BEGIN_DATE = "charge_subject_begin_date";
    public static final String FIELD_CHARGE_SUBJECT_END_DATE = "charge_subject_end_date";
    public static final String FIELD_SHOULD_PAY_AMOUNT = "should_pay_amount";
    public static final String FIELD_PAYED_AMOUNT = "payed_amount";
    public static final String FIELD_PAY_CHANNEL = "pay_channel";
    public static final String FIELD_PAY_TIME = "pay_time";
    public static final String FIELD_POS_DEVICE_ID = "pos_device_id";
    public static final String FIELD_RECONCILIATION_RESULT = "reconciliation_result";
    public static final String FIELD_CONTRACT_ID = "contract_id";
    public static final String FIELD_BILL_ID = "bill_id";
    public static final String FIELD_VOUCHER_STATUS = "voucher_status";
    public static final String FIELD_ACCOUNTING_MONTH = "accounting_month";
    public static final String FIELD_PREPAYMENT_OFFSET_CODE = "prepayment_offset_code";
    public static final String FIELD_CURRENT = "current";
    public static final String FIELD_SIZE = "size";
    public static final String FIELD_FULL_PAGE = "full_page";
    public static final String FIELD_EXT1 = "ext1";
    public static final String FIELD_EXT2 = "ext2";
    public static final String FIELD_EXT3 = "ext3";
    public static final String FIELD_EXT4 = "ext4";
    public static final String FIELD_EXT5 = "ext5";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
                                @TableId(value = "collection_id", type = IdType.ASSIGN_UUID)
                                  private String collectionId;

    /**
     * 收款编号
     */
    @ApiModelProperty(value = "收款编号--入库时是我们自己的分布式id")
                            private String collectionNo;

    /**
     * 业财返回的收款单ID
     */
    @ApiModelProperty(value = "业财返回的收款单ID")
                            private String receiptNo;

    /**
     * 是否开具电子凭证
     */
    @ApiModelProperty(value = "是否开具电子凭证")
                            private String electronicVoucher;

    /**
     * 现金盘点存款状态
     */
    @ApiModelProperty(value = "现金盘点存款状态")
                            private String depositStatus;

    /**
     * 关联账单
     */
    @ApiModelProperty(value = "关联账单")
                            private String billNo;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
                            private String billCycle;

    /**
     * 剩余应缴金额
     */
    @ApiModelProperty(value = "剩余应缴金额")
                            private BigDecimal remainingAmountPayable;

    /**
     * 当前页未存款总额
     */
    @ApiModelProperty(value = "当前页未存款总额")
                            private BigDecimal totalUndeposits;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 小区/楼宇名称
     */
    @ApiModelProperty(value = "小区/楼宇名称")
                            private String residentialQuartersName;

    /**
     * 楼栋
     */
    @ApiModelProperty(value = "楼栋")
                            private String buildingNo;

    /**
     * 单元
     */
    @ApiModelProperty(value = "单元")
                            private String unitNo;

    /**
     * 所在楼层
     */
    @ApiModelProperty(value = "所在楼层")
                            private String floorNo;

    /**
     * 门牌号
     */
    @ApiModelProperty(value = "门牌号")
                            private String roomNo;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
                            private String address;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String houseName;

    /**
     * 租户/组织名称
     */
    @ApiModelProperty(value = "租户/组织名称")
                            private String tenantName;



    /**
     * 客户证件号码
     */
    @ApiModelProperty(value = "客户证件号码")
                            private String certNo;

    /**
     * 客户联系电话
     */
    @ApiModelProperty(value = "客户联系电话")
                            private String tenantMobile;

    /**
     * 制单人
     */
    @ApiModelProperty(value = "制单人")
                            private String operatorName;

    /**
     * 收款单唯一标识码
     */
    @ApiModelProperty(value = "收款单唯一标识码")
                            private String chargeCode;

    /**
     * 合同唯一标识码
     */
    @ApiModelProperty(value = "合同唯一标识码")
                            private String contractCode;

    /**
     * 收款单日期(支付时间)
     */
    @ApiModelProperty(value = "收款单日期(支付时间)")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date chargeDate;

    /**
     * 收款日期开始
     */
    @ApiModelProperty(value = "收款日期开始")
                            private String chargeDateStart;

    /**
     * 收款日期结束
     */
    @ApiModelProperty(value = "收款日期结束")
                            private String chargeDateEnd;

    /**
     * 收款单状态
     */
    @ApiModelProperty(value = "收款单状态")
                            private String chargeStatus;

    /**
     * 银行回单号
     */
    @ApiModelProperty(value = "银行回单号")
                            private String bankReceiptNo;

    /**
     * 银行回单日期
     */
    @ApiModelProperty(value = "银行回单日期")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date bankReceiptDate;

    /**
     * 收费科目
     */
    @ApiModelProperty(value = "收费科目")
                            private String chargeSubject;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
                            private String paymentType;

    /**
     * 收款金额
     */
    @ApiModelProperty(value = "收款金额")
                            private BigDecimal chargeMoney;

    /**
     * 手续费金额
     */
    @ApiModelProperty(value = "手续费金额")
                            private BigDecimal serFee;

    /**
     * 收款单对账状态
     */
    @ApiModelProperty(value = "收款单对账状态")
                            private String reconciliationStatus;

    /**
     * 是否进行预交冲抵
     */
    @ApiModelProperty(value = "是否进行预交冲抵")
                            private String isPrePaymentOffset;

    /**
     * 收款银行
     */
    @ApiModelProperty(value = "收款银行")
                            private String bank;

    /**
     * 收款银行支行
     */
    @ApiModelProperty(value = "收款银行支行")
                            private String bankBranch;

    /**
     * 收款银行联号
     */
    @ApiModelProperty(value = "收款银行联号")
                            private String interBankNo;

    /**
     * 收款银行户名
     */
    @ApiModelProperty(value = "收款银行户名")
                            private String bankAccountName;

    /**
     * 收款银行账号
     */
    @ApiModelProperty(value = "收款银行账号")
                            private String bankAccountNo;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
                            private String cny;

    /**
     * 账户性质
     */
    @ApiModelProperty(value = "账户性质")
                            private String bankAccountType;

    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
                            private String paymentChannel;

    /**
     * 缴费人姓名
     */
    @ApiModelProperty(value = "缴费人姓名")
                            private String payerName;

    /**
     * 缴费银行
     */
    @ApiModelProperty(value = "缴费银行")
                            private String paymentBank;

    /**
     * 缴费银行账户
     */
    @ApiModelProperty(value = "缴费银行账户")
                            private String paymentBankAccountNo;

    /**
     * 缴费银行支行编码
     */
    @ApiModelProperty(value = "缴费银行支行编码")
                            private String paymentBankBranchCode;

    /**
     * 主收款单ID
     */
    @ApiModelProperty(value = "主收款单ID")
                            private String receipt;

    /**
     * 账单对应的收费科目
     */
    @ApiModelProperty(value = "账单对应的收费科目")
                            private String billChargeSubject;

    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
                            private String chargeSubjectBeginDate;

    /**
     * 收费科目终止日期
     */
    @ApiModelProperty(value = "收费科目终止日期")
                            private String chargeSubjectEndDate;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
                            private BigDecimal shouldPayAmount;

    /**
     * 实缴金额
     */
    @ApiModelProperty(value = "实缴金额")
                            private BigDecimal payedAmount;

    /**
     * 支付方式2
     */
    @ApiModelProperty(value = "支付方式2")
                            private String payChannel;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
                            private String payTime;

    /**
     * POS设备号
     */
    @ApiModelProperty(value = "POS设备号")
                            private String posDeviceId;

    /**
     * 对账结果
     */
    @ApiModelProperty(value = "对账结果")
                            private String reconciliationResult;

    /**
     * 合同ID(查询条件)
     */
    @ApiModelProperty(value = "合同ID(查询条件)")
                            private String contractId;

    /**
     * 账单ID(查询条件)
     */
    @ApiModelProperty(value = "账单ID(查询条件)")
                            private String billId;

    /**
     * 收款单是否生成正式凭证(查询条件)
     */
    @ApiModelProperty(value = "收款单是否生成正式凭证(查询条件)")
                            private String voucherStatus;

    /**
     * 收款单会计月(查询条件)
     */
    @ApiModelProperty(value = "收款单会计月(查询条件)")
                            private String accountingMonth;

    /**
     * 预交冲抵单唯一标识码(查询条件)
     */
    @ApiModelProperty(value = "预交冲抵单唯一标识码(查询条件)")
                            private String prepaymentOffsetCode;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
                            private Integer current;

    /**
     * 每页显示多少条
     */
    @ApiModelProperty(value = "每页显示多少条")
                            private Integer size;

    /**
     * 全量分页标识
     */
    @ApiModelProperty(value = "全量分页标识")
                            private String fullPage;

    /**
     * ext1
     */
    @ApiModelProperty(value = "ext1")
                            private String ext1;

    /**
     * ext2
     */
    @ApiModelProperty(value = "ext2")
                            private String ext2;

    /**
     * ext3
     */
    @ApiModelProperty(value = "ext3")
                            private String ext3;

    /**
     * ext4
     */
    @ApiModelProperty(value = "ext4")
                            private String ext4;

    /**
     * ext5
     */
    @ApiModelProperty(value = "ext5")
                            private String ext5;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantCode;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    /**
     * @return 主键id
     */
    public String getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(String collectionId) {
        this.collectionId = collectionId;
    }

    /**
     * @return 收款编号
     */
    public String getCollectionNo() {
        return collectionNo;
    }

    public void setCollectionNo(String collectionNo) {
        this.collectionNo = collectionNo;
    }

    /**
     * @return 业财返回的收款单ID
     */
    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    /**
     * @return 是否开具电子凭证
     */
    public String getElectronicVoucher() {
        return electronicVoucher;
    }

    public void setElectronicVoucher(String electronicVoucher) {
        this.electronicVoucher = electronicVoucher;
    }

    /**
     * @return 现金盘点存款状态
     */
    public String getDepositStatus() {
        return depositStatus;
    }

    public void setDepositStatus(String depositStatus) {
        this.depositStatus = depositStatus;
    }

    /**
     * @return 关联账单
     */
    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    /**
     * @return 账单周期
     */
    public String getBillCycle() {
        return billCycle;
    }

    public void setBillCycle(String billCycle) {
        this.billCycle = billCycle;
    }

    /**
     * @return 剩余应缴金额
     */
    public BigDecimal getRemainingAmountPayable() {
        return remainingAmountPayable;
    }

    public void setRemainingAmountPayable(BigDecimal remainingAmountPayable) {
        this.remainingAmountPayable = remainingAmountPayable;
    }

    /**
     * @return 当前页未存款总额
     */
    public BigDecimal getTotalUndeposits() {
        return totalUndeposits;
    }

    public void setTotalUndeposits(BigDecimal totalUndeposits) {
        this.totalUndeposits = totalUndeposits;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 小区/楼宇名称
     */
    public String getResidentialQuartersName() {
        return residentialQuartersName;
    }

    public void setResidentialQuartersName(String residentialQuartersName) {
        this.residentialQuartersName = residentialQuartersName;
    }

    /**
     * @return 楼栋
     */
    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    /**
     * @return 单元
     */
    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    /**
     * @return 所在楼层
     */
    public String getFloorNo() {
        return floorNo;
    }

    public void setFloorNo(String floorNo) {
        this.floorNo = floorNo;
    }

    /**
     * @return 门牌号
     */
    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    /**
     * @return 房源地址
     */
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * @return 租户/组织名称
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 客户证件号码
     */
    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    /**
     * @return 客户联系电话
     */
    public String getTenantMobile() {
        return tenantMobile;
    }

    public void setTenantMobile(String tenantMobile) {
        this.tenantMobile = tenantMobile;
    }

    /**
     * @return 制单人
     */
    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    /**
     * @return 收款单唯一标识码
     */
    public String getChargeCode() {
        return chargeCode;
    }

    public void setChargeCode(String chargeCode) {
        this.chargeCode = chargeCode;
    }

    /**
     * @return 合同唯一标识码
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 收款单日期(支付时间)
     */
    public Date getChargeDate(){
        if(chargeDate!=null){
            return (Date)chargeDate.clone();
        }else{
            return null;
        }
    }

    public void setChargeDate(Date chargeDate) {
        if(chargeDate==null){
            this.chargeDate = null;
        }else{
            this.chargeDate = (Date)chargeDate.clone();
        }
    }

    /**
     * @return 收款日期开始
     */
    public String getChargeDateStart() {
        return chargeDateStart;
    }

    public void setChargeDateStart(String chargeDateStart) {
        this.chargeDateStart = chargeDateStart;
    }

    /**
     * @return 收款日期结束
     */
    public String getChargeDateEnd() {
        return chargeDateEnd;
    }

    public void setChargeDateEnd(String chargeDateEnd) {
        this.chargeDateEnd = chargeDateEnd;
    }

    /**
     * @return 收款单状态
     */
    public String getChargeStatus() {
        return chargeStatus;
    }

    public void setChargeStatus(String chargeStatus) {
        this.chargeStatus = chargeStatus;
    }

    /**
     * @return 银行回单号
     */
    public String getBankReceiptNo() {
        return bankReceiptNo;
    }

    public void setBankReceiptNo(String bankReceiptNo) {
        this.bankReceiptNo = bankReceiptNo;
    }

    /**
     * @return 银行回单日期
     */
    public Date getBankReceiptDate(){
        if(bankReceiptDate!=null){
            return (Date)bankReceiptDate.clone();
        }else{
            return null;
        }
    }

    public void setBankReceiptDate(Date bankReceiptDate) {
        if(bankReceiptDate==null){
            this.bankReceiptDate = null;
        }else{
            this.bankReceiptDate = (Date)bankReceiptDate.clone();
        }
    }

    /**
     * @return 收费科目
     */
    public String getChargeSubject() {
        return chargeSubject;
    }

    public void setChargeSubject(String chargeSubject) {
        this.chargeSubject = chargeSubject;
    }

    /**
     * @return 支付方式
     */
    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    /**
     * @return 收款金额
     */
    public BigDecimal getChargeMoney() {
        return chargeMoney;
    }

    public void setChargeMoney(BigDecimal chargeMoney) {
        this.chargeMoney = chargeMoney;
    }

    /**
     * @return 手续费金额
     */
    public BigDecimal getSerFee() {
        return serFee;
    }

    public void setSerFee(BigDecimal serFee) {
        this.serFee = serFee;
    }

    /**
     * @return 收款单对账状态
     */
    public String getReconciliationStatus() {
        return reconciliationStatus;
    }

    public void setReconciliationStatus(String reconciliationStatus) {
        this.reconciliationStatus = reconciliationStatus;
    }

    /**
     * @return 是否进行预交冲抵
     */
    public String getIsPrePaymentOffset() {
        return isPrePaymentOffset;
    }

    public void setIsPrePaymentOffset(String isPrePaymentOffset) {
        this.isPrePaymentOffset = isPrePaymentOffset;
    }

    /**
     * @return 收款银行
     */
    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    /**
     * @return 收款银行支行
     */
    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
    }

    /**
     * @return 收款银行联号
     */
    public String getInterBankNo() {
        return interBankNo;
    }

    public void setInterBankNo(String interBankNo) {
        this.interBankNo = interBankNo;
    }

    /**
     * @return 收款银行户名
     */
    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    /**
     * @return 收款银行账号
     */
    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    /**
     * @return 币种
     */
    public String getCny() {
        return cny;
    }

    public void setCny(String cny) {
        this.cny = cny;
    }

    /**
     * @return 账户性质
     */
    public String getBankAccountType() {
        return bankAccountType;
    }

    public void setBankAccountType(String bankAccountType) {
        this.bankAccountType = bankAccountType;
    }

    /**
     * @return 支付渠道
     */
    public String getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(String paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    /**
     * @return 缴费人姓名
     */
    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    /**
     * @return 缴费银行
     */
    public String getPaymentBank() {
        return paymentBank;
    }

    public void setPaymentBank(String paymentBank) {
        this.paymentBank = paymentBank;
    }

    /**
     * @return 缴费银行账户
     */
    public String getPaymentBankAccountNo() {
        return paymentBankAccountNo;
    }

    public void setPaymentBankAccountNo(String paymentBankAccountNo) {
        this.paymentBankAccountNo = paymentBankAccountNo;
    }

    /**
     * @return 缴费银行支行编码
     */
    public String getPaymentBankBranchCode() {
        return paymentBankBranchCode;
    }

    public void setPaymentBankBranchCode(String paymentBankBranchCode) {
        this.paymentBankBranchCode = paymentBankBranchCode;
    }

    /**
     * @return 主收款单ID
     */
    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    /**
     * @return 账单对应的收费科目
     */
    public String getBillChargeSubject() {
        return billChargeSubject;
    }

    public void setBillChargeSubject(String billChargeSubject) {
        this.billChargeSubject = billChargeSubject;
    }

    /**
     * @return 收费科目起始日期
     */
    public String getChargeSubjectBeginDate() {
        return chargeSubjectBeginDate;
    }

    public void setChargeSubjectBeginDate(String chargeSubjectBeginDate) {
        this.chargeSubjectBeginDate = chargeSubjectBeginDate;
    }

    /**
     * @return 收费科目终止日期
     */
    public String getChargeSubjectEndDate() {
        return chargeSubjectEndDate;
    }

    public void setChargeSubjectEndDate(String chargeSubjectEndDate) {
        this.chargeSubjectEndDate = chargeSubjectEndDate;
    }

    /**
     * @return 应缴金额
     */
    public BigDecimal getShouldPayAmount() {
        return shouldPayAmount;
    }

    public void setShouldPayAmount(BigDecimal shouldPayAmount) {
        this.shouldPayAmount = shouldPayAmount;
    }

    /**
     * @return 实缴金额
     */
    public BigDecimal getPayedAmount() {
        return payedAmount;
    }

    public void setPayedAmount(BigDecimal payedAmount) {
        this.payedAmount = payedAmount;
    }

    /**
     * @return 支付方式2
     */
    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    /**
     * @return 支付时间
     */
    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    /**
     * @return POS设备号
     */
    public String getPosDeviceId() {
        return posDeviceId;
    }

    public void setPosDeviceId(String posDeviceId) {
        this.posDeviceId = posDeviceId;
    }

    /**
     * @return 对账结果
     */
    public String getReconciliationResult() {
        return reconciliationResult;
    }

    public void setReconciliationResult(String reconciliationResult) {
        this.reconciliationResult = reconciliationResult;
    }

    /**
     * @return 合同ID(查询条件)
     */
    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    /**
     * @return 账单ID(查询条件)
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 收款单是否生成正式凭证(查询条件)
     */
    public String getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(String voucherStatus) {
        this.voucherStatus = voucherStatus;
    }

    /**
     * @return 收款单会计月(查询条件)
     */
    public String getAccountingMonth() {
        return accountingMonth;
    }

    public void setAccountingMonth(String accountingMonth) {
        this.accountingMonth = accountingMonth;
    }

    /**
     * @return 预交冲抵单唯一标识码(查询条件)
     */
    public String getPrepaymentOffsetCode() {
        return prepaymentOffsetCode;
    }

    public void setPrepaymentOffsetCode(String prepaymentOffsetCode) {
        this.prepaymentOffsetCode = prepaymentOffsetCode;
    }

    /**
     * @return 页码
     */
    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    /**
     * @return 每页显示多少条
     */
    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    /**
     * @return 全量分页标识
     */
    public String getFullPage() {
        return fullPage;
    }

    public void setFullPage(String fullPage) {
        this.fullPage = fullPage;
    }

    /**
     * @return ext1
     */
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * @return ext2
     */
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * @return ext3
     */
    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    /**
     * @return ext4
     */
    public String getExt4() {
        return ext4;
    }

    public void setExt4(String ext4) {
        this.ext4 = ext4;
    }

    /**
     * @return ext5
     */
    public String getExt5() {
        return ext5;
    }

    public void setExt5(String ext5) {
        this.ext5 = ext5;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmCollectionV2Entity{" +
            "collectionId=" + collectionId +
            ", collectionNo=" + collectionNo +
            ", receiptNo=" + receiptNo +
            ", electronicVoucher=" + electronicVoucher +
            ", depositStatus=" + depositStatus +
            ", billNo=" + billNo +
            ", billCycle=" + billCycle +
            ", remainingAmountPayable=" + remainingAmountPayable +
            ", totalUndeposits=" + totalUndeposits +
            ", projectName=" + projectName +
            ", residentialQuartersName=" + residentialQuartersName +
            ", buildingNo=" + buildingNo +
            ", unitNo=" + unitNo +
            ", floorNo=" + floorNo +
            ", roomNo=" + roomNo +
            ", address=" + address +
            ", tenantName=" + tenantName +
            ", certNo=" + certNo +
            ", tenantMobile=" + tenantMobile +
            ", operatorName=" + operatorName +
            ", chargeCode=" + chargeCode +
            ", contractCode=" + contractCode +
            ", chargeDate=" + chargeDate +
            ", chargeDateStart=" + chargeDateStart +
            ", chargeDateEnd=" + chargeDateEnd +
            ", chargeStatus=" + chargeStatus +
            ", bankReceiptNo=" + bankReceiptNo +
            ", bankReceiptDate=" + bankReceiptDate +
            ", chargeSubject=" + chargeSubject +
            ", paymentType=" + paymentType +
            ", chargeMoney=" + chargeMoney +
            ", serFee=" + serFee +
            ", reconciliationStatus=" + reconciliationStatus +
            ", isPrePaymentOffset=" + isPrePaymentOffset +
            ", bank=" + bank +
            ", bankBranch=" + bankBranch +
            ", interBankNo=" + interBankNo +
            ", bankAccountName=" + bankAccountName +
            ", bankAccountNo=" + bankAccountNo +
            ", cny=" + cny +
            ", bankAccountType=" + bankAccountType +
            ", paymentChannel=" + paymentChannel +
            ", payerName=" + payerName +
            ", paymentBank=" + paymentBank +
            ", paymentBankAccountNo=" + paymentBankAccountNo +
            ", paymentBankBranchCode=" + paymentBankBranchCode +
            ", receipt=" + receipt +
            ", billChargeSubject=" + billChargeSubject +
            ", chargeSubjectBeginDate=" + chargeSubjectBeginDate +
            ", chargeSubjectEndDate=" + chargeSubjectEndDate +
            ", shouldPayAmount=" + shouldPayAmount +
            ", payedAmount=" + payedAmount +
            ", payChannel=" + payChannel +
            ", payTime=" + payTime +
            ", posDeviceId=" + posDeviceId +
            ", reconciliationResult=" + reconciliationResult +
            ", contractId=" + contractId +
            ", billId=" + billId +
            ", voucherStatus=" + voucherStatus +
            ", accountingMonth=" + accountingMonth +
            ", prepaymentOffsetCode=" + prepaymentOffsetCode +
            ", current=" + current +
            ", size=" + size +
            ", fullPage=" + fullPage +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", ext3=" + ext3 +
            ", ext4=" + ext4 +
            ", ext5=" + ext5 +
            ", delFlag=" + delFlag +
        "}";
    }
}