package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 推送的递增规则信息 vo实体类
 *
 * <AUTHOR>
 * @since 2023/9/1
 */
@Data
@ApiModel(value = "推送的递增规则信息", description = "推送的递增规则信息")
public class BbctIncreaseRuleVo extends McpBaseVo implements Serializable {

    /**
     * 递增规则ID
     */
    @ApiModelProperty(value = "递增规则ID")
    private Integer increaseRuleId;

    /**
     * 递增比例
     */
    @ApiModelProperty(value = "递增比例")
    private BigDecimal increaseProportion;

    /**
     * 递增金额
     */
    @ApiModelProperty(value = "递增金额")
    private BigDecimal increaseAmount;

    /**
     * 递增周期
     */
    @ApiModelProperty(value = "递增周期")
    private String increasePeriod;

    /**
     * 递增顺序
     */
    @ApiModelProperty(value = "递增顺序")
    private Integer increaseOrder;

    /**
     * 递增类型
     */
    @ApiModelProperty(value = "递增类型")
    private String increaseType;
}
