package com.bonc.ioc.bzf.business.invoice.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum InvoiceEnums {
    IS_DEFAULT_NO("0", "不是默认"),
    IS_DEFAULT_YES("1", "是默认"),

    INVOICE_KPLX_BLUE("0", "蓝字发票"),
    INVOICE_KPLX_RED("1", "红字发票"),

    REQUEST_TYPE_LIST("0", "列表"),
    REQUEST_TYPE_DETAIL("1", "详情"),

    //发票开具操作类型
    OPERATIONTYPE_BATCH("01", "批量"),
    OPERATIONTYPE_MERGE("02", " 合并"),

    DFFJXQTYPE_DEFAULT("01", "默认"),
    PDFFJXQTYPE_DETAIL("02", "展示明细"),

    SEND_TYPE_PHONE("0","短信"),
    SNED_TYPE_EMAIL("1","邮箱");

    private final String code;
    private final String msg;

}

