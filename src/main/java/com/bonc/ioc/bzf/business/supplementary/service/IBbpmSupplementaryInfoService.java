package com.bonc.ioc.bzf.business.supplementary.service;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryInfoEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 追加单表 服务类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
public interface IBbpmSupplementaryInfoService extends IMcpBaseService<BbpmSupplementaryInfoEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbpmSupplementaryInfoVo vo);

    /**
     * 新增追加单信息
     *
     * @param vo 追加单信息 vo实体
     * @return 主键id
     */
    String insertSupplementaryInfo(BbpmSupplementaryInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbpmSupplementaryInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param supplementaryId 需要删除的追加单id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String supplementaryId);

    /**
     * 删除追加单信息
     *
     * @param supplementaryId 追加单id
     */
    void removeSupplementaryInfo(String supplementaryId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param supplementaryIdList 需要删除的追加单id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> supplementaryIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的追加单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbpmSupplementaryInfoVo vo);

    /**
     * 更新追加单信息
     *
     * @param vo 追加单信息 vo实体
     */
    void updateSupplementaryInfo(BbpmSupplementaryInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的追加单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbpmSupplementaryInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的追加单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbpmSupplementaryInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的追加单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbpmSupplementaryInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param supplementaryId 需要查询的追加单id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    BbpmSupplementaryInfoVo selectByIdRecord(String supplementaryId);

    /**
     * 查询追加单信息
     *
     * @param supplementaryId 追加单id
     * @return 追加单信息
     */
    BbpmSupplementaryInfoVo selectSupplementaryInfo(String supplementaryId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbpmSupplementaryInfoPageResultVo>> selectByPageRecord(BbpmSupplementaryInfoPageVo vo);

    /**
     * 分页查询追加单列表
     *
     * @param vo 请求参数 vo实体
     * @return 追加单列表
     */
    PageResult<List<BbpmSupplementaryInfoPageResultVo>> selectSupplementaryInfoPage(BbpmSupplementaryInfoPageVo vo);

    /**
     * 提交
     *
     * @param supplementaryId 追加单id
     */
    void submit(String supplementaryId);

    /**
     * 撤回
     *
     * @param supplementaryId 追加单id
     */
    void cancel(String supplementaryId);

    /**
     * 处理审核
     *
     * @param vo 审批 vo实体
     */
    void dealApprove(BbpmApproveVo vo);

    /**
     * 分页查询追加单审批列表
     *
     * @param vo 请求参数 vo实体
     * @return 追加单列表
     */
    PageResult<List<BbpmSupplementaryInfoPageResultVo>> selectApproveInfoPage(BbpmSupplementaryInfoPageVo vo);

    /**
     * 追加单信息统计
     *
     * @return 追加单信息统计
     */
    BbpmSupplementaryStatisticVo supplementaryInfoStatistic();

    /**
     * 追加单审批信息统计
     *
     * @return 追加单审批信息统计
     */
    BbpmSupplementaryStatisticVo approveInfoStatistic();

    /**
     * 获取操作记录列表
     *
     * @param supplementaryId 追加单id
     * @return 操作记录列表
     */
    List<BbpmApproveDetailInfoVo> operateDetailInfo(String supplementaryId);

    /**
     * 获取账单明细
     *
     * @param vo 请求参数 vo实体
     * @return 账单明细
     */
    Map<String, List<BbpmSupplementaryPreviewBillVo>> getPreviewBill(BbpmSupplementaryInfoVo vo);

    /**
     * 获取导出数据
     *
     * @param vo 请求参数 vo实体
     * @return 导出数据
     */
    List<BbpmSupplementaryExportVo> getExportData(BbpmSupplementaryInfoPageVo vo);
}
