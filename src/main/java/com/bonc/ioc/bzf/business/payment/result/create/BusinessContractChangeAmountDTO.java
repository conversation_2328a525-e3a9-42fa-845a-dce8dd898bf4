package com.bonc.ioc.bzf.business.payment.result.create;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * BusinessContractChangeAmount(商业合同租金标准、缩租)
 */
@Data
public class BusinessContractChangeAmountDTO implements Serializable {

    @ApiModelProperty(value = "变更合同号")
    private String contractCode;	//变更合同号 String 是

    @ApiModelProperty(value = "协议号")
    private String agreementCode;	//协议号	String 是 保证唯一

    @ApiModelProperty(value = "变更账期类型")
    private String changeAccountingPeriodType;	//变更账期类型 String 是 01,合同起始日; 02,当期第一天; 03,下账期第一天;

    @ApiModelProperty(value = "申请试算日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date requestDate;  	//申请试算日期 LocalDate 是 进行试算并提交合同变更申请时间保持一致，防止试算与真实抵扣金额不一致

    @ApiModelProperty(value = "缩减商铺Id")
    private List<String> cutShortRoomList;	//缩减商铺Id	List<String> 否 需要缩减的商铺id

    @ApiModelProperty(value = "抵扣列表")
    private List<DeductionBill> deductionBillList;	//抵扣列表 List 盈余金额抵扣账单列表

    @ApiModelProperty(value = "商铺信息")
    private List<Room> roomList;	//商铺信息



}
