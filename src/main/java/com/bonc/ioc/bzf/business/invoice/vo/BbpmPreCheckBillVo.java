package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发票开具--主类
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value="BbpmInvoiceIssueVo对象", description="发票开具--主类")
@Data
public class BbpmPreCheckBillVo extends McpBaseVo implements Serializable{
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "抬头类型01:企业单位 02:个人/非企业单位")
    private String titleType;
    @ApiModelProperty(value = "开票账单信息列表")
    private List<InvoiceBillVo> invoiceBillList;

    @ApiModelProperty(value = "项目业态01公租房02保租房03商业04共有产权房05仓储06车位")
    private String projectFormat;
    @ApiModelProperty(value = "签约类型01:散租02:趸租大合同03:趸租管理协议04:其他")
    private String signType;


}
