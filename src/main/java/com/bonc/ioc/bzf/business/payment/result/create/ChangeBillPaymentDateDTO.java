package com.bonc.ioc.bzf.business.payment.result.create;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * changeBillPaymentDateDTOList	变更合同的账单列表
 */
@Data
public class ChangeBillPaymentDateDTO implements Serializable {
    @ApiModelProperty(value = "账单唯一识别码")
    private String billCode;//账单唯一识别码	String

    @ApiModelProperty(value = "变更日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date paymentDate;

}
