package com.bonc.ioc.bzf.business.payment.result;

import com.bonc.ioc.bzf.business.payment.result.create.ChargeSubjectParamsRequest;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 公租房散租五期合同生成账单 主类
 */
@Data
@Builder
public class FivePhaseRequest {

    private String parentContractId;       //	 趸租大合同id   String 64 原趸租大合同id（有就传）
    private String originalContractId;     //	 原合同ID 	  String 64 02变更非身份证号的基础信息(现在只有02这一种类型)
    private String contractStatus;         //	 五期合同状态   String 4

    @ApiModelProperty(value = "期限开始时间")
    @NotBlank(message = "期限开始时间",groups = {UpdateValidatorGroup.class})
    private String   periodBeginDate;        //	 期限开始时间   Date

    @ApiModelProperty(value = "期限结束时间")
    @NotBlank(message = "期限结束时间",groups = {UpdateValidatorGroup.class})
    private String   periodEndDate;          //	 期限结束时间   Date

    private String projectId;              //	 项目ID        String 64  多个参数编码以英文逗号分隔
    private String houseId;                //	 房屋ID        String 64  多个参数编码以英文逗号分隔

    //    计费科目列表
    private List<ChargeSubjectParamsRequest> chargeSubjectList;

}
