package com.bonc.ioc.bzf.business.reminder.service;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendMainLogEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 催缴规则消息发送日志--子表  服务类
 *
 * <AUTHOR>
 * @date 2023-08-09
 * @change 2023-08-09 by binghong.tang for init
 */
public interface IBbpmMessageSendMainLogService extends IMcpBaseService<BbpmMessageSendMainLogEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    String insertRecord(BbpmMessageSendMainLogVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmMessageSendMainLogVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param mainId 需要删除的唯一标识
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void removeByIdRecord(String mainId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param mainIdList 需要删除的唯一标识
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> mainIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void updateByIdRecord(BbpmMessageSendMainLogVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmMessageSendMainLogVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void saveByIdRecord(BbpmMessageSendMainLogVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmMessageSendMainLogVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param mainId 需要查询的唯一标识
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    BbpmMessageSendMainLogVo selectByIdRecord(String mainId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    PageResult<List<BbpmMessageSendMainLogPageResultVo>> selectByPageRecord(BbpmMessageSendMainLogPageVo vo);
}
