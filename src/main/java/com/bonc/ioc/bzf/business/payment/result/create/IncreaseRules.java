package com.bonc.ioc.bzf.business.payment.result.create;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncreaseRules	递增规则列表
 */
@Data
public class IncreaseRules implements Serializable {

    @ApiModelProperty(value = "递增规则ID")
    private Integer increaseRuleId; //递增规则ID	Int	否 从1开始递增

    @ApiModelProperty(value = "递增比例")
    private BigDecimal increaseProportion;	//递增比例 BigDecimal 否 5,4 按照比例递增，与下面的参数二选一

    @ApiModelProperty(value = "递增金额")
    private BigDecimal increaseAmount;	//递增金额 BigDecimal 否 按照金额递增，与上面的参数二选一

    @ApiModelProperty(value = "递增周期")
    private String increasePeriod;	//递增周期 String 否 01月02季03半年04年

    @ApiModelProperty(value = "递增顺序")
    private Integer increaseOrder;	//递增顺序 Int 否 如果上面的递增周期是年，则1表示每/第一年 2表示每/第二年 3表示每/第三年

    @ApiModelProperty(value = "递增类型")
    private String increaseType;	//递增类型 String 否 01每 02第

}
