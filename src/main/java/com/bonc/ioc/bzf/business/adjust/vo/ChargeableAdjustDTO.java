package com.bonc.ioc.bzf.business.adjust.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ChargeableAdjustDTO implements java.io.Serializable{

    @ApiModelProperty(value = "0:抵扣，1:退款（试算时可不传）")
    private String deductionType;

    @ApiModelProperty(value = "退款渠道 0:线下退款，1:线上退款（试算时可不传）")
    private String refundChannel;


    @ApiModelProperty(value = "调整账单列表")
    private List<AdjustBillDTO> adjustBillDTOList;
}
