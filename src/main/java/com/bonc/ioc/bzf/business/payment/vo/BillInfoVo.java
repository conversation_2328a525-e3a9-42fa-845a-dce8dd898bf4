package com.bonc.ioc.bzf.business.payment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BillInfoVo implements java.io.Serializable {

    /**
     * 缴费次数
     */
    @ApiModelProperty(value = "缴费次数")
    private String chargePeriod;

    /**
     * 缴费开始时间
     */
    @ApiModelProperty(value = "缴费开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date chargeStartDate;

    /**
     * 缴费结束时间
     */
    @ApiModelProperty(value = "缴费结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date chargeEndDate;

    /**
     * 天数
     */
    @ApiModelProperty(value = "天数")
    private Integer days;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
    private BigDecimal payableMoney;

    /**
     * 增值税率
     */
    @ApiModelProperty(value = "增值税率")
    private BigDecimal taxRate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal excludingRateMoney;

    /**
     * 增值税额
     */
    @ApiModelProperty(value = "增值税额")
    private BigDecimal rateMoney;

    /**
     * 日租金
     */
    @ApiModelProperty(value = "日租金")
    private BigDecimal dayMoney;

    /**
     * 房源名称
     */
    @ApiModelProperty(value = "房源名称")
    private String houseName;

    /**
     * 房屋ID
     */
    @ApiModelProperty(value = "房屋ID")
    private String houseId;

    /**
     * 应缴日期
     */
    @ApiModelProperty(value = "应缴日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payableDate;

    /**
     * 待缴金额
     */
    @ApiModelProperty(value = "待缴金额")
    private BigDecimal toBePaidMoney;

    /**
     * 收费周期
     */
    @ApiModelProperty(value = "收费周期")
    private String chargeCycleStr;

    /**
     * 支付状态
     */
    @ApiModelProperty(value = "支付状态")
    private String paymentStatusStr;

    /**
     * 缴费状态
     */
    @ApiModelProperty(value = "缴费状态")
    private String chargeStatusStr;

    /**
     * 账单状态
     */
    @ApiModelProperty(value = "账单状态")
    private String statusStr;

    /**
     * 已缴金额
     */
    @ApiModelProperty(value = "已缴金额")
    private BigDecimal paidInMoney;

    /**
     * 计费科目
     */
    @ApiModelProperty(value = "计费科目")
    private String chargeSubjectNo;

    /**
     * 承租方
     */
    @ApiModelProperty(value = "承租方")
    private String lessee;

    /**
     * 循环单次
     */
    @ApiModelProperty(value = "循环单次")
    private String chargeTypeStr;

    /**
     * 账单类型
     */
    @ApiModelProperty(value = "账单类型")
    private String ownerStr;
}
