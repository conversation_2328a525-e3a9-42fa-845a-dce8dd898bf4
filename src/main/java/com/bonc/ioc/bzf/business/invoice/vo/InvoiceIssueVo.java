package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Setter;

import java.io.Serializable;
import java.util.Optional;

/**
 * 发票开具--开票信息
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value = "InvoiceIssueVo对象", description = "发票开具--开票信息")
@Data
public class InvoiceIssueVo extends McpBaseVo implements Serializable {
    @Setter
    @ApiModelProperty(value = "开票类型0：蓝字发票1：红字发票")
    private String kplx;
    @Setter
    @ApiModelProperty(value = "发票类型81：专票82：普票")
    private String fplxdm;
    @Setter
    @ApiModelProperty(value = "购买方纳税人识别号")
    private String gmfNsrsbh;
    @Setter
    @ApiModelProperty(value = "购买方名称")
    private String gmfMc;
    @Setter
    @ApiModelProperty(value = "购买方地址")
    private String gmfDz;
    @Setter
    @ApiModelProperty(value = "购买方电话")
    private String gmfDh;
    @Setter
    @ApiModelProperty(value = "购买方银行账号")
    private String gmfYhzh;
    @Setter
    @ApiModelProperty(value = "购买方银行名称")
    private String gmfYhmc;
    @Setter
    @ApiModelProperty(value = "消费者邮箱号")
    private String gmfDzyx;
    @Setter
    @ApiModelProperty(value = "消费者手机号")
    private String gmfSjh;
    @Setter
    @ApiModelProperty(value = "销售方名称(开票方)")
    private String xsfMc;
    @Setter
    @ApiModelProperty(value = "房源地址")
    private String houseAddr;
    @Setter
    @ApiModelProperty(value = "备注")
    private String bz;
    @Setter
    @ApiModelProperty(value = "抬头类型01:企业单位 02:个人/非企业单位")
    private String titleType;
    @Setter
    @ApiModelProperty(value = "开票人名称,取值为 系统操作人名称。")
    private String kprMc;
    @Setter
    @ApiModelProperty(value = "开票人代码 kprDm，暂时先去系统操作人id")
    private String kprDm;
    @Setter
    @ApiModelProperty(value = "租户ID")
    private String tenantCode;
    @Setter
    @ApiModelProperty(value = "租户名称")
    private String tenantName;
    @Setter
    @ApiModelProperty(value = "委托代理人")
    private String authorizedAgent ;
    @Setter
    @ApiModelProperty(value = "委托代理人电话")
    private String authorizedAgentMobile;
    @Setter
    @ApiModelProperty(value = "小区/楼宇名称")
    private String residentialQuartersName;
    @Setter
    @ApiModelProperty(value = "项目业态")
    private String projectFormat;
    @Setter
    @ApiModelProperty(value = "业务类别代码")
    private String ywlbDm;
    @Setter
    @ApiModelProperty(value = "签约类型（01-散租 02-趸租 03-其他）")
    private String signType;

    @ApiModelProperty(value = "pdf附件详情类型01：默认,只按照类别暂时总价,数量为1;02：展示明细,按照类别,单价维度展示明细,数量按照实际显示")
    private String pdfFjXqType;


    @ApiModelProperty(value = "不动产地址")
    private String  realEstateAddr;
    @ApiModelProperty(value = "不动产详细地址")
    private String realEstateDetailAddr;

    public void setRealEstateAddr(String realEstateAddr) {
        this.realEstateAddr = Optional.ofNullable(realEstateAddr).orElse("");
    }
    public void setFplxdm(String fplxdm) {
        this.fplxdm = Optional.ofNullable(fplxdm).orElse("");
    }

    public void setRealEstateDetailAddr(String realEstateDetailAddr) {
        this.realEstateDetailAddr = Optional.ofNullable(realEstateDetailAddr).orElse("");
    }

    public void setPdfFjXqType(String pdfFjXqType) {
        this.pdfFjXqType =  Optional.ofNullable(pdfFjXqType).orElse("");
    }

    public void setSignType(String signType) {
        this.signType = Optional.ofNullable(signType).orElse("");
    }

    public void setKplx(String kplx) {
        this.kplx = Optional.ofNullable(kplx).orElse("");
    }

    public void setGmfNsrsbh(String gmfNsrsbh) {
        this.gmfNsrsbh = Optional.ofNullable(gmfNsrsbh).orElse("");
    }

    public void setGmfMc(String gmfMc) {
        this.gmfMc = Optional.ofNullable(gmfMc).orElse("");
    }

    public void setGmfDz(String gmfDz) {
        this.gmfDz = Optional.ofNullable(gmfDz).orElse("");
    }

    public void setGmfDh(String gmfDh) {
        this.gmfDh = Optional.ofNullable(gmfDh).orElse("");
    }

    public void setGmfYhzh(String gmfYhzh) {
        this.gmfYhzh = Optional.ofNullable(gmfYhzh).orElse("");
    }

    public void setGmfYhmc(String gmfYhmc) {
        this.gmfYhmc = Optional.ofNullable(gmfYhmc).orElse("");
    }

    public void setGmfDzyx(String gmfDzyx) {
        this.gmfDzyx = Optional.ofNullable(gmfDzyx).orElse("");
    }

    public void setGmfSjh(String gmfSjh) {
        this.gmfSjh = Optional.ofNullable(gmfSjh).orElse("");
    }

    public void setXsfMc(String xsfMc) {
        this.xsfMc = Optional.ofNullable(xsfMc).orElse("");
    }

    public void setHouseAddr(String houseAddr) {
        this.houseAddr = Optional.ofNullable(houseAddr).orElse("");
    }

    public void setBz(String bz) {
        this.bz = Optional.ofNullable(bz).orElse("");
    }

    public void setTitleType(String titleType) {
        this.titleType = Optional.ofNullable(titleType).orElse("");
    }

    public void setKprMc(String kprMc) {
        this.kprMc = Optional.ofNullable(kprMc).orElse("");
    }

    public void setKprDm(String kprDm) {
        this.kprDm = Optional.ofNullable(kprDm).orElse("");
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = Optional.ofNullable(tenantCode).orElse("");
    }

    public void setTenantName(String tenantName) {
        this.tenantName = Optional.ofNullable(tenantName).orElse("");
    }

    public void setAuthorizedAgent(String authorizedAgent) {
        this.authorizedAgent = Optional.ofNullable(authorizedAgent).orElse("");
    }

    public void setAuthorizedAgentMobile(String authorizedAgentMobile) {
        this.authorizedAgentMobile = Optional.ofNullable(authorizedAgentMobile).orElse("");
    }

    public void setResidentialQuartersName(String residentialQuartersName) {
        this.residentialQuartersName = Optional.ofNullable(residentialQuartersName).orElse("");
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = Optional.ofNullable(projectFormat).orElse("");
    }

    public void setYwlbDm(String ywlbDm) {
        this.ywlbDm = Optional.ofNullable(ywlbDm).orElse("");
    }
}
