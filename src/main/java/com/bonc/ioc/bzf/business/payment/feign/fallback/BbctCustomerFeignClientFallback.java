package com.bonc.ioc.bzf.business.payment.feign.fallback;

import com.bonc.ioc.bzf.business.payment.feign.feign.BbctCustomerFeignClient;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.PathVariable;

public class BbctCustomerFeignClientFallback implements BbctCustomerFeignClient {
    @Override
    public AppReply<String> getCustomerIdByUserId(String userId) {
        return null;
    }

    @Override
    public AppReply<String> getDepartmentName(@PathVariable("cid") String cid, @PathVariable("oid") String oid) {
        return null;
    }
}
