package com.bonc.ioc.bzf.business.reminder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 3.36. 查询未缴费账单列表请求参数
 */
@Data
public class ListByPayableDateVo {
    @ApiModelProperty(value = "项目ID")
    private String projectId;//	项目ID	String	是	64	业务中台ID
    @ApiModelProperty(value = "合同编码")
    private String contractCode;//	合同编码	String	否	100	如果不传，查询项目下所有的账单数据
    @ApiModelProperty(value = "收费科目")
    private String chargeSubject;//	收费科目	String	否	38	01:房屋租金
//    @ApiModelProperty(value = "未缴清配置值(应缴日期天数)")
//    private Integer unpaidConfigValue;//payableDateDays; 	应缴日期天数	int	是	10	可输入正负数
    @ApiModelProperty(value = "合同类型01：散租合同 02：趸租大合同03：趸租管理协议")
    private String contractType;//	合同类型	String	是	2	01：散租合同 02：趸租大合同03：趸租管理协议07:商业
    @ApiModelProperty(value = "业务类型01：公租房02：保租房")
    private String businessType;//	业务类型	String	是	2	01：公租房02：保租房03:商业04:共有产权房05:仓储06:车位
    @ApiModelProperty(value = "账单类别01：企业 02：个人")
    private String owner;//	账单类别	String	否	2	01：企业 02：个人
//    @ApiModelProperty(value = "未对平配置值")
//    private Integer unevenConfigValue;//未对平配置值	int	是	10	可输入正负数

    @ApiModelProperty(value = "应缴费日期")
    private String payableDate;

    @ApiModelProperty(value = "收款日期(缴费日期)")
    private String chargeDate;// chargeDate	  收款日期	String		128	  缴费日期



}
