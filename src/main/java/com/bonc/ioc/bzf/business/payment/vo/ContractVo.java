package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(value="ContractVo对象", description="租户合同集合")
@Data
public class ContractVo implements Serializable{
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "合同ID")
    private String contractId;
}
