package com.bonc.ioc.bzf.business.adjust.vo;

import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class ChargeableAdjustBillVO implements java.io.Serializable{

    @ApiModelProperty(value = "账单周期")
    private String chargePeriod;

    @ApiModelProperty(value = "房源地址")
    private String roomAddress;

    @ApiModelProperty(value = "收费科目（费用项目）")
    @McpDictPoint(dictCode = "BILLING_YC_BILLCHARGESUBJECT", overTransCopyTo = "chargeSubjectName")
    private String chargeSubject;

    @ApiModelProperty(value = "费用项目名称")
    private String chargeSubjectName;

    @ApiModelProperty(value = "账单类别")
    private String owner;

    @ApiModelProperty(value = "应缴金额")
    private BigDecimal payableMoney;

    @ApiModelProperty(value = "实缴金额")
    private BigDecimal paidInMoney;

    @ApiModelProperty(value = "待缴金额")
    private BigDecimal toBePaidMoney;

    @ApiModelProperty(value = "账单缴费状态")
    private String chargeStatus;

    @ApiModelProperty(value = "账单状态")
    private String billStatus;

    @ApiModelProperty(value = "调整后应缴金额")
    private BigDecimal adjustPayableMoney;

    @ApiModelProperty(value = "调整后实缴金额")
    private BigDecimal adjustPaidInMoney;

    @ApiModelProperty(value = "调整后待缴金额")
    private BigDecimal adjustToBePaidMoney;

    @ApiModelProperty(value = "调整后账单缴费状态")
    private String adjustChargeStatus;

    @ApiModelProperty(value = "调整后账单状态")
    private String adjustBillStatus;

    @ApiModelProperty(value = "调整金额")
    private BigDecimal adjustMoney;

    @ApiModelProperty(value = "应退应抵金额")
    private BigDecimal rentingOutMoney;

    @ApiModelProperty(value = "账单号")
    private Long billCode;

    @ApiModelProperty(value = "转入金额")
    private Long deductionMoney;

    @ApiModelProperty(value = "转出金额")
    private Long inDeductionMoney;

    @ApiModelProperty(value = "调整后转入金额")
    private Long adjustDeductionMoney;

    @ApiModelProperty(value = "调整后转出金额")
    private Long adjustInDeductionMoney;

}
