package com.bonc.ioc.bzf.business.payment.utils;

import javax.activation.DataSource;
import java.io.*;
import java.util.List;

/**
 * 发送邮件接口
 *
 * @version v1.0
 * @create <PERSON><PERSON>hunyu
 * @date 2022/8/6 16:36
 */
public interface IEmailUtile {

    /**
     * 发送邮件
     *
     * @creator <PERSON><PERSON><PERSON><PERSON>
     * @date 2022/8/6 16:38
     * @createDescribe
     *
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     *
     * @param mailHost                  邮件服务器域名
     * @param mailProtocol              邮件服务器协议
     * @param sendEmailAccountNumber    发送着帐号
     * @param receiveEmailAccountNumber 接收者帐号
     * @param mailTittle                邮件标题
     * @param mailText                  邮件标题
     * @param photos                    图片集合
     * @param files                     附件路径路径集合
     * @return
     * @exception
     */
    void sendEmail(String mailHost,
                   String mailProtocol,
                   String sendEmailAccountNumber,
                   String sendEmailPassword,
                   String receiveEmailAccountNumber,
                   String mailTittle,
                   String mailText,
                   List<Photo> photos,
                   List<File> files) throws Exception;



    class Photo{
        private String name;
        private String url;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

     class File{

        private String name;

        private byte[] bytes;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public byte[] getBytes() {
            return bytes;
        }

        public void setBytes(byte[] bytes) {
            this.bytes = bytes;
        }
    }

    class ByteArrayDataSource implements DataSource {

        private byte[] bytes;
        private String contentType = "application/octet-stream";
        private String name;

        public ByteArrayDataSource(byte[] bytes, String name) {
            this.bytes = bytes;
            this.name = name;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(this.bytes);
        }

        @Override
        public OutputStream getOutputStream() throws IOException {
            return new ByteArrayOutputStream();
        }

        @Override
        public String getContentType() {
            return this.contentType;
        }

        @Override
        public String getName() {
            return this.name;
        }
    }
}
