package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 五期合同房屋 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/25
 */
@Data
@ApiModel(value = "五期合同房屋", description = "五期合同房屋")
public class BillHouseVo extends McpBaseVo implements Serializable {

    /**
     * 房屋id
     */
    @ApiModelProperty(value = "房屋id")
    private String houseId;

    /**
     * 计费科目列表
     */
    @ApiModelProperty(value = "计费科目列表")
    private List<ChargeSubjectVo> chargeSubjectList;

}
