package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票红冲和开具
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value="BbpmInvoiceRedFlushAndIssueVo对象", description="发票红冲和开具")
@Data
public class BbpmInvoiceRedFlushAndIssueVo extends McpBaseVo implements Serializable{

    @ApiModelProperty(value = "发票红冲")
    private BbpmInvoiceRedFlushVo bbpmInvoiceRedFlushVo;

    @ApiModelProperty(value = "发票开具")
    private BbpmInvoiceIssueVo bbpmInvoiceIssueVo;

}
