package com.bonc.ioc.bzf.business.reminder.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 缴费提醒合并表集合 实体类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@ApiModel(value="BbpmReminderRulesMainAndSubAllVo对象", description="缴费提醒合并表集合")
@Data
public class BbpmReminderRulesMainAndSubAllVo extends McpBaseVo implements Serializable{


    @ApiModelProperty(value = "未缴/未足额缴纳")
    private BbpmReminderRulesMainAndSubVo unpaid;

    @ApiModelProperty(value = "已缴但未对平")
    private BbpmReminderRulesMainAndSubVo unequal;

}
