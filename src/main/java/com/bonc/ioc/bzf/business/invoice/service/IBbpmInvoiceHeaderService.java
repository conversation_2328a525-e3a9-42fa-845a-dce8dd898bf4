package com.bonc.ioc.bzf.business.invoice.service;

import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceHeaderEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 常用发票抬头信息表 服务类
 *
 * <AUTHOR>
 * @date 2023-05-05
 * @change 2023-05-05 by binghong.tang for init
 */
public interface IBbpmInvoiceHeaderService extends IMcpBaseService<BbpmInvoiceHeaderEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    String insertRecord(BbpmInvoiceHeaderVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmInvoiceHeaderVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param invoiceHeaderId 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    void removeByIdRecord(String invoiceHeaderId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param invoiceHeaderIdList 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> invoiceHeaderIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的常用发票抬头信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    void updateByIdRecord(BbpmInvoiceHeaderVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的常用发票抬头信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmInvoiceHeaderVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的常用发票抬头信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    void saveByIdRecord(BbpmInvoiceHeaderVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的常用发票抬头信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmInvoiceHeaderVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param invoiceHeaderId 需要查询的唯一标识符
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    BbpmInvoiceHeaderVo selectByIdRecord(String invoiceHeaderId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    PageResult<List<BbpmInvoiceHeaderPageResultVo>> selectByPageRecord(BbpmInvoiceHeaderPageVo vo);
}
