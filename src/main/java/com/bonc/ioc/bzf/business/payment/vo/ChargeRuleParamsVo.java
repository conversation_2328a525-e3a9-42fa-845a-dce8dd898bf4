package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 获取计费结果接口 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeRuleParamsVo {

	@ApiModelProperty(value = "服务类型")
	@NotBlank(message = "服务类型",groups = {UpdateValidatorGroup.class})
	private  String serviceType;

	@NotBlank(message = "收费科目编号",groups = {UpdateValidatorGroup.class})
	@ApiModelProperty(value = "收费科目编号")
	private String chargeSubjectNo;
}
