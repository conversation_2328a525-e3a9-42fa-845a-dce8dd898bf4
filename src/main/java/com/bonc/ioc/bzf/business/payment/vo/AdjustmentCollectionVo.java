package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value="AdjustmentCollectionVo对象", description="收款调整")
@Data
public class AdjustmentCollectionVo extends McpBasePageVo implements Serializable {

    @ApiModelProperty(value = "申请人编码")
    private String applicantCode;
    @ApiModelProperty(value = "申请人姓名")
    private String applicantName;
    @ApiModelProperty(value = "项目Id")
    private String projectId;
    @ApiModelProperty(value = "调整金额")
    private String adjustMoney;

    @ApiModelProperty(value = "附件信息")
    private List<AdjustmentFileVo> fileDatas;

    @ApiModelProperty(value = "被调整账单数据信息")
    private List<AdjustmentFromToVo> fromList;
    @ApiModelProperty(value = "调整至账单数据信息")
    private List<AdjustmentFromToVo> toList;


}
