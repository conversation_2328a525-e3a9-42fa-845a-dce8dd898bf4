package com.bonc.ioc.bzf.business.payment.feign.feign;

import com.bonc.ioc.bzf.business.payment.feign.fallback.MssFileFeignClientFallback;
import com.bonc.ioc.bzf.business.payment.vo.FIleResultVo;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Mcp sys server 系统管理
 *
 * <AUTHOR>
 * @date 2021/5/20 11:54
 * @change: 2021/5/20 11:54 by jin.xu for init
 */
@FeignClient(value = "mcp-file", fallback = MssFileFeignClientFallback.class,configuration = FeignExceptionConfiguration.class)
public interface MssFileFeignClient {

    @GetMapping("/mcp-file/downloadFileBase64")
    @FeignExceptionCheck
    AppReply<String> downloadFileBase64(@RequestParam("id") Integer id);

    /**
     * 通过用户ID查询文件信息
     *
     * @return com.bonc.ioc.common.util.AppReply
     * <AUTHOR>
     * @date 2022/08/17
     * @change 2022-/08/17 by 姚春雨 for init
     * @since 1.0.0
     */
    @GetMapping("/mcp-file/getFileInfo")
    @FeignExceptionCheck
    AppReply<FIleResultVo> getFileInfoById(@RequestParam(name = "id") Integer id);

}
