package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 消息模板参数表 实体类
 *
 * <AUTHOR>
 * @date 2022-08-03
 * @change 2022-08-03 by sqj for init
 */
@ApiModel(value="MessTemplateParamVo对象", description="消息模板参数表")
public class MessTemplateParamVo extends McpBaseVo implements Serializable{


    @NotBlank(message = "不能为空",groups = {UpdateValidatorGroup.class})
                                  private String templateParamId;

    /**
     * 参数内容，消息模板中保存的内容
     */
    @ApiModelProperty(value = "参数内容，消息模板中保存的内容，消息模板新增编辑时将内容中进行替换的参数，例：${customerName}")
                            private String paramContent;

    /**
     * 英文名称
     */
    @ApiModelProperty(value = "英文名称；例：customerName，发送消息接口调用时map中使用")
                            private String englishName;

    /**
     * 中文名称，系统页面上显示的名称
     */
    @ApiModelProperty(value = "中文名称，系统页面上显示的名称；例：客户名称")
                            private String chineseName;

    /**
     * 模板类型id
     */
    @ApiModelProperty(value = "模板类型id")
                            private String templateTypeId;

    /**
     * 删除标记 1=删除，0=未删除
     */
    @ApiModelProperty(value = "删除标记 1=删除，0=未删除")
                            private Integer isDelete;

    /**
     * @return 
     */
    public String getTemplateParamId() {
        return templateParamId;
    }

    public void setTemplateParamId(String templateParamId) {
        this.templateParamId = templateParamId;
    }

    /**
     * @return 参数内容，消息模板中保存的内容
     */
    public String getParamContent() {
        return paramContent;
    }

    public void setParamContent(String paramContent) {
        this.paramContent = paramContent;
    }

    /**
     * @return 英文名称
     */
    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    /**
     * @return 中文名称，系统页面上显示的名称
     */
    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    /**
     * @return 模板类型id
     */
    public String getTemplateTypeId() {
        return templateTypeId;
    }

    public void setTemplateTypeId(String templateTypeId) {
        this.templateTypeId = templateTypeId;
    }

    /**
     * @return 删除标记 1=删除，0=未删除
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "MessTemplateParamVo{" +
            "templateParamId=" + templateParamId +
            ", paramContent=" + paramContent +
            ", englishName=" + englishName +
            ", chineseName=" + chineseName +
            ", templateTypeId=" + templateTypeId +
            ", isDelete=" + isDelete +
        "}";
    }
}
