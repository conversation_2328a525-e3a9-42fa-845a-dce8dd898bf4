package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.BbpmBillCollectionRelationshipEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 账单与收款对应关系表v3.0 Mapper 接口
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Mapper
public interface BbpmBillCollectionRelationshipMapper extends McpBaseMapper<BbpmBillCollectionRelationshipEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     */
    List<BbpmBillCollectionRelationshipPageResultVo> selectByPageCustom(@Param("vo") BbpmBillCollectionRelationshipPageVo vo );
}
