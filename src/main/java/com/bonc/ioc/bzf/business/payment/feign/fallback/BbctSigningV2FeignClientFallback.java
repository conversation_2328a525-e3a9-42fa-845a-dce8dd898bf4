package com.bonc.ioc.bzf.business.payment.feign.fallback;


import com.bonc.ioc.bzf.business.payment.feign.feign.BbctSigningV2FeignCilent;
import com.bonc.ioc.bzf.business.payment.vo.ProjectVoV2;
import com.bonc.ioc.common.util.AppReply;

import java.util.List;

/**
 * 签约中心 feign补偿类
 *
 * <AUTHOR>
 * @since 2023/5/8
 */
public class BbctSigningV2FeignClientFallback implements BbctSigningV2FeignCilent {

    @Override
    public AppReply<List<ProjectVoV2>> selectProject() {
        return null;
    }

    @Override
    public AppReply<List<ProjectVoV2>> selectProjectByAllAuthority() {
        return null;
    }
}
