package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * 支付数据参数对象
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023/3/5 10:53
 */
@Data
@ApiModel(value = "PayDataParmsVo对象", description = "支付数据参数对象")
public class PayDataParmsVo {

    @ApiModelProperty(value = " 账单信息")
    private List<PayDataBillInfoParmsVo> billInfo;

    @ApiModelProperty(value = "支付方式(1:工行iphne；2:工行Android；23:微信APP；24:支付宝APP)")
    @NotBlank(message = "支付方式不能为空")
    @Max(value = 2,message = "支付方式长度不能超过2")
    private String type;

    @ApiModelProperty(value = "总金额（元）")
    @NotBlank(message = "总金额不能为空")
    @Max(value = 20,message = "总金额长度不能超过20")
    private String totalAmount;

    @ApiModelProperty(value = "勾选的主账单总数量")
    @NotBlank(message = "勾选的主账单总数量不能为空")
    @Max(value = 20,message = "勾选的主账单总数量长度不能超过20")
    private String totalNum;

    @ApiModelProperty(value = "用于工银e支付完成后回调到商户页面")
    private String returnUrl;

    @ApiModelProperty(value = "项目ID（业务中台）")
    @NotBlank(message = "项目ID不能为空")
    private String projectId;
}
