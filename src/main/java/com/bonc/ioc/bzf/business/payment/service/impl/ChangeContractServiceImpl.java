package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCashCollectionVoucherMapper;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCollectionMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmCollectionEntity;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.service.*;
import com.bonc.ioc.bzf.business.payment.utils.RedisDistributedId;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ChangeContractServiceImpl implements IChangeContractService {

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmCollectionMapper baseMapper;

    @Resource
    private BbpmCashCollectionVoucherMapper bbpmCashCollectionVoucherMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmCollectionService baseService;

    @Resource
    private IBbpmBillCollectionRelationshipService iBbpmBillCollectionRelationshipService;

    @Resource
    private McpDictSession mcpDictSession;

    @Autowired
    RedisDistributedId redisDistributedId;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IBbpmBillCollectionDetailsService iBbpmBillCollectionDetailsService;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Value("${export.maxSize}")
    private Integer exportMaxSize;

    @Resource
    private RestTemplateUtil restTemplateUtil ;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    @Resource
    private IBbpmBillManagementService iBbpmBillManagementService;

    /**
     * 散租1，趸租2，趸租管理协议
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String changeContractPersonal(BbsChangeRecordVo vo) {
        //原实体
        //CorporateCollectionVo corporateCollectionVo = vo.getCorporateCollectionVo();
        String code = "10000";
        if ("01".equals(vo.getSignType())) {
            ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                    .changeType("02")
                    .projectId(vo.getProjectId())
                    .baseInfoChangeDto(BaseInfoChangeDtoRequest.builder()
                            .beforeContractCode(vo.getContractCode())
                            .tenantId(vo.getCustomerNo())
                            .tenantName(vo.getCustomerName())
                            .tenantMobile(vo.getCustomerTel())
                            .tenantBankName(vo.getBankName())
                            .tenantBankCode(vo.getBankNameCode())
                            .tenantBankAccountNo(vo.getBankCard())
                            .tenantBankAccountName(vo.getCustomerName())
                            .agreementNo(vo.getBankAgreementNo())
                            .tenantSupplierNo(vo.getTenantSupplierNo())
                            .tenantSupplierName(vo.getTenantSupplierName())
                            .withholding(vo.getBankIsAgreement())
                            .build())
                    .build();
            code = updateContractPublic(changeContractRequest);

        } else if ("02".equals(vo.getSignType())) {
            String projectIds = vo.getProjectId();
            List<String> projectIdList = Arrays.asList(projectIds.split(","));

            for(String projectId : projectIdList) {
                System.out.println(projectId);
                ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                        .changeType("02")
                        .projectId(projectId)
                        .baseInfoChangeDto(BaseInfoChangeDtoRequest.builder()
                                /*.beforeContractCode(vo.getContractCode())*/
                                .beforeReletContractCode(vo.getContractCode())
                                .companyId(vo.getUnitId())
                                .companyName(vo.getUnitName())
                                .companySupplierNo(vo.getCompanySupplierNo())
                                .companySupplierName(vo.getCompanySupplierName())
                                .authorizedAgent(vo.getAuthorizedAgent())
                                .authorizedAgentMobile(vo.getAuthorizedAgentMobile())
                                .build())
                        .build();
                code = updateContractPublic(changeContractRequest);
            }

        } else if ("03".equals(vo.getSignType())) {
            ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                    .changeType("02")
                    .projectId(vo.getProjectId())
                    .baseInfoChangeDto(BaseInfoChangeDtoRequest.builder()
                            .beforeContractCode(vo.getContractCode()) // 协议编号
                            .tenantId(vo.getCustomerNo())//租户编号
                            .tenantName(vo.getCustomerName()) //租户名称
                            .tenantMobile(vo.getCustomerTel()) //租户电话
                            .tenantBankName(vo.getBankName()) // 开户银行名称
                            .tenantBankCode(vo.getBankNameCode()) // 开户银行编码
                            .tenantBankAccountNo(vo.getBankCard()) // 开户银行卡号
                            .tenantBankAccountName(vo.getCustomerName())
                            .agreementNo(vo.getBankAgreementNo())
                            .withholding(vo.getBankIsAgreement()) // 是否签署代扣代缴协议
                            // 2024-02-02 GXP添加协议所属趸租大合同编号
                            .beforeReletContractCode(vo.getParentContractCode())
                            .companyId(vo.getUnitId()) //单位编号
                            .companyName(vo.getUnitName()) //单位名称
                            .tenantSupplierNo(vo.getTenantSupplierNo())
                            .tenantSupplierName(vo.getTenantSupplierName())
                            .companySupplierNo(vo.getCompanySupplierNo())
                            .companySupplierName(vo.getCompanySupplierName())
                            .authorizedAgent(vo.getAuthorizedAgent())
                            .authorizedAgentMobile(vo.getAuthorizedAgentMobile())
                            .build())
                    .build();
            code = updateContractPublic(changeContractRequest);

        }
        return code;
    }

    /**
     * 3.59.商业合同变更退款生成付款单接口
     *
     * @param vo
     * @return
     */
    @Override
    public BusinessGeneratePaymentByGXResultVo businessGeneratePaymentByGX(BusinessGeneratePaymentByGXParamVo vo) {
        ParentRequest<BusinessGeneratePaymentByGXParamVo> bankRequestVo = new ParentRequest<>();
        bankRequestVo.setData(vo);
        String resultString = null;
        ChargeRespondVo<BusinessGeneratePaymentByGXResultVo> result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("3.59.商业合同变更退款生成付款单接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result =  bfipSettlementFeignClient.businessGeneratePaymentByGX(bankRequestVo);
        } else {
            String url = yecaiUrl + "/settlement/payment/BusinessGeneratePaymentByGX";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("3.59.商业合同变更退款生成付款单接口,工银返回:"+ JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }
        return result.getData();
    }

    /**
     * 调用 3.5.合同变更单据更新接口
     * @param changeContractRequest
     * @return
     */
    private String updateContractPublic(ChangeContractRequest changeContractRequest){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<ChangeContractRequest> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(changeContractRequest);

        log.info("3.5.合同变更单据更新接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.5.合同变更单据更新接口请求参数json:" + jsonRequest);

        //工银接口暂无
        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipChargeFeignClient.updateByContract(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/charge/v1/bill/updateByContract", parentRequest);
        }
        log.info("调用工银3.5.合同变更单据更新接口返回结果为:" + responseBody);

        FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.5.合同变更单据更新接口失败:"+responseBody);
            throw new McpException("*提示:"+faceMdMapResult.getMessage());
        }
        System.out.println(faceMdMapResult.getCode());
        return faceMdMapResult.getCode();
        //return "00000";
    }

}
