package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 推送的家具租金信息 vo实体类
 *
 * <AUTHOR>
 * @since 2023/9/1
 */
@Data
@ApiModel(value = "推送的家具租金信息", description = "推送的家具租金信息")
public class BbctFurnitureRentalVo extends McpBaseVo implements Serializable {

    /**
     * 家具ID
     */
    @ApiModelProperty(value = "家具ID")
    private String furnitureId;

    /**
     * 家具名称
     */
    @ApiModelProperty(value = "家具名称")
    private String furnitureName;

    /**
     * 家具租金
     */
    @ApiModelProperty(value = "家具租金")
    private BigDecimal furnitureRental;
}
