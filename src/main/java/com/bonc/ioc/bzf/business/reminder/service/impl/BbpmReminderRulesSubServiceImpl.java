package com.bonc.ioc.bzf.business.reminder.service.impl;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesSubEntity;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmReminderRulesSubMapper;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmReminderRulesSubService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 缴费提醒规则--子表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmReminderRulesSubServiceImpl extends McpBaseServiceImpl<BbpmReminderRulesSubEntity> implements IBbpmReminderRulesSubService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmReminderRulesSubMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmReminderRulesSubService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmReminderRulesSubVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmReminderRulesSubEntity entity = new BbpmReminderRulesSubEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setRulesSubId(null);
        if(!baseService.insert(entity)) {
            log.error("缴费提醒规则--子表新增失败:" + entity.toString());
            throw new McpException("缴费提醒规则--子表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getRulesSubId(),1)) {
                log.error("缴费提醒规则--子表新增后保存历史失败:" + entity.toString());
                throw new McpException("缴费提醒规则--子表新增后保存历史失败");
            }

            log.debug("缴费提醒规则--子表新增成功:"+entity.getRulesSubId());
            return entity.getRulesSubId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmReminderRulesSubVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmReminderRulesSubEntity> entityList = new ArrayList<>();
        for (BbpmReminderRulesSubVo item:voList) {
            BbpmReminderRulesSubEntity entity = new BbpmReminderRulesSubEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmReminderRulesSubEntity item:entityList){
            item.setRulesSubId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("缴费提醒规则--子表新增失败");
            throw new McpException("缴费提醒规则--子表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmReminderRulesSubEntity::getRulesSubId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("缴费提醒规则--子表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("缴费提醒规则--子表批量新增后保存历史失败");
            }

            log.debug("缴费提醒规则--子表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param rulesSubId 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String rulesSubId) {
        if(!StringUtils.isEmpty(rulesSubId)) {
            if(!baseService.saveOperationHisById(rulesSubId,3)) {
                log.error("缴费提醒规则--子表删除后保存历史失败:" + rulesSubId);
                throw new McpException("缴费提醒规则--子表删除后保存历史失败");
            }

            if(!baseService.removeById(rulesSubId)) {
                log.error("缴费提醒规则--子表删除失败");
                throw new McpException("缴费提醒规则--子表删除失败"+rulesSubId);
            }
        } else {
            throw new McpException("缴费提醒规则--子表删除失败唯一标识符为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rulesSubIdList 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> rulesSubIdList) {
        if(!CollectionUtils.isEmpty(rulesSubIdList)) {
            int oldSize = rulesSubIdList.size();
            rulesSubIdList = rulesSubIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(rulesSubIdList) || oldSize != rulesSubIdList.size()) {
                throw new McpException("缴费提醒规则--子表批量删除失败 存在主键id为空的记录"+StringUtils.join(rulesSubIdList));
            }

            if(!baseService.saveOperationHisByIds(rulesSubIdList,3)) {
                log.error("缴费提醒规则--子表批量删除后保存历史失败:" + StringUtils.join(rulesSubIdList));
                throw new McpException("缴费提醒规则--子表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(rulesSubIdList)) {
                log.error("缴费提醒规则--子表批量删除失败");
                throw new McpException("缴费提醒规则--子表批量删除失败"+StringUtils.join(rulesSubIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缴费提醒规则--子表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmReminderRulesSubVo vo) {
        if(vo != null) {
            BbpmReminderRulesSubEntity entity = new BbpmReminderRulesSubEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getRulesSubId())) {
                throw new McpException("缴费提醒规则--子表更新失败传入唯一标识符为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("缴费提醒规则--子表更新失败");
                throw new McpException("缴费提醒规则--子表更新失败"+entity.getRulesSubId());
            } else {
                if(!baseService.saveOperationHisById(entity.getRulesSubId(),2)) {
                    log.error("缴费提醒规则--子表更新后保存历史失败:" + entity.getRulesSubId());
                    throw new McpException("缴费提醒规则--子表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("缴费提醒规则--子表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缴费提醒规则--子表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmReminderRulesSubVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReminderRulesSubEntity> entityList = new ArrayList<>();

            for (BbpmReminderRulesSubVo item:voList){
                BbpmReminderRulesSubEntity entity = new BbpmReminderRulesSubEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getRulesSubId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("缴费提醒规则--子表批量更新失败 存在唯一标识符为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("缴费提醒规则--子表批量更新失败");
                throw new McpException("缴费提醒规则--子表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getRulesSubId())).map(BbpmReminderRulesSubEntity::getRulesSubId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("缴费提醒规则--子表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("缴费提醒规则--子表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缴费提醒规则--子表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmReminderRulesSubVo vo) {
        if(vo != null) {
            BbpmReminderRulesSubEntity entity = new BbpmReminderRulesSubEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("缴费提醒规则--子表保存失败");
                throw new McpException("缴费提醒规则--子表保存失败"+entity.getRulesSubId());
            } else {
                if(!baseService.saveOperationHisById(entity.getRulesSubId(),4)) {
                    log.error("缴费提醒规则--子表保存后保存历史失败:" + entity.getRulesSubId());
                    throw new McpException("缴费提醒规则--子表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("缴费提醒规则--子表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缴费提醒规则--子表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmReminderRulesSubVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReminderRulesSubEntity> entityList = new ArrayList<>();

            for (BbpmReminderRulesSubVo item:voList){
                BbpmReminderRulesSubEntity entity = new BbpmReminderRulesSubEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("缴费提醒规则--子表批量保存失败");
                throw new McpException("缴费提醒规则--子表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getRulesSubId())).map(BbpmReminderRulesSubEntity::getRulesSubId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("缴费提醒规则--子表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("缴费提醒规则--子表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param rulesSubId 需要查询的唯一标识符
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmReminderRulesSubVo selectByIdRecord(String rulesSubId) {
        BbpmReminderRulesSubVo vo = new BbpmReminderRulesSubVo();

        if(!StringUtils.isEmpty(rulesSubId)) {
            BbpmReminderRulesSubEntity entity = baseService.selectById(rulesSubId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmReminderRulesSubPageResultVo>> selectByPageRecord(BbpmReminderRulesSubPageVo vo) {
        List<BbpmReminderRulesSubPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
