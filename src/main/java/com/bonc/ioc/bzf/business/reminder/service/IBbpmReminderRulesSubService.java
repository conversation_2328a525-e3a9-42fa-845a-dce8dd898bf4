package com.bonc.ioc.bzf.business.reminder.service;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesSubEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 缴费提醒规则--子表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
public interface IBbpmReminderRulesSubService extends IMcpBaseService<BbpmReminderRulesSubEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    String insertRecord(BbpmReminderRulesSubVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmReminderRulesSubVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param rulesSubId 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void removeByIdRecord(String rulesSubId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rulesSubIdList 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> rulesSubIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缴费提醒规则--子表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void updateByIdRecord(BbpmReminderRulesSubVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缴费提醒规则--子表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmReminderRulesSubVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缴费提醒规则--子表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void saveByIdRecord(BbpmReminderRulesSubVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缴费提醒规则--子表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmReminderRulesSubVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param rulesSubId 需要查询的唯一标识符
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    BbpmReminderRulesSubVo selectByIdRecord(String rulesSubId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    PageResult<List<BbpmReminderRulesSubPageResultVo>> selectByPageRecord(BbpmReminderRulesSubPageVo vo);
}
