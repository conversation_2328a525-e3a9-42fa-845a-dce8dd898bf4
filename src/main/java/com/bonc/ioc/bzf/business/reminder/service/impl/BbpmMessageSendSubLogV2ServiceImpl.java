package com.bonc.ioc.bzf.business.reminder.service.impl;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendSubLogV2Entity;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmMessageSendSubLogV2Mapper;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmMessageSendSubLogV2Service;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * (催缴记录)催缴规则消息发送日志--子表V2 服务类实现
 *
 * <AUTHOR>
 * @date 2024-04-23
 * @change 2024-04-23 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmMessageSendSubLogV2ServiceImpl extends McpBaseServiceImpl<BbpmMessageSendSubLogV2Entity> implements IBbpmMessageSendSubLogV2Service {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmMessageSendSubLogV2Mapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmMessageSendSubLogV2Service baseService;

    /**
     * Bean 字典转化
     */
    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmMessageSendSubLogV2Vo vo) {
        if(vo == null) {
            return null;
        }

        BbpmMessageSendSubLogV2Entity entity = new BbpmMessageSendSubLogV2Entity();
        BeanUtils.copyProperties(vo, entity);

        entity.setNoticeId(null);
        if(!baseService.insert(entity)) {
            log.error("(催缴记录)催缴规则消息发送日志--子表V2新增失败:" + entity.toString());
            throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getNoticeId(),1)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2新增后保存历史失败:" + entity.toString());
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2新增后保存历史失败");
            }

            log.debug("(催缴记录)催缴规则消息发送日志--子表V2新增成功:"+entity.getNoticeId());
            return entity.getNoticeId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmMessageSendSubLogV2Vo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmMessageSendSubLogV2Entity> entityList = new ArrayList<>();
        for (BbpmMessageSendSubLogV2Vo item:voList) {
            BbpmMessageSendSubLogV2Entity entity = new BbpmMessageSendSubLogV2Entity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmMessageSendSubLogV2Entity item:entityList){
            item.setNoticeId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("(催缴记录)催缴规则消息发送日志--子表V2新增失败");
            throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmMessageSendSubLogV2Entity::getNoticeId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量新增后保存历史失败");
            }

            log.debug("(催缴记录)催缴规则消息发送日志--子表V2新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param noticeId 需要删除的通知ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String noticeId) {
        if(!StringUtils.isEmpty(noticeId)) {
            if(!baseService.saveOperationHisById(noticeId,3)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2删除后保存历史失败:" + noticeId);
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2删除后保存历史失败");
            }

            if(!baseService.removeById(noticeId)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2删除失败");
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2删除失败"+noticeId);
            }
        } else {
            throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2删除失败通知ID为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param noticeIdList 需要删除的通知ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> noticeIdList) {
        if(!CollectionUtils.isEmpty(noticeIdList)) {
            int oldSize = noticeIdList.size();
            noticeIdList = noticeIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(noticeIdList) || oldSize != noticeIdList.size()) {
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量删除失败 存在主键id为空的记录"+StringUtils.join(noticeIdList));
            }

            if(!baseService.saveOperationHisByIds(noticeIdList,3)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2批量删除后保存历史失败:" + StringUtils.join(noticeIdList));
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(noticeIdList)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2批量删除失败");
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量删除失败"+StringUtils.join(noticeIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的(催缴记录)催缴规则消息发送日志--子表V2
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmMessageSendSubLogV2Vo vo) {
        if(vo != null) {
            BbpmMessageSendSubLogV2Entity entity = new BbpmMessageSendSubLogV2Entity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getNoticeId())) {
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2更新失败传入通知ID为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2更新失败");
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2更新失败"+entity.getNoticeId());
            } else {
                if(!baseService.saveOperationHisById(entity.getNoticeId(),2)) {
                    log.error("(催缴记录)催缴规则消息发送日志--子表V2更新后保存历史失败:" + entity.getNoticeId());
                    throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的(催缴记录)催缴规则消息发送日志--子表V2
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmMessageSendSubLogV2Vo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmMessageSendSubLogV2Entity> entityList = new ArrayList<>();

            for (BbpmMessageSendSubLogV2Vo item:voList){
                BbpmMessageSendSubLogV2Entity entity = new BbpmMessageSendSubLogV2Entity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getNoticeId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量更新失败 存在通知ID为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2批量更新失败");
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getNoticeId())).map(BbpmMessageSendSubLogV2Entity::getNoticeId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("(催缴记录)催缴规则消息发送日志--子表V2批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的(催缴记录)催缴规则消息发送日志--子表V2
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmMessageSendSubLogV2Vo vo) {
        if(vo != null) {
            BbpmMessageSendSubLogV2Entity entity = new BbpmMessageSendSubLogV2Entity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2保存失败");
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2保存失败"+entity.getNoticeId());
            } else {
                if(!baseService.saveOperationHisById(entity.getNoticeId(),4)) {
                    log.error("(催缴记录)催缴规则消息发送日志--子表V2保存后保存历史失败:" + entity.getNoticeId());
                    throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的(催缴记录)催缴规则消息发送日志--子表V2
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmMessageSendSubLogV2Vo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmMessageSendSubLogV2Entity> entityList = new ArrayList<>();

            for (BbpmMessageSendSubLogV2Vo item:voList){
                BbpmMessageSendSubLogV2Entity entity = new BbpmMessageSendSubLogV2Entity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("(催缴记录)催缴规则消息发送日志--子表V2批量保存失败");
                throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getNoticeId())).map(BbpmMessageSendSubLogV2Entity::getNoticeId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("(催缴记录)催缴规则消息发送日志--子表V2批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("(催缴记录)催缴规则消息发送日志--子表V2批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param noticeId 需要查询的通知ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmMessageSendSubLogV2Vo selectByIdRecord(String noticeId) {
        BbpmMessageSendSubLogV2Vo vo = new BbpmMessageSendSubLogV2Vo();

        if(!StringUtils.isEmpty(noticeId)) {
            BbpmMessageSendSubLogV2Entity entity = baseService.selectById(noticeId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change
     * 2024-04-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmMessageSendSubLogV2PageResultVo>> selectByPageRecord(BbpmMessageSendSubLogV2PageVo vo) {
        List<BbpmMessageSendSubLogV2PageResultVo> result = baseMapper.selectByPageCustom(vo);
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }
}
