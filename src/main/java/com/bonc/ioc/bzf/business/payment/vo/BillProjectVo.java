package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 五期合同项目 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/25
 */
@Data
@ApiModel(value = "五期合同项目", description = "五期合同项目")
public class BillProjectVo extends McpBaseVo implements Serializable {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 五期合同房屋列表
     */
    @ApiModelProperty(value = "五期合同房屋列表")
    private List<BillHouseVo> roomList;

}
