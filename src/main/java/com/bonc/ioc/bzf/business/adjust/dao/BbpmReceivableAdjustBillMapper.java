package com.bonc.ioc.bzf.business.adjust.dao;

import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustBillEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import java.util.List;

/**
 * 应收调整账单表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@Mapper
public interface BbpmReceivableAdjustBillMapper extends McpBaseMapper<BbpmReceivableAdjustBillEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change 2025-02-18 by yuanxuesong for init
     */
    List<BbpmReceivableAdjustBillPageResultVo> selectByPageCustom(@Param("vo") BbpmReceivableAdjustBillPageVo vo );

    void removeByAdjustId(@Param("adjustId") String adjustId);

    List<AdjustBillDTO> selectByAdjustId(@Param("adjustId") String adjustId);

    List<BbpmReceivableAdjustBillEntity> selectBillForAdjust(@Param("adjustId") String adjustId);
}
