package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpDictEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 字典表 服务类
 *
 * <AUTHOR>
 * @date 2022-09-22
 * @change 2022-09-22 by wtl for init
 */
public interface IBbpDictService extends IMcpBaseService<BbpDictEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    String insertRecord(BbpDictVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    List<String> insertBatchRecord(List<BbpDictVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param dictId 需要删除的字典表主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    void removeByIdRecord(String dictId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param dictIdList 需要删除的字典表主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    void removeByIdsRecord(List<String> dictIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的字典表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    void updateByIdRecord(BbpDictVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的字典表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    void updateBatchByIdRecord(List<BbpDictVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的字典表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    void saveByIdRecord(BbpDictVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的字典表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    void saveBatchByIdRecord(List<BbpDictVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param dictId 需要查询的字典表主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    BbpDictVo selectByIdRecord(String dictId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    PageResult<List<BbpDictPageResultVo>> selectByPageRecord(BbpDictPageVo vo);
}
