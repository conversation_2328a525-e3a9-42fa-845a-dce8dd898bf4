package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> 获取计费结果接口 返回实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeRuleResultVo {

	@ApiModelProperty(value = "参数编码列表")
	private  String paramCodeList;

	@ApiModelProperty(value = "计费规则名称")
	private String chargeRuleName;

	@ApiModelProperty(value = "计费规则编号")
	private String chargeRuleNo;
}
