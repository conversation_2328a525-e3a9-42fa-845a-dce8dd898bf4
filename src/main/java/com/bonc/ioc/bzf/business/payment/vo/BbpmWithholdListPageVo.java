package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 手动报盘 未缴账单列表查询 实体类
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@ApiModel(value="BbpmWithholdListPageVo对象", description="未缴账单列表查询")
@Data
public class BbpmWithholdListPageVo extends McpBasePageVo implements Serializable{

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "租户合同集合")
    private List<ContractVo> contractList;

    @ApiModelProperty(value = "业务类型01:按运营项目报盘,02:按租户报盘")
    private String bizType;

    @ApiModelProperty(value = "全量分页标识Y:是,N:否")
    private String fullPage;


    @ApiModelProperty(value = "当前页码")
    private Integer current;

    @ApiModelProperty(value = "每页最大数目")
    private Integer size;


}
