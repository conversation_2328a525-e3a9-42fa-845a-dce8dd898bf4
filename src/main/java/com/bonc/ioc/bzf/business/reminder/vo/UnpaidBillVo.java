package com.bonc.ioc.bzf.business.reminder.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 未缴账单返回参数
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@ApiModel(value = "UnpaidBillVo对象", description = "未缴账单")
@Data
public class UnpaidBillVo extends McpBaseVo implements Serializable {
    @ApiModelProperty(value = "账单唯一识别码")
    private String billCode;//	账单唯一识别码	String	是	64
    @ApiModelProperty(value = "合同编号")
    private String contractCode;//	合同编号	String	是	20
    @ApiModelProperty(value = "账单类别01：企业02：个人")
    private String owner;//	账单类别	String	是	2	01：企业02：个人
    @ApiModelProperty(value = "当前期次")
    private String chargePeriod;//	当前期次	String	是	128
    @ApiModelProperty(value = "收费科目开始时间")
    private String chargeStartDate;//	收费科目开始时间	String	是	128
    @ApiModelProperty(value = "收费科目结束时间")
    private String chargeEndDate;//	收费科目结束时间	String	是	128
    @ApiModelProperty(value = "应缴费日期")
    private String payableDate;//	应缴费日期	String	是	128
    @ApiModelProperty(value = "应缴金额")
    private BigDecimal payableMoney;//	应缴金额	BigDecimal	是	13,4
    @ApiModelProperty(value = "未缴金额")
    private BigDecimal toBePaidMoney;//	未缴金额	BigDecimal	是	13,4
    @ApiModelProperty(value = "租户/趸租企业ID")
    private String tenantId;//	租户/趸租企业ID	String	是	128	业务中台  租户/趸租企业ID

    @ApiModelProperty(value = "租户/趸租企业名称")
    private String tenantName;//	租户/趸租企业名称	String	是	50
    @ApiModelProperty(value = "租户手机号/企业委托代理电话")
    private String tenantMobile;//	租户手机号/企业委托代理电话	String	否	15
    @ApiModelProperty(value = "账单缴费状态01：已足额缴费 02：已缴部分支付 03：未缴")
    private String chargeStatus;//	账单缴费状态	String	是	10	01：已足额缴费 02：已缴部分支付 03：未缴
    @ApiModelProperty(value = "计费科目编号")
    @McpDictPoint(dictCode = "BILLING_YC_BILLCHARGESUBJECT", overTransCopyTo = "billChargeSubjectName")
    private String chargeSubject;//	计费科目编号	String	是	10	01 房屋租金 02 押金03家具家电租金04车位租金05仓库租金06能源费07物业费08违约金09损坏赔偿13空置费



    @ApiModelProperty(value = "是否对平0:否,1:是")
    private int isEven;//是否对平 int	是	1	0:否，1:是
    @ApiModelProperty(value = "实际收款金额")
    private BigDecimal targetAmount ;//实际收款金额	BigDecimal	是	13，4
    @ApiModelProperty(value = "未对平金额")
    private BigDecimal unevenMoney;//未对平金额	BigDecimal	是	13，4
    @ApiModelProperty(value = "项目id")
    private String projectId;//项目id	String	是	100
    @ApiModelProperty(value = "项目名称")
    private String projectName;//项目名称	String	是	32
    @ApiModelProperty(value = "房源名称")
    private String houseName;//房源名称	String	是	128
    @ApiModelProperty(value = "证件号码")
    private String certNo;//证件号码	String	是	32


    @ApiModelProperty(value = "账单周期")
    private String billCycle;
    @ApiModelProperty(value = "账单对应的收费科目名称")
    private String billChargeSubjectName;

    @ApiModelProperty(value = "收款日期(缴费日期)")
    private String chargeDate;//chargeDate	收款日期	String	否	128	缴费日期

}
