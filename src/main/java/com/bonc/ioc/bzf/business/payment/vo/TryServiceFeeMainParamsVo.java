package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.bzf.business.payment.result.create.ChargeSubjectParamsRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 3.45 增值服务费试算接口 请求参数
 */
@Data
public class TryServiceFeeMainParamsVo {

    @ApiModelProperty(value = "合同开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginDate;
    @ApiModelProperty(value = "合同结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    @ApiModelProperty(value = "计费科目列表")
    private TryChargeSubjectVo chargeSubject;

    @ApiModelProperty(value = "大合同开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date parentContractBeginDate;
    @ApiModelProperty(value = "大合同结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date parentContractEndDate;
    @ApiModelProperty(value = "合同所有者")
    private String owner;
    @ApiModelProperty(value = "项目业态")
    private String projectFormat;
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "合同周期")
    private String contractPricePeriod;

}
