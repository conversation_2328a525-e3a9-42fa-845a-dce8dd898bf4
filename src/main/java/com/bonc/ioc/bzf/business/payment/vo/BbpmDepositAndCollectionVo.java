package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 现金盘点存款单表v3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@ApiModel(value="BbpmDepositAndCollectionVo存款和收款对象", description="存款和收款对象")
public class BbpmDepositAndCollectionVo extends McpBaseVo implements Serializable{
    private BbpmDepositSlipVo bbpmDepositSlipVo;
    private List<BbpmCollectionVo> bbpmCollectionVoList;

    public BbpmDepositSlipVo getBbpmDepositSlipVo() {
        return bbpmDepositSlipVo;
    }

    public void setBbpmDepositSlipVo(BbpmDepositSlipVo bbpmDepositSlipVo) {
        this.bbpmDepositSlipVo = bbpmDepositSlipVo;
    }

    public List<BbpmCollectionVo> getBbpmCollectionVoList() {
        return bbpmCollectionVoList;
    }

    public void setBbpmCollectionVoList(List<BbpmCollectionVo> bbpmCollectionVoList) {
        this.bbpmCollectionVoList = bbpmCollectionVoList;
    }
}
