package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class RoomVacancyFeeVo {

    @ApiModelProperty(value = "趸租管理协议ID")
    private String contractId;

    @ApiModelProperty(value = "房屋ID")
    private String houseId;


}
