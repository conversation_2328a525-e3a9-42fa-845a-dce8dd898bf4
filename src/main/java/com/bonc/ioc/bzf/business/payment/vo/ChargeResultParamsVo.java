package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 查询计费规则列表接口 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeResultParamsVo {

	@ApiModelProperty(value = "计费规则编号")
	@NotBlank(message = "计费规则编号",groups = {UpdateValidatorGroup.class})
	private  String chargeRuleNo;

	@ApiModelProperty(value = "参数值列表")
	private ChargeRuleSubParamsVo paramValueList;

}
