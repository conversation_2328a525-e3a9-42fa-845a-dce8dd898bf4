package com.bonc.ioc.bzf.business.payment.feign.fallback;

import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.vo.*;
import feign.Response;

import java.util.List;
import java.util.Map;

/**
 * 描述内容
 *
 * <AUTHOR>
 * @date 2022/2/21 16:55
 * @change 2022/2/21 16:55 by zhou<PERSON><PERSON>@bonc.com.cn for init
 */
public class BfipSettlementFeignClientFallback implements BfipSettlementFeignClient {


    @Override
    public String updateBillOffline(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String updateBillOfflinePublic(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String pay(PayParmsVo vo) {
        return null;
    }

    @Override
    public String payUnlock(Map vo) {
        return null;
    }

    @Override
    public String manualOffer(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String queryInvoice(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String issueInvoice(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String preCheckBill(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String redflush(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String pushInvoice(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo<List<ChargeBankResultVo>> listByProjectId(BankRequestVo<ChargeBankParamsVo> bankRequestVo) {
        return null;
    }

    @Override
    public String listBankBranchCode(ParentRequest parentRequest){
        return null;
    }

    @Override
    public String info(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String page(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public Response exceldownload(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public ChargeRespondVo<List<ChargeBankResultV2Vo>> listBankByProjectId(BankRequestVo<ChargeBankParamsV2Vo> bankRequestVo) {
        return null;
    }

    @Override
    public String getTaxRateList(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String list(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String record(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String detail(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public Response exceldownloadV2(ParentRequest parentRequest) {
        return null;
    }

    @Override
    public String getOffLinePages(ParentRequest parentRequest) {
        return null;
    }

    /**
     * 3.59.商业合同变更退款生成付款单接口
     *
     * @param parentRequest
     * @return
     */
    @Override
    public ChargeRespondVo<BusinessGeneratePaymentByGXResultVo> businessGeneratePaymentByGX(ParentRequest<BusinessGeneratePaymentByGXParamVo> parentRequest) {
        return null;
    }

    @Override
    public String businessGeneratePaymentByGXString(ParentRequest<BusinessGeneratePaymentByGXParamVo> parentRequest) {
        return null;
    }


}
