package com.bonc.ioc.bzf.business.invoice.service.impl;

import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceHeaderEntity;
import com.bonc.ioc.bzf.business.invoice.dao.BbpmInvoiceHeaderMapper;
import com.bonc.ioc.bzf.business.invoice.enums.InvoiceEnums;
import com.bonc.ioc.bzf.business.invoice.service.IBbpmInvoiceHeaderService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 常用发票抬头信息表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-05-05
 * @change 2023-05-05 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmInvoiceHeaderServiceImpl extends McpBaseServiceImpl<BbpmInvoiceHeaderEntity> implements IBbpmInvoiceHeaderService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmInvoiceHeaderMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmInvoiceHeaderService baseService;

    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmInvoiceHeaderVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmInvoiceHeaderEntity entity = new BbpmInvoiceHeaderEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setInvoiceHeaderId(null);
        if(!baseService.insert(entity)) {
            log.error("常用发票抬头信息表新增失败:" + entity.toString());
            throw new McpException("常用发票抬头信息表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getInvoiceHeaderId(),1)) {
                log.error("常用发票抬头信息表新增后保存历史失败:" + entity.toString());
                throw new McpException("常用发票抬头信息表新增后保存历史失败");
            }
            //选为默认发票时
            if(InvoiceEnums.IS_DEFAULT_YES.getCode().equals(entity.getIsDefault())){
                BbpmInvoiceHeaderVo bbpmInvoiceHeaderVo = baseMapper.selectByIsDefault(InvoiceEnums.IS_DEFAULT_YES.getCode(),null);
                if(bbpmInvoiceHeaderVo != null){
                    BbpmInvoiceHeaderEntity bbpmInvoiceHeaderEntity = new BbpmInvoiceHeaderEntity();
                    bbpmInvoiceHeaderEntity.setInvoiceHeaderId(bbpmInvoiceHeaderVo.getInvoiceHeaderId());
                    bbpmInvoiceHeaderEntity.setIsDefault(InvoiceEnums.IS_DEFAULT_NO.getCode());
                    baseService.updateById(bbpmInvoiceHeaderEntity);
                }
            }
            log.debug("常用发票抬头信息表新增成功:"+entity.getInvoiceHeaderId());
            return entity.getInvoiceHeaderId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmInvoiceHeaderVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmInvoiceHeaderEntity> entityList = new ArrayList<>();
        for (BbpmInvoiceHeaderVo item:voList) {
            BbpmInvoiceHeaderEntity entity = new BbpmInvoiceHeaderEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmInvoiceHeaderEntity item:entityList){
            item.setInvoiceHeaderId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("常用发票抬头信息表新增失败");
            throw new McpException("常用发票抬头信息表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmInvoiceHeaderEntity::getInvoiceHeaderId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("常用发票抬头信息表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("常用发票抬头信息表批量新增后保存历史失败");
            }

            log.debug("常用发票抬头信息表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param invoiceHeaderId 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String invoiceHeaderId) {
        BbpmInvoiceHeaderEntity entity = new BbpmInvoiceHeaderEntity();
        entity.setInvoiceHeaderId(invoiceHeaderId);
        entity.setDelFlag(0);
        baseService.updateById(entity);
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param invoiceHeaderIdList 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> invoiceHeaderIdList) {
        if(!CollectionUtils.isEmpty(invoiceHeaderIdList)) {
            int oldSize = invoiceHeaderIdList.size();
            invoiceHeaderIdList = invoiceHeaderIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(invoiceHeaderIdList) || oldSize != invoiceHeaderIdList.size()) {
                throw new McpException("常用发票抬头信息表批量删除失败 存在主键id为空的记录"+StringUtils.join(invoiceHeaderIdList));
            }

            if(!baseService.saveOperationHisByIds(invoiceHeaderIdList,3)) {
                log.error("常用发票抬头信息表批量删除后保存历史失败:" + StringUtils.join(invoiceHeaderIdList));
                throw new McpException("常用发票抬头信息表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(invoiceHeaderIdList)) {
                log.error("常用发票抬头信息表批量删除失败");
                throw new McpException("常用发票抬头信息表批量删除失败"+StringUtils.join(invoiceHeaderIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的常用发票抬头信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmInvoiceHeaderVo vo) {
        if(vo != null) {
            BbpmInvoiceHeaderEntity entity = new BbpmInvoiceHeaderEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getInvoiceHeaderId())) {
                throw new McpException("常用发票抬头信息表更新失败传入唯一标识符为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("常用发票抬头信息表更新失败");
                throw new McpException("常用发票抬头信息表更新失败"+entity.getInvoiceHeaderId());
            } else {
                if(!baseService.saveOperationHisById(entity.getInvoiceHeaderId(),2)) {
                    log.error("常用发票抬头信息表更新后保存历史失败:" + entity.getInvoiceHeaderId());
                    throw new McpException("常用发票抬头信息表更新后保存历史失败");
                }
                //选为默认发票时
                if(InvoiceEnums.IS_DEFAULT_YES.getCode().equals(entity.getIsDefault())){
                    BbpmInvoiceHeaderVo bbpmInvoiceHeaderVo = baseMapper.selectByIsDefault(InvoiceEnums.IS_DEFAULT_YES.getCode(),entity.getInvoiceHeaderId());
                    if(bbpmInvoiceHeaderVo != null){
                        BbpmInvoiceHeaderEntity bbpmInvoiceHeaderEntity = new BbpmInvoiceHeaderEntity();
                        bbpmInvoiceHeaderEntity.setInvoiceHeaderId(bbpmInvoiceHeaderVo.getInvoiceHeaderId());
                        bbpmInvoiceHeaderEntity.setIsDefault(InvoiceEnums.IS_DEFAULT_NO.getCode());
                        baseService.updateById(bbpmInvoiceHeaderEntity);
                    }
                }
            }
        } else {
            throw new McpException("常用发票抬头信息表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的常用发票抬头信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmInvoiceHeaderVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmInvoiceHeaderEntity> entityList = new ArrayList<>();

            for (BbpmInvoiceHeaderVo item:voList){
                BbpmInvoiceHeaderEntity entity = new BbpmInvoiceHeaderEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getInvoiceHeaderId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("常用发票抬头信息表批量更新失败 存在唯一标识符为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("常用发票抬头信息表批量更新失败");
                throw new McpException("常用发票抬头信息表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getInvoiceHeaderId())).map(BbpmInvoiceHeaderEntity::getInvoiceHeaderId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("常用发票抬头信息表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("常用发票抬头信息表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的常用发票抬头信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmInvoiceHeaderVo vo) {
        if(vo != null) {
            BbpmInvoiceHeaderEntity entity = new BbpmInvoiceHeaderEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("常用发票抬头信息表保存失败");
                throw new McpException("常用发票抬头信息表保存失败"+entity.getInvoiceHeaderId());
            } else {
                if(!baseService.saveOperationHisById(entity.getInvoiceHeaderId(),4)) {
                    log.error("常用发票抬头信息表保存后保存历史失败:" + entity.getInvoiceHeaderId());
                    throw new McpException("常用发票抬头信息表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("常用发票抬头信息表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的常用发票抬头信息表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmInvoiceHeaderVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmInvoiceHeaderEntity> entityList = new ArrayList<>();

            for (BbpmInvoiceHeaderVo item:voList){
                BbpmInvoiceHeaderEntity entity = new BbpmInvoiceHeaderEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("常用发票抬头信息表批量保存失败");
                throw new McpException("常用发票抬头信息表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getInvoiceHeaderId())).map(BbpmInvoiceHeaderEntity::getInvoiceHeaderId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("常用发票抬头信息表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("常用发票抬头信息表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param invoiceHeaderId 需要查询的唯一标识符
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmInvoiceHeaderVo selectByIdRecord(String invoiceHeaderId) {
        BbpmInvoiceHeaderVo vo = new BbpmInvoiceHeaderVo();

        if(!StringUtils.isEmpty(invoiceHeaderId)) {
            BbpmInvoiceHeaderEntity entity = baseService.selectById(invoiceHeaderId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                //字典转换
                mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmInvoiceHeaderPageResultVo>> selectByPageRecord(BbpmInvoiceHeaderPageVo vo) {
        List<BbpmInvoiceHeaderPageResultVo> result = baseMapper.selectByPageCustom(vo);
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }
}
