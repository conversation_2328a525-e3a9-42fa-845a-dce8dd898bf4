package com.bonc.ioc.bzf.business.adjust.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 应收调整审核表 实体类
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@TableName("bbpm_receivable_adjust_examine")
@ApiModel(value="BbpmReceivableAdjustExamineEntity对象", description="应收调整审核表")
public class BbpmReceivableAdjustExamineEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_ID = "id";
    public static final String FIELD_ADJUST_ID = "adjust_id";
    public static final String FIELD_EXAMINE_DESCRIBE = "examine_describe";
    public static final String FIELD_EXAMINE_STATUS = "examine_status";
    public static final String FIELD_EXT1 = "ext1";
    public static final String FIELD_EXT2 = "ext2";
    public static final String FIELD_DEL_FLAG = "del_flag";
    public static final String FIELD_CREATE_USER_NAME = "create_user_name";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
                                @TableId(value = "id", type = IdType.ASSIGN_UUID)
                                  private String id;

    /**
     * 应收调整id
     */
    @ApiModelProperty(value = "应收调整id")
                            private String adjustId;

    /**
     * 审核说明
     */
    @ApiModelProperty(value = "审核说明")
                            private String examineDescribe;

    /**
     * 审核状态
     */
    @ApiModelProperty(value = "审核状态")
    @McpDictPoint(dictCode = "ADJUST_STATUS", overTransCopyTo = "examineStatusName")
                            private String examineStatus;

    /**
     * 退款状态名称
     */
    @ApiModelProperty(value = "审核状态名称")
    private String examineStatusName;

    /**
     * 备用字段1
     */
    @ApiModelProperty(value = "备用字段1")
                            private String ext1;

    /**
     * 备用字段2
     */
    @ApiModelProperty(value = "备用字段2")
                            private String ext2;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
                            private String createUserName;

    public String getExamineStatusName() {
        return examineStatusName;
    }

    public void setExamineStatusName(String examineStatusName) {
        this.examineStatusName = examineStatusName;
    }

    /**
     * @return 主键
     */
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return 应收调整id
     */
    public String getAdjustId() {
        return adjustId;
    }

    public void setAdjustId(String adjustId) {
        this.adjustId = adjustId;
    }

    /**
     * @return 审核说明
     */
    public String getExamineDescribe() {
        return examineDescribe;
    }

    public void setExamineDescribe(String examineDescribe) {
        this.examineDescribe = examineDescribe;
    }

    /**
     * @return 审核状态
     */
    public String getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(String examineStatus) {
        this.examineStatus = examineStatus;
    }

    /**
     * @return 备用字段1
     */
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * @return 备用字段2
     */
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 创建人姓名
     */
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

      @Override
    public String toString() {
        return "BbpmReceivableAdjustExamineEntity{" +
            "id=" + id +
            ", adjustId=" + adjustId +
            ", examineDescribe=" + examineDescribe +
            ", examineStatus=" + examineStatus +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", delFlag=" + delFlag +
            ", createUserName=" + createUserName +
        "}";
    }
}