package com.bonc.ioc.bzf.business.payment.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 返回参数  resultList	增值服务费试算结果列表
 */
@Data
public class TryResultListVo {

    @ApiModelProperty(value = "开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    @ApiModelProperty(value = "结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty(value = "分摊前增值服务费")
    private BigDecimal serviceFee;
    @ApiModelProperty(value = "企业分摊的增值服务费")
    private BigDecimal serviceFeeCompanyAmount;
    @ApiModelProperty(value = "个人应付的增值服务费")
    private BigDecimal serviceFeePersonalAmount;


}
