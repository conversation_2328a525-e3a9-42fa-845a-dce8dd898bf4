package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 推送的优惠规则信息 vo实体类
 *
 * <AUTHOR>
 * @since 2023/9/1
 */
@Data
@ApiModel(value = "推送的优惠规则信息", description = "推送的优惠规则信息")
public class BbctPreferentRuleVo extends McpBaseVo implements Serializable {

    /**
     * 优惠方式
     */
    @ApiModelProperty(value = "优惠方式")
    private String preferentialType;

    /**
     * 优惠开始日期
     */
    @ApiModelProperty(value = "优惠开始日期")
    private String preferentialBeginDate;

    /**
     * 优惠结束日期
     */
    @ApiModelProperty(value = "优惠结束日期")
    private String preferentialEndDate;

    /**
     * 优惠规则ID
     */
    @ApiModelProperty(value = "优惠规则ID")
    private Integer preferentRuleId;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal preferentialAmount;
    @ApiModelProperty(value = "优惠比例")
    private BigDecimal preferentialRatio;
}
