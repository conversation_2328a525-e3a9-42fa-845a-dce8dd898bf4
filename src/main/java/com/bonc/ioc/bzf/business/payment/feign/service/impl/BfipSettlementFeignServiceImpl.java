package com.bonc.ioc.bzf.business.payment.feign.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.service.IBfipSettlementFeignService;
import com.bonc.ioc.bzf.business.payment.vo.PayParmsVo;
import com.bonc.ioc.bzf.business.payment.vo.PayResultVo;
import com.bonc.ioc.bzf.business.payment.vo.PayUnlockParmsVo;
import com.bonc.ioc.bzf.business.payment.vo.PayUnlockResultVo;
import com.bonc.ioc.common.exception.McpException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/3/4 22:52
 */
@Slf4j
@Service
public class BfipSettlementFeignServiceImpl implements IBfipSettlementFeignService {

    @Autowired
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    @Override
    public PayResultVo pay(PayParmsVo vo) {
        String result = bfipSettlementFeignClient.pay(vo);

        log.info("【调用工银支付接口 - 请求参数】{}",vo.toString());
        log.info("【调用工银支付接口 - 返回结果】{}",result);

        if (StringUtils.isBlank(result)){
            log.info("【调用工银支付接口】未获取到响应结果");
            throw new McpException("系统异常");
        }

        return JSONObject.parseObject(result, PayResultVo.class);
    }

    @Override
    public PayUnlockResultVo payUnlock(PayUnlockParmsVo vo) {
        Map data = new HashMap<>();
        data.put("orderTrxid",vo.getOrderTrxid());
        data.put("projectId",vo.getProjectId());

        Map map = new HashMap<>();
        map.put("data",data);
        String result = bfipSettlementFeignClient.payUnlock(map);

        log.info("【调用工银支付解锁接口 - 请求参数】{}",vo.toString());
        log.info("【调用工银支付解锁接口 - 返回结果】{}",result);

        if (StringUtils.isBlank(result)){
            log.info("【调用工银支付解锁接口】未获取到响应结果");
            throw new McpException("系统异常");
        }


        return JSONObject.parseObject(result, PayUnlockResultVo.class);
    }
}
