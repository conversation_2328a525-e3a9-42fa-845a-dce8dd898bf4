package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ChargeSubjectBillDTO implements java.io.Serializable {

    /**
     * 计费科目编码
     */
    @ApiModelProperty(value = "计费科目编码")
    private String chargeSubject;

    /**
     * 账单列表
     */
    @ApiModelProperty(value = "账单列表")
    private List<BillInfoVo> billList;
}
