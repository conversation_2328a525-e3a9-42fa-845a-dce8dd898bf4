package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付结果数据对象
 *
 * <AUTHOR>
 * @date 2022-03-04
 * @change 2022-03-04 by 姚春雨 for init
 */
@Data
@ApiModel(value = "PayResultDataVo对象", description = "支付结果数据对象")
public class PayResultDataVo extends McpBaseVo implements Serializable {

    @ApiModelProperty(value = "工行API平台APPID（应用在API 开放平台注册时生成）")
    private String appId;

    @ApiModelProperty(value = "消息通讯唯一编号（每次调用独立生成，APP级唯一）")
    private String msgId;

    @ApiModelProperty(value = "请求参数格式")
    private String format;

    @ApiModelProperty(value = "字符集")
    private String charset;

    @ApiModelProperty(value = "加密方式")
    private String encryptType;

    @ApiModelProperty(value = "签名类型")
    private String signType;

    @ApiModelProperty(value = "时间戳（yyyy-MM-dd HH:mm:ss）")
    private String timestamp;

    @ApiModelProperty(value = "证书")
    private String ca;

    @ApiModelProperty(value = "请求参数的集合")
    private String tranData;

    @ApiModelProperty(value = "订单签名数据")
    private String merSignMsg;





}
