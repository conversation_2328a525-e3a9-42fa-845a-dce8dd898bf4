package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 消息模板信息表 实体类
 *
 * <AUTHOR>
 * @date 2022-08-03
 * @change 2022-08-03 by sqj for init
 */
@ApiModel(value="MessTemplateInfoPageVo对象", description="消息模板信息表")
public class MessTemplateInfoPageVo extends McpBasePageVo implements Serializable{


    @NotBlank(message = "不能为空",groups = {UpdateValidatorGroup.class})
    @ApiModelProperty(value = "消息模板id", hidden = true)
                                  private String messTemplateId;

    /**
     * 消息模板名称
     */
    @ApiModelProperty(value = "消息模板名称", hidden = true)
                            private String messTemplateName;

    /**
     * 消息模板内容
     */
    @ApiModelProperty(value = "消息模板内容", hidden = true)
                            private String messTemplateContent;

    /**
     * 模板类型id
     */
    @ApiModelProperty(value = "模板类型id", hidden = true)
                            private String templateTypeId;

    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型;01-站内信/通知消息；02-短信消息",required = true)
                            private String messageType;

    /**
     * 系统类型
     */
    @ApiModelProperty(value = "系统类型;01-看房中心；02-选房中心；03-签约中心；04-入住中心",required = true)
    private String systemType;

    /**
     * 模板类型编码
     */
    @ApiModelProperty(value = "模板类型编码", hidden = true)
    private String templateTypeCode;

    /**
     * 删除标记 1=删除，0=未删除
     */
    @ApiModelProperty(value = "删除标记 1=删除，0=未删除", hidden = true)
                            private Integer isDelete;

    /**
     * @return 
     */
    public String getMessTemplateId() {
        return messTemplateId;
    }

    public void setMessTemplateId(String messTemplateId) {
        this.messTemplateId = messTemplateId;
    }

    /**
     * @return 消息模板名称
     */
    public String getMessTemplateName() {
        return messTemplateName;
    }

    public void setMessTemplateName(String messTemplateName) {
        this.messTemplateName = messTemplateName;
    }

    /**
     * @return 消息模板内容
     */
    public String getMessTemplateContent() {
        return messTemplateContent;
    }

    public void setMessTemplateContent(String messTemplateContent) {
        this.messTemplateContent = messTemplateContent;
    }

    /**
     * @return 模板类型id
     */
    public String getTemplateTypeId() {
        return templateTypeId;
    }

    public void setTemplateTypeId(String templateTypeId) {
        this.templateTypeId = templateTypeId;
    }

    /**
     * @return 消息类型
     */
    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    /**
     * @return 系统类型
     */
    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    /**
     * @return 删除标记 1=删除，0=未删除
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getTemplateTypeCode() {
        return templateTypeCode;
    }

    public void setTemplateTypeCode(String templateTypeCode) {
        this.templateTypeCode = templateTypeCode;
    }

    @Override
    public String toString() {
        return "MessTemplateInfoPageVo{" +
            "messTemplateId=" + messTemplateId +
            ", messTemplateName=" + messTemplateName +
            ", messTemplateContent=" + messTemplateContent +
            ", templateTypeId=" + templateTypeId +
            ", messageType=" + messageType +
            ", systemType=" + systemType +
            ", isDelete=" + isDelete +
        "}";
    }
}
