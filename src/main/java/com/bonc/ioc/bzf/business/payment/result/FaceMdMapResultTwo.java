package com.bonc.ioc.bzf.business.payment.result;


import lombok.Data;
import org.apache.commons.collections4.map.CaseInsensitiveMap;

/**
 * 
{
    "data": {
        "cc": "本科",
        "rxrq": "20140901",
        "xxxs": "普通全日制",
        "xm": "任*",
        "zsbh": "****************",
        "byrq": "20180620",
        "zjhm": "2306021996********",
        "zymc": "计算机科学与技术",
        "yxmc": "黑龙*********"
    },
    "dataSize": "1",
    "status": 200,
    "desc": "处理成功",
    "responseId": "f91ec3c2292948eb879fae41445e59f8"
}
 *
 */
@Data
public class FaceMdMapResultTwo<T> {
	String busiCode;
	String code;
	String message;

	T data;
}


