package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@ApiModel(value="抵扣当期后盈余金额", description="抵扣当期后盈余金额")
@Data
public class PreviewBillsResultDeductedAmount implements java.io.Serializable{

    @ApiModelProperty(value = "计费科目 01 租金 02 押金")
    private String chargeSubjectNo;

    @ApiModelProperty(value = "抵扣后金额 负数为补缴金额 正数为可退可抵扣金额")
    private BigDecimal deductedAmount;
}
