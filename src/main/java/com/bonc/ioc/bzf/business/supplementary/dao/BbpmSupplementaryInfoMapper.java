package com.bonc.ioc.bzf.business.supplementary.dao;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryInfoEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.supplementary.vo.*;

import java.util.List;

/**
 * 追加单表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Mapper
public interface BbpmSupplementaryInfoMapper extends McpBaseMapper<BbpmSupplementaryInfoEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    List<BbpmSupplementaryInfoPageResultVo> selectByPageCustom(@Param("vo") BbpmSupplementaryInfoPageVo vo);

    /**
     * 分页查询追加单列表
     *
     * @param vo 请求参数 vo实体
     * @return 追加单列表
     */
    List<BbpmSupplementaryInfoPageResultVo> selectSupplementaryInfoPage(@Param("vo") BbpmSupplementaryInfoPageVo vo);

    /**
     * 统计
     *
     * @param vo 请求参数 vo实体
     * @return 统计结果
     */
    Integer statistic(@Param("vo") BbpmSupplementaryInfoPageVo vo);

    /**
     * 根据合同编号查询在途追加账单数量
     *
     * @param contractNo 合同编号
     * @return 追加账单数量
     */
    Integer selectDoingSupplementaryInfoSize(@Param("contractNo") String contractNo);

    /**
     * 根据日期尾号查询追加单号的序号
     * @param formattedDate
     * @return
     */
    String selectSupplementaryCodeSerial(@Param("formattedDate") String formattedDate);
}
