package com.bonc.ioc.bzf.business.payment.result;

 

import java.util.List;

import lombok.Data;
import org.apache.commons.collections4.map.CaseInsensitiveMap;
/**
 * {
    "data": [
        {
            "fzjg": "山东省建设厅",
            "fzrq": "2020-04-10 00:00:00",
            "zsyxq": "2025-02-04 00:00:00",
            "qyzcsd": "山东省",
            "qyfddbr": "王江波",
            "qymc": "威海市建设装饰工程有限公司",
            "tyshxydm": "913710001667078316",
            "zzlb": "设计",
            "zzzsh": "A237014960",
            "zzmc": "建筑装饰工程设计专项乙级"
        },
        {
            "fzjg": "山东省建设厅",
            "fzrq": "2017-03-23 00:00:00",
            "zsyxq": "2021-12-31 00:00:00",
            "qyzcsd": "山东省",
            "qyfddbr": "王江波",
            "qymc": "威海市建设装饰工程有限公司",
            "tyshxydm": "913710001667078316",
            "zzlb": "建筑业",
            "zzzsh": "D237026766",
            "zzmc": "建筑装修装饰工程专业承包贰级"
        }
    ],
    "dataSize": "1",
    "status": 200,
    "desc": "处理成功",
    "responseId": "f4f564139f30463f966c453d6b8c17b4"
  }
 *
 */
@Data
public class FaceMdListResult<T> {
//	Integer status;
//    String desc;
//    String dataSize;
//    String responseId;
	     
    List<T> data;

	String busiCode;
	String code;
	String message;

//	public Integer getStatus() {
//		return status;
//	}
//
//	public void setStatus(Integer status) {
//		this.status = status;
//	}
//
//	public String getDesc() {
//		return desc;
//	}
//
//	public void setDesc(String desc) {
//		this.desc = desc;
//	}
//
//	public String getDataSize() {
//		return dataSize;
//	}
//
//	public void setDataSize(String dataSize) {
//		this.dataSize = dataSize;
//	}
//
//	public String getResponseId() {
//		return responseId;
//	}
//
//	public void setResponseId(String responseId) {
//		this.responseId = responseId;
//	}
//
//	public List<T> getData() {
//		return data;
//	}
//
//	public void setData(List<T> data) {
//		this.data = data;
//	}
    
}


