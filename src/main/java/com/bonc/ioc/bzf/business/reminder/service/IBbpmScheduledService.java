package com.bonc.ioc.bzf.business.reminder.service;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmScheduledEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 缴费定时器 服务类
 *
 * <AUTHOR>
 * @date 2023-08-09
 * @change 2023-08-09 by binghong.tang for init
 */
public interface IBbpmScheduledService extends IMcpBaseService<BbpmScheduledEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    String insertRecord(BbpmScheduledVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmScheduledVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param cronId 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void removeByIdRecord(String cronId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param cronIdList 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> cronIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缴费定时器
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void updateByIdRecord(BbpmScheduledVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缴费定时器
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmScheduledVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缴费定时器
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void saveByIdRecord(BbpmScheduledVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缴费定时器
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmScheduledVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param cronId 需要查询的唯一标识符
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    BbpmScheduledVo selectByIdRecord(String cronId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    PageResult<List<BbpmScheduledPageResultVo>> selectByPageRecord(BbpmScheduledPageVo vo);
}
