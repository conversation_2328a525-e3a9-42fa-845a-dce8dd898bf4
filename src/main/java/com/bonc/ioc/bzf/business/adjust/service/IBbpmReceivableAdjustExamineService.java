package com.bonc.ioc.bzf.business.adjust.service;

import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustExamineEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 应收调整审核表 服务类
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
public interface IBbpmReceivableAdjustExamineService extends IMcpBaseService<BbpmReceivableAdjustExamineEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    String insertRecord(BbpmReceivableAdjustExamineVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    List<String> insertBatchRecord(List<BbpmReceivableAdjustExamineVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void removeByIdRecord(String id);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param idList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void removeByIdsRecord(List<String> idList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的应收调整审核表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void updateByIdRecord(BbpmReceivableAdjustExamineVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的应收调整审核表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void updateBatchByIdRecord(List<BbpmReceivableAdjustExamineVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应收调整审核表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void saveByIdRecord(BbpmReceivableAdjustExamineVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的应收调整审核表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void saveBatchByIdRecord(List<BbpmReceivableAdjustExamineVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    BbpmReceivableAdjustExamineVo selectByIdRecord(String id);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    PageResult<List<BbpmReceivableAdjustExaminePageResultVo>> selectByPageRecord(BbpmReceivableAdjustExaminePageVo vo);
}
