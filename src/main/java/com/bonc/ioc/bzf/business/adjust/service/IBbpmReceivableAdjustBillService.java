package com.bonc.ioc.bzf.business.adjust.service;

import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustBillEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 应收调整账单表 服务类
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
public interface IBbpmReceivableAdjustBillService extends IMcpBaseService<BbpmReceivableAdjustBillEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    String insertRecord(BbpmReceivableAdjustBillVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    List<String> insertBatchRecord(List<BbpmReceivableAdjustBillVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void removeByIdRecord(String id);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param idList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void removeByIdsRecord(List<String> idList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的应收调整账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void updateByIdRecord(BbpmReceivableAdjustBillVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的应收调整账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void updateBatchByIdRecord(List<BbpmReceivableAdjustBillVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应收调整账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void saveByIdRecord(BbpmReceivableAdjustBillVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的应收调整账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void saveBatchByIdRecord(List<BbpmReceivableAdjustBillVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    BbpmReceivableAdjustBillVo selectByIdRecord(String id);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    PageResult<List<BbpmReceivableAdjustBillPageResultVo>> selectByPageRecord(BbpmReceivableAdjustBillPageVo vo);

    /**
     * 试算
     * @param vo
     * @return
     */
    List<CalculationResultVo> adjustBillView(CalculationParamVo vo);

    /**
     * 更新账单
     * @param vo
     * @return
     */
    Boolean updateBill(CalculationParamVo vo);
}
