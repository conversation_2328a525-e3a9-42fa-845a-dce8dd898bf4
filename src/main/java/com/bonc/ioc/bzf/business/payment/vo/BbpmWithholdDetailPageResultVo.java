package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报盘明细查询
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@ApiModel(value = "BbpmWithholdDetailPageResultVo对象", description = "报盘明细查询")
@Data
public class BbpmWithholdDetailPageResultVo extends McpBasePageVo implements Serializable {

    /**
     * 账单ID
     */
    @ApiModelProperty(value = "账单ID")
    private String billId;

    /**
     * 账单编号
     */
    @ApiModelProperty(value = "账单编号")
    private String billNo;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
    private String billCycle;

    /**
     * 账单周期(从1开始递增)
     */
    @ApiModelProperty(value = "账单周期(从1开始递增)")
    private Integer chargeSubjectPeriod;
    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
    private String chargeSubjectBeginDate;

    /**
     * 收费科目终止日期
     */
    @ApiModelProperty(value = "收费科目终止日期")
    private String chargeSubjectEndDate;

    /**
     * 租户名称
     */
    @ApiModelProperty(value = "承租人")
    private String tenantName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String certNo;
    @ApiModelProperty(value = "证件类型")
    @McpDictPoint(dictCode = "WHITELIST_CERTIFICATE_TYPE_CODE")
    private String certType;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String tenantMobile;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;


    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String houseName;
    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "代扣金额")
    private BigDecimal amount;

    /**
     * 账单对应的收费科目
     */
    @ApiModelProperty(value = "账单对应的收费科目")
    private String billChargeSubject;

    /**
     * 账单对应的收费科目名称
     */
    @ApiModelProperty(value = "账单对应的收费科目名称")
    @McpDictPoint(dictCode = "BILLING_YC_BILLCHARGESUBJECT")
    private String billChargeSubjectName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "扣款时间")
    private String chargeTime;
    @ApiModelProperty(value = "付款人账号")
    private String tenantBankAccountNo;
    @ApiModelProperty(value = "请求编号")
    private String reqNo;
    @ApiModelProperty(value = "交易流水号")
    private String orderNo;
    @ApiModelProperty(value = "合同编号")
    private String contractCode	;
    @ApiModelProperty(value = "状态")
    @McpDictPoint(dictCode = "WITHHOLD_STATUS", overTransCopyTo = "statusName")
    private String status;
    @ApiModelProperty(value = "失败原因")
    private String message;
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "全量分页标识Y:是,N:否")
    private String fullPage;


    @ApiModelProperty(value = "当前页码")
    private Integer current;

    @ApiModelProperty(value = "每页最大数目")
    private Integer size;

    @ApiModelProperty(value = "公租房备案号")
    private String publicRecordNo;


}
