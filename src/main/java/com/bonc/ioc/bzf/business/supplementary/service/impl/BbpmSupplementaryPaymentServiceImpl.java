package com.bonc.ioc.bzf.business.supplementary.service.impl;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPaymentEntity;
import com.bonc.ioc.bzf.business.supplementary.dao.BbpmSupplementaryPaymentMapper;
import com.bonc.ioc.bzf.business.supplementary.enums.DelFlagEnum;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmSupplementaryPaymentProductService;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmSupplementaryPaymentService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

/**
 * 追加账单表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Slf4j
@Service
public class BbpmSupplementaryPaymentServiceImpl extends McpBaseServiceImpl<BbpmSupplementaryPaymentEntity> implements IBbpmSupplementaryPaymentService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmSupplementaryPaymentMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmSupplementaryPaymentService baseService;

    /**
     * 追加账单产品相关 服务实例
     */
    @Resource
    private IBbpmSupplementaryPaymentProductService supplementaryPaymentProductService;

    /**
     * 字典相关 session实例
     */
    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbpmSupplementaryPaymentVo vo) {
        if (vo == null) {
            return null;
        }

        BbpmSupplementaryPaymentEntity entity = new BbpmSupplementaryPaymentEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setPaymentId(null);
        if (!baseService.insert(entity)) {
            log.error("追加账单表新增失败:" + entity.toString());
            throw new McpException("追加账单表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getPaymentId(), 1)) {
                log.error("追加账单表新增后保存历史失败:" + entity.toString());
                throw new McpException("追加账单表新增后保存历史失败");
            }

            log.debug("追加账单表新增成功:" + entity.getPaymentId());
            return entity.getPaymentId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmSupplementaryPaymentVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmSupplementaryPaymentEntity> entityList = new ArrayList<>();
        for (BbpmSupplementaryPaymentVo item : voList) {
            BbpmSupplementaryPaymentEntity entity = new BbpmSupplementaryPaymentEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmSupplementaryPaymentEntity item : entityList) {
            item.setPaymentId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("追加账单表新增失败");
            throw new McpException("追加账单表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbpmSupplementaryPaymentEntity::getPaymentId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("追加账单表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("追加账单表批量新增后保存历史失败");
            }

            log.debug("追加账单表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * 批量新增并且新增产品信息
     *
     * @param voList 需要保存的记录
     * @return 主键id列表
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchAndProduct(List<BbpmSupplementaryPaymentVo> voList) {
        List<String> idList = insertBatchRecord(voList);
        List<BbpmSupplementaryPaymentProductVo> supplementaryPaymentProductList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            List<BbpmSupplementaryPaymentProductVo> tempList = voList.get(i).getSupplementaryPaymentProductList();
            if (CollectionUtils.isNotEmpty(tempList)) {
                for (BbpmSupplementaryPaymentProductVo productVo : tempList) {
                    productVo.setParentId(idList.get(i));
                    productVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
                }
                supplementaryPaymentProductList.addAll(tempList);
            }
        }
        supplementaryPaymentProductService.insertBatchRecord(supplementaryPaymentProductList);
        return idList;
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param paymentId 需要删除的追加账单id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String paymentId) {
        if (!StringUtils.isEmpty(paymentId)) {
            if (!baseService.saveOperationHisById(paymentId, 3)) {
                log.error("追加账单表删除后保存历史失败:" + paymentId);
                throw new McpException("追加账单表删除后保存历史失败");
            }

            if (!baseService.removeById(paymentId)) {
                log.error("追加账单表删除失败");
                throw new McpException("追加账单表删除失败" + paymentId);
            }
        } else {
            throw new McpException("追加账单表删除失败追加账单id为空");
        }
    }

    /**
     * 根据上级id删除(逻辑删除)
     *
     * @param parentId 上级id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByParentId(String parentId) {
        List<BbpmSupplementaryPaymentVo> paymentList = selectListByParentId(parentId);
        baseMapper.updateDelFlagByParentId(parentId, DelFlagEnum.DELETED.getCode());
        if (CollectionUtils.isNotEmpty(paymentList)) {
            for (BbpmSupplementaryPaymentVo paymentVo : paymentList) {
                supplementaryPaymentProductService.removeByParentId(paymentVo.getPaymentId());
            }
        }
    }

    /**
     * 根据上级id删除(物理删除)
     *
     * @param parentId 上级id
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void deleteByParentId(String parentId) {
        baseMapper.deleteByParentId(parentId);
        supplementaryPaymentProductService.deleteBySupplementaryId(parentId);
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param paymentIdList 需要删除的追加账单id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> paymentIdList) {
        if (!CollectionUtils.isEmpty(paymentIdList)) {
            int oldSize = paymentIdList.size();
            paymentIdList = paymentIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(paymentIdList) || oldSize != paymentIdList.size()) {
                throw new McpException("追加账单表批量删除失败 存在主键id为空的记录" + StringUtils.join(paymentIdList));
            }

            if (!baseService.saveOperationHisByIds(paymentIdList, 3)) {
                log.error("追加账单表批量删除后保存历史失败:" + StringUtils.join(paymentIdList));
                throw new McpException("追加账单表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(paymentIdList)) {
                log.error("追加账单表批量删除失败");
                throw new McpException("追加账单表批量删除失败" + StringUtils.join(paymentIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的追加账单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmSupplementaryPaymentVo vo) {
        if (vo != null) {
            BbpmSupplementaryPaymentEntity entity = new BbpmSupplementaryPaymentEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getPaymentId())) {
                throw new McpException("追加账单表更新失败传入追加账单id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("追加账单表更新失败");
                throw new McpException("追加账单表更新失败" + entity.getPaymentId());
            } else {
                if (!baseService.saveOperationHisById(entity.getPaymentId(), 2)) {
                    log.error("追加账单表更新后保存历史失败:" + entity.getPaymentId());
                    throw new McpException("追加账单表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("追加账单表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的追加账单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmSupplementaryPaymentVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmSupplementaryPaymentEntity> entityList = new ArrayList<>();

            for (BbpmSupplementaryPaymentVo item : voList) {
                BbpmSupplementaryPaymentEntity entity = new BbpmSupplementaryPaymentEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getPaymentId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("追加账单表批量更新失败 存在追加账单id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("追加账单表批量更新失败");
                throw new McpException("追加账单表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getPaymentId())).map(BbpmSupplementaryPaymentEntity::getPaymentId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("追加账单表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("追加账单表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的追加账单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmSupplementaryPaymentVo vo) {
        if (vo != null) {
            BbpmSupplementaryPaymentEntity entity = new BbpmSupplementaryPaymentEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("追加账单表保存失败");
                throw new McpException("追加账单表保存失败" + entity.getPaymentId());
            } else {
                if (!baseService.saveOperationHisById(entity.getPaymentId(), 4)) {
                    log.error("追加账单表保存后保存历史失败:" + entity.getPaymentId());
                    throw new McpException("追加账单表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("追加账单表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的追加账单表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmSupplementaryPaymentVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmSupplementaryPaymentEntity> entityList = new ArrayList<>();

            for (BbpmSupplementaryPaymentVo item : voList) {
                BbpmSupplementaryPaymentEntity entity = new BbpmSupplementaryPaymentEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("追加账单表批量保存失败");
                throw new McpException("追加账单表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getPaymentId())).map(BbpmSupplementaryPaymentEntity::getPaymentId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("追加账单表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("追加账单表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param paymentId 需要查询的追加账单id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmSupplementaryPaymentVo selectByIdRecord(String paymentId) {
        BbpmSupplementaryPaymentVo vo = new BbpmSupplementaryPaymentVo();

        if (!StringUtils.isEmpty(paymentId)) {
            BbpmSupplementaryPaymentEntity entity = baseService.selectById(paymentId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 根据上级id查询追加账单列表
     *
     * @param parentId 上级id
     * @return 追加账单列表
     */
    @Override
    public List<BbpmSupplementaryPaymentVo> selectListByParentId(String parentId) {
        List<BbpmSupplementaryPaymentVo> supplementaryPaymentList = baseMapper.selectListByParentId(parentId);
        if (CollectionUtils.isNotEmpty(supplementaryPaymentList)) {
            for (BbpmSupplementaryPaymentVo paymentVo : supplementaryPaymentList) {
                paymentVo.setSupplementaryPaymentProductList(supplementaryPaymentProductService.selectListByParentId(paymentVo.getPaymentId()));
            }
        }
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(supplementaryPaymentList);
        return supplementaryPaymentList;
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmSupplementaryPaymentPageResultVo>> selectByPageRecord(BbpmSupplementaryPaymentPageVo vo) {
        List<BbpmSupplementaryPaymentPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
