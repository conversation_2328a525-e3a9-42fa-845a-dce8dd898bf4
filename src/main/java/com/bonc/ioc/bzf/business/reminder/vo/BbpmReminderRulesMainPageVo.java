package com.bonc.ioc.bzf.business.reminder.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 缴费提醒规则--主表 实体类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@ApiModel(value="BbpmReminderRulesMainPageVo对象", description="缴费提醒规则--主表")
public class BbpmReminderRulesMainPageVo extends McpBasePageVo implements Serializable{


    /**
     * 规则编号
     */
    @ApiModelProperty(value = "规则编号")
    @NotBlank(message = "规则编号不能为空",groups = {UpdateValidatorGroup.class})
                                  private String rulesId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    @NotBlank(message = "规则名称不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String ruleName;

    /**
     * 合同类型:01散租合同,02趸租合同,03趸租管理协议
     */
    @ApiModelProperty(value = "合同类型:01散租合同,02趸租合同,03趸租管理协议")
                            private String contractType;

    /**
     * 业务类型:01公租房,02保租房
     */
    @ApiModelProperty(value = "业务类型:01公租房,02保租房")
                            private String businessType;

    /**
     * 启用状态:01停用,02启用
     */
    @ApiModelProperty(value = "启用状态:01停用,02启用")
                            private String enableStatus;

    /**
     * 启用时间
     */
    @ApiModelProperty(value = "启用时间")
//            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private String activationTime;
    @ApiModelProperty(value = "启用时间开始")
    private String activationTimeStart;
    @ApiModelProperty(value = "启用时间结束")
    private String activationTimeEnd;

    /**
     * 停用时间
     */
    @ApiModelProperty(value = "停用时间")
//            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private String downTime;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    @ApiModelProperty(value = "创建时间" )
    private String createTime;
    @ApiModelProperty(value = "创建时间开始" )
    private String createTimeStart;
    @ApiModelProperty(value = "创建时间结束" )
    private String createTimeEnd;

    @ApiModelProperty(value = "更新时间开始" )
    private String modifyTimeStart;
    @ApiModelProperty(value = "更新时间开始" )
    private String modifyTimeEnd;


    public String getModifyTimeStart() {
        return modifyTimeStart;
    }

    public void setModifyTimeStart(String modifyTimeStart) {
        this.modifyTimeStart = modifyTimeStart;
    }

    public String getModifyTimeEnd() {
        return modifyTimeEnd;
    }

    public void setModifyTimeEnd(String modifyTimeEnd) {
        this.modifyTimeEnd = modifyTimeEnd;
    }

    public String getActivationTimeStart() {
        return activationTimeStart;
    }

    public void setActivationTimeStart(String activationTimeStart) {
        this.activationTimeStart = activationTimeStart;
    }

    public String getActivationTimeEnd() {
        return activationTimeEnd;
    }

    public void setActivationTimeEnd(String activationTimeEnd) {
        this.activationTimeEnd = activationTimeEnd;
    }

    public String getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * @return 规则编号
     */
    public String getRulesId() {
        return rulesId;
    }

    public void setRulesId(String rulesId) {
        this.rulesId = rulesId;
    }

    /**
     * @return 规则名称
     */
    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    /**
     * @return 合同类型:01散租合同,02趸租合同,03趸租管理协议
     */
    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    /**
     * @return 业务类型:01公租房,02保租房
     */
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * @return 启用状态:01停用,02启用
     */
    public String getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(String enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getActivationTime() {
        return activationTime;
    }

    public void setActivationTime(String activationTime) {
        this.activationTime = activationTime;
    }

    public String getDownTime() {
        return downTime;
    }

    public void setDownTime(String downTime) {
        this.downTime = downTime;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmReminderRulesMainPageVo{" +
            "rulesId=" + rulesId +
            ", ruleName=" + ruleName +
            ", contractType=" + contractType +
            ", businessType=" + businessType +
            ", enableStatus=" + enableStatus +
            ", activationTime=" + activationTime +
            ", downTime=" + downTime +
            ", delFlag=" + delFlag +
        "}";
    }
}
