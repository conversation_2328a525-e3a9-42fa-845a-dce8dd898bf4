package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 合同标的表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-20
 * @change 2023-05-20 by sx for init
 */
@Data
@ApiModel(value = "BbctContractSubjectMatterVo合同标的对象", description = "合同标的")
public class BbctContractSubjectMatterVo extends McpBaseVo implements Serializable {


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateValidatorGroup.class})
    private String subjectMatterId;

    /**
     * 关系表ID
     */
    @ApiModelProperty(value = "关系表ID")
    private String relationId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
    private String productNo;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 项目编号/运营项目
     */
    @ApiModelProperty(value = "项目编号/运营项目")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 租金标准（元/m²/月）
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）")
    private String rentStandardNo;

    /**
     * 租金标准（元/m²/月）名称
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）名称")
    private String rentStandardName;

    @ApiModelProperty(value = "租金标准（元/m²/月）金额")
    private String rent;

    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
    private Double leaseTermRent;

    /**
     * 选房渠道
     */
    @ApiModelProperty(value = "选房渠道")
    private String houseSelectionChannelsNo;

    /**
     * 选房渠道名称
     */
    @ApiModelProperty(value = "选房渠道名称")
    private String houseSelectionChannelsName;

    /**
     * 小区或楼宇
     */
    @ApiModelProperty(value = "小区或楼宇")
    private String communityBuildingNo;

    /**
     * 小区或楼宇名称
     */
    @ApiModelProperty(value = "小区或楼宇名称")
    private String communityBuildingName;

    /**
     * 组团
     */
    @ApiModelProperty(value = "组团")
    private String groupNo;

    /**
     * 组团名称
     */
    @ApiModelProperty(value = "组团名称")
    private String groupName;

    /**
     * 楼号
     */
    @ApiModelProperty(value = "楼号")
    private String buildingNo;

    /**
     * 楼名称
     */
    @ApiModelProperty(value = "楼名称")
    private String buildingName;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    private String unitNo;

    /**
     * 单元名称
     */
    @ApiModelProperty(value = "单元名称")
    private String unitName;

    /**
     * 户型
     */
    @ApiModelProperty(value = "户型")
    private String houseTypeNo;

    /**
     * 户型名称
     */
    @ApiModelProperty(value = "户型名称")
    private String houseTypeName;

    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
    private String houseStructArea;

    /**
     * 套内面积
     */
    @ApiModelProperty(value = "套内面积")
    private String innerSleeveArea;

    /**
     * 使用面积
     */
    @ApiModelProperty(value = "使用面积")
    private String useArea;

    /**
     * 套型
     */
    @ApiModelProperty(value = "套型")
    private String jacketed;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String roomNo;

    /**
     * 房间名称
     */
    @ApiModelProperty(value = "房间名称")
    private String roomName;

    /**
     * 小区所在区
     */
    @ApiModelProperty(value = "小区所在区")
    private String communityRegion;

    /**
     * 小区地址
     */
    @ApiModelProperty(value = "小区地址")
    private String communityAddress;

    @ApiModelProperty(value = "产品扩展")
    private String productExtend;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 标的业务分类1
     */
    @ApiModelProperty(value = "标的业务分类1")
    private String productBussinessType1;

    /**
     * 标的业务分类2
     */
    @ApiModelProperty(value = "标的业务分类2")
    private String productBussinessType2;

    /**
     * 标的业务分类3
     */
    @ApiModelProperty(value = "标的业务分类3")
    private String productBussinessType3;

    /**
     * 所在层
     */
    @ApiModelProperty(value = "所在层")
    private String currentFloorNo;

    /**
     * 总层数
     */
    @ApiModelProperty(value = "总层数")
    private String totalFloorNo;

    /**
     * 房间朝向
     */
    @ApiModelProperty(value = "房间朝向")
    private String houseOrientation;

    @ApiModelProperty(value = "项目简称")
    private String projectShortName;

    @ApiModelProperty(value = "运营主体类型")
    private String operateEntityType;

    @ApiModelProperty(value = "运营主体名称")
    private String operateEntityName;

    @ApiModelProperty(value = "运营单位业务中台编号")
    private String operateUnitBusinessNo;

    @ApiModelProperty(value = "运营单位编号（NCC）")
    private String operateUnitNo;

    @ApiModelProperty(value = "运营单位名称")
    private String operateUnitName;

    @ApiModelProperty(value = "项目区域业务中台编号")
    private String projectAreaBusinessNo;

    @ApiModelProperty(value = "项目区域编号（NCC）")
    private String projectAreaNo;

    @ApiModelProperty(value = "项目区域名称")
    private String projectAreaName;

    @ApiModelProperty(value = "项目业态")
    private String projectFormat;

    @ApiModelProperty(value = "所在小区或楼宇名称")
    private String projectEstate;
    /**
     * 居室名称
     */
    @ApiModelProperty(value = "居室名称")
    private String bedRoom;

    @ApiModelProperty(value = "租金单位名称（元/㎡/月、元/月")
    private String rentUnit;

    @ApiModelProperty("房源编号NCC编码")
    private String houseNoNcc;
    @ApiModelProperty("项目编号NCC编码")
    private String projectNoNcc;
    @ApiModelProperty("房屋租赁类型 01 公祖， 07 保租")
    private String houseHireType;
    @ApiModelProperty("房间号")
    private String houseNo;
    @ApiModelProperty("床号")
    private String bedNo;
    @ApiModelProperty("租赁类型：按套，按间，按床")
    private String leaseMode;
    @ApiModelProperty("房态状态")
    private String historyState;
    @ApiModelProperty("房源编号")
    private String houseCode;
    @ApiModelProperty("来源节点(配租登记，看房，选房，入住)")
    private String sourceNode;
    @ApiModelProperty("定价策率编号")
    private String priceSideRatioNo;
    @ApiModelProperty("定价策率名称")
    private String priceSideRatioName;
    @ApiModelProperty("月租金规则编号（工银）")
    private String monthlyRentRulesNo;
    @ApiModelProperty("月租金规则名称（工银）")
    private String monthlyRentRulesName;
    @ApiModelProperty("公祖租金标准")
    private Double publicRentStandard;
    @ApiModelProperty("市场租金标准")
    private Double marketRentStandard;
    @ApiModelProperty("人才租金标准")
    private Double talentRentStandard;

    @ApiModelProperty("房屋实勘图")
    private String imgId;

    /**
     * rrId
     */
    @ApiModelProperty("rrId")
    private String rrId;

    /**
     * 是否有燃气费(0.否 1.是)
     */
    @ApiModelProperty("是否有燃气费(0.否 1.是)")
    private String heatingPayment;

    @ApiModelProperty("行政部门审核房号")
    private String houseAuditCode;

    @ApiModelProperty("主体结构")
    private String majorStructure;

    @ApiModelProperty("预测或实测")
    private String measurement;
}
