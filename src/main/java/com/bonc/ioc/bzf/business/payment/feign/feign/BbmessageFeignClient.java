package com.bonc.ioc.bzf.business.payment.feign.feign;


import com.bonc.ioc.bzf.business.payment.feign.fallback.BbmessageFeignClientFallback;
import com.bonc.ioc.bzf.business.payment.result.MessageInterfaceParamVo;
import com.bonc.ioc.bzf.business.payment.result.MessageInterfaceResponseVo;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Mcp sys server 系统管理
 *
 * <AUTHOR>
 * @date 2021/5/20 11:54
 * @change: 2021/5/20 11:54 by wtl for init
 */
@FeignClient(value = "bzf-business-message",fallback = BbmessageFeignClientFallback.class,
        configuration = FeignExceptionConfiguration.class)
public interface BbmessageFeignClient {

    /**
     * getTemplateInfo 获取模板信息
     * @param sendMessageTypeList 消息类型
     */
    @PostMapping(value = "/bzf-business-message/template/getTemplateContent")
    AppReply<List<TemplateInfoContentVo>> getTemplateInfo(@RequestBody List<String> sendMessageTypeList);

    /**
     * realTimeMessagePush 短信实时接口
     *
     * @param paramVo 发送的通知里面的内容
     * @return  com.bonc.ioc.common.util.AppReply<java.lang.String>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/06/10 18:01:59
     * @change 2022/06/10 18:01:59 by sqj for init
     */
    @PostMapping(value = "/bzf-business-message/bzfsms/allContentRealTimeMessagePush")
    AppReply<MessageInterfaceResponseVo> realTimeInternalMessagePush(@RequestBody MessageInterfaceParamVo paramVo);

    /**
     * realTimemailMessagePush 站内信实时接口
     *
     * @param paramVo 发送的通知里面的内容
     * @return  com.bonc.ioc.common.util.AppReply<java.lang.String>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/06/10 18:01:59
     * @change 2022/06/10 18:01:59 by sqj for init
     */
    @PostMapping(value = "/bzf-business-message/znx/allContentRealTimeMessagePush")
    AppReply<MessageInterfaceResponseVo> realTimemailMessagePush(@RequestBody MessageInterfaceParamVo paramVo);

    /**
     * realTimemailMessagePush 站内信实时接口
     *
     * @param paramVo 发送的通知里面的内容
     * @return  com.bonc.ioc.common.util.AppReply<java.lang.String>
     * @since 1.0.0
     * <AUTHOR>
     * @date 2022/06/10 18:01:59
     * @change 2022/06/10 18:01:59 by sqj for init
     */
    @PostMapping(value = "/bzf-business-message/bzfsms/allContentRealTimeMessagePush")
    AppReply<MessageInterfaceResponseVo> realTimemailMessagePushv2(@RequestBody MessageInterfaceParamVo paramVo);

    /**消息模板分页接口
     * @param
     * @return
     * <AUTHOR> @date
     * @change
     * @since 1.0.0
     */
    @GetMapping(value = "/bzf-business-message/messTemplateInfoEntity/selectByPage")
    @FeignExceptionCheck
    AppReply<PageResult<List<MessTemplateInfoPageResultVo>>> selectByPageTemplateList(@SpringQueryMap MessTemplateInfoPageVo vo);

    /**模板类型列表
     * @param
     * @return
     * <AUTHOR> @date
     * @change
     * @since 1.0.0
     */
    @PostMapping(value = "/bzf-business-message/messTemplateTypeEntity/selectTempTypeList")
    @FeignExceptionCheck
    AppReply<List<MessTemplateTypeVo>> selectTempTypeList(@RequestBody MessTemplateTypeVo vo);

    /**根据模板类型id查询参数对应关系
     * @param
     * @return
     * <AUTHOR> @date
     * @change
     * @since 1.0.0
     */
    @GetMapping(value = "/bzf-business-message/messTemplateParamEntity/selectParamByTemplateTypeId")
    @FeignExceptionCheck
    AppReply<List<MessTemplateParamVo>> selectParamByTemplateTypeId(@RequestParam("templateTypeId") String templateTypeId);

    /**新增或更新模板
     * @param
     * @return
     * <AUTHOR> @date
     * @change
     * @since 1.0.0
     */
    @PostMapping(value = "/bzf-business-message/messTemplateInfoEntity/saveById")
    @FeignExceptionCheck
    AppReply saveByIdRecord(@RequestBody MessTemplateInfoVo vo);

    /**查看模板(短信。消息)
     * @param
     * @return
     * <AUTHOR> @date
     * @change
     * @since 1.0.0
     */
    @GetMapping(value = "/bzf-business-message/messTemplateInfoEntity/selectById")
    @FeignExceptionCheck
    AppReply<MessTemplateInfoVo> selectTemplateById(@RequestParam("messTemplateId") String messTemplateId);

    /**根据主键删除模板(短信，消息)
     * @param
     * @return
     * <AUTHOR> @date
     * @change
     * @since 1.0.0
     */
    @PostMapping(value = "/bzf-business-message/messTemplateInfoEntity/removeById")
    @FeignExceptionCheck
    AppReply removeTemplateByIdRecord(@RequestBody String messTemplateId);

    @GetMapping(value = "/bzf-business-message/messTemplateInfoEntity/selectAllTemplate")
    @FeignExceptionCheck
    AppReply<List<MessTemplateInfoPageResultVo>> selectAllTemplate(@RequestParam("messageType") String messageType,
                                                                   @RequestParam("systemType") String systemType,
                                                                   @RequestParam("templateTypeCode") String templateTypeCode);
}
