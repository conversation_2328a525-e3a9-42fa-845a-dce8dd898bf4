package com.bonc.ioc.bzf.business.adjust.service;

import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.apache.ibatis.annotations.Param;

/**
 * 应收调整 服务类
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
public interface IBbpmReceivableAdjustService extends IMcpBaseService<BbpmReceivableAdjustEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    String insertRecord(BbpmReceivableAdjustVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    List<String> insertBatchRecord(List<BbpmReceivableAdjustVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void removeByIdRecord(String id);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param idList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void removeByIdsRecord(List<String> idList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的应收调整
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void updateByIdRecord(BbpmReceivableAdjustVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的应收调整
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void updateBatchByIdRecord(List<BbpmReceivableAdjustVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应收调整
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    String saveByIdRecord(BbpmReceivableAdjustVo vo);

    String adjustmentImport(BbpmReceivableAdjustVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的应收调整
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    void saveBatchByIdRecord(List<BbpmReceivableAdjustVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    BbpmReceivableAdjustVo selectByIdRecord(String id);

    BbpmReceivableAdjustVo selectByCcid(String id);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    PageResult<List<BbpmReceivableAdjustPageResultVo>> selectByPageRecord(BbpmReceivableAdjustPageVo vo);

    /**
     * selectByExportList 导出数据查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    List<AdjustBillExportVO> selectByExportList(BbpmReceivableAdjustPageVo vo);

    void updateForTk(AdjustGxVo vo);

    AdjustTj selectForTj();

    List<BbpmBillForAdjustPageVo> selectBillForAdjust(String adjustId);

    String[] selectContractForAdjustId(String id);

    /**
     * 修改追加账单状态
     * @param id
     * @param status
     */
    void updateAdjustStatus(String id,String status);
}
