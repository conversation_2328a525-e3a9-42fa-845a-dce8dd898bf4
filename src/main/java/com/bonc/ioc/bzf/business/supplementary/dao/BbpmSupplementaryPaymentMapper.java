package com.bonc.ioc.bzf.business.supplementary.dao;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPaymentEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.supplementary.vo.*;

import java.util.List;

/**
 * 追加账单表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Mapper
public interface BbpmSupplementaryPaymentMapper extends McpBaseMapper<BbpmSupplementaryPaymentEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    List<BbpmSupplementaryPaymentPageResultVo> selectByPageCustom(@Param("vo") BbpmSupplementaryPaymentPageVo vo);

    /**
     * 根据上级id更新删除标识
     *
     * @param parentId 上级id
     * @param delFlag  删除标识
     */
    void updateDelFlagByParentId(@Param("parentId") String parentId, @Param("delFlag") String delFlag);

    /**
     * 根据上级id删除
     *
     * @param parentId 上级id
     */
    void deleteByParentId(@Param("parentId") String parentId);

    /**
     * 根据上级id查询追加账单列表
     *
     * @param parentId 上级id
     * @return 追加账单列表
     */
    List<BbpmSupplementaryPaymentVo> selectListByParentId(@Param("parentId") String parentId);
}
