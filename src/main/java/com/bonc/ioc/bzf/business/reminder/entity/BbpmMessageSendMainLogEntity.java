package com.bonc.ioc.bzf.business.reminder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 催缴规则消息发送日志--子表  实体类
 *
 * <AUTHOR>
 * @date 2023-08-09
 * @change 2023-08-09 by binghong.tang for init
 */
@TableName("bbpm_message_send_main_log")
@ApiModel(value="BbpmMessageSendMainLogEntity对象", description="催缴规则消息发送日志--子表 ")
public class BbpmMessageSendMainLogEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_MAIN_ID = "main_id";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_ERROR_MSQ = "error_msq";
    public static final String FIELD_ERROR_MSQ_ALL = "error_msq_all";

    /**
     * 唯一标识
     */
    @ApiModelProperty(value = "唯一标识")
                                @TableId(value = "main_id", type = IdType.ASSIGN_UUID)
                                  private String mainId;

    /**
     * 任务状态ST-开始,EN-完成,EX-异常
     */
    @ApiModelProperty(value = "任务状态ST-开始,EN-完成,EX-异常")
                            private String status;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
                            private String errorMsq;

    /**
     * 详细错误信息
     */
    @ApiModelProperty(value = "详细错误信息")
                            private String errorMsqAll;
    @ApiModelProperty(value = "请求消息中心报文")
    private String request;
    @ApiModelProperty(value = "消息中心响应报文")
    private String response;

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }
    /**
     * @return 唯一标识
     */
    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    /**
     * @return 任务状态ST-开始,EN-完成,EX-异常
     */
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return 错误信息
     */
    public String getErrorMsq() {
        return errorMsq;
    }

    public void setErrorMsq(String errorMsq) {
        this.errorMsq = errorMsq;
    }

    /**
     * @return 详细错误信息
     */
    public String getErrorMsqAll() {
        return errorMsqAll;
    }

    public void setErrorMsqAll(String errorMsqAll) {
        this.errorMsqAll = errorMsqAll;
    }

      @Override
    public String toString() {
        return "BbpmMessageSendMainLogEntity{" +
            "mainId=" + mainId +
            ", status=" + status +
            ", errorMsq=" + errorMsq +
            ", errorMsqAll=" + errorMsqAll +
        "}";
    }
}