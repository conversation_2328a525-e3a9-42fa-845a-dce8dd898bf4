package com.bonc.ioc.bzf.business.payment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 账单与收款对应关系表v3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@TableName("bbpm_bill_collection_relationship")
@ApiModel(value="BbpmBillCollectionRelationshipEntity对象", description="账单与收款对应关系表v3.0")
public class BbpmBillCollectionRelationshipEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_BILL_COLLECTION_ID = "bill_collection_id";
    public static final String FIELD_BILL_NO = "bill_no";
    public static final String FIELD_COLLECTION_NO = "collection_no";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
                                @TableId(value = "bill_collection_id", type = IdType.ASSIGN_UUID)
                                  private String billCollectionId;

    /**
     * 账单编号
     */
    @ApiModelProperty(value = "账单编号")
                            private String billNo;

    /**
     * 收款编号
     */
    @ApiModelProperty(value = "收款编号")
                            private String collectionNo;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 主键id
     */
    public String getBillCollectionId() {
        return billCollectionId;
    }

    public void setBillCollectionId(String billCollectionId) {
        this.billCollectionId = billCollectionId;
    }

    /**
     * @return 账单编号
     */
    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    /**
     * @return 收款编号
     */
    public String getCollectionNo() {
        return collectionNo;
    }

    public void setCollectionNo(String collectionNo) {
        this.collectionNo = collectionNo;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmBillCollectionRelationshipEntity{" +
            "billCollectionId=" + billCollectionId +
            ", billNo=" + billNo +
            ", collectionNo=" + collectionNo +
            ", delFlag=" + delFlag +
        "}";
    }
}