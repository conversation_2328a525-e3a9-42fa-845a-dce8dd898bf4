package com.bonc.ioc.bzf.business.adjust.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdjustBillDTO implements java.io.Serializable{
    @ApiModelProperty(value = "账单编号")
    private Long billCode;

    @ApiModelProperty(value = "调整后应缴金额")
    private BigDecimal adjustMoney;

    @ApiModelProperty(value = "企业/个人标示--01企业，02个人")
    private String ext1;
}
