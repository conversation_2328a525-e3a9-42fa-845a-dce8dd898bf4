package com.bonc.ioc.bzf.business.reminder.service;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendSubLogEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 催缴规则消息发送日志--子表  服务类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
public interface IBbpmMessageSendSubLogService extends IMcpBaseService<BbpmMessageSendSubLogEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    String insertRecord(BbpmMessageSendSubLogVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmMessageSendSubLogVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param noticeId 需要删除的通知ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void removeByIdRecord(String noticeId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param noticeIdList 需要删除的通知ID
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> noticeIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void updateByIdRecord(BbpmMessageSendSubLogVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmMessageSendSubLogVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void saveByIdRecord(BbpmMessageSendSubLogVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的催缴规则消息发送日志--子表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmMessageSendSubLogVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param noticeId 需要查询的通知ID
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    BbpmMessageSendSubLogVo selectByIdRecord(String noticeId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    PageResult<List<BbpmMessageSendSubLogPageResultVo>> selectByPageRecord(BbpmMessageSendSubLogPageVo vo);
}
