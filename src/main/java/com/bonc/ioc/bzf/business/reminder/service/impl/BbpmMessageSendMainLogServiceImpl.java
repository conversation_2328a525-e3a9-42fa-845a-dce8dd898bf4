package com.bonc.ioc.bzf.business.reminder.service.impl;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendMainLogEntity;
import com.bonc.ioc.bzf.business.reminder.dao.BbpmMessageSendMainLogMapper;
import com.bonc.ioc.bzf.business.reminder.service.IBbpmMessageSendMainLogService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 催缴规则消息发送日志主表  服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-09
 * @change 2023-08-09 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmMessageSendMainLogServiceImpl extends McpBaseServiceImpl<BbpmMessageSendMainLogEntity> implements IBbpmMessageSendMainLogService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmMessageSendMainLogMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmMessageSendMainLogService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmMessageSendMainLogVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmMessageSendMainLogEntity entity = new BbpmMessageSendMainLogEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setMainId(null);
        if(!baseService.insert(entity)) {
            log.error("催缴规则消息发送日志主表 新增失败:" + entity.toString());
            throw new McpException("催缴规则消息发送日志主表 新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getMainId(),1)) {
                log.error("催缴规则消息发送日志主表 新增后保存历史失败:" + entity.toString());
                throw new McpException("催缴规则消息发送日志主表 新增后保存历史失败");
            }

            log.debug("催缴规则消息发送日志主表 新增成功:"+entity.getMainId());
            return entity.getMainId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmMessageSendMainLogVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmMessageSendMainLogEntity> entityList = new ArrayList<>();
        for (BbpmMessageSendMainLogVo item:voList) {
            BbpmMessageSendMainLogEntity entity = new BbpmMessageSendMainLogEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmMessageSendMainLogEntity item:entityList){
            item.setMainId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("催缴规则消息发送日志主表 新增失败");
            throw new McpException("催缴规则消息发送日志主表 新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmMessageSendMainLogEntity::getMainId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("催缴规则消息发送日志主表 批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("催缴规则消息发送日志主表 批量新增后保存历史失败");
            }

            log.debug("催缴规则消息发送日志主表 新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param mainId 需要删除的唯一标识
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String mainId) {
        if(!StringUtils.isEmpty(mainId)) {
            if(!baseService.saveOperationHisById(mainId,3)) {
                log.error("催缴规则消息发送日志主表 删除后保存历史失败:" + mainId);
                throw new McpException("催缴规则消息发送日志主表 删除后保存历史失败");
            }

            if(!baseService.removeById(mainId)) {
                log.error("催缴规则消息发送日志主表 删除失败");
                throw new McpException("催缴规则消息发送日志主表 删除失败"+mainId);
            }
        } else {
            throw new McpException("催缴规则消息发送日志主表 删除失败唯一标识为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param mainIdList 需要删除的唯一标识
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> mainIdList) {
        if(!CollectionUtils.isEmpty(mainIdList)) {
            int oldSize = mainIdList.size();
            mainIdList = mainIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(mainIdList) || oldSize != mainIdList.size()) {
                throw new McpException("催缴规则消息发送日志主表 批量删除失败 存在主键id为空的记录"+StringUtils.join(mainIdList));
            }

            if(!baseService.saveOperationHisByIds(mainIdList,3)) {
                log.error("催缴规则消息发送日志主表 批量删除后保存历史失败:" + StringUtils.join(mainIdList));
                throw new McpException("催缴规则消息发送日志主表 批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(mainIdList)) {
                log.error("催缴规则消息发送日志主表 批量删除失败");
                throw new McpException("催缴规则消息发送日志主表 批量删除失败"+StringUtils.join(mainIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的催缴规则消息发送日志主表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmMessageSendMainLogVo vo) {
        if(vo != null) {
            BbpmMessageSendMainLogEntity entity = new BbpmMessageSendMainLogEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getMainId())) {
                throw new McpException("催缴规则消息发送日志主表 更新失败传入唯一标识为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("催缴规则消息发送日志主表 更新失败");
                throw new McpException("催缴规则消息发送日志主表 更新失败"+entity.getMainId());
            } else {
                if(!baseService.saveOperationHisById(entity.getMainId(),2)) {
                    log.error("催缴规则消息发送日志主表 更新后保存历史失败:" + entity.getMainId());
                    throw new McpException("催缴规则消息发送日志主表 更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("催缴规则消息发送日志主表 更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的催缴规则消息发送日志主表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmMessageSendMainLogVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmMessageSendMainLogEntity> entityList = new ArrayList<>();

            for (BbpmMessageSendMainLogVo item:voList){
                BbpmMessageSendMainLogEntity entity = new BbpmMessageSendMainLogEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getMainId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("催缴规则消息发送日志主表 批量更新失败 存在唯一标识为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("催缴规则消息发送日志主表 批量更新失败");
                throw new McpException("催缴规则消息发送日志主表 批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getMainId())).map(BbpmMessageSendMainLogEntity::getMainId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("催缴规则消息发送日志主表 批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("催缴规则消息发送日志主表 批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的催缴规则消息发送日志主表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmMessageSendMainLogVo vo) {
        if(vo != null) {
            BbpmMessageSendMainLogEntity entity = new BbpmMessageSendMainLogEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("催缴规则消息发送日志主表 保存失败");
                throw new McpException("催缴规则消息发送日志主表 保存失败"+entity.getMainId());
            } else {
                if(!baseService.saveOperationHisById(entity.getMainId(),4)) {
                    log.error("催缴规则消息发送日志主表 保存后保存历史失败:" + entity.getMainId());
                    throw new McpException("催缴规则消息发送日志主表 保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("催缴规则消息发送日志主表 保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的催缴规则消息发送日志主表 
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmMessageSendMainLogVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmMessageSendMainLogEntity> entityList = new ArrayList<>();

            for (BbpmMessageSendMainLogVo item:voList){
                BbpmMessageSendMainLogEntity entity = new BbpmMessageSendMainLogEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("催缴规则消息发送日志主表 批量保存失败");
                throw new McpException("催缴规则消息发送日志主表 批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getMainId())).map(BbpmMessageSendMainLogEntity::getMainId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("催缴规则消息发送日志主表 批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("催缴规则消息发送日志主表 批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param mainId 需要查询的唯一标识
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmMessageSendMainLogVo selectByIdRecord(String mainId) {
        BbpmMessageSendMainLogVo vo = new BbpmMessageSendMainLogVo();

        if(!StringUtils.isEmpty(mainId)) {
            BbpmMessageSendMainLogEntity entity = baseService.selectById(mainId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-09
     * @change
     * 2023-08-09 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmMessageSendMainLogPageResultVo>> selectByPageRecord(BbpmMessageSendMainLogPageVo vo) {
        List<BbpmMessageSendMainLogPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
