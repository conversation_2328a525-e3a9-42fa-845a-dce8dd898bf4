package com.bonc.ioc.bzf.business.adjust.dao;

import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import java.util.List;

/**
 * 应收调整 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@Mapper
public interface BbpmReceivableAdjustMapper extends McpBaseMapper<BbpmReceivableAdjustEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change 2025-02-18 by yuanxuesong for init
     */
    List<BbpmReceivableAdjustPageResultVo> selectByPageCustom(@Param("vo") BbpmReceivableAdjustPageVo vo );

    BbpmReceivableAdjustVo selectByIdecord(@Param("id") String id);

    BbpmReceivableAdjustVo selectByCcid(@Param("ccid") String ccid);

    String selectTzbh(@Param("formattedDate") String formattedDate);

    List<BbpmReceivableAdjustPageResultVo> selectByExportList(@Param("vo") BbpmReceivableAdjustPageVo vo );

    void updateForGrTk(@Param("vo") AdjustGxVo vo);

    void updateForQyTk(@Param("vo") AdjustGxVo vo);

    AdjustTj selectForTj(@Param("vo") BbpmReceivableAdjustPageVo vo );

    String[] selectContractForAdjustId(@Param("id") String id,@Param("contractNo") String contractNo);
    /**
     * 修改追加账单状态
     * @param id
     * @param status
     */
    void updateAdjustStatus(@Param("id") String id , @Param("status") String status);
}
