package com.bonc.ioc.bzf.business.invoice.dao;

import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceQueryEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import java.util.List;

/**
 * 已开发票查询 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@Mapper
public interface BbpmInvoiceQueryMapper extends McpBaseMapper<BbpmInvoiceQueryEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-13
     * @change 2023-05-13 by binghong.tang for init
     */
    List<BbpmInvoiceQueryPageResultVo> selectByPageCustom(@Param("vo") BbpmInvoiceQueryPageVo vo );
}
