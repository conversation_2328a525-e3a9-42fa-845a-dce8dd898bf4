package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangePeriodDTO;
import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangeRentFreeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value="合同变更试算账单预览请求", description="合同变更试算账单预览请求")
@Data
public class ChangeTrialPreviewBillsParamsVo implements java.io.Serializable{
    //	变更类型	String	是		15 免租期变更（contractChangeRentFreeDTO） 16 缴费周期变更（contractChangePeriodDTO）
    private String changeType;
    //	    项目id 	String	64
    private String projectId;

    //免租期变更
    private BusinessContractChangeRentFreeDTO contractChangeRentFreeDTO;
    //缴费周期变更
    private BusinessContractChangePeriodDTO contractChangePeriodDTO;

}
