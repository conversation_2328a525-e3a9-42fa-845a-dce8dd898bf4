package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.bzf.business.adjust.vo.RentingOutDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "账单预览结果实体", description = "账单预览结果实体")
@Data
public class ChangeTrialPreviewBillsResultVo implements java.io.Serializable {

    /**
     * 房源id
     */
    @ApiModelProperty(value = "房源id")
    private String houseId;

    /**
     * 试算后的账单列表
     */
    @ApiModelProperty(value = "试算后的账单列表")
    private List<ChargeSubjectBillDTO> chargeSubjectBillList;

    /**
     * 应退/抵扣金额（嵌套数组结构）
     */
    @ApiModelProperty(value = "应退/抵扣金额")
    private List<List<RentingOutDetailVO>> rentingOutDetailVOList;
}
