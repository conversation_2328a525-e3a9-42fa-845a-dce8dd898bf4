package com.bonc.ioc.bzf.business.reminder.service;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmCollectionWhitelistEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 催缴白名单表 服务类
 *
 * <AUTHOR>
 * @date 2024-04-24
 * @change 2024-04-24 by binghong.tang for init
 */
public interface IBbpmCollectionWhitelistService extends IMcpBaseService<BbpmCollectionWhitelistEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    String insertRecord(BbpmCollectionWhitelistVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmCollectionWhitelistVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param whiteId 需要删除的白名单id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    void removeByIdRecord(String whiteId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param whiteIdList 需要删除的白名单id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> whiteIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的催缴白名单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    void updateByIdRecord(BbpmCollectionWhitelistVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的催缴白名单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmCollectionWhitelistVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的催缴白名单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    void saveByIdRecord(BbpmCollectionWhitelistVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的催缴白名单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmCollectionWhitelistVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param whiteId 需要查询的白名单id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    BbpmCollectionWhitelistVo selectByIdRecord(String whiteId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-24
     * @change
     * 2024-04-24 by binghong.tang for init
     */
    PageResult<List<BbpmCollectionWhitelistPageResultVo>> selectByPageRecord(BbpmCollectionWhitelistPageVo vo);
}
