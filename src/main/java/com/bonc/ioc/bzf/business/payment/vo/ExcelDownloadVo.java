package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 *下载实体
 * <AUTHOR>
 * @date 2023-10-20
 * @change 2023-10-20 by binghong.tang for init
 */
@ApiModel(value="ExcelDownloadVo", description="下载实体")
public class ExcelDownloadVo extends McpBaseVo implements Serializable{


    /**
     * 请求编号
     */
    @ApiModelProperty(value = "请求编号")

                                  private String requestNo;


    @ApiModelProperty(value = "类型")
                            private String type;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
}
