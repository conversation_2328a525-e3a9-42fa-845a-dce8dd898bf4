package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpmPayeeEntity;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 支付模块服务层接口
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023/3/4 22:23
 */
public interface IPayService {


    /**
     * 支付
     *
     * @creator <PERSON><PERSON><PERSON><PERSON>
     * @date 2023/3/4 22:47
     * @createDescribe
     *
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     *
     * @param vo 支付参数对象
     * @return
     * @exception
     */
    PayResultVo pay(PayParmsVo vo);

    /**
     * 支付j解锁
     *
     * @creator <PERSON><PERSON><PERSON><PERSON>
     * @date 2023/3/4 22:47
     * @createDescribe
     *
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     *
     * @param vo 支付参数对象
     * @return
     * @exception
     */
    PayUnlockResultVo payUnlock(PayUnlockParmsVo vo);

}
