package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * pdf水印参数
 */
@Data
public class WaterMarkVo implements Serializable {

    @ApiModelProperty("原始文件路径")
    private String sourcePath;

    @ApiModelProperty("结果文件路径")
    private String resultPath;

    @ApiModelProperty(value = "文件名称")
    private String pdfName;

    @ApiModelProperty("水印文字")
    private String text;

    @ApiModelProperty("水印图片文件id")
    private String picturefileId;

    @ApiModelProperty("水印图片路径")
    private String pictureUrl;

    @ApiModelProperty("文字水印x轴位置，默认200")
    private Integer textOffsetX;

    @ApiModelProperty("文字水印y轴位置，默认400")
    private Integer textOffsetY;

    @ApiModelProperty("文字水印倾斜角度，默认45")
    private Integer textRotate;

    @ApiModelProperty("图片水印x轴位置，默认200")
    private Integer pictureOffsetX;

    @ApiModelProperty("图片水印y轴位置，默认400")
    private Integer pictureOffsetY;

    @ApiModelProperty("图片水印倾斜角度，默认45")
    private Integer pictureRotate;

}
