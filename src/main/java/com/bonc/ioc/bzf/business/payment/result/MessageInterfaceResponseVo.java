package com.bonc.ioc.bzf.business.payment.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 接口返回的信息-本系统接口返回使用的类
 *
 * <AUTHOR>
 * @date 2022-06-14 11:43
 * @change 2022-06-14 11:43 by sqj for init
 */
@Data
public class MessageInterfaceResponseVo {


    @ApiModelProperty(value = "消息类型 viewOrder-看房预约；viewNotice-看房通知; selectionNotice-选房通知；signingNotice-签约通知；checkInNotice-入住通知")
    private String sendMessageType;

    @ApiModelProperty(value = "接口调用返回各明细推送结果")
    private List<MessageInterfaceResponseDetailVo> detailVos;

    @ApiModelProperty(value = "定时任务-群发接口任务id")
    private String taskId;
}
