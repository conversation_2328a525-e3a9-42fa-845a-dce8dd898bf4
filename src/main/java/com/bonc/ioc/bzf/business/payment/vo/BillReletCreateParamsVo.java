package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>  3.26 趸租大合同生成账单接口
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class BillReletCreateParamsVo {
    @ApiModelProperty(value = "起租日超多少天算违约金")
    private Integer breakDays;
    @ApiModelProperty(value = "违约金比例")
    private BigDecimal breakRate;
    @ApiModelProperty(value = "补贴比例")
    private BigDecimal subsidyProportion;
    @ApiModelProperty(value = "趸租企业ID")
    private String companyId;
    @ApiModelProperty(value = "趸租企业证照类型")
    private String companyIDType;
    @ApiModelProperty(value = "趸租企业社会信用代码")
    private String socialCreditCode;
    @ApiModelProperty(value = "趸租企业名称")
    private String companyName;
    @ApiModelProperty(value = "趸租企业客户编号")
    private String companyCustomerNo;
    @ApiModelProperty(value = "趸租企业客商编号")
    private String companySupplierNo;
    @ApiModelProperty(value = "趸租企业客商名称")
    private String companySupplierName;

    @ApiModelProperty(value = "合同ID")
    @NotBlank(message = "合同ID", groups = {UpdateValidatorGroup.class})
    private String contractId;
    @ApiModelProperty(value = "合同分类")
    @NotBlank(message = "合同分类", groups = {UpdateValidatorGroup.class})
    private String contractClassification;
    @ApiModelProperty(value = "合同类型")
    @NotBlank(message = "合同类型", groups = {UpdateValidatorGroup.class})
    private String contractType;
    @ApiModelProperty(value = "合同当前状态")
    @NotBlank(message = "合同当前状态", groups = {UpdateValidatorGroup.class})
    private String contractStatus;
    @ApiModelProperty(value = "合同起始日期")
    @NotBlank(message = "合同起始日期", groups = {UpdateValidatorGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginDate;
    @ApiModelProperty(value = "合同终止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotBlank(message = "合同终止日期", groups = {UpdateValidatorGroup.class})
    private Date contractEndDate;
    @ApiModelProperty(value = "合同签订时间")
    @NotBlank(message = "合同签订时间", groups = {UpdateValidatorGroup.class})
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractSignTime;
    @ApiModelProperty(value = "合同起租日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotBlank(message = "合同起租日", groups = {UpdateValidatorGroup.class})
    private Date contractCommencementDate;
    @ApiModelProperty(value = "合同签约计价单位")
    @NotBlank(message = "合同签约计价单位", groups = {UpdateValidatorGroup.class})
    private String contractPriceUnit;
    @ApiModelProperty(value = "合同计价周期")
    @NotBlank(message = "合同计价周期", groups = {UpdateValidatorGroup.class})
    private String contractPricePeriod;
    @ApiModelProperty(value = "合同面积")
    @NotBlank(message = "合同面积", groups = {UpdateValidatorGroup.class})
    private BigDecimal contractArea;
    @ApiModelProperty(value = "审批人")
    @NotBlank(message = "审批人", groups = {UpdateValidatorGroup.class})
    private String approver;
    @ApiModelProperty(value = "审批时间")
    @NotBlank(message = "审批时间", groups = {UpdateValidatorGroup.class})
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    @ApiModelProperty(value = "项目列表")
    private List<ProjectVo> projectList;

    @Override
    public String toString() {
        return "BillCreateParamsVo{" +
                "breakDays=" + breakDays +
                ", breakRate=" + breakRate +
                ", subsidyProportion=" + subsidyProportion +
                ", companyId='" + companyId + '\'' +
                ", companyIDType='" + companyIDType + '\'' +
                ", socialCreditCode='" + socialCreditCode + '\'' +
                ", companyName='" + companyName + '\'' +
                ", companyCustomerNo='" + companyCustomerNo + '\'' +
                ", companySupplierNo='" + companySupplierNo + '\'' +
                ", companySupplierName='" + companySupplierName + '\'' +
                ", contractId='" + contractId + '\'' +
                ", contractClassification='" + contractClassification + '\'' +
                ", contractType='" + contractType + '\'' +
                ", contractStatus='" + contractStatus + '\'' +
                ", contractBeginDate=" + contractBeginDate +
                ", contractEndDate=" + contractEndDate +
                ", contractSignTime=" + contractSignTime +
                ", contractCommencementDate=" + contractCommencementDate +
                ", contractPriceUnit='" + contractPriceUnit + '\'' +
                ", contractPricePeriod='" + contractPricePeriod + '\'' +
                ", contractArea=" + contractArea +
                ", approver='" + approver + '\'' +
                ", approveTime=" + approveTime +
                ", projectList=" + projectList +
                '}';
    }



}
