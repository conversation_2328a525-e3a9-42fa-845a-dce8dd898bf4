package com.bonc.ioc.bzf.business.adjust.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 应收调整账单表 实体类
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@TableName("bbpm_receivable_adjust_bill")
@ApiModel(value="BbpmReceivableAdjustBillEntity对象", description="应收调整账单表")
public class BbpmReceivableAdjustBillEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_ID = "id";
    public static final String FIELD_ADJUST_ID = "adjust_id";
    public static final String FIELD_BILL_ID = "bill_id";
    public static final String FIELD_ADJUSTMENT_AMOUNT = "adjustment_amount";
    public static final String FIELD_EXT1 = "ext1";
    public static final String FIELD_EXT2 = "ext2";
    public static final String FIELD_DEL_FLAG = "del_flag";
    public static final String FIELD_CREATE_USER_NAME = "create_user_name";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
                                @TableId(value = "id", type = IdType.ASSIGN_UUID)
                                  private String id;

    /**
     * 应收调整id
     */
    @ApiModelProperty(value = "应收调整id")
                            private String adjustId;

    /**
     * 账单id
     */
    @ApiModelProperty(value = "账单id")
                            private String billId;

    /**
     * 调整后应缴金额
     */
    @ApiModelProperty(value = "调整后应缴金额")
                            private BigDecimal adjustmentAmount;

    /**
     * 备用字段1
     */
    @ApiModelProperty(value = "账单类型（01：企业，02：个人）")
                            private String ext1;

    /**
     * 备用字段2
     */
    @ApiModelProperty(value = "备用字段2")
                            private String ext2;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
                            private String createUserName;

    /**
     * @return 主键
     */
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return 应收调整id
     */
    public String getAdjustId() {
        return adjustId;
    }

    public void setAdjustId(String adjustId) {
        this.adjustId = adjustId;
    }

    /**
     * @return 账单id
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 调整后应缴金额
     */
    public BigDecimal getAdjustmentAmount() {
        return adjustmentAmount;
    }

    public void setAdjustmentAmount(BigDecimal adjustmentAmount) {
        this.adjustmentAmount = adjustmentAmount;
    }

    /**
     * @return 备用字段1
     */
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * @return 备用字段2
     */
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 创建人姓名
     */
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

      @Override
    public String toString() {
        return "BbpmReceivableAdjustBillEntity{" +
            "id=" + id +
            ", adjustId=" + adjustId +
            ", billId=" + billId +
            ", adjustmentAmount=" + adjustmentAmount +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", delFlag=" + delFlag +
            ", createUserName=" + createUserName +
        "}";
    }
}