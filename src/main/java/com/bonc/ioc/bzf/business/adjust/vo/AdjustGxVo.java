package com.bonc.ioc.bzf.business.adjust.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AdjustGxVo implements java.io.Serializable{

    @ApiModelProperty(value = "退款类型")
    private String owner;

    @ApiModelProperty(value = "付款单唯一标识")
    private String paymentCode;

    @ApiModelProperty(value = "退款状态")
    private String paymentStatus;

    @ApiModelProperty(value = "退款失败原因")
    private String remark;
}
