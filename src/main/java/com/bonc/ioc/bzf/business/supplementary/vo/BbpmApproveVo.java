package com.bonc.ioc.bzf.business.supplementary.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 审批 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/10
 */
@Data
@ApiModel(value = "审批 vo实体", description = "审批 vo实体")
public class BbpmApproveVo extends BbpmStatusVo implements Serializable {

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    private String remark;

    /**
     * 合同信息 vo实体
     */
    @ApiModelProperty(value = "合同信息 vo实体")
    BbctContractManagementVo contractManagementVo;
}
