package com.bonc.ioc.bzf.business.payment.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 02变更非身份证的基础信息
 */
@Data
@Builder
public class BaseInfoChangeDtoRequest {

    private String changeType;              //  变更类型	String	10 02变更非身份证号的基础信息(现在只有02这一种类型)
    private String projectId;               //  项目ID  String  64

    private String beforeContractCode;      //  变更前合同code     String  64
    private String tenantId;	            //  租户ID            String  64
    private String tenantBankName;	        //  租户开户总行名称    String  200
    private String tenantBankCode;          //  租户开户总行编码
    private String tenantBankBranchName;    //  租户开户支行名称
    private String tenantBankBrachCode;     //  租户开户支行编码
    private String tenantBankAccountName;   //  租户银行卡户名
    private String tenantBankAccountNo;	    //  租户银行卡卡号      String  30
    private String withholdingSummary;      //  银行卡代扣摘要
    private String withholdingRemark;       //  银行卡代扣备注
    private String agreementNo;             //  代扣鉴权协议号
    private String tenantName;	            //  租户姓名           String  50
    private String tenantMobile;	        //  租户手机号         String  15
    private String mailUrl;                 //  租户邮箱地址
    private String publicRecordNo;          //  公租房备案号
    private String tenantSupplierNo;        //  租户客商编号
    private String tenantSupplierName;      //  租户客商名称
    private String withholding;	            //  是否进行银行卡代扣   String  4

    private String beforeReletContractCode; //  原趸租大合同code    String  64
    private String companyId;	            //  企业ID             String  64
    private String companyIDType;           //  趸租企业证照类型
    private String socialCreditCode;        //  趸租企业社会信用代码
    private String companyName;	            //  企业名称            String  50
    private String companyCustomerNo;       //  趸租企业客户编号
    private String companySupplierNo;       //  趸租企业客商编号
    private String companySupplierName;     //  趸租企业客商名称


    private String authorizedAgent;//委托代理人
    private String authorizedAgentMobile;//委托代理人电话
}
