package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 3.25. 查询收款凭证接口
 */
@Data
public class CorporateSearchVo {
    //code	主收款单唯一识别码
    @ApiModelProperty(value = "主收款单唯一识别码")
    private String code;

    //  支付类型	String	是	10	支付方式，05：对公转账；06：支票支付
    @ApiModelProperty(value = "支付方式，05：对公转账；06：支票支付")
    private String paymentType;

    //	项目ID	String	是	64	业务中台ID
    @ApiModelProperty(value = "业务中台ID")
    private String projectId;




}
