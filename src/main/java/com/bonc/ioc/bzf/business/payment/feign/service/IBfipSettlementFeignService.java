package com.bonc.ioc.bzf.business.payment.feign.service;

import com.bonc.ioc.bzf.business.payment.vo.PayParmsVo;
import com.bonc.ioc.bzf.business.payment.vo.PayResultVo;
import com.bonc.ioc.bzf.business.payment.vo.PayUnlockParmsVo;
import com.bonc.ioc.bzf.business.payment.vo.PayUnlockResultVo;

/**
 * @version v1.0
 * @create YaoChunyu
 * @date 2023/3/4 22:52
 */
public interface IBfipSettlementFeignService {

    /**
     * 支付
     *
     * @creator <PERSON><PERSON><PERSON><PERSON>
     * @date 2023/3/4 22:47
     * @createDescribe
     *
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     *
     * @param vo 支付参数对象
     * @return
     * @exception
     */
    PayResultVo pay(PayParmsVo vo);

    /**
     * 支付j解锁
     *
     * @creator <PERSON><PERSON><PERSON><PERSON>
     * @date 2023/3/4 22:47
     * @createDescribe
     *
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     *
     * @param vo 支付参数对象
     * @return
     * @exception
     */
    PayUnlockResultVo payUnlock(PayUnlockParmsVo vo);
}
