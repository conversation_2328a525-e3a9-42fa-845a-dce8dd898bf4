package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发票红冲
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value="BbpmInvoiceRedFlushVo对象", description="发票红冲")
@Data
public class BbpmInvoiceRedFlushVo extends McpBaseVo implements Serializable{
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "原发票代码")
    private String yfpDm;
    @ApiModelProperty(value = "原发票号码")
    private String yfpHm;
    @ApiModelProperty(value = "原发票请求流水号")
    private String yfpqqlsh;
    @ApiModelProperty(value = "红冲原因 01:退租,02:换开")
    private String reason;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期")
    private String kprq;

}
