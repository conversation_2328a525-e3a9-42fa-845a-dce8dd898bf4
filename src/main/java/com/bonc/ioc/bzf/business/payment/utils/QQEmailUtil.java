package com.bonc.ioc.bzf.business.payment.utils;

import com.sun.mail.util.MailSSLSocketFactory;

import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Properties;

/**
 * 使用qq邮件服务器发送邮件
 *
 * 可发送qq、163、新浪
 *
 * @version v1.0
 * @create Yao<PERSON>hunyu
 * @date 2022/8/6 16:36
 */
public class QQEmailUtil implements IEmailUtile {

    @Override
    public void sendEmail(String mailHost, String mailProtocol, String sendEmailAccountNumber, String sendEmailPassword, String receiveEmailAccountNumber, String mailTittle, String mailText, List<Photo> photos, List<File> files) throws Exception {
        Session session = createQqEmailSession(mailHost,mailProtocol,sendEmailAccountNumber, sendEmailPassword);
        // 2、通过session得到transport对象
        Transport ts = session.getTransport();
        // 3、连上邮件服务器，需要发件人提供邮箱的用户名和密码进行验证
        ts.connect(mailHost, sendEmailAccountNumber, sendEmailPassword);
        MimeMessage message = createMimeMessage(session, sendEmailAccountNumber, receiveEmailAccountNumber, mailTittle, mailText, photos, files);
        // 5、发送邮件
        ts.sendMessage(message, message.getAllRecipients());
        ts.close();
    }

    /**
     * 创建会话
     *
     * @creator YaoChunyu
     * @date 2022/8/6 17:17
     * @createDescribe
     *
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     *
     * @param mailHost 邮件服务器域名
     * @param mailProtocol 邮件服务器协议
     * @param sendEmailAccountNumber 发送者的帐号
     * @param sendEmailPassword 发送者的密码
     * @return
     * @exception
     */
    public Session createQqEmailSession(String mailHost,String mailProtocol,String sendEmailAccountNumber, String sendEmailPassword) throws Exception {
        // 获取系统属性
        Properties properties = System.getProperties();

        // 设置邮件服务器
        properties.setProperty("mail.smtp.host", mailHost);
        properties.setProperty("mail.transport.protocol", mailProtocol);
        properties.put("mail.smtp.auth", "true");

        MailSSLSocketFactory sf = new MailSSLSocketFactory();
        sf.setTrustAllHosts(true);
        properties.put("mail.smtp.ssl.enable", "true");
        properties.put("mail.smtp.ssl.socketFactory", sf);
        // 获取默认session对象
        Session session = Session.getDefaultInstance(properties, new Authenticator() {
            @Override
            public PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(sendEmailAccountNumber, sendEmailPassword);
            }
        });
        return session;
    }


    /**
     * 生成一封带附件和带图片的邮件
     *
     * @param session                   会话
     * @param sendEmailAccountNumber    发送着帐号
     * @param receiveEmailAccountNumber 接收者帐号
     * @param mailTittle                邮件标题
     * @param mailText                  邮件标题
     * @param photos                    图片集合
     * @param files                     附件路径路径集合
     * @return
     * @throws
     * @creator YaoChunyu
     * @date 2022/8/6 16:39
     * @createDescribe
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     */
    public MimeMessage createMimeMessage(Session session,
                                         String sendEmailAccountNumber,
                                         String receiveEmailAccountNumber,
                                         String mailTittle,
                                         String mailText,
                                         List<Photo> photos,
                                         List<File> files) throws Exception {
        // 整个邮件信息（包括基本信息和内容信息。内容信息包括附件和消息体）
        MimeMessage message = new MimeMessage(session);

        // 设置邮件的基本信息
        message.setFrom(new InternetAddress(sendEmailAccountNumber));
        message.setRecipient(Message.RecipientType.TO, new InternetAddress(receiveEmailAccountNumber));// 收件人
        message.setSubject(mailTittle);

        // 整个邮件内容信息部分
        MimeMultipart mimeMultipart = new MimeMultipart();

        // 附件
        MimeBodyPart attach;
        if (files != null && files.size() > 0) {
            for (File file : files) {
                if(file != null && file.getBytes() != null && file.getName() != null && !"".equals(file.getName())){
                    attach = new MimeBodyPart();
                    DataHandler dh = new DataHandler(new ByteArrayDataSource(file.getBytes(),file.getName()));
                    attach.setDataHandler(dh);
                    attach.setFileName(MimeUtility.encodeText(file.getName()));
                    // 描述关系:正文和附件
                    mimeMultipart.addBodyPart(attach);
                }

            }
        }

        // 消息体
        MimeBodyPart bodyPart = new MimeBodyPart();
        // 消息体中文本
        MimeMultipart text = new MimeMultipart();
        StringBuffer content = new StringBuffer();
        content.append(mailText);
        // 消息体中图片
        if (photos != null && photos.size() > 0) {
            for (Photo photo : photos) {
                if (photo != null) {
                    if (photo.getName() != null && !"".equals(photo.getName()) && photo.getUrl() != null && !"".equals(photo.getUrl())) {
                        content.append("<br/>");
                        content.append("<img src='cid:").append(photo.getName()).append("'>");
                        MimeBodyPart image = new MimeBodyPart();
                        image.setDataHandler(new DataHandler(new FileDataSource(photo.getUrl())));
                        image.setContentID(photo.getName());
                        text.addBodyPart(image);
                    }
                }
            }

        }
        MimeBodyPart textText = new MimeBodyPart();
        textText.setContent(content.toString(), "text/html;charset=UTF-8");
        text.addBodyPart(textText);
        text.setSubType("related");
        bodyPart.setContent(text);


        mimeMultipart.addBodyPart(bodyPart);
        mimeMultipart.setSubType("mixed");

        // 设置邮件的内容信息
        message.setContent(mimeMultipart);
        message.saveChanges();

        // 返回创建好的的邮件
        return message;
    }


    /**
     * 调用案例
     *
     * @creator YaoChunyu
     * @date 2022/8/6 16:54
     * @createDescribe
     *
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     *
     * @param  emailAddress 接收者邮箱，substringFileBaseCode 文件的base64，sendEmailAccountNumber 发件人账号 sendEmailPassword  发件人密钥，mailHost 邮件服务器域名
     * @return
     * @exception
     */
    public void mailBoxParameter(String fileType,String emailAddress,String substringFileBaseCode,String sendEmailAccountNumber,String sendEmailPassword,String mailHost,String contractCode) throws IOException {

        /**
         * 邮件的服务器协议
         *  qq：smtp
         *
         *  如果不设置会报"javax.mail.NoSuchProviderException: Invalid protocol: null"的错
         */
        String mailProtocol = "smtp";

        /**
         * 邮件的标题
         */
        String mailTittle = "我的合同";

        /**
         * 邮件的文本内容
         */
        String mailText = "我的合同";


        List<File> files = new ArrayList<>(); // 发送附件的路径
        byte[] decode = Base64.getDecoder().decode(substringFileBaseCode);
        File file = new File();
        file.setBytes(decode);
        if (".pdf".equals(fileType)){
            file.setName(contractCode+".pdf");
        }else if(".doc".equals(fileType)){
            file.setName(contractCode+".doc");
        }else if (".docx".equals(fileType)){
            file.setName(contractCode+".docx");
        }else {
            file.setName(contractCode);
        }

        files.add(file);

//        java.io.File file1 = new java.io.File("F:\\system\\桌面\\物品交接单.pdf");
//        if (!file1.exists()){
//         file1.createNewFile();
//        }
//
//        OutputStream os = new FileOutputStream(file1);
//        os.write(decode);
//        os.flush();
//        os.close();


        try {
            sendEmail(
                    mailHost,
                    mailProtocol,
                    sendEmailAccountNumber,
                    sendEmailPassword,
                    emailAddress,
                    mailTittle,
                    mailText,
                    null,
                    files
            );
            System.out.println("发送成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
