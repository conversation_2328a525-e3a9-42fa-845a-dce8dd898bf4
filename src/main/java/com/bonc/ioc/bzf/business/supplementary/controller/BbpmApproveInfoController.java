package com.bonc.ioc.bzf.business.supplementary.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import com.bonc.ioc.common.validator.inf.*;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.bind.annotation.RestController;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmApproveInfoService;
import io.swagger.annotations.*;
import com.bonc.ioc.bzf.business.supplementary.entity.*;
import java.util.List;
import com.bonc.ioc.bzf.business.supplementary.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 审批表 前端控制器
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@RestController
@RequestMapping("/bbpmApproveInfoEntity")
@Api(tags = "审批表")
@Validated
public class BbpmApproveInfoController extends McpBaseController {
    @Resource
    private IBbpmApproveInfoService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "pyj")
    @ApiOperation(value = "新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "审批表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmApproveInfoVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要新增的记录 列表
     * @return  com.bonc.ioc.common.util.AppReply<List<String>> 返回新增后的主键 列表
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @PostMapping(value = "/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "pyj")
    @ApiOperation(value = "批量新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键集合")})
    public AppReply<List<String>> insertBatchRecord(@ApiParam(value = "审批表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbpmApproveInfoVo> voList){
        AppReply<List<String>> appReply = new AppReply<List<String>>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertBatchRecord(voList));
        return appReply;
     }

    /**
     * removeByIdRecord 根据主键删除
     * @param approveId 需要删除的审批id
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "pyj")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "需要删除的审批id" ,required = false) @RequestBody String approveId){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdRecord(approveId);
        return appReply;
     }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param approveIdList 需要删除的审批id集合
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @PostMapping(value = "/batch/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "pyj")
    @ApiOperation(value = "根据主键删除-批量", notes = "根据主键批量删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdsRecord(@ApiParam(value = "需要删除的审批id" ,required = false) @RequestBody List<String> approveIdList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdsRecord(approveIdList);
        return appReply;
     }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的审批表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "pyj")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateByIdRecord(@ApiParam(value = "需要更新的审批表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbpmApproveInfoVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateByIdRecord(vo);
        return appReply;
     }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的审批表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @PostMapping(value = "/batch/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "pyj")
    @ApiOperation(value = "根据主键更新-批量", notes = "根据主键批量更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateBatchByIdRecord(@ApiParam(value = "需要更新的审批表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) List<BbpmApproveInfoVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的审批表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @PostMapping(value = "/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "pyj")
    @ApiOperation(value = "根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveByIdRecord(@ApiParam(value = "需要更新或新增的审批表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmApproveInfoVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveByIdRecord(vo);
        return appReply;
     }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的审批表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @PostMapping(value = "/batch/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "pyj")
    @ApiOperation(value = "根据主键更新或新增-批量", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveBatchByIdRecord(@ApiParam(value = "需要更新或新增的审批表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbpmApproveInfoVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * selectByIdRecord 根据主键查询
     * @param approveId 需要查询的审批id
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "pyj")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmApproveInfoVo> selectByIdRecord(@ApiParam(value = "需要查询的审批id" ,required = false) @RequestParam(required = false) String approveId){
        AppReply<BbpmApproveInfoVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(approveId));
        return appReply;
     }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "pyj")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmApproveInfoPageResultVo>>> selectByPageRecord(BbpmApproveInfoPageVo vo){
        AppReply<PageResult<List<BbpmApproveInfoPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }

}

