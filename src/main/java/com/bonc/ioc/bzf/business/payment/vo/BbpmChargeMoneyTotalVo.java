package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 收款单查询统计 实体类
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@ApiModel(value="BbpmChargeMoneyTotalVo", description="收款单查询统计")
@Data
public class BbpmChargeMoneyTotalVo extends McpBaseVo implements Serializable{


    @ApiModelProperty(value = "总收款金额")
    private BigDecimal totalChargeMoney;

    @ApiModelProperty(value = "暂时无用")
    private BigDecimal preChargeMoneyTotal;
    @ApiModelProperty(value = "暂时无用")
    private BigDecimal actualChargeMoneyTotal;


}
