package com.bonc.ioc.bzf.business.payment.result.create;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 3.27. 趸租管理协议生成账单接口
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
@Builder
public class BillAgreementCreateParamsRequest {
    @ApiModelProperty(value = "大合同ID")
    private String parentContractId;
    @ApiModelProperty(value = "起租日超多少天算违约金")
    private Integer breakDays;
    @ApiModelProperty(value = "违约金比例")
    private BigDecimal breakRate;
    @ApiModelProperty(value = "补贴比例")
    private BigDecimal subsidyProportion;

    @ApiModelProperty(value = "计费科目列表")
    @Singular("chargeSubjecParamsRequest")
    private List<ChargeSubjectParamsRequest> chargeSubjectList;

    @ApiModelProperty(value = "家具租金列表")
    @Singular("furnitureRentalParamsRequest")
    private List<FurnitureRentalParamsRequest> furnitureRentalList;

    @ApiModelProperty(value = "租户开户总行名称")
    @NotBlank(message = "租户开户总行名称", groups = {UpdateValidatorGroup.class})
    private String tenantBankName;
    @ApiModelProperty(value = "租户开户总行编码")
    @NotBlank(message = "租户开户总行编码", groups = {UpdateValidatorGroup.class})
    private String tenantBankCode;
    @ApiModelProperty(value = "租户开户支行名称")
    @NotBlank(message = "租户开户支行名称", groups = {UpdateValidatorGroup.class})
    private String tenantBankBranchName;
    @ApiModelProperty(value = "租户开户支行编码")
    @NotBlank(message = "租户开户支行编码", groups = {UpdateValidatorGroup.class})
    private String tenantBankBrachCode;
    @ApiModelProperty(value = "租户银行卡户名")
    @NotBlank(message = "租户银行卡户名", groups = {UpdateValidatorGroup.class})
    private String tenantBankAccountName;
    @ApiModelProperty(value = "租户银行卡卡号")
    @NotBlank(message = "租户银行卡卡号", groups = {UpdateValidatorGroup.class})
    private String tenantBankAccountNo;
    @ApiModelProperty(value = "是否进行银行卡代扣")
    @NotBlank(message = "是否进行银行卡代扣", groups = {UpdateValidatorGroup.class})
    private String withholding;
    @ApiModelProperty(value = "银行卡代扣摘要")
    @NotBlank(message = "银行卡代扣摘要", groups = {UpdateValidatorGroup.class})
    private String withholdingSummary;
    @ApiModelProperty(value = "银行卡代扣备注")
    @NotBlank(message = "银行卡代扣备注", groups = {UpdateValidatorGroup.class})
    private String withholdingRemark;
    @ApiModelProperty(value = "代扣鉴权协议号")
    @NotBlank(message = "代扣鉴权协议号", groups = {UpdateValidatorGroup.class})
    private String agreementNo;
    @ApiModelProperty(value = "租户姓名")
    @NotBlank(message = "租户姓名", groups = {UpdateValidatorGroup.class})
    private String tenantName;
    @ApiModelProperty(value = "租户手机号")
    @NotBlank(message = "租户手机号", groups = {UpdateValidatorGroup.class})
    private String tenantMobile;
    @ApiModelProperty(value = "租户ID")
    @NotBlank(message = "租户ID", groups = {UpdateValidatorGroup.class})
    private String tenantId;
    @ApiModelProperty(value = "租户证件类型")
    @NotBlank(message = "租户证件类型", groups = {UpdateValidatorGroup.class})
    private String tenantIDType;
    @ApiModelProperty(value = "租户证件号码")
    @NotBlank(message = "租户证件号码", groups = {UpdateValidatorGroup.class})
    private String idNumber;
    @ApiModelProperty(value = "租户邮箱地址")
    @NotBlank(message = "租户邮箱地址", groups = {UpdateValidatorGroup.class})
    private String mailUrl;
    @ApiModelProperty(value = "公租房备案号")
    @NotBlank(message = "公租房备案号", groups = {UpdateValidatorGroup.class})
    private String publicRecordNo;
    @ApiModelProperty(value = "租户客户编号")
    @NotBlank(message = "租户客户编号", groups = {UpdateValidatorGroup.class})
    private String tenantCustomerNo;
    @ApiModelProperty(value = "租户客商编号")
    @NotBlank(message = "公租房备案号", groups = {UpdateValidatorGroup.class})
    private String tenantSupplierNo;
    @ApiModelProperty(value = "租户客商名称")
    @NotBlank(message = "租户客商名称", groups = {UpdateValidatorGroup.class})
    private String tenantSupplierName;
    @ApiModelProperty(value = "趸租企业ID")
    private String companyId;
    @ApiModelProperty(value = "趸租企业证照类型")
    private String companyIDType;
    @ApiModelProperty(value = "趸租企业社会信用代码")
    private String socialCreditCode;
    @ApiModelProperty(value = "趸租企业名称")
    private String companyName;
    @ApiModelProperty(value = "趸租企业客户编号")
    private String companyCustomerNo;
    @ApiModelProperty(value = "趸租企业客商编号")
    private String companySupplierNo;
    @ApiModelProperty(value = "趸租企业客商名称")
    private String companySupplierName;
    @ApiModelProperty(value = "委托代理人")
    private String authorizedAgent ;
    @ApiModelProperty(value = "委托代理人电话")
    private String  authorizedAgentMobile ;

    @ApiModelProperty(value = "项目ID")
    @NotBlank(message = "项目ID", groups = {UpdateValidatorGroup.class})
    private String projectId;
    @ApiModelProperty(value = "项目编号")
    @NotBlank(message = "项目编号", groups = {UpdateValidatorGroup.class})
    private String projectNo;
    @ApiModelProperty(value = "项目名称")
    @NotBlank(message = "项目名称", groups = {UpdateValidatorGroup.class})
    private String projectName;
    @ApiModelProperty(value = "项目简称")
    @NotBlank(message = "项目简称", groups = {UpdateValidatorGroup.class})
    private String projectShortName;
    @ApiModelProperty(value = "运营主体类型")
    @NotBlank(message = "运营主体类型", groups = {UpdateValidatorGroup.class})
    private String operateEntityType;
    @ApiModelProperty(value = "运营主体名称")
    @NotBlank(message = "运营主体名称", groups = {UpdateValidatorGroup.class})
    private String operateEntityName;
    @ApiModelProperty(value = "运营单位业务中台编号")
    @NotBlank(message = "运营单位业务中台编号", groups = {UpdateValidatorGroup.class})
    private String operateUnitBusinessNo;
    @ApiModelProperty(value = "运营单位编号")
    @NotBlank(message = "运营单位编号", groups = {UpdateValidatorGroup.class})
    private String operateUnitNo;
    @ApiModelProperty(value = "运营单位名称")
    @NotBlank(message = "运营单位名称", groups = {UpdateValidatorGroup.class})
    private String operateUnitName;
    @ApiModelProperty(value = "项目区域业务中台编号")
    @NotBlank(message = "项目区域业务中台编号", groups = {UpdateValidatorGroup.class})
    private String projectAreaBusinessNo;
    @ApiModelProperty(value = "项目区域编号")
    @NotBlank(message = "项目区域编号", groups = {UpdateValidatorGroup.class})
    private String projectAreaNo;
    @ApiModelProperty(value = "项目区域名称")
    @NotBlank(message = "项目区域名称", groups = {UpdateValidatorGroup.class})
    private String projectAreaName;
    @ApiModelProperty(value = "项目业态")
    @NotBlank(message = "项目业态", groups = {UpdateValidatorGroup.class})
    private String projectFormat;
    @ApiModelProperty(value = "项目所属区县")
    @NotBlank(message = "项目所属区县", groups = {UpdateValidatorGroup.class})
    private String projectDistrict;
    @ApiModelProperty(value = "项目坐落")
    @NotBlank(message = "项目坐落", groups = {UpdateValidatorGroup.class})
    private String projectLocation;
    @ApiModelProperty(value = "所在小区或楼宇名称")
    @NotBlank(message = "所在小区或楼宇名称", groups = {UpdateValidatorGroup.class})
    private String projectEstate;

    @ApiModelProperty(value = "房屋ID")
    @NotBlank(message = "房屋ID", groups = {UpdateValidatorGroup.class})
    private String houseId;
    @ApiModelProperty(value = "房源编号")
    @NotBlank(message = "房源编号", groups = {UpdateValidatorGroup.class})
    private String houseNo;
    @ApiModelProperty(value = "房源名称")
    @NotBlank(message = "房源名称", groups = {UpdateValidatorGroup.class})
    private String houseName;
    @ApiModelProperty(value = "房号编号")
    @NotBlank(message = "房号编号", groups = {UpdateValidatorGroup.class})
    private String houseNumberNo;
    @ApiModelProperty(value = "房号")
    @NotBlank(message = "房号", groups = {UpdateValidatorGroup.class})
    private String houseNumber;
    @ApiModelProperty(value = "楼号")
    @NotBlank(message = "楼号", groups = {UpdateValidatorGroup.class})
    private String buildingNo;
    @ApiModelProperty(value = "单元号")
    @NotBlank(message = "单元号", groups = {UpdateValidatorGroup.class})
    private String unitNo;
    @ApiModelProperty(value = "所在层/总层数")
    @NotBlank(message = "所在层/总层数", groups = {UpdateValidatorGroup.class})
    private String floorNo;
    @ApiModelProperty(value = "房间号")
    @NotBlank(message = "房间号", groups = {UpdateValidatorGroup.class})
    private String roomNo;
    @ApiModelProperty(value = "户型")
    @NotBlank(message = "户型", groups = {UpdateValidatorGroup.class})
    private String houseType;
    @ApiModelProperty(value = "房间朝向")
    @NotBlank(message = "房间朝向", groups = {UpdateValidatorGroup.class})
    private String houseOrientation;
    @ApiModelProperty(value = "NC57老合同ID")
    @NotBlank(message = "NC57老合同ID", groups = {UpdateValidatorGroup.class})
    private String oldContractId;
    @ApiModelProperty(value = "合同ID")
    @NotBlank(message = "合同ID", groups = {UpdateValidatorGroup.class})
    private String contractId;
    @ApiModelProperty(value = "合同分类")
    @NotBlank(message = "合同分类", groups = {UpdateValidatorGroup.class})
    private String contractClassification;
    @ApiModelProperty(value = "合同类型")
    @NotBlank(message = "合同类型", groups = {UpdateValidatorGroup.class})
    private String contractType;
    @ApiModelProperty(value = "合同类型")
    @NotBlank(message = "合同类型", groups = {UpdateValidatorGroup.class})
    private String parkContractType;
    @ApiModelProperty(value = "合同当前状态")
    @NotBlank(message = "合同当前状态", groups = {UpdateValidatorGroup.class})
    private String contractStatus;
    @ApiModelProperty(value = "合同起始日期")
    @NotBlank(message = "合同起始日期", groups = {UpdateValidatorGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginDate;
    @ApiModelProperty(value = "合同终止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotBlank(message = "合同终止日期", groups = {UpdateValidatorGroup.class})
    private Date contractEndDate;
    @ApiModelProperty(value = "合同签订时间")
    @NotBlank(message = "合同签订时间", groups = {UpdateValidatorGroup.class})
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractSignTime;
    @ApiModelProperty(value = "合同起租日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotBlank(message = "合同起租日", groups = {UpdateValidatorGroup.class})
    private Date contractCommencementDate;
    @ApiModelProperty(value = "合同签约计价单位")
    @NotBlank(message = "合同签约计价单位", groups = {UpdateValidatorGroup.class})
    private String contractPriceUnit;
    @ApiModelProperty(value = "合同计价周期")
    @NotBlank(message = "合同计价周期", groups = {UpdateValidatorGroup.class})
    private String contractPricePeriod;
    @ApiModelProperty(value = "合同面积")
    @NotBlank(message = "合同面积", groups = {UpdateValidatorGroup.class})
    private BigDecimal contractArea;
    @ApiModelProperty(value = "签约房间类型")
    @NotBlank(message = "签约房间类型", groups = {UpdateValidatorGroup.class})
    private String roomType;
    @ApiModelProperty(value = "审批人")
    @NotBlank(message = "审批人", groups = {UpdateValidatorGroup.class})
    private String approver;
    @ApiModelProperty(value = "审批时间")
    @NotBlank(message = "审批时间", groups = {UpdateValidatorGroup.class})
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    @ApiModelProperty(value = "企业开户总行名称")
    private String companyBankName;
    @ApiModelProperty(value = "企业开户总行编码")
    private String companyBankCode;
    @ApiModelProperty(value = "企业开户支行名称")
    private String companyBankBranchName;
    @ApiModelProperty(value = "企业开户支行编码")
    private String companyBankBranchCode;
    @ApiModelProperty(value = "企业银行卡户名")
    private String companyBankAccountName;
    @ApiModelProperty(value = "企业银行卡卡号")
    private String companyBankAccountNo;
    @ApiModelProperty(value = "企业纳税识别号")
    private String companyTaxNo;

    @ApiModelProperty(value = "是否历史合同数据")
    private  String historyData;

    @ApiModelProperty(value = "老合同ID")
    private String beforeContractId;//老合同ID	String	否	64	reits或者调换房之前老合同编号
    @ApiModelProperty(value = "老项目ID")
    private String beforeProjectId;//老项目ID	String	否	64	reits或者调换房之前老合同对应的项目id
    @ApiModelProperty(value = "签约场景01reits02调换房")
    private String sceneType;//签约场景	String	否	10	01 reits 02 调换房

//    @ApiModelProperty(value = "区域")
//    private  String houseArea;

    @ApiModelProperty(value = "首尾非整账期标识")
    private String noCompleteType;

    @Override
    public String toString() {
        return "BillAgreementCreateParamsRequest{" +
                "parentContractId='" + parentContractId + '\'' +
                ", breakDays=" + breakDays +
                ", breakRate=" + breakRate +
                ", subsidyProportion=" + subsidyProportion +
                ", chargeSubjectList=" + chargeSubjectList +
                ", furnitureRentalList=" + furnitureRentalList +
                ", tenantBankName='" + tenantBankName + '\'' +
                ", tenantBankCode='" + tenantBankCode + '\'' +
                ", tenantBankBranchName='" + tenantBankBranchName + '\'' +
                ", tenantBankBrachCode='" + tenantBankBrachCode + '\'' +
                ", tenantBankAccountName='" + tenantBankAccountName + '\'' +
                ", tenantBankAccountNo='" + tenantBankAccountNo + '\'' +
                ", withholding='" + withholding + '\'' +
                ", withholdingSummary='" + withholdingSummary + '\'' +
                ", withholdingRemark='" + withholdingRemark + '\'' +
                ", agreementNo='" + agreementNo + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", tenantMobile='" + tenantMobile + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", tenantIDType='" + tenantIDType + '\'' +
                ", idNumber='" + idNumber + '\'' +
                ", mailUrl='" + mailUrl + '\'' +
                ", publicRecordNo='" + publicRecordNo + '\'' +
                ", tenantCustomerNo='" + tenantCustomerNo + '\'' +
                ", tenantSupplierNo='" + tenantSupplierNo + '\'' +
                ", tenantSupplierName='" + tenantSupplierName + '\'' +
                ", companyId='" + companyId + '\'' +
                ", companyIDType='" + companyIDType + '\'' +
                ", socialCreditCode='" + socialCreditCode + '\'' +
                ", companyName='" + companyName + '\'' +
                ", companyCustomerNo='" + companyCustomerNo + '\'' +
                ", companySupplierNo='" + companySupplierNo + '\'' +
                ", companySupplierName='" + companySupplierName + '\'' +
                ", projectId='" + projectId + '\'' +
                ", projectNo='" + projectNo + '\'' +
                ", projectName='" + projectName + '\'' +
                ", projectShortName='" + projectShortName + '\'' +
                ", operateEntityType='" + operateEntityType + '\'' +
                ", operateEntityName='" + operateEntityName + '\'' +
                ", operateUnitBusinessNo='" + operateUnitBusinessNo + '\'' +
                ", operateUnitNo='" + operateUnitNo + '\'' +
                ", operateUnitName='" + operateUnitName + '\'' +
                ", projectAreaBusinessNo='" + projectAreaBusinessNo + '\'' +
                ", projectAreaNo='" + projectAreaNo + '\'' +
                ", projectAreaName='" + projectAreaName + '\'' +
                ", projectFormat='" + projectFormat + '\'' +
                ", projectDistrict='" + projectDistrict + '\'' +
                ", projectLocation='" + projectLocation + '\'' +
                ", projectEstate='" + projectEstate + '\'' +
                ", houseId='" + houseId + '\'' +
                ", houseNo='" + houseNo + '\'' +
                ", houseName='" + houseName + '\'' +
                ", houseNumberNo='" + houseNumberNo + '\'' +
                ", houseNumber='" + houseNumber + '\'' +
                ", buildingNo='" + buildingNo + '\'' +
                ", unitNo='" + unitNo + '\'' +
                ", floorNo='" + floorNo + '\'' +
                ", roomNo='" + roomNo + '\'' +
                ", houseType='" + houseType + '\'' +
                ", houseOrientation='" + houseOrientation + '\'' +
                ", oldContractId='" + oldContractId + '\'' +
                ", contractId='" + contractId + '\'' +
                ", contractClassification='" + contractClassification + '\'' +
                ", contractType='" + contractType + '\'' +
                ", contractStatus='" + contractStatus + '\'' +
                ", contractBeginDate=" + contractBeginDate +
                ", contractEndDate=" + contractEndDate +
                ", contractSignTime=" + contractSignTime +
                ", contractCommencementDate=" + contractCommencementDate +
                ", contractPriceUnit='" + contractPriceUnit + '\'' +
                ", contractPricePeriod='" + contractPricePeriod + '\'' +
                ", contractArea=" + contractArea +
                ", roomType='" + roomType + '\'' +
                ", approver='" + approver + '\'' +
                ", approveTime=" + approveTime +
                ", companyTaxNo='" + companyTaxNo + '\'' +
                '}';
    }
}
