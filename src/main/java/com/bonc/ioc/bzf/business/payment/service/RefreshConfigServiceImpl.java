package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.service.impl.IRefreshConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.refresh.ContextRefresher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

@Slf4j
@Service
public class RefreshConfigServiceImpl implements IRefreshConfigService {
    @Resource
    private ContextRefresher contextRefresher;

    @Override
    public Set<String> refreshConfig() {
        Set<String> set = contextRefresher.refresh();
        return set;
    }

}
