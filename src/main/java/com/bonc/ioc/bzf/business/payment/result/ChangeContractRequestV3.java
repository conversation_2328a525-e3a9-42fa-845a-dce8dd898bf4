package com.bonc.ioc.bzf.business.payment.result;

import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangeAmountDTO;
import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangePaymentDateDTO;
import com.bonc.ioc.bzf.business.payment.result.create.BusinessContractChangeStartDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 合同变更 主类
 */
@Data
public class ChangeContractRequestV3 {

    private List<String> changeType;//	变更类型	String	10 02变更非身份证号的基础信息(现在只有02这一种类型)
    private String projectId;//	    项目id 	String	64

    @ApiModelProperty(value = "商业账单缴费日变更")
    private BusinessContractChangePaymentDateDTO businessContractChangePaymentDateDTO;//businessContractChangePaymentDateDTO(商业账单缴费日变更)
    @ApiModelProperty(value = "商业起租日变更")
    private BusinessContractChangeStartDTO businessContractChangeStartDTO;//(商业起租日变更)
    @ApiModelProperty(value = "商业合同租金标准、缩租")
    private BusinessContractChangeAmountDTO businessContractChangeAmountDTO;//(商业合同租金标准、缩租)


}
