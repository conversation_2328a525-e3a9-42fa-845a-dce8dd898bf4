package com.bonc.ioc.bzf.business.supplementary.service.impl;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmApproveDetailInfoEntity;
import com.bonc.ioc.bzf.business.supplementary.dao.BbpmApproveDetailInfoMapper;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmApproveDetailInfoService;
import com.bonc.ioc.bzf.utils.common.user.UserUtil;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.supplementary.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 审批明细表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Slf4j
@Service
public class BbpmApproveDetailInfoServiceImpl extends McpBaseServiceImpl<BbpmApproveDetailInfoEntity> implements IBbpmApproveDetailInfoService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmApproveDetailInfoMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmApproveDetailInfoService baseService;

    /**
     * 用户相关 服务实例
     */
    @Resource
    private UserUtil userUtil;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmApproveDetailInfoVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmApproveDetailInfoEntity entity = new BbpmApproveDetailInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setApproveDetailId(null);
        if(!baseService.insert(entity)) {
            log.error("审批明细表新增失败:" + entity.toString());
            throw new McpException("审批明细表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getApproveDetailId(),1)) {
                log.error("审批明细表新增后保存历史失败:" + entity.toString());
                throw new McpException("审批明细表新增后保存历史失败");
            }

            log.debug("审批明细表新增成功:"+entity.getApproveDetailId());
            return entity.getApproveDetailId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmApproveDetailInfoVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmApproveDetailInfoEntity> entityList = new ArrayList<>();
        for (BbpmApproveDetailInfoVo item:voList) {
            BbpmApproveDetailInfoEntity entity = new BbpmApproveDetailInfoEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmApproveDetailInfoEntity item:entityList){
            item.setApproveDetailId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("审批明细表新增失败");
            throw new McpException("审批明细表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmApproveDetailInfoEntity::getApproveDetailId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("审批明细表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("审批明细表批量新增后保存历史失败");
            }

            log.debug("审批明细表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param approveDetailId 需要删除的审批明细id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String approveDetailId) {
        if(!StringUtils.isEmpty(approveDetailId)) {
            if(!baseService.saveOperationHisById(approveDetailId,3)) {
                log.error("审批明细表删除后保存历史失败:" + approveDetailId);
                throw new McpException("审批明细表删除后保存历史失败");
            }

            if(!baseService.removeById(approveDetailId)) {
                log.error("审批明细表删除失败");
                throw new McpException("审批明细表删除失败"+approveDetailId);
            }
        } else {
            throw new McpException("审批明细表删除失败审批明细id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param approveDetailIdList 需要删除的审批明细id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> approveDetailIdList) {
        if(!CollectionUtils.isEmpty(approveDetailIdList)) {
            int oldSize = approveDetailIdList.size();
            approveDetailIdList = approveDetailIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(approveDetailIdList) || oldSize != approveDetailIdList.size()) {
                throw new McpException("审批明细表批量删除失败 存在主键id为空的记录"+StringUtils.join(approveDetailIdList));
            }

            if(!baseService.saveOperationHisByIds(approveDetailIdList,3)) {
                log.error("审批明细表批量删除后保存历史失败:" + StringUtils.join(approveDetailIdList));
                throw new McpException("审批明细表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(approveDetailIdList)) {
                log.error("审批明细表批量删除失败");
                throw new McpException("审批明细表批量删除失败"+StringUtils.join(approveDetailIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmApproveDetailInfoVo vo) {
        if(vo != null) {
            BbpmApproveDetailInfoEntity entity = new BbpmApproveDetailInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getApproveDetailId())) {
                throw new McpException("审批明细表更新失败传入审批明细id为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("审批明细表更新失败");
                throw new McpException("审批明细表更新失败"+entity.getApproveDetailId());
            } else {
                if(!baseService.saveOperationHisById(entity.getApproveDetailId(),2)) {
                    log.error("审批明细表更新后保存历史失败:" + entity.getApproveDetailId());
                    throw new McpException("审批明细表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("审批明细表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmApproveDetailInfoVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmApproveDetailInfoEntity> entityList = new ArrayList<>();

            for (BbpmApproveDetailInfoVo item:voList){
                BbpmApproveDetailInfoEntity entity = new BbpmApproveDetailInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getApproveDetailId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("审批明细表批量更新失败 存在审批明细id为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("审批明细表批量更新失败");
                throw new McpException("审批明细表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getApproveDetailId())).map(BbpmApproveDetailInfoEntity::getApproveDetailId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("审批明细表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("审批明细表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmApproveDetailInfoVo vo) {
        if(vo != null) {
            BbpmApproveDetailInfoEntity entity = new BbpmApproveDetailInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("审批明细表保存失败");
                throw new McpException("审批明细表保存失败"+entity.getApproveDetailId());
            } else {
                if(!baseService.saveOperationHisById(entity.getApproveDetailId(),4)) {
                    log.error("审批明细表保存后保存历史失败:" + entity.getApproveDetailId());
                    throw new McpException("审批明细表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("审批明细表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmApproveDetailInfoVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmApproveDetailInfoEntity> entityList = new ArrayList<>();

            for (BbpmApproveDetailInfoVo item:voList){
                BbpmApproveDetailInfoEntity entity = new BbpmApproveDetailInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("审批明细表批量保存失败");
                throw new McpException("审批明细表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getApproveDetailId())).map(BbpmApproveDetailInfoEntity::getApproveDetailId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("审批明细表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("审批明细表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param approveDetailId 需要查询的审批明细id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmApproveDetailInfoVo selectByIdRecord(String approveDetailId) {
        BbpmApproveDetailInfoVo vo = new BbpmApproveDetailInfoVo();

        if(!StringUtils.isEmpty(approveDetailId)) {
            BbpmApproveDetailInfoEntity entity = baseService.selectById(approveDetailId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmApproveDetailInfoPageResultVo>> selectByPageRecord(BbpmApproveDetailInfoPageVo vo) {
        List<BbpmApproveDetailInfoPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 获取操作记录列表
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 操作记录列表
     */
    @Override
    public List<BbpmApproveDetailInfoVo> selectListByParentId(String parentId, String approveType) {
        List<BbpmApproveDetailInfoVo> resultList = baseMapper.selectListByParentId(parentId, approveType);
        for (BbpmApproveDetailInfoVo approveDetailInfoVo : resultList) {
            if (StringUtils.isNotBlank(approveDetailInfoVo.getCreateUser())) {
                approveDetailInfoVo.setCreateUserName(userUtil.getUserName(approveDetailInfoVo.getCreateUser()));
            }
        }
        return resultList;
    }
}
