package com.bonc.ioc.bzf.business.supplementary.vo;

import java.util.Date;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 追加单试算表 实体类
 *
 * <AUTHOR>
 * @date 2025-04-08
 * @change 2025-04-08 by pyj for init
 */
@Data
@ApiModel(value = "BbpmSupplementaryPreviewBillPageVo对象", description = "追加单试算表")
public class BbpmSupplementaryPreviewBillPageVo extends McpBasePageVo implements Serializable {

    /**
     * 试算id
     */
    @ApiModelProperty(value = "试算id")
    @NotBlank(message = "试算id不能为空", groups = {UpdateValidatorGroup.class})
    private String billId;

    /**
     * 上级id
     */
    @ApiModelProperty(value = "上级id")
    private String parentId;

    /**
     * 费用项目
     */
    @ApiModelProperty(value = "费用项目")
    private String chargeSubject;

    /**
     * 承租人名称
     */
    @ApiModelProperty(value = "承租人名称")
    private String tenantName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String orgName;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String roomAddress;

    /**
     * 应缴费日期
     */
    @ApiModelProperty(value = "应缴费日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payableDate;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
    private String payableMoney;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private String noTaxMoney;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private String taxRate;

    /**
     * 增值税额
     */
    @ApiModelProperty(value = "增值税额")
    private String rateMoney;

    /**
     * 账单类型(01.个人 02.企业)
     */
    @ApiModelProperty(value = "账单类型(01.个人 02.企业)")
    private String billOwner;

    /**
     * 计费方式(01.循环 02.单次)
     */
    @ApiModelProperty(value = "计费方式(01.循环 02.单次)")
    private String cyclicOrSingle;

    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date chargeSubjectBeginDate;

    /**
     * 收费科目结束日期
     */
    @ApiModelProperty(value = "收费科目结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date chargeSubjectEndDate;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
    private Integer chargeSubjectPeriod;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;
}
