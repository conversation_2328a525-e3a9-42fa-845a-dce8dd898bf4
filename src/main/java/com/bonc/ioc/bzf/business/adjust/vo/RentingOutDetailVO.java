package com.bonc.ioc.bzf.business.adjust.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RentingOutDetailVO implements java.io.Serializable{

    @ApiModelProperty(value = "收费科目 01 租金，02押金")
    private String chargeSubject;

    @ApiModelProperty(value = "应退/抵金额合计")
    private BigDecimal rentingOutMoneyTotal;

    @ApiModelProperty(value = "应退金额（个人）")
    private BigDecimal rentingOutMoneyPerson;

    @ApiModelProperty(value = "应退金额（企业）")
    private BigDecimal rentingOutMoneyCompany;

    @ApiModelProperty(value = "应抵金额（个人）")
    private BigDecimal deductionMoneyPerson;

    @ApiModelProperty(value = "应抵金额（企业）")
    private BigDecimal deductionMoneyCompany;

}
