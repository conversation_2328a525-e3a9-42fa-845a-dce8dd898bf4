package com.bonc.ioc.bzf.business.payment.result.create;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 押金退回相关
 */
@Data
@Builder
public class DepositReturnRequest {
    @ApiModelProperty(value = "押金处理方式(01转入新合同02 退回银行卡03退回其他银行卡)")
    private String returnType;
    @ApiModelProperty(value = "开户总行网点名称")
    private String bankName;
    @ApiModelProperty(value = "开户总行网点行号")
    private String bankCode;
    @ApiModelProperty(value = "开户行网点名称")
    private String bankBranchName;
    @ApiModelProperty(value = "开户行网点行号")
    private String bankBranchCode;
    @ApiModelProperty(value = "银行卡所在省")
    private String bankProvince;
    @ApiModelProperty(value = "银行卡所在市")
    private String bankCity;
    @ApiModelProperty(value = "银行卡户名")
    private String bankAccountName;
    @ApiModelProperty(value = "银行卡卡号")
    private String bankAccountNo;

    @Override
    public String toString() {
        return "DepositReturnRequest{" +
                "returnType='" + returnType + '\'' +
                ", bankName='" + bankName + '\'' +
                ", bankCode='" + bankCode + '\'' +
                ", bankBranchName='" + bankBranchName + '\'' +
                ", bankBranchCode='" + bankBranchCode + '\'' +
                ", bankProvince='" + bankProvince + '\'' +
                ", bankCity='" + bankCity + '\'' +
                ", bankAccountName='" + bankAccountName + '\'' +
                ", bankAccountNo='" + bankAccountNo + '\'' +
                '}';
    }
}
