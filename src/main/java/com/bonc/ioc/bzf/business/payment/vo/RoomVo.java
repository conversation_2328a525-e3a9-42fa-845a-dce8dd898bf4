package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class RoomVo {
    @ApiModelProperty(value = "计费科目列表")
    private List<ChargeSubjecParamsVo> chargeSubjectList;

    @ApiModelProperty(value = "家具租金列表")
    private List<FurnitureRentalParamsVo> furnitureRentalList;

    @ApiModelProperty(value = "房屋ID")
    @NotBlank(message = "房屋ID", groups = {UpdateValidatorGroup.class})
    private String houseId;
    @ApiModelProperty(value = "房源编号")
    @NotBlank(message = "房源编号", groups = {UpdateValidatorGroup.class})
    private String houseNo;
    @ApiModelProperty(value = "房源名称")
    @NotBlank(message = "房源名称", groups = {UpdateValidatorGroup.class})
    private String houseName;
    @ApiModelProperty(value = "房号编号")
    @NotBlank(message = "房号编号", groups = {UpdateValidatorGroup.class})
    private String houseNumberNo;
    @ApiModelProperty(value = "房号")
    @NotBlank(message = "房号", groups = {UpdateValidatorGroup.class})
    private String houseNumber;
    @ApiModelProperty(value = "楼号")
    @NotBlank(message = "楼号", groups = {UpdateValidatorGroup.class})
    private String buildingNo;
    @ApiModelProperty(value = "单元号")
    @NotBlank(message = "单元号", groups = {UpdateValidatorGroup.class})
    private String unitNo;
    @ApiModelProperty(value = "所在层/总层数")
    @NotBlank(message = "所在层/总层数", groups = {UpdateValidatorGroup.class})
    private String floorNo;
    @ApiModelProperty(value = "房间号")
    @NotBlank(message = "房间号", groups = {UpdateValidatorGroup.class})
    private String roomNo;
    @ApiModelProperty(value = "户型")
    @NotBlank(message = "户型", groups = {UpdateValidatorGroup.class})
    private String houseType;
    @ApiModelProperty(value = "房间朝向")
    @NotBlank(message = "房间朝向", groups = {UpdateValidatorGroup.class})
    private String houseOrientation;
    @ApiModelProperty(value = "签约房间类型")
    @NotBlank(message = "签约房间类型", groups = {UpdateValidatorGroup.class})
    private String roomType;

    @Override
    public String toString() {
        return "RoomVo{" +
                "chargeSubjectList=" + chargeSubjectList +
                ", furnitureRentalList=" + furnitureRentalList +
                ", houseId='" + houseId + '\'' +
                ", houseNo='" + houseNo + '\'' +
                ", houseName='" + houseName + '\'' +
                ", houseNumberNo='" + houseNumberNo + '\'' +
                ", houseNumber='" + houseNumber + '\'' +
                ", buildingNo='" + buildingNo + '\'' +
                ", unitNo='" + unitNo + '\'' +
                ", floorNo='" + floorNo + '\'' +
                ", roomNo='" + roomNo + '\'' +
                ", houseType='" + houseType + '\'' +
                ", houseOrientation='" + houseOrientation + '\'' +
                ", roomType='" + roomType + '\'' +
                '}';
    }


}
