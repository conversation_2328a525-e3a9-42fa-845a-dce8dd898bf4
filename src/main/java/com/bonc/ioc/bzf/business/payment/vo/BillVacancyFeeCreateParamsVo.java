package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.bzf.business.payment.result.create.RoomRequest;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Singular;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 3.30 空置费生成账单接口
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class BillVacancyFeeCreateParamsVo {
	@ApiModelProperty(value = "趸租大合同ID")
	private String parentContractId;
	@ApiModelProperty(value = "空置日期")
	private Date vacancyDate;

	@ApiModelProperty(value = "项目列表")
	private List<ProjectVacancyFeeVo> projectList;

}
