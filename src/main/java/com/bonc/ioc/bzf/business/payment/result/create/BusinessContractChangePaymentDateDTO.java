package com.bonc.ioc.bzf.business.payment.result.create;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * businessContractChangePaymentDateDTO(商业账单缴费日变更)
 */
@Data
public class BusinessContractChangePaymentDateDTO implements Serializable {
    @ApiModelProperty(value = "变更合同号")
    private String contractCode;//变更合同号	String
    @ApiModelProperty(value = "变更合同的账单列表")
    private List<ChangeBillPaymentDateDTO> changeBillPaymentDateDTOList;


}
