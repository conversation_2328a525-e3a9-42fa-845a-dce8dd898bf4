package com.bonc.ioc.bzf.business.supplementary.vo;

import java.util.Date;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 审批表 实体类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Data
@ApiModel(value = "BbpmApproveInfoPageVo对象", description = "审批表")
public class BbpmApproveInfoPageVo extends McpBasePageVo implements Serializable {

    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    @NotBlank(message = "审批id不能为空", groups = {UpdateValidatorGroup.class})
    private String approveId;

    /**
     * 上级id
     */
    @ApiModelProperty(value = "上级id")
    private String parentId;

    /**
     * 审批类型(1.追加账单)
     */
    @ApiModelProperty(value = "审批类型(1.追加账单)")
    private String approveType;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 提交人id
     */
    @ApiModelProperty(value = "提交人id")
    private String submitUserId;

    /**
     * 提交人名称
     */
    @ApiModelProperty(value = "提交人名称")
    private String submitUserName;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverUserId;

    /**
     * 审批人名称
     */
    @ApiModelProperty(value = "审批人名称")
    private String approverUserName;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;
}
