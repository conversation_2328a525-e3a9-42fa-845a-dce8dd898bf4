package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpmMainLesseeExcelEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更-主承租人导入参数日志表 服务类
 *
 * <AUTHOR>
 * @date 2023-11-01
 * @change 2023-11-01 by binghong.tang for init
 */
public interface IBbpmMainLesseeExcelService extends IMcpBaseService<BbpmMainLesseeExcelEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    String insertRecord(BbpmMainLesseeExcelVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmMainLesseeExcelVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    void removeByIdRecord(String id);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param idList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> idList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更-主承租人导入参数日志表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    void updateByIdRecord(BbpmMainLesseeExcelVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更-主承租人导入参数日志表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmMainLesseeExcelVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更-主承租人导入参数日志表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    void saveByIdRecord(BbpmMainLesseeExcelVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更-主承租人导入参数日志表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmMainLesseeExcelVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    BbpmMainLesseeExcelVo selectByIdRecord(String id);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    PageResult<List<BbpmMainLesseeExcelPageResultVo>> selectByPageRecord(BbpmMainLesseeExcelPageVo vo);
}
