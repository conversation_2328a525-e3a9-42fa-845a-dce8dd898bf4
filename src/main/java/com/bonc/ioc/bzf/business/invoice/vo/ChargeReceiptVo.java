package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票开具--主收款单票据列表
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value="ChargeReceiptVo对象", description="发票开具--主收款单票据列表")
@Data
public class ChargeReceiptVo extends McpBaseVo implements Serializable{

    @ApiModelProperty(value = "主收款单唯一标识码")
    private String chargeCode;

    @ApiModelProperty(value = "合同编号")
    private String contractCode	;
}
