package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.bzf.business.adjust.vo.RentingOutDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "账单预览结果实体V2", description = "账单预览结果实体V2")
@Data
public class ChangeTrialPreviewBillsResultV2Vo implements java.io.Serializable {

    /**
     * 房源id
     */
    @ApiModelProperty(value = "房源id")
    private String houseId;

    /**
     * 试算后的账单列表
     */
    @ApiModelProperty(value = "试算后的账单列表")
    private List<ChargeSubjectBillDTO> chargeSubjectBillList;

    /**
     * 应退/抵扣金额（展开为一维数组）
     */
    @ApiModelProperty(value = "应退/抵扣金额")
    private List<RentingOutDetailVO> rentingOutDetailVOList;
}
