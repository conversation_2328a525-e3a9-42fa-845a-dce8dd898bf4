package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 账单管理--手动报盘 实体类
 */
@ApiModel(value="BbpmBillManagementManualVo对象", description="账单管理--手动报盘 ")
@Data
public class BbpmBillManagementManualVo implements Serializable{


    @ApiModelProperty(value = "第一种方式--项目ID")
    private String projectId;

    @ApiModelProperty(value = "第二种方式--集合")
    private List<Map<String,String>> contractList;

    @ApiModelProperty(value = "待报盘账单总条数")
    private Long billTotalNum;
    @ApiModelProperty(value = "业务类型01:按运营项目报盘02:按租户报盘")
    private String bizType;
    @ApiModelProperty(value = "申请批次")
    private String batchNo;


}
