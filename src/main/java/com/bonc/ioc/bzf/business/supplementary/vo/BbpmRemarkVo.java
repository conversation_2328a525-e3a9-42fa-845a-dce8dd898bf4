package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 评论 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/10
 */
@Data
@ApiModel(value = "评论 vo实体", description = "评论 vo实体")
public class BbpmRemarkVo extends McpBaseVo implements Serializable {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空")
    private String id;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    private String remark;

    /**
     * 评论时间
     */
    @ApiModelProperty(value = "评论时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date date;

    /**
     * 合同编号列表
     */
    @ApiModelProperty(value = "合同编号列表")
    private List<String> contractCodeList;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;
}
