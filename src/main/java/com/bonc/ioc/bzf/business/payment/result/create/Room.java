package com.bonc.ioc.bzf.business.payment.result.create;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * roomList	商铺信息
 */
@Data
public class Room implements Serializable {

    @ApiModelProperty(value = "商铺id")
    private String houseId;	//商铺ID	 String	是 (如果是新的houseId,需要传商铺相关信息，具体参数与商业签约一致)

    @ApiModelProperty(value = "变更前商铺id")
    private String beforeHouseId;	//变更前商铺id String 否 缩租后原商铺Id

    @ApiModelProperty(value = "计费科目列表")
    private List<ChargeSubject> chargeSubjectList;  //计费科目列表 是 递增、免租等规则不变就传原来的，变了就传新的

    @ApiModelProperty(value = "房源编号")
    private String houseNo;	 //房源编号 String 是	10 需与NCC的房源编号一致

    @ApiModelProperty(value = "房源名称")
    private String houseName;	//房源名称 String 是 100

    @ApiModelProperty(value = "房号编号")
    private String houseNumberNo;	//房号编号 String 否 10 需与NCC的房号编号一致，宜居公司的两个项目才有，宜居公司的两个项目为必填

    @ApiModelProperty(value = "房号")
    private String houseNumberName;	 //房号 String 否 20	NCC房号,宜居公司的两个项目为必填

    @ApiModelProperty(value = "楼号")
    private String buildingNo;	//楼号 String 是	10 该房屋的楼栋号，除特殊情况外，应为三位数字，不足三位的前面补0，例如001，010，000等

    @ApiModelProperty(value = "单元号")
    private String unitNo;	//单元号	String 是 10 填写该房屋的单元号，除特殊情况外，应为两位数字，不足两位的前面补0，例如01，10，00等

    @ApiModelProperty(value = "所在层/总层数")
    private String floorNo;	//所在层/总层数 String 是 10 所在层/总层数：指房间所在层数/总层数，例如6/12，地下楼层可直接填写所在层数，例如-1

    @ApiModelProperty(value = "房间号")
    private String roomNo;	 //房间号 String 否 10 填写该房屋实测的房间号或者房间名称+数字（无房间号的），纯数字的房间号应填写为四位数字，不足四位的前面补0，例如0301，1010，0910等

    @ApiModelProperty(value = "户型")
    private String houseType;	//户型 String 是	10 填写房屋的户型结构，包括“平层、错层、复式层、跃层、其他

    @ApiModelProperty(value = "房间朝向")
    private String houseOrientation;  //房间朝向	String 是 10 结合建筑物的正立面或称建筑的主要立面所面对的方向，以房屋主要的采光方向（客厅、卧室为主）来认定朝向

    @ApiModelProperty(value = "签约房间类型")
    private String roomType;  //签约房间类型 String 是 10	01普通公租房 02机构公租房 03人才公租房 04商铺

    @ApiModelProperty(value = "房间面积")
    private BigDecimal roomArea;	//房间面积 BigDecimal 13，4 房间面积（平米）


}
