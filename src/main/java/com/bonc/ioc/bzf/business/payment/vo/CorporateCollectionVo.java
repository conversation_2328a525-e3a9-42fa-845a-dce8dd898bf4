package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.bzf.business.payment.result.CorporateCollectionSubRequest;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 3.23  趸租对公收款接口：线下转账和支票收款请求 主类
 */
@Data
@ApiModel(value = "CorporateCollectionVo对象", description = "支票和线下转账实体")
public class CorporateCollectionVo {
    @ApiModelProperty(value = "附件在文件中心的ID")
    private String receiptNoUrl;
    @ApiModelProperty(value = "上传人,对应上传业务员姓名")
    private String uploader;
    @ApiModelProperty(value = "转账凭证号,支付方式为线下转账，必传")
    private String transBillNo;
    @ApiModelProperty(value = "支票流水号,支付方式为支票，必传")
    private String checkNo;
    @ApiModelProperty(value = "付款方账户号,支付方式为线下转账，必传")
    private String payAcctNo;
    @ApiModelProperty(value = "付款方名称,支付方式为线下转账，必传")
    private String payer;
    @ApiModelProperty(value = "收款方账户号,支付方式为线下转账，必传")
    private String recepitAcctNo;
    @ApiModelProperty(value = "收款方名称	,支付方式为线下转账，必传")
    private String recepiter;
    @ApiModelProperty(value = "金额,支付方式为线下转账，必传")
    private BigDecimal amount;
    @ApiModelProperty(value = "交易日期,yyyy/MM/dd上传日期,支付方式为线下转账，必传")
    private String transDate;
    @ApiModelProperty(value = "摘要,支付方式为线下转账，必传")
    private String summary;
    @ApiModelProperty(value = "附言,支付方式为线下转账，必传")
    private String remark;
    @ApiModelProperty(value = "用途,支付方式为线下转账，必传")
    private String useage;

    @ApiModelProperty(value = "银行总行联行号")
    private String bankCode;

    @ApiModelProperty(value = "转账银行名称")
    private String transferBankName;
    @ApiModelProperty(value = "收款银行编码")
    private String receiptBankCode;
    @ApiModelProperty(value = "收款银行名称")
    private String  receiptBankName;
    @ApiModelProperty(value = "收款银行账户")
    private String receiptBankAcct;

    @ApiModelProperty(value = "转账金额--查询收款凭证接口时使用")
    private BigDecimal totalTransAmount;
    @ApiModelProperty(value = "交易日期--查询收款凭证接口时使用")
    private String transDateStr;

    @ApiModelProperty(value = "凭证号或者支票流水号的总金额")
    private BigDecimal totalAmount;


    @ApiModelProperty(value = "银行支行名称")
    private String branchName;
    @ApiModelProperty(value = "银行总行名称")
    private String bankName;
    @ApiModelProperty(value = "银行账户")
    private String bankAccountNo;
    @ApiModelProperty(value = "银行户名")
    private String bankAccountName;

    @ApiModelProperty(value ="是否是多项目认款01是02否")
    private String  multiProject;
    @ApiModelProperty(value ="是否是多项目认款名称")
    private String  multiProjectName;
    @ApiModelProperty(value ="收款银行账号")
    private String receiptBankAcctNo;
    @ApiModelProperty(value ="收款银行户名")
    private String receiptBankAccountName;
    @ApiModelProperty(value ="收款银行支行名称")
    private String receiptBankBranchName;


}
