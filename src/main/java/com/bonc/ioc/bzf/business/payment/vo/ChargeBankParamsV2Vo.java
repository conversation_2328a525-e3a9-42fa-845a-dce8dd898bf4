package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 根据项目查询保障房收款开户行
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeBankParamsV2Vo {

	@ApiModelProperty(value = "项目id")
	@NotBlank(message = "项目id",groups = {UpdateValidatorGroup.class})
	private  String projectId;


}
