package com.bonc.ioc.bzf.business.supplementary.dao;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmApproveDetailInfoEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.supplementary.vo.*;

import java.util.List;

/**
 * 审批明细表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Mapper
public interface BbpmApproveDetailInfoMapper extends McpBaseMapper<BbpmApproveDetailInfoEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    List<BbpmApproveDetailInfoPageResultVo> selectByPageCustom(@Param("vo") BbpmApproveDetailInfoPageVo vo);

    /**
     * 获取操作记录列表
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 操作记录列表
     */
    List<BbpmApproveDetailInfoVo> selectListByParentId(@Param("parentId") String parentId,
                                                       @Param("approveType") String approveType);
}
