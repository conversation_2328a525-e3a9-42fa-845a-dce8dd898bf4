package com.bonc.ioc.bzf.business.payment.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 押金条 实体类
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by binghong.tang for init
 */
@ApiModel(value="BbpmCashPledgePageResultVo对象", description="押金条")
public class BbpmCashPledgePageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 唯一标识符
     */
    @ApiModelProperty(value = "唯一标识符")
    @NotBlank(message = "唯一标识符不能为空",groups = {UpdateValidatorGroup.class})
                                  private String cashPledgeId;

    /**
     * 账单id
     */
    @ApiModelProperty(value = "账单id")
    @NotBlank(message = "账单id不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String billId;

    /**
     * 合同code
     */
    @ApiModelProperty(value = "合同code")
                            private String contractCode;

    /**
     * 计费科目编号:02押金
     */
    @ApiModelProperty(value = "计费科目编号:02押金")
    @NotBlank(message = "计费科目编号:02押金不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String chargeSubjectNo;

    /**
     * 签字状态:01未签字,02已签字
     */
    @ApiModelProperty(value = "签字状态:01未签字,02已签字,03不用签字")
                            private String signStatus;

    /**
     * 押金条文件地址
     */
    @ApiModelProperty(value = "押金条文件地址")
                            private String file;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    /**
     * 项目业态
     */
    @ApiModelProperty(value = "项目业态")
    private String projectFormat;

    public String getProjectFormat() {
        return projectFormat;
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = projectFormat;
    }
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
    /**
     * @return 唯一标识符
     */
    public String getCashPledgeId() {
        return cashPledgeId;
    }

    public void setCashPledgeId(String cashPledgeId) {
        this.cashPledgeId = cashPledgeId;
    }

    /**
     * @return 账单id
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 合同code
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 计费科目编号:02押金
     */
    public String getChargeSubjectNo() {
        return chargeSubjectNo;
    }

    public void setChargeSubjectNo(String chargeSubjectNo) {
        this.chargeSubjectNo = chargeSubjectNo;
    }

    /**
     * @return 签字状态:01未签字,02已签字
     */
    public String getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(String signStatus) {
        this.signStatus = signStatus;
    }

    /**
     * @return 押金条文件地址
     */
    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmCashPledgePageResultVo{" +
            "cashPledgeId=" + cashPledgeId +
            ", billId=" + billId +
            ", contractCode=" + contractCode +
            ", chargeSubjectNo=" + chargeSubjectNo +
            ", signStatus=" + signStatus +
            ", file=" + file +
            ", delFlag=" + delFlag +
        "}";
    }
}
