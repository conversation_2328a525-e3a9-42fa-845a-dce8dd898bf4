package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 推送的产品信息 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/25
 */
@Data
@ApiModel(value = "推送的产品信息", description = "推送的产品信息")
public class BbctPushProductInfoVo extends McpBaseVo implements Serializable {

    /**
     * 签约方信息列表
     */
    @ApiModelProperty(value = "签约方信息列表")
    private List<BbctPushSignatoryInfoVo> signatoryInfoList;


    /**
     * 计费科目列表
     */
    @ApiModelProperty(value = "计费科目列表")
    private List<BbctPushChargeSubjectVo> chargeSubjectList;

    /**
     * 家具租金列表
     */
    @ApiModelProperty(value = "家具租金列表")
    private List<BbctFurnitureRentalVo> furnitureRentalList;

    /**
     * 房屋ID
     */
    @ApiModelProperty(value = "房屋ID")
    private String houseId;

    /**
     * 房屋编号
     */
    @ApiModelProperty(value = "房屋编号")
    private String houseNo;

    /**
     * 房源名称
     */
    @ApiModelProperty(value = "房源名称")
    private String houseName;

    /**
     * 楼号
     */
    @ApiModelProperty(value = "楼号")
    private String buildingNo;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    private String unitNo;

    /**
     * 所在层
     */
    @ApiModelProperty(value = "所在层")
    private String currentFloorNo;

    /**
     * 总层数
     */
    @ApiModelProperty(value = "总层数")
    private String totalFloorNo;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String roomNo;

    /**
     * 户型
     */
    @ApiModelProperty(value = "户型")
    private String houseType;

    /**
     * 房间朝向
     */
    @ApiModelProperty(value = "房间朝向")
    private String houseOrientation;

    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
    private BigDecimal roomArea;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目简称
     */
    @ApiModelProperty(value = "项目简称")
    private String projectShortName;

    /**
     * 运营主体类型
     */
    @ApiModelProperty(value = "运营主体类型")
    private String operateEntityType;

    /**
     * 运营主体名称
     */
    @ApiModelProperty(value = "运营主体名称")
    private String operateEntityName;

    /**
     * 运营单位业务中台编号
     */
    @ApiModelProperty(value = "运营单位业务中台编号")
    private String operateUnitBusinessNo;

    /**
     * 运营单位编号
     */
    @ApiModelProperty(value = "运营单位编号")
    private String operateUnitNo;

    /**
     * 运营单位名称
     */
    @ApiModelProperty(value = "运营单位名称")
    private String operateUnitName;

    /**
     * 项目区域业务中台编号
     */
    @ApiModelProperty(value = "项目区域业务中台编号")
    private String projectAreaBusinessNo;

    /**
     * 项目区域编号
     */
    @ApiModelProperty(value = "项目区域编号")
    private String projectAreaNo;

    /**
     * 项目区域名称
     */
    @ApiModelProperty(value = "项目区域名称")
    private String projectAreaName;

    /**
     * 项目业态
     */
    @ApiModelProperty(value = "项目业态")
    private String projectFormat;

    /**
     * 所在小区或楼宇名称
     */
    @ApiModelProperty(value = "所在小区或楼宇名称")
    private String projectEstate;

    /**
     * 签约房间类型
     */
    @ApiModelProperty(value = "签约房间类型")
    private String roomType;

    /**
     * 租金标准
     */
    @ApiModelProperty(value = "租金标准")
    private String rentStandardType;

    @ApiModelProperty(value = "车位资产类型")
    private String parkPropertyType;

    @ApiModelProperty(value = "车位位置")
    private String carLocation;

    @ApiModelProperty(value = "二级业态")
    private String secBusinessType;


//    @ApiModelProperty(value = "区域")
//    private String houseArea;
}
