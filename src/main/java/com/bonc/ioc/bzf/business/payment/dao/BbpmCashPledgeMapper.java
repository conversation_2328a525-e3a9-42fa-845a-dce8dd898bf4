package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.BbpmCashPledgeEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 押金条 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by binghong.tang for init
 */
@Mapper
public interface BbpmCashPledgeMapper extends McpBaseMapper<BbpmCashPledgeEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change 2023-05-17 by binghong.tang for init
     */
    List<BbpmCashPledgePageResultVo> selectByPageCustom(@Param("vo") BbpmCashPledgePageVo vo );

    List<BbpmCashPledgeVo> selectByConditions(@Param("vo") BbpmCashPledgeVo vo );
}
