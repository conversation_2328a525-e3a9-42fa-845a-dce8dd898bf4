package com.bonc.ioc.bzf.business.supplementary.enums;

/**
 * 追加账单计费标准 枚举类
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
public enum SupplementaryChargeStandardEnum {

    /**
     * 单价
     */
    UNIT_PRICE("1", "单价"),

    /**
     * 一口价
     */
    RENT_STANDARD("2", "一口价"),

    /**
     * 月租金百分比
     */
    PERCENT("3", "月租金百分比");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    SupplementaryChargeStandardEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    SupplementaryChargeStandardEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        SupplementaryChargeStandardEnum[] enums = values();
        for (SupplementaryChargeStandardEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
