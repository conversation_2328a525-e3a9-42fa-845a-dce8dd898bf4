package com.bonc.ioc.bzf.business.payment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 现金盘点存款单表v3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@TableName("bbpm_deposit_slip")
@ApiModel(value="BbpmDepositSlipEntity对象", description="现金盘点存款单表v3.0")
public class BbpmDepositSlipEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_DEPOSIT_ID = "deposit_id";
    public static final String FIELD_DEPOSIT_SLIP_NO = "deposit_slip_no";
    public static final String FIELD_DEPOSIT_STATUS = "deposit_status";
    public static final String FIELD_PAID_IN_AMOUNT = "paid_in_amount";
    public static final String FIELD_COLLECTION_DOC_QTY = "collection_doc_qty";
    public static final String FIELD_TRANSACTION_NO = "transaction_no";
    public static final String FIELD_DEPOSIT_TIME = "deposit_time";
    public static final String FIELD_DEPOSITOR = "depositor";
    public static final String FIELD_VOUCHER_UPLOAD_STATUS = "voucher_upload_status";
    public static final String FIELD_BANK_VOUCHER = "bank_voucher";
    public static final String FIELD_UPLOADER_ID = "uploader_id";
    public static final String FIELD_UPLOADER_NAME = "uploader_name";
    public static final String FIELD_UPLOADER_DATE = "uploader_date";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
                                @TableId(value = "deposit_id", type = IdType.ASSIGN_UUID)
                                  private String depositId;

    /**
     * 存款单号
     */
    @ApiModelProperty(value = "存款单号")
                            private String depositSlipNo;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
                            private String depositStatus;

    /**
     * 存款金额
     */
    @ApiModelProperty(value = "存款金额")
                            private BigDecimal paidInAmount;

    /**
     * 收款单数量
     */
    @ApiModelProperty(value = "收款单数量")
                            private String collectionDocQty;

    /**
     * 交易单号
     */
    @ApiModelProperty(value = "交易单号")
                            private String transactionNo;

    /**
     * 存款时间
     */
    @ApiModelProperty(value = "存款时间")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date depositTime;

    /**
     * 存款人
     */
    @ApiModelProperty(value = "存款人")
                            private String depositor;

    /**
     * 凭证上传状态--无用
     */
    @ApiModelProperty(value = "凭证上传状态--无用")
                            private String voucherUploadStatus;

    /**
     * 银行凭证
     */
    @ApiModelProperty(value = "银行凭证")
                            private String bankVoucher;

    /**
     * 上传人id
     */
    @ApiModelProperty(value = "上传人id")
                            private String uploaderId;

    /**
     * 上传人name
     */
    @ApiModelProperty(value = "上传人name")
                            private String uploaderName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date uploaderDate;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 业财返回的收款单ID
     */
    @ApiModelProperty(value = "业财返回的收款单ID")
    private String receiptNo;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 银行code
     */
    @ApiModelProperty(value = "银行code")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "收款银行账户")
    private String receiptBankAcct;
    @ApiModelProperty(value = "实际存款金额")
    private BigDecimal totalCashAmount;


    @ApiModelProperty(value = "银行支行名称")
    private String branchName;
    @ApiModelProperty(value = "银行账户")
    private String bankAccountNo;
    @ApiModelProperty(value = "银行户名")
    private String bankAccountName;

    @ApiModelProperty(value ="是否是多项目认款01是02否")
    private String  multiProject;

    public String getMultiProject() {
        return multiProject;
    }

    public void setMultiProject(String multiProject) {
        this.multiProject = multiProject;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }


    public String getReceiptBankAcct() {
        return receiptBankAcct;
    }

    public void setReceiptBankAcct(String receiptBankAcct) {
        this.receiptBankAcct = receiptBankAcct;
    }

    public BigDecimal getTotalCashAmount() {
        return totalCashAmount;
    }

    public void setTotalCashAmount(BigDecimal totalCashAmount) {
        this.totalCashAmount = totalCashAmount;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    /**
     * @return 主键id
     */
    public String getDepositId() {
        return depositId;
    }

    public void setDepositId(String depositId) {
        this.depositId = depositId;
    }

    /**
     * @return 存款单号
     */
    public String getDepositSlipNo() {
        return depositSlipNo;
    }

    public void setDepositSlipNo(String depositSlipNo) {
        this.depositSlipNo = depositSlipNo;
    }

    /**
     * @return 状态
     */
    public String getDepositStatus() {
        return depositStatus;
    }

    public void setDepositStatus(String depositStatus) {
        this.depositStatus = depositStatus;
    }

    /**
     * @return 存款金额
     */
    public BigDecimal getPaidInAmount() {
        return paidInAmount;
    }

    public void setPaidInAmount(BigDecimal paidInAmount) {
        this.paidInAmount = paidInAmount;
    }

    /**
     * @return 收款单数量
     */
    public String getCollectionDocQty() {
        return collectionDocQty;
    }

    public void setCollectionDocQty(String collectionDocQty) {
        this.collectionDocQty = collectionDocQty;
    }

    /**
     * @return 交易单号
     */
    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    /**
     * @return 存款时间
     */
    public Date getDepositTime(){
        if(depositTime!=null){
            return (Date)depositTime.clone();
        }else{
            return null;
        }
    }

    public void setDepositTime(Date depositTime) {
        if(depositTime==null){
            this.depositTime = null;
        }else{
            this.depositTime = (Date)depositTime.clone();
        }
    }

    /**
     * @return 存款人
     */
    public String getDepositor() {
        return depositor;
    }

    public void setDepositor(String depositor) {
        this.depositor = depositor;
    }

    /**
     * @return 凭证上传状态--无用
     */
    public String getVoucherUploadStatus() {
        return voucherUploadStatus;
    }

    public void setVoucherUploadStatus(String voucherUploadStatus) {
        this.voucherUploadStatus = voucherUploadStatus;
    }

    /**
     * @return 银行凭证
     */
    public String getBankVoucher() {
        return bankVoucher;
    }

    public void setBankVoucher(String bankVoucher) {
        this.bankVoucher = bankVoucher;
    }

    /**
     * @return 上传人id
     */
    public String getUploaderId() {
        return uploaderId;
    }

    public void setUploaderId(String uploaderId) {
        this.uploaderId = uploaderId;
    }

    /**
     * @return 上传人name
     */
    public String getUploaderName() {
        return uploaderName;
    }

    public void setUploaderName(String uploaderName) {
        this.uploaderName = uploaderName;
    }

    /**
     * @return 上传时间
     */
    public Date getUploaderDate(){
        if(uploaderDate!=null){
            return (Date)uploaderDate.clone();
        }else{
            return null;
        }
    }

    public void setUploaderDate(Date uploaderDate) {
        if(uploaderDate==null){
            this.uploaderDate = null;
        }else{
            this.uploaderDate = (Date)uploaderDate.clone();
        }
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmDepositSlipEntity{" +
            "depositId=" + depositId +
            ", depositSlipNo=" + depositSlipNo +
            ", depositStatus=" + depositStatus +
            ", paidInAmount=" + paidInAmount +
            ", collectionDocQty=" + collectionDocQty +
            ", transactionNo=" + transactionNo +
            ", depositTime=" + depositTime +
            ", depositor=" + depositor +
            ", voucherUploadStatus=" + voucherUploadStatus +
            ", bankVoucher=" + bankVoucher +
            ", uploaderId=" + uploaderId +
            ", uploaderName=" + uploaderName +
            ", uploaderDate=" + uploaderDate +
            ", delFlag=" + delFlag +
        "}";
    }
}