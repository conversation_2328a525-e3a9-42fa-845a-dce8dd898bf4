package com.bonc.ioc.bzf.business.payment.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 站内信或者是短信推送的具体内容-本系统接口请求用的参数
 *
 * <AUTHOR>
 * @date 2022-06-10 15:22
 * @change 2022-06-10 15:22 by sqj for init
 */
@Data
public class MessageInterfaceParamContentVo {

    @ApiModelProperty(value = "手机号-必填", required = true)
    private String phone;

    @ApiModelProperty(value = "消息模板里面参数的内容，key为消息模板参数里面的英文名；例：{'customerName':'张三','dataFrom':'2022-01-01 12:00:00'}", required = true)
    private Map<String, Object> paramMap;

    @ApiModelProperty(value = "发送时间，短信实发接口定时发送时间。 格式为 yyyyMMddHHmm，值小于或等于当前时间则立即发送，默认立即发送， 选填")
    private String sendTime;

    @ApiModelProperty(value = "接收人-用户id")
    private String toUser;

    @ApiModelProperty(value = "业务id")
    private String businessId;
}
