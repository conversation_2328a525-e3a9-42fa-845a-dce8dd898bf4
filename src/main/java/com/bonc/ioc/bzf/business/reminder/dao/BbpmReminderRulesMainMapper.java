package com.bonc.ioc.bzf.business.reminder.dao;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesMainEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import java.util.List;

/**
 * 缴费提醒规则--主表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@Mapper
public interface BbpmReminderRulesMainMapper extends McpBaseMapper<BbpmReminderRulesMainEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change 2023-08-04 by binghong.tang for init
     */
    List<BbpmReminderRulesMainPageResultVo> selectByPageCustom(@Param("vo") BbpmReminderRulesMainPageVo vo );


    /**
     * 规则名称查询
     * @param vo
     * @return
     */
    BbpmReminderRulesMainVo findByRuleName(@Param("vo") BbpmReminderRulesMainVo vo );

    /**
     * 连接查询
     * @return
     */
    List<BbpmReminderRulesMainAndSubVo> selectMainAndSubAll();
}
