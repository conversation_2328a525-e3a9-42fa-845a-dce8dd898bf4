package com.bonc.ioc.bzf.business.payment.service.impl;

import com.bonc.ioc.bzf.business.payment.entity.BbpmWithholdRecordEntity;
import com.bonc.ioc.bzf.business.payment.dao.BbpmWithholdRecordMapper;
import com.bonc.ioc.bzf.business.payment.service.IBbpmWithholdRecordService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.payment.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 报盘记录查询 服务类实现
 *
 * <AUTHOR>
 * @date 2024-02-23
 * @change 2024-02-23 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmWithholdRecordServiceImpl extends McpBaseServiceImpl<BbpmWithholdRecordEntity> implements IBbpmWithholdRecordService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmWithholdRecordMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmWithholdRecordService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmWithholdRecordVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmWithholdRecordEntity entity = new BbpmWithholdRecordEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setBatchNo(null);
        if(!baseService.insert(entity)) {
            log.error("报盘记录查询新增失败:" + entity.toString());
            throw new McpException("报盘记录查询新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getBatchNo(),1)) {
                log.error("报盘记录查询新增后保存历史失败:" + entity.toString());
                throw new McpException("报盘记录查询新增后保存历史失败");
            }

            log.debug("报盘记录查询新增成功:"+entity.getBatchNo());
            return entity.getBatchNo();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmWithholdRecordVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmWithholdRecordEntity> entityList = new ArrayList<>();
        for (BbpmWithholdRecordVo item:voList) {
            BbpmWithholdRecordEntity entity = new BbpmWithholdRecordEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmWithholdRecordEntity item:entityList){
            item.setBatchNo(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("报盘记录查询新增失败");
            throw new McpException("报盘记录查询新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmWithholdRecordEntity::getBatchNo).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("报盘记录查询批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("报盘记录查询批量新增后保存历史失败");
            }

            log.debug("报盘记录查询新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param batchNo 需要删除的申请批次
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String batchNo) {
        if(!StringUtils.isEmpty(batchNo)) {
            if(!baseService.saveOperationHisById(batchNo,3)) {
                log.error("报盘记录查询删除后保存历史失败:" + batchNo);
                throw new McpException("报盘记录查询删除后保存历史失败");
            }

            if(!baseService.removeById(batchNo)) {
                log.error("报盘记录查询删除失败");
                throw new McpException("报盘记录查询删除失败"+batchNo);
            }
        } else {
            throw new McpException("报盘记录查询删除失败申请批次为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param batchNoList 需要删除的申请批次
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> batchNoList) {
        if(!CollectionUtils.isEmpty(batchNoList)) {
            int oldSize = batchNoList.size();
            batchNoList = batchNoList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(batchNoList) || oldSize != batchNoList.size()) {
                throw new McpException("报盘记录查询批量删除失败 存在主键id为空的记录"+StringUtils.join(batchNoList));
            }

            if(!baseService.saveOperationHisByIds(batchNoList,3)) {
                log.error("报盘记录查询批量删除后保存历史失败:" + StringUtils.join(batchNoList));
                throw new McpException("报盘记录查询批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(batchNoList)) {
                log.error("报盘记录查询批量删除失败");
                throw new McpException("报盘记录查询批量删除失败"+StringUtils.join(batchNoList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的报盘记录查询
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmWithholdRecordVo vo) {
        if(vo != null) {
            BbpmWithholdRecordEntity entity = new BbpmWithholdRecordEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getBatchNo())) {
                throw new McpException("报盘记录查询更新失败传入申请批次为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("报盘记录查询更新失败");
                throw new McpException("报盘记录查询更新失败"+entity.getBatchNo());
            } else {
                if(!baseService.saveOperationHisById(entity.getBatchNo(),2)) {
                    log.error("报盘记录查询更新后保存历史失败:" + entity.getBatchNo());
                    throw new McpException("报盘记录查询更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("报盘记录查询更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的报盘记录查询
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmWithholdRecordVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmWithholdRecordEntity> entityList = new ArrayList<>();

            for (BbpmWithholdRecordVo item:voList){
                BbpmWithholdRecordEntity entity = new BbpmWithholdRecordEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getBatchNo())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("报盘记录查询批量更新失败 存在申请批次为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("报盘记录查询批量更新失败");
                throw new McpException("报盘记录查询批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getBatchNo())).map(BbpmWithholdRecordEntity::getBatchNo).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("报盘记录查询批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("报盘记录查询批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的报盘记录查询
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmWithholdRecordVo vo) {
        if(vo != null) {
            BbpmWithholdRecordEntity entity = new BbpmWithholdRecordEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("报盘记录查询保存失败");
                throw new McpException("报盘记录查询保存失败"+entity.getBatchNo());
            } else {
                if(!baseService.saveOperationHisById(entity.getBatchNo(),4)) {
                    log.error("报盘记录查询保存后保存历史失败:" + entity.getBatchNo());
                    throw new McpException("报盘记录查询保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("报盘记录查询保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的报盘记录查询
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmWithholdRecordVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmWithholdRecordEntity> entityList = new ArrayList<>();

            for (BbpmWithholdRecordVo item:voList){
                BbpmWithholdRecordEntity entity = new BbpmWithholdRecordEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("报盘记录查询批量保存失败");
                throw new McpException("报盘记录查询批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getBatchNo())).map(BbpmWithholdRecordEntity::getBatchNo).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("报盘记录查询批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("报盘记录查询批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param batchNo 需要查询的申请批次
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmWithholdRecordVo selectByIdRecord(String batchNo) {
        BbpmWithholdRecordVo vo = new BbpmWithholdRecordVo();

        if(!StringUtils.isEmpty(batchNo)) {
            BbpmWithholdRecordEntity entity = baseService.selectById(batchNo);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-02-23
     * @change
     * 2024-02-23 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmWithholdRecordPageResultVo>> selectByPageRecord(BbpmWithholdRecordPageVo vo) {
        List<BbpmWithholdRecordPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
