package com.bonc.ioc.bzf.business.payment.result;

import lombok.Data;

@Data
public class InvoiceIssueResult {
    //交易编号	5	是	fpkj
    private String id;
    //交易描述
    private String comment;
    //返回代码	8	是	0000成功，其它失败
    private String returncode;
    //返回信息
    private String returnmsg;
    //发票代码	12	否
    private String fpDm;
    //发票号码	8	否
    private String fpHm;
    //开票日期	14	否	YYYYMMDDHHMMSS
    private String kprq;
    //发票密文		否
    private String fpMw;
    //校验码
    private String jym;
    //发票pdf文件路径
    private String pdflj;
}
