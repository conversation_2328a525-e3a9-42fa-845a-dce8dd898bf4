package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.bzf.business.payment.result.create.ChargeSubjectParamsRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 商铺信息 vo实体类
 *
 * <AUTHOR>
 * @since 2025/6/27
 */
@Data
public class RoomInfoVo implements java.io.Serializable {

    @ApiModelProperty(value = "商铺ID")
    private String houseId;

    @ApiModelProperty(value = "计费科目列表")
    private List<ChargeSubjectParamsRequest> chargeSubjectList;
}
