package com.bonc.ioc.bzf.business.payment.controller;


import com.bonc.ioc.bzf.business.payment.service.IBbsiRuleInfoService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 签约规则信息表 前端控制器
 *
 * <AUTHOR>
 * @date 2022-11-29
 * @change 2022-11-29 by ly for init
 */
@RestController
@RequestMapping("/v2/business/bbsiRuleInfoEntity")
@Api(tags = "计费规则")
@Validated
public class BbsiRuleInfoBusinessController extends McpBaseController {
    @Resource
    private IBbsiRuleInfoService baseService;


    /**
     *  selectRentRuleList 租金计费规则下拉框
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    @GetMapping(value = "/selectRentRuleList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "ly")
    @ApiOperation(value = "租金计费规则下拉框", notes = "租金计费规则下拉框", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ChargeRuleResultVo>> selectRentRuleList(Map<String, Object> vo){
        AppReply<List<ChargeRuleResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectRentRuleList(vo));
        return appReply;
    }

    /**
     *  selectRentRuleList 租金计费规则下拉框
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    @GetMapping(value = "/selectRentRuleListV2", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "ly")
    @ApiOperation(value = "租金计费规则下拉框V2", notes = "租金计费规则下拉框(chargeSubjectNo、serviceType)", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ChargeRuleResultVo>> selectRentRuleListV2(ChargeRuleParamsVo chargeRuleParamsVo){
        AppReply<List<ChargeRuleResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectRentRuleListV2(chargeRuleParamsVo));
        return appReply;
    }

    /**
     * @return
     * @version 1.0 获取计费结果接口
     * <AUTHOR>
     * @Date 2022/12/9
     */
    @PostMapping(value = "/manualOffer", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "获取计费结果接口", notes = "获取计费结果接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<ChargeRespondVo> manualOffer(@RequestBody ChargeResultParamsVo chargeResultParamsVo){
        AppReply<ChargeRespondVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getChargeResult(chargeResultParamsVo));
        return appReply;
    }


    /**
     *  getBankByPorjectId 根据项目id获取银行
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    @GetMapping(value = "/getBankByPorjectId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "ly")
    @ApiOperation(value = "根据项目id获取银行", notes = "根据项目id获取银行", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ChargeBankResultVo>> getBankByPorjectId(@RequestParam(required = true) String projectId,@RequestParam(required = false) String bankCode,@RequestParam(required = false) String bankAccountNo){
        AppReply<List<ChargeBankResultVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getBankByPorjectId(projectId,bankCode,bankAccountNo));
        return appReply;
    }

    /**
     *  3.42. 分行银行编码以及名称分页查询接口
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    @GetMapping(value = "/getListBankBranchCode", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "ly")
    @ApiOperation(value = "根据项目id获取银行", notes = "根据项目id获取银行", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult> getListBankBranchCode(@Validated(UpdateValidatorGroup.class) BankBranchCodeParamsVo bankBranchCodeParamsVo){
        AppReply<PageResult> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getListBankBranchCode(bankBranchCodeParamsVo));
        return appReply;
    }


    /**
     *  getBankByPorjectIdV2 3.44根据项目查询保障房收款开户行
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-12
     * @change
     * 2022-12-12 by ly for init
     */
    @GetMapping(value = "/getBankByPorjectIdV2", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "ly")
    @ApiOperation(value = "3.44根据项目查询保障房收款开户行", notes = "3.44根据项目查询保障房收款开户行", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<ChargeBankResultV2Vo>> getBankByPorjectIdV2(@RequestParam(required = true) String projectId){
        AppReply<List<ChargeBankResultV2Vo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getBankByPorjectIdV2(projectId));
        return appReply;
    }


    /**
     * @return
     * @version 1.0 获取计费结果接口
     * <AUTHOR>
     * @Date 2022/12/9
     */
    @PostMapping(value = "/getTaxRateList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "3.38查询税率配置列表", notes = "3.38查询税率配置列表", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<TaxRateResultVo> getTaxRateList(@RequestBody TaxRateVo vo){
        AppReply<TaxRateResultVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.getTaxRateList(vo));
        return appReply;
    }

    @PostMapping(value = "/chargePaymentAdjustSubmit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "3.50提交收款单调整功能接口", notes = "3.50提交收款单调整功能接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> chargePaymentAdjustSubmit(@RequestBody AdjustmentCollectionVo vo){
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.chargePaymentAdjustSubmit(vo));
        return appReply;
    }

}

