package com.bonc.ioc.bzf.business.invoice.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发票开具--开票账单信息列表
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
@ApiModel(value="InvoiceBillVo对象", description="发票开具--开票账单信息列表")
@Data
public class InvoiceBillVo extends McpBaseVo implements Serializable{

    @ApiModelProperty(value = "主账单唯一识别码")
    private String billId;

    @ApiModelProperty(value = "开票金额（单位:元）")
    private BigDecimal invoiceMoney	;

    @ApiModelProperty(value = "合同编号")
    private String contractCode	;

    @ApiModelProperty(value = "房源地址")
    private String houseName;

}
