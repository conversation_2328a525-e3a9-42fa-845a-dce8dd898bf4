package com.bonc.ioc.bzf.business.adjust.entity;

/**
 * 角色 枚举类
 *
 * <AUTHOR>
 * @since 2023/6/1
 */
public enum RoleEnum {

    /**
     * 管理员
     */
    MANAGER("1", "管理员"),

    /**
     * 工作人员
     */
    STAFF("2", "工作人员"),

    /**
     * 客户
     */
    CUSTOMER("3", "客户");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    RoleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    RoleEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        RoleEnum[] enums = values();
        for (RoleEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
