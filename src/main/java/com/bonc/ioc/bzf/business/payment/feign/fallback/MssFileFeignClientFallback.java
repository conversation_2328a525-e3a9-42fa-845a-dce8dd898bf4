package com.bonc.ioc.bzf.business.payment.feign.fallback;

import com.bonc.ioc.bzf.business.payment.feign.feign.MssFileFeignClient;
import com.bonc.ioc.bzf.business.payment.vo.FIleResultVo;
import com.bonc.ioc.common.util.AppReply;

public class MssFileFeignClientFallback implements MssFileFeignClient {
    @Override
    public AppReply<String> downloadFileBase64(Integer id) {
        return null;
    }

    @Override
    public AppReply<FIleResultVo> getFileInfoById(Integer id) {
        return null;
    }
}
