package com.bonc.ioc.bzf.business.payment.controller;


import com.bonc.ioc.bzf.business.payment.service.impl.IRefreshConfigService;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Set;

/**
 * @ClassName CommonController
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2023-04-19 19:09
 **/
@RestController
@RequestMapping("/v2/common")
@Api(tags = "A-刷新nacos配置")
@Validated
public class RefreshConfigController {
    @Resource
    IRefreshConfigService iRefreshConfigService;
    @GetMapping(value = "/refreshConfig", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "tbh")
    @ApiOperation(value = "刷新配置的值", notes = "更改nacos配置后，调用此接口主动刷新配置文件的值")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Set<String>> refreshConfig() {
        AppReply<Set<String>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(iRefreshConfigService.refreshConfig());
        return appReply;
    }

}
