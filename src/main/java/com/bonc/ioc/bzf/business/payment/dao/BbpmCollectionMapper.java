package com.bonc.ioc.bzf.business.payment.dao;

import com.bonc.ioc.bzf.business.payment.entity.BbpmCollectionEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.payment.vo.*;
import java.util.List;

/**
 * 收款表(部分来源业财)v3.0 Mapper 接口
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Mapper
public interface BbpmCollectionMapper extends McpBaseMapper<BbpmCollectionEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change 2022-12-02 by binghong.tang for init
     */
    List<BbpmCollectionPageResultVo> selectByPageCustom(@Param("vo") BbpmCollectionPageVo vo );
    /**
     * 根据存款单id查询收款单列表信息
     * @param depositId
     * @return
     */
    List<BbpmCollectionVo> selectDepositAndCollectionList(@Param("depositId") String depositId);
    /**
     * 根据账单号查询收款单列表信息
     * @param billNo
     * @return
     */
    List<BbpmCollectionVo> selectBillAndCollectionList(@Param("billNo") String billNo);

    /**
     * 未存款--所有的
     * @param vo
     * @return
     */
    List<BbpmCollectionPageResultVo> selectUndepositsList(@Param("vo") BbpmCollectionVo vo );

    BbpmCollectionVo selectCollectionById(@Param("messageId") String messageId);

    /**
     * 根据id更新凭证状态
     * @param id
     * @return
     */
    void updateCertificateStateById(String id);
}
