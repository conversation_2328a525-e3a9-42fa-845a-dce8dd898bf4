package com.bonc.ioc.bzf.business.payment.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.vo.BankBranchCodeResultVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsParamsVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ResponseStatusException;

import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@Component
public class RestTemplateUtil<T> {

    public static RestTemplate restTemplate;

    static {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(60 * 1000);
        requestFactory.setReadTimeout(60 * 1000);
        restTemplate = new RestTemplate(requestFactory);
        restTemplate.getMessageConverters().set(1, (HttpMessageConverter<?>) new StringHttpMessageConverter(StandardCharsets.UTF_8));
    }

    /**
     * 封装的post通用部分
     *
     * @param url    请求路径
     * @param httpParams 参数
     * @return 请求成功返回body, 请求失败抛出异常
     */
    public String post(String url, T httpParams) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity(httpParams, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        if (response.getStatusCode() != HttpStatus.OK) {
            throw new ResponseStatusException(response.getStatusCode(), "请求出错");
        }
        return response.getBody();
    }

    /**
     * 发送GET请求
     * @param url
     * @param param
     * @return
     */
    public String GetData(String url, Map<String,String> param){
        restTemplate=new RestTemplate();
        // 请勿轻易改变此提交方式，大部分的情况下，提交方式都是表单提交
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return restTemplate.getForEntity(url,String.class,param).getBody();
    }
    /**
     * 发送POST-JSON请求
     * @param url
     * @param param
     * @return
     */
    public String PostJsonData(String url, JSONObject param){
        restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity<JSONObject> requestEntity = new HttpEntity<JSONObject>(param, headers);
        return restTemplate.postForEntity(url,requestEntity, String.class).getBody();
    }

    /**
     * 发送POST-JSON请求
     * @param url
     * @param param
     * @return
     */
    public ChargeRespondVo postJsonDataByVo(String url, Object param){
        log.info("RestTemplate postJsonDataByVo 请求参数(" + url + ")：" + param.toString());
        restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity<Object> requestEntity = new HttpEntity<Object>(param, headers);
        return restTemplate.postForEntity(url,requestEntity, ChargeRespondVo.class).getBody();
    }
    public String postJsonBankBranchCodeVo(String url, Object param){
        log.info("RestTemplate BankBranchCodeResultVo 请求参数(" + url + ")：" + param.toString());
        restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity<Object> requestEntity = new HttpEntity<Object>(param, headers);
        return restTemplate.postForEntity(url,requestEntity, String.class).getBody();
    }

    public ChargeRespondVo postJsonStringByVo(String url, String param){
        log.info("RestTemplate postJsonStringByVo 请求参数(" + url + ")：" + param);
        restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity<String> requestEntity = new HttpEntity<String>(param, headers);
        return restTemplate.postForEntity(url,requestEntity, ChargeRespondVo.class).getBody();
    }
    /**
     * 发送POST 表单请求
     * @param url
     * @param param
     * @return
     */
    public String PostFormData(String url, MultiValueMap<String,String> param){
        restTemplate=new RestTemplate();
        // 请勿轻易改变此提交方式，大部分的情况下，提交方式都是表单提交
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return restTemplate.postForEntity(url,param,String.class).getBody();
    }

    public static void main(String arg[]) throws Exception{
        ObjectMapper mapper = new ObjectMapper();
        ParentRequest<PreviewBillsParamsVo> bankRequestVo = new ParentRequest<>();
        RestTemplate restTemplate=new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.ALL.toString());
        HttpEntity<String> requestEntity = new HttpEntity<String>(mapper.writeValueAsString(bankRequestVo), headers);
        Object o = restTemplate.postForEntity("https://mock.apipost.net/mock/33b819/api/charge/v1/bill/business/getPreviewBills?apipost_id=2356c81bfcb007",requestEntity, ChargeRespondVo.class).getBody();
        System.out.println(JSON.toJSON(o).toString());
    }

}

