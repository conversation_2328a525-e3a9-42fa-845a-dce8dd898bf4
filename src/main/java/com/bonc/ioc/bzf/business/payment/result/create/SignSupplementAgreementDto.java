package com.bonc.ioc.bzf.business.payment.result.create;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 03 购房签订补充协议(详见signSupplementAgreementDto)
 */
@Data
@Builder
public class SignSupplementAgreementDto {
    private String beforeContractCode;//	原趸租管理协议号/散租合同号	String
    private String agreementCode;//	购房协议号	String
    private BigDecimal newRentStandard;//	新租金金额
    private String agreementStartTime;//	协议开始时间	String
    private String agreementEndTime;//	协议结束时间	String

}
