package com.bonc.ioc.bzf.business.payment.feign.feign;

import com.bonc.ioc.bzf.business.payment.feign.fallback.BbctCustomerFeignClientFallback;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * @description: 客户中心
 * @author: zhaoweiming
 * @date: 2022-12-12 8:55
 * @param:
 * @return:
 * @since 1.0.0
 **/
@FeignClient(contextId = "customer" , value = "bzf-business-customer",fallback = BbctCustomerFeignClientFallback.class,
        configuration = FeignExceptionConfiguration.class)
public interface BbctCustomerFeignClient {

    /**
     * 通过用户ID查询客户ID
     * @param userId
     * @return
     */
    @PostMapping(value = "/bzf-business-customer/bbcCustomerUserRelEntity/getCustomerIdByUserId")
    @FeignExceptionCheck
    AppReply<String> getCustomerIdByUserId(@RequestParam("userId") String userId);


    /**
     * 获取部门名称
     *
     * @param cid      个人客户ID
     * @param oid      机构客户ID
     * @return AppReply<String> 返回封装的部门名称
     */
    @GetMapping("/bzf-business-customer/v2/atomic/org/employees/{cid}/{oid}/department/name")
    @FeignExceptionCheck
    @LogPoint(system = "bzf-business-customer")
    AppReply<String> getDepartmentName(@PathVariable("cid") String cid, @PathVariable("oid") String oid);
}
