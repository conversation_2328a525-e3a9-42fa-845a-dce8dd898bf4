package com.bonc.ioc.bzf.business.reminder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 缴费提醒规则--主表 实体类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
@TableName("bbpm_reminder_rules_main")
@ApiModel(value="BbpmReminderRulesMainEntity对象", description="缴费提醒规则--主表")
public class BbpmReminderRulesMainEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_RULES_ID = "rules_id";
    public static final String FIELD_RULE_NAME = "rule_name";
    public static final String FIELD_CONTRACT_TYPE = "contract_type";
    public static final String FIELD_BUSINESS_TYPE = "business_type";
    public static final String FIELD_ENABLE_STATUS = "enable_status";
    public static final String FIELD_ACTIVATION_TIME = "activation_time";
    public static final String FIELD_DOWN_TIME = "down_time";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 规则编号
     */
    @ApiModelProperty(value = "规则编号")
                                @TableId(value = "rules_id", type = IdType.ASSIGN_UUID)
                                  private String rulesId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
                            private String ruleName;

    /**
     * 合同类型:01散租合同,02趸租合同,03趸租管理协议
     */
    @ApiModelProperty(value = "合同类型:01散租合同,02趸租合同,03趸租管理协议")
                            private String contractType;

    /**
     * 业务类型:01公租房,02保租房
     */
    @ApiModelProperty(value = "业务类型:01公租房,02保租房")
                            private String businessType;

    /**
     * 启用状态:01停用,02启用
     */
    @ApiModelProperty(value = "启用状态:01停用,02启用")
//    @TableField("enable_status")
                            private String enableStatus;

    /**
     * 启用时间
     */
    @ApiModelProperty(value = "启用时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date activationTime;

    /**
     * 停用时间
     */
    @ApiModelProperty(value = "停用时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date downTime;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 规则编号
     */
    public String getRulesId() {
        return rulesId;
    }

    public void setRulesId(String rulesId) {
        this.rulesId = rulesId;
    }

    /**
     * @return 规则名称
     */
    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    /**
     * @return 合同类型:01散租合同,02趸租合同,03趸租管理协议
     */
    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    /**
     * @return 业务类型:01公租房,02保租房
     */
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * @return 启用状态:01停用,02启用
     */
    public String getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(String enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return 启用时间
     */
    public Date getActivationTime(){
        if(activationTime!=null){
            return (Date)activationTime.clone();
        }else{
            return null;
        }
    }

    public void setActivationTime(Date activationTime) {
        if(activationTime==null){
            this.activationTime = null;
        }else{
            this.activationTime = (Date)activationTime.clone();
        }
    }

    /**
     * @return 停用时间
     */
    public Date getDownTime(){
        if(downTime!=null){
            return (Date)downTime.clone();
        }else{
            return null;
        }
    }

    public void setDownTime(Date downTime) {
        if(downTime==null){
            this.downTime = null;
        }else{
            this.downTime = (Date)downTime.clone();
        }
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmReminderRulesMainEntity{" +
            "rulesId=" + rulesId +
            ", ruleName=" + ruleName +
            ", contractType=" + contractType +
            ", businessType=" + businessType +
            ", enableStatus=" + enableStatus +
            ", activationTime=" + activationTime +
            ", downTime=" + downTime +
            ", delFlag=" + delFlag +
        "}";
    }
}