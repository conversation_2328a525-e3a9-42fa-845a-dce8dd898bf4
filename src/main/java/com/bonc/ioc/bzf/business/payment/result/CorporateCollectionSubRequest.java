package com.bonc.ioc.bzf.business.payment.result;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 3.23  趸租对公收款接口：线下转账和支票收款请求 子类
 */
@Data
@Builder
public class CorporateCollectionSubRequest {
    //	租户ID	String	是	20
    private String tenantId;
    //	合同ID	String	是	20
    private String contractId;
    //	账单ID	String	是	20
    private String billId;
    // 账单金额 BigDecimal	是	13.4
    private BigDecimal billAmout;


    //2024年12月冲刺增加字段
    private String billChargeSubject;//	账单的计费科目
    private String chargeSubjectPeriod;//	账单的期次
    private BigDecimal billPayableAmount;//	账单待缴金额

}
