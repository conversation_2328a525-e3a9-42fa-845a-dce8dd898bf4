package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 *下载实体
 * <AUTHOR>
 * @date 2023-10-20
 * @change 2023-10-20 by binghong.tang for init
 */
@ApiModel(value="ExcelDownloadV2Vo", description="下载实体")
@Data
public class ExcelDownloadV2Vo extends McpBaseVo implements Serializable{

    @ApiModelProperty(value = "申请批次")
    private String batchNo;
    @ApiModelProperty(value = "承租人")
    private String tenantName;
    @ApiModelProperty(value = "证件号码")
    private String certNo;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "房源地址")
    private String houseName;


}
