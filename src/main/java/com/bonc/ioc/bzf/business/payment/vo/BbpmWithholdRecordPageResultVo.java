package com.bonc.ioc.bzf.business.payment.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 报盘记录查询 实体类
 *
 * <AUTHOR>
 * @date 2024-02-23
 * @change 2024-02-23 by binghong.tang for init
 */
@ApiModel(value="BbpmWithholdRecordPageResultVo对象", description="报盘记录查询")
public class BbpmWithholdRecordPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 申请批次
     */
    @ApiModelProperty(value = "申请批次")
    @NotBlank(message = "申请批次不能为空",groups = {UpdateValidatorGroup.class})
                                  private String batchNo;

    /**
     * 运营项目名称
     */
    @ApiModelProperty(value = "运营项目名称")
                            private String projectName;

    /**
     * 报盘人数
     */
    @ApiModelProperty(value = "报盘人数")
                            private Long tenantNum;

    /**
     * 账单共计条数
     */
    @ApiModelProperty(value = "账单共计条数")
                            private Long billTotalNum;

    /**
     * 成功账单
     */
    @ApiModelProperty(value = "成功账单")
                            private Long billSuccessNum;

    /**
     * 失败账单
     */
    @ApiModelProperty(value = "失败账单")
//                            private Long billFailedNum ---> billFailureNum;
    private Long billFailureNum;

    /**
     * 支付中账单
     */
    @ApiModelProperty(value = "支付中账单")
//                            private Long billDeaingNum ---> billDealingNum;
    private Long billDealingNum;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
                            private String applicant;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
                            private String applyTime;

    @ApiModelProperty(value = "项目ID")
    private String projectIds;
    @ApiModelProperty(value = "申请起始日期")
    private String applyStartTime;
    @ApiModelProperty(value = "申请截至日期")
    private String applyEndTime;


    @ApiModelProperty(value = "业务类型01：按运营项目报盘02：按租户报盘")
    @McpDictPoint(dictCode = "WITHHOLD_BIZTYPE", overTransCopyTo = "bizTypeName")
    private String bizType;
    @ApiModelProperty(value = "业务类型名称")
    private String bizTypeName;
    @ApiModelProperty(value = "代扣标识01：当前账期代扣02：跨账期代扣")
    @McpDictPoint(dictCode = "WITHHOLD_WITHHOLDFLAG", overTransCopyTo = "withholdFlagName")
    private String withholdFlag;
    @ApiModelProperty(value = "代扣标识名称")
    private String withholdFlagName;
    @ApiModelProperty(value = "代扣模式01：手动报盘02：自动报盘")
    @McpDictPoint(dictCode = "WITHHOLD_WITHHOLDMODE", overTransCopyTo = "withholdModeName")
    private String withholdMode;
    @ApiModelProperty(value = "代扣模式名称")
    private String withholdModeName;

    public String getBizTypeName() {
        return bizTypeName;
    }

    public void setBizTypeName(String bizTypeName) {
        this.bizTypeName = bizTypeName;
    }

    public String getWithholdFlagName() {
        return withholdFlagName;
    }

    public void setWithholdFlagName(String withholdFlagName) {
        this.withholdFlagName = withholdFlagName;
    }

    public String getWithholdModeName() {
        return withholdModeName;
    }

    public void setWithholdModeName(String withholdModeName) {
        this.withholdModeName = withholdModeName;
    }

    public String getWithholdFlag() {
        return withholdFlag;
    }

    public void setWithholdFlag(String withholdFlag) {
        this.withholdFlag = withholdFlag;
    }

    public String getWithholdMode() {
        return withholdMode;
    }

    public void setWithholdMode(String withholdMode) {
        this.withholdMode = withholdMode;
    }

    public String getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(String projectIds) {
        this.projectIds = projectIds;
    }

    public String getApplyStartTime() {
        return applyStartTime;
    }

    public void setApplyStartTime(String applyStartTime) {
        this.applyStartTime = applyStartTime;
    }

    public String getApplyEndTime() {
        return applyEndTime;
    }

    public void setApplyEndTime(String applyEndTime) {
        this.applyEndTime = applyEndTime;
    }

    /**
     * @return 申请批次
     */
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * @return 运营项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 报盘人数
     */
    public Long getTenantNum() {
        return tenantNum;
    }

    public void setTenantNum(Long tenantNum) {
        this.tenantNum = tenantNum;
    }

    /**
     * @return 账单共计条数
     */
    public Long getBillTotalNum() {
        return billTotalNum;
    }

    public void setBillTotalNum(Long billTotalNum) {
        this.billTotalNum = billTotalNum;
    }

    /**
     * @return 成功账单
     */
    public Long getBillSuccessNum() {
        return billSuccessNum;
    }

    public void setBillSuccessNum(Long billSuccessNum) {
        this.billSuccessNum = billSuccessNum;
    }


    public Long getBillFailureNum() {
        return billFailureNum;
    }

    public void setBillFailureNum(Long billFailureNum) {
        this.billFailureNum = billFailureNum;
    }

    public Long getBillDealingNum() {
        return billDealingNum;
    }

    public void setBillDealingNum(Long billDealingNum) {
        this.billDealingNum = billDealingNum;
    }

    /**
     * @return 申请人
     */
    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    /**
     * @return 申请时间
     */
    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    /**
     * @return 业务类型
     */
    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

      @Override
    public String toString() {
        return "BbpmWithholdRecordPageResultVo{" +
            "batchNo=" + batchNo +
            ", projectName=" + projectName +
            ", tenantNum=" + tenantNum +
            ", billTotalNum=" + billTotalNum +
            ", billSuccessNum=" + billSuccessNum +

            ", applicant=" + applicant +
            ", applyTime=" + applyTime +
            ", bizType=" + bizType +
        "}";
    }
}
