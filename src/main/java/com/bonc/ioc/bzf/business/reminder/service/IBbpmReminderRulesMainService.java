package com.bonc.ioc.bzf.business.reminder.service;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmReminderRulesMainEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 缴费提醒规则--主表 服务类
 *
 * <AUTHOR>
 * @date 2023-08-04
 * @change 2023-08-04 by binghong.tang for init
 */
public interface IBbpmReminderRulesMainService extends IMcpBaseService<BbpmReminderRulesMainEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    String insertRecord(BbpmReminderRulesMainVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmReminderRulesMainVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param rulesId 需要删除的规则编号
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void removeByIdRecord(String rulesId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rulesIdList 需要删除的规则编号
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> rulesIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的缴费提醒规则--主表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void updateByIdRecord(BbpmReminderRulesMainVo vo);

    /**
     * 启用停用
     * @param vo
     */
    void enableDeactivate(BbpmReminderRulesMainVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的缴费提醒规则--主表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmReminderRulesMainVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的缴费提醒规则--主表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void saveByIdRecord(BbpmReminderRulesMainVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的缴费提醒规则--主表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmReminderRulesMainVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param rulesId 需要查询的规则编号
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    BbpmReminderRulesMainVo selectByIdRecord(String rulesId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-04
     * @change
     * 2023-08-04 by binghong.tang for init
     */
    PageResult<List<BbpmReminderRulesMainPageResultVo>> selectByPageRecord(BbpmReminderRulesMainPageVo vo);


    List<UnpaidBillMainVo> listByPayableDate(ListByPayableDateVo vo);
}
