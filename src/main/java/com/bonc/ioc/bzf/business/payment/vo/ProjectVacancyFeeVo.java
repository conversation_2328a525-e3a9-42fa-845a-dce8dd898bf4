package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 项目信息
 */
@Data
public class ProjectVacancyFeeVo {
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "房屋列表")
    private List<RoomVacancyFeeVo> roomList;
}
