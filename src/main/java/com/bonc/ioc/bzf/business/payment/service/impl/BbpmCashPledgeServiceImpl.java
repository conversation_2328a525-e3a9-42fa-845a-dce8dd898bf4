package com.bonc.ioc.bzf.business.payment.service.impl;

import com.bonc.ioc.bzf.business.payment.entity.BbpmCashPledgeEntity;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCashPledgeMapper;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCashPledgeService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.payment.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 押金条 服务类实现
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmCashPledgeServiceImpl extends McpBaseServiceImpl<BbpmCashPledgeEntity> implements IBbpmCashPledgeService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmCashPledgeMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmCashPledgeService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmCashPledgeVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmCashPledgeEntity entity = new BbpmCashPledgeEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setCashPledgeId(null);
        if(!baseService.insert(entity)) {
            log.error("押金条新增失败:" + entity.toString());
            throw new McpException("押金条新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getCashPledgeId(),1)) {
                log.error("押金条新增后保存历史失败:" + entity.toString());
                throw new McpException("押金条新增后保存历史失败");
            }

            log.debug("押金条新增成功:"+entity.getCashPledgeId());
            return entity.getCashPledgeId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmCashPledgeVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmCashPledgeEntity> entityList = new ArrayList<>();
        for (BbpmCashPledgeVo item:voList) {
            BbpmCashPledgeEntity entity = new BbpmCashPledgeEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmCashPledgeEntity item:entityList){
            item.setCashPledgeId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("押金条新增失败");
            throw new McpException("押金条新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmCashPledgeEntity::getCashPledgeId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("押金条批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("押金条批量新增后保存历史失败");
            }

            log.debug("押金条新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param cashPledgeId 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String cashPledgeId) {
        if(!StringUtils.isEmpty(cashPledgeId)) {
            if(!baseService.saveOperationHisById(cashPledgeId,3)) {
                log.error("押金条删除后保存历史失败:" + cashPledgeId);
                throw new McpException("押金条删除后保存历史失败");
            }

            if(!baseService.removeById(cashPledgeId)) {
                log.error("押金条删除失败");
                throw new McpException("押金条删除失败"+cashPledgeId);
            }
        } else {
            throw new McpException("押金条删除失败唯一标识符为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param cashPledgeIdList 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> cashPledgeIdList) {
        if(!CollectionUtils.isEmpty(cashPledgeIdList)) {
            int oldSize = cashPledgeIdList.size();
            cashPledgeIdList = cashPledgeIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(cashPledgeIdList) || oldSize != cashPledgeIdList.size()) {
                throw new McpException("押金条批量删除失败 存在主键id为空的记录"+StringUtils.join(cashPledgeIdList));
            }

            if(!baseService.saveOperationHisByIds(cashPledgeIdList,3)) {
                log.error("押金条批量删除后保存历史失败:" + StringUtils.join(cashPledgeIdList));
                throw new McpException("押金条批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(cashPledgeIdList)) {
                log.error("押金条批量删除失败");
                throw new McpException("押金条批量删除失败"+StringUtils.join(cashPledgeIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的押金条
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmCashPledgeVo vo) {
        if(vo != null) {
            BbpmCashPledgeEntity entity = new BbpmCashPledgeEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getCashPledgeId())) {
                throw new McpException("押金条更新失败传入唯一标识符为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("押金条更新失败");
                throw new McpException("押金条更新失败"+entity.getCashPledgeId());
            } else {
                if(!baseService.saveOperationHisById(entity.getCashPledgeId(),2)) {
                    log.error("押金条更新后保存历史失败:" + entity.getCashPledgeId());
                    throw new McpException("押金条更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("押金条更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的押金条
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmCashPledgeVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmCashPledgeEntity> entityList = new ArrayList<>();

            for (BbpmCashPledgeVo item:voList){
                BbpmCashPledgeEntity entity = new BbpmCashPledgeEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getCashPledgeId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("押金条批量更新失败 存在唯一标识符为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("押金条批量更新失败");
                throw new McpException("押金条批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getCashPledgeId())).map(BbpmCashPledgeEntity::getCashPledgeId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("押金条批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("押金条批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的押金条
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmCashPledgeVo vo) {
        if(vo != null) {
            BbpmCashPledgeEntity entity = new BbpmCashPledgeEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("押金条保存失败");
                throw new McpException("押金条保存失败"+entity.getCashPledgeId());
            } else {
                if(!baseService.saveOperationHisById(entity.getCashPledgeId(),4)) {
                    log.error("押金条保存后保存历史失败:" + entity.getCashPledgeId());
                    throw new McpException("押金条保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("押金条保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的押金条
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmCashPledgeVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmCashPledgeEntity> entityList = new ArrayList<>();

            for (BbpmCashPledgeVo item:voList){
                BbpmCashPledgeEntity entity = new BbpmCashPledgeEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("押金条批量保存失败");
                throw new McpException("押金条批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getCashPledgeId())).map(BbpmCashPledgeEntity::getCashPledgeId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("押金条批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("押金条批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param cashPledgeId 需要查询的唯一标识符
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmCashPledgeVo selectByIdRecord(String cashPledgeId) {
        BbpmCashPledgeVo vo = new BbpmCashPledgeVo();

        if(!StringUtils.isEmpty(cashPledgeId)) {
            BbpmCashPledgeEntity entity = baseService.selectById(cashPledgeId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmCashPledgePageResultVo>> selectByPageRecord(BbpmCashPledgePageVo vo) {
        List<BbpmCashPledgePageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据条件查询 不分页
     * @param vo
     * @return
     */
    @Override
    public BbpmCashPledgeVo selectByConditions(BbpmCashPledgeVo vo) {
        List<BbpmCashPledgeVo> result = baseMapper.selectByConditions(vo);
        return result!=null && result.size() > 0 ? result.get(0) : null;
    }

    /**
     * 根据条件查询 不分页
     * @param vo
     * @return
     */
    @Override
    public List<BbpmCashPledgeVo> selectByList(BbpmCashPledgeVo vo) {
        List<BbpmCashPledgeVo> result = baseMapper.selectByConditions(vo);
        return result;
    }
}
