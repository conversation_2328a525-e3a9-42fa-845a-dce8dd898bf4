package com.bonc.ioc.bzf.business.payment.service.impl;

import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeePageVo;
import com.bonc.ioc.bzf.business.payment.dao.BbpmPayeeMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmPayeeEntity;
import com.bonc.ioc.bzf.business.payment.service.IBbpmPayeeService;
import com.bonc.ioc.bzf.business.payment.vo.BbpmPayeeVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * pos机  收款回传
 *
 * <AUTHOR>
 * @date 2023/1/3 11:11
 * @change 2023/1/3 11:11 by liujian for init
 */
@Slf4j
@Service
public class BbpmPayeeServiceImpl extends McpBaseServiceImpl<BbpmPayeeEntity> implements IBbpmPayeeService{
    @Resource
    private BbpmPayeeMapper bbpmPayeeMapper;

    @Override
    public Boolean savePayee(BbpmPayeeVo bbpmPayeeVo) {
        if (bbpmPayeeVo==null){
            throw new McpException("未接收到数据");
        }
        if (bbpmPayeeMapper.insertPayee(bbpmPayeeVo)<=0){
            log.error("收款回传失败");
            throw new McpException("收款回传失败");
        }
        return true;
    }


    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-01-24
     * @change
     * 2024-01-24 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmPayeePageResultVo>> selectByPageRecord(BbpmPayeePageVo vo) {
        List<BbpmPayeePageResultVo> result = bbpmPayeeMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
