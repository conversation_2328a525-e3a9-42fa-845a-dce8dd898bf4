package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 白名单 实体类
 *
 * <AUTHOR>
 * @date 2023-01-03
 * @change 2023-01-03 by xuduo for init
 */
@ApiModel(value="WhiteListVo对象", description="白名单")
public class WhiteListVo extends McpBaseVo implements Serializable{

    @ApiModelProperty(value = "个人客户主键")
    private String personCustomerId;

    @ApiModelProperty(value = "个人客户拓展信息主键")
    private String personCustomerExtendId;

    @ApiModelProperty(value = "主申请人姓名")
    private String name;

    @ApiModelProperty(value = "公租备案号")
    private String publicRentalFilingNum;

    @ApiModelProperty(value = "性别编码")
    private String genderCode;

    @ApiModelProperty(value = "性别名称")
    private String genderCodeName;

    @ApiModelProperty(value = "民族编码")
    private String nationCode;

    @ApiModelProperty(value = "民族名称")
    private String nationName;

    @ApiModelProperty(value = "证件类型编码")
    private String certificateTypeCode;

    @ApiModelProperty(value = "证件类型名称")
    private String certificateTypeName;

    @ApiModelProperty(value = "证件号码")
    private String certificateNum;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "配租状态编码")
    private String rentStatusCode;

    @ApiModelProperty(value = "配租状态名称")
    private String rentStatusName;

    @ApiModelProperty(value = "是否白名单编码--0:否 1:是")
    private String isWhiteList;

    @ApiModelProperty(value = "是否白名单名称")
    private String isWhiteListName;

    @ApiModelProperty(value = "公租补贴比例")
    private Double publicRentalSubsidyRatio;

    @ApiModelProperty(value = "修改时间")
    private String modifyTime;

    @ApiModelProperty(value = "修改人")
    private String modifyUser;

    @ApiModelProperty(value = "在租房源")
    private String atRentalSource;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "操作类型01新增,02删除,03更新")
    private String operateType;

    @ApiModelProperty(value = "实际补贴比例")
    private String realityRentalSubsidyRatio;

    public String getRealityRentalSubsidyRatio() {
        return realityRentalSubsidyRatio;
    }

    public void setRealityRentalSubsidyRatio(String realityRentalSubsidyRatio) {
        this.realityRentalSubsidyRatio = realityRentalSubsidyRatio;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getAtRentalSource() {
        return atRentalSource;
    }

    public void setAtRentalSource(String atRentalSource) {
        this.atRentalSource = atRentalSource;
    }

    public String getPersonCustomerId() {
        return personCustomerId;
    }

    public void setPersonCustomerId(String personCustomerId) {
        this.personCustomerId = personCustomerId;
    }

    public String getPersonCustomerExtendId() {
        return personCustomerExtendId;
    }

    public void setPersonCustomerExtendId(String personCustomerExtendId) {
        this.personCustomerExtendId = personCustomerExtendId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPublicRentalFilingNum() {
        return publicRentalFilingNum;
    }

    public void setPublicRentalFilingNum(String publicRentalFilingNum) {
        this.publicRentalFilingNum = publicRentalFilingNum;
    }

    public String getGenderCode() {
        return genderCode;
    }

    public void setGenderCode(String genderCode) {
        this.genderCode = genderCode;
    }

    public String getGenderCodeName() {
        return genderCodeName;
    }

    public void setGenderCodeName(String genderCodeName) {
        this.genderCodeName = genderCodeName;
    }

    public String getNationCode() {
        return nationCode;
    }

    public void setNationCode(String nationCode) {
        this.nationCode = nationCode;
    }

    public String getNationName() {
        return nationName;
    }

    public void setNationName(String nationName) {
        this.nationName = nationName;
    }

    public String getCertificateTypeCode() {
        return certificateTypeCode;
    }

    public void setCertificateTypeCode(String certificateTypeCode) {
        this.certificateTypeCode = certificateTypeCode;
    }

    public String getCertificateTypeName() {
        return certificateTypeName;
    }

    public void setCertificateTypeName(String certificateTypeName) {
        this.certificateTypeName = certificateTypeName;
    }

    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRentStatusCode() {
        return rentStatusCode;
    }

    public void setRentStatusCode(String rentStatusCode) {
        this.rentStatusCode = rentStatusCode;
    }

    public String getRentStatusName() {
        return rentStatusName;
    }

    public void setRentStatusName(String rentStatusName) {
        this.rentStatusName = rentStatusName;
    }

    public String getIsWhiteList() {
        return isWhiteList;
    }

    public void setIsWhiteList(String isWhiteList) {
        this.isWhiteList = isWhiteList;
    }

    public String getIsWhiteListName() {
        return isWhiteListName;
    }

    public void setIsWhiteListName(String isWhiteListName) {
        this.isWhiteListName = isWhiteListName;
    }

    public Double getPublicRentalSubsidyRatio() {
        return publicRentalSubsidyRatio;
    }

    public void setPublicRentalSubsidyRatio(Double publicRentalSubsidyRatio) {
        this.publicRentalSubsidyRatio = publicRentalSubsidyRatio;
    }
}
