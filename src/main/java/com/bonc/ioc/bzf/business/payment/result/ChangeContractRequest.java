package com.bonc.ioc.bzf.business.payment.result;

import com.bonc.ioc.bzf.business.adjust.vo.ChargeableAdjustDTO;
import com.bonc.ioc.bzf.business.payment.vo.ContractChangePeriodDTO;
import com.bonc.ioc.bzf.business.payment.result.create.ContractChangeRentAreaDto;
import com.bonc.ioc.bzf.business.payment.result.create.ReletTenantChangeDto;
import com.bonc.ioc.bzf.business.payment.result.create.SignSupplementAgreementDto;
import com.bonc.ioc.bzf.business.payment.result.create.TenantChangeDto;
import lombok.Builder;
import lombok.Data;

/**
 * 合同变更 主类
 */
@Data
@Builder
public class ChangeContractRequest {

    private String changeType;//	变更类型	String	10 02变更非身份证号的基础信息(现在只有02这一种类型)
    private String projectId;//	    项目id 	String	64

    private BaseInfoChangeDtoRequest baseInfoChangeDto;	//类型02散租变更非身份证号的基础信息实体

    private TenantChangeDto tenantChangeDto;//01承租人变更,备案家庭不变--散租

    private SignSupplementAgreementDto signSupplementAgreementDTO;//03 购房签订补充协议

    private ReletTenantChangeDto reletTenantChangeDto;//01承租人变更 --趸租

    private ContractChangeRentAreaDto contractChangeRentAreaDTO;//07租金标准/面积变更

    private ChargeableAdjustDTO chargeableAdjustDTO; //应收调整

    private ContractChangePeriodDTO contractChangePeriodDTO; //缴费周期变更
}
