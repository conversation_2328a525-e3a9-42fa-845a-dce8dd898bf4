package com.bonc.ioc.bzf.business.invoice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.invoice.dao.BbpmInvoiceHeaderMapper;
import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceHeaderEntity;
import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceQueryEntity;
import com.bonc.ioc.bzf.business.invoice.enums.InvoiceEnums;
import com.bonc.ioc.bzf.business.invoice.service.IBbpmInvoiceHeaderService;
import com.bonc.ioc.bzf.business.invoice.service.IBbpmInvoiceIssueService;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCashCollectionVoucherMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmCashCollectionVoucherEntity;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BbsigningFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCashCollectionVoucherService;
import com.bonc.ioc.bzf.business.payment.utils.DateComparisonUtils;
import com.bonc.ioc.bzf.business.payment.utils.RedisMultiLock;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmBillManagementVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCashCollectionVoucherVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.constant.UserInfo;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.utils.CurrentUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserReq;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserResp;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发票开具
 *
 * <AUTHOR>
 * @date 2023-05-05
 * @change 2023-05-05 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmInvoiceIssueServiceImpl extends McpBaseServiceImpl<BbpmInvoiceQueryEntity> implements IBbpmInvoiceIssueService {

    @Resource
    private IBbpmInvoiceIssueService baseService;

    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Autowired
    private RedisMultiLock redisMultiLock;

    @Resource
    private IBbpmBillManagementService iBbpmBillManagementService;

    @Resource
    private BbpmCashCollectionVoucherMapper bbpmCashCollectionVoucherMapper;

    @Resource
    private IBbpmCashCollectionVoucherService iBbpmCashCollectionVoucherService;

    /**
     *  preCheckBill 账单预校验
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    public String preCheckBill(BbpmPreCheckBillVo vo) {
        if (vo == null) {
            return null;
        }
        //账单
        List<InvoiceBillVo> invoiceBillList = vo.getInvoiceBillList();
        if(invoiceBillList == null && invoiceBillList.size() == 0){
            throw new McpException("开票账单信息列表不能为空");
        }
        for(InvoiceBillVo invoiceBillVo : invoiceBillList){
            if(StringUtils.isBlank(invoiceBillVo.getBillId())){
                throw new McpException("billId不能为空");
            }
        }
        List<String> newList = invoiceBillList.stream().map(InvoiceBillVo::getBillId).collect(Collectors.toList());
        log.info("账单预校验=="+newList.toString());
        if(redisMultiLock.multiLock(newList)){
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                ParentRequest<BbpmPreCheckBillVo> parentRequest = new ParentRequest<>();
                parentRequest.setTime(sdf.format(new Date()));
                parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
                parentRequest.setData(vo);
                log.info("账单预校验接口请求参数:" + parentRequest.toString());
                String jsonRequest = JSONObject.toJSONString(parentRequest);
                log.info("账单预校验接口请求参数json:" + jsonRequest);

                String responseBody = "";
                if (yecaiFeign) {
                    responseBody = bfipSettlementFeignClient.preCheckBill(parentRequest);
                } else {
                    responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/einvoice/preCheckBill", parentRequest);
                }
                log.info("调用工银3.19.5账单预校验接口返回结果为:" + responseBody);

                FaceMdMapResultTwo<InvoiceIssueResult> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);

                if(!("00000").equals(faceMdMapResult.getCode())){
                    log.error("调用工银3.19.5账单预校验接口失败:"+responseBody);
                    throw new McpException("*提示:"+faceMdMapResult.getMessage());
                }else{
                    //{ "busiCode": "", "code": "00000", "data": null, "message": "校验成功" }

//                    JSONObject jsonObject = JSON.parseObject(String.valueOf(faceMdMapResult.getData()));
//                    InvoiceIssueResult invoiceIssueResult = jsonObject.toJavaObject(InvoiceIssueResult.class);
//                    if(!("0000").equals(invoiceIssueResult.getReturncode())){
//                        log.error("调用工银3.19.5账单预校验接口失败:"+responseBody);
//                        throw new McpException(invoiceIssueResult.getReturncode()+":"+invoiceIssueResult.getReturnmsg());
//                    }

                    return "true";
                }
            }finally {
                redisMultiLock.unMultiLock(newList);
            }
        }else{
            throw new McpException("账单"+newList.toString()+"正在预校验");
        }
    }

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2023-05-05
     * @change 2023-05-05 by binghong.tang for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String issueInvoice(BbpmInvoiceIssueVo vo) {
        if (vo == null) {
            return null;
        }
        if(StringUtils.isBlank(vo.getOperationType())){
            throw new McpException("operationType不能为空");
        }
        //账单
        List<InvoiceBillVo> invoiceBillList = vo.getInvoiceBillList();
        if(invoiceBillList == null && invoiceBillList.size() == 0){
            throw new McpException("开票账单信息列表不能为空");
        }
        for(InvoiceBillVo invoiceBillVo : invoiceBillList){
            if(StringUtils.isBlank(invoiceBillVo.getBillId())){
                throw new McpException("billId不能为空");
            }
        }
        List<String> newList = invoiceBillList.stream().map(InvoiceBillVo::getBillId).collect(Collectors.toList());
        log.info("账单=="+newList.toString());
        if(redisMultiLock.multiLock(newList)){
            try {
                //合并开票
                if(InvoiceEnums.OPERATIONTYPE_MERGE.getCode().equals(vo.getOperationType())){
                    //判断是否跨合同开票
                    boolean containsDuplicate = true;
                    if(invoiceBillList.size() > 1){
                        long count = invoiceBillList.stream()
                                .map(InvoiceBillVo::getContractCode)
                                .distinct()
                                .count();
                        if(count == 1){
                            //都是一样的合同，去重后长度为1
                            containsDuplicate = true;
                        }else {
                            //不是一样的合同,去重后长度大于1
                            containsDuplicate = false;
                        }
                    }
                    if(containsDuplicate){
                        //没有跨合同 或 只有一个账单时
                        vo.getInvoiceIssue().setPdfFjXqType(InvoiceEnums.DFFJXQTYPE_DEFAULT.getCode());
                    }else{
                        //跨合同时
                        vo.getInvoiceIssue().setPdfFjXqType(InvoiceEnums.PDFFJXQTYPE_DETAIL.getCode());
                        //"houseAddr房源地址"需要处理一下，前端默认取的第一个账单的地址。合并开票要按合同号去重后拼接地址，批量开票需要根据账单里的地址重新set一下
                        List<String> addressList = invoiceBillList.stream()
                                .map(InvoiceBillVo::getHouseName)
                                .distinct().collect(Collectors.toList());
                        if(addressList!=null && addressList.size()>0){
                            vo.getInvoiceIssue().setHouseAddr(addressList.stream().collect(Collectors.joining(", ")));
                        }
                    }

                    String id = issueInvoiceSend(vo);
                    return "开票成功";
                }else if(InvoiceEnums.OPERATIONTYPE_BATCH.getCode().equals(vo.getOperationType())){
                    StringBuffer reason = new StringBuffer();
                    StringBuffer result = new StringBuffer();
                    int successNum = 0;
                    int failNum = 0;
                    //批量开票 循环调用
                    for(InvoiceBillVo invoiceBillVo : invoiceBillList){
                        BbpmInvoiceIssueVo bbpmInvoiceIssueVo = new BbpmInvoiceIssueVo();
                        bbpmInvoiceIssueVo.setProjectId(vo.getProjectId());
                        //单个默认01
                        vo.getInvoiceIssue().setPdfFjXqType(InvoiceEnums.DFFJXQTYPE_DEFAULT.getCode());
                        //"houseAddr房源地址"需要处理一下，前端默认取的第一个账单的地址
                        vo.getInvoiceIssue().setHouseAddr(invoiceBillVo.getHouseName());
                        bbpmInvoiceIssueVo.setInvoiceIssue(vo.getInvoiceIssue());
                        List<InvoiceBillVo> newBillList = new ArrayList<>();
                        newBillList.add(invoiceBillVo);
                        bbpmInvoiceIssueVo.setInvoiceBillList(newBillList);

                        try{
                            String id = issueInvoiceSend(bbpmInvoiceIssueVo);
                            successNum++;
                        }catch (Exception e){
                            e.printStackTrace();
                            failNum++;
                            reason.append(e.getMessage()).append(";");
                        }
                    }
                    result.append("成功").append(successNum).append("条");
                    if(failNum != 0){
                        result.append(",失败").append(failNum).append("条");
                        result.append("失败原因:").append(reason);
                        throw new McpException(result.toString());
                    }

                    return result.toString();
                }else {
                    throw new McpException("operationType(操作类型01:批量 02:合并)不能为空");
                }
            }finally {
                redisMultiLock.unMultiLock(newList);
            }
        }else{

            throw new McpException("账单"+newList.toString()+"正在开票中");
        }
    }

    private String issueInvoiceSend(BbpmInvoiceIssueVo vo){
        //燕保·百湾家园,房屋号：（公）朝阳区百子湾项目009号楼 01单元1308租金期（2023-04-05至2023-07-04） 租金合计：9882.60
        //备注处理 先获取账单，再组装相关信息
        StringBuffer bz = new StringBuffer();
        List<InvoiceBillVo> invoiceBillList = vo.getInvoiceBillList();
        if(invoiceBillList.size()==1){
            BbpmBillManagementVo billManagementVo = new BbpmBillManagementVo();
            billManagementVo.setProjectId(vo.getProjectId());
            billManagementVo.setBillId(invoiceBillList.get(0).getBillId());
            billManagementVo.setChargeOwner(vo.getInvoiceIssue().getTitleType());
            BbpmBillManagementPageResultVo resultVo = iBbpmBillManagementService.selectSingleBill(billManagementVo);
            if(resultVo != null){
                String projectFormatName = "";
                if(PaymentEnums.PROJECTFORMAT_GZ.getCode().equals(resultVo.getProjectFormat())){
                    projectFormatName = "公";
                }else if(PaymentEnums.PROJECTFORMAT_BZ.getCode().equals(resultVo.getProjectFormat())){
                    projectFormatName = "保";
                }else if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(resultVo.getProjectFormat())){
                    projectFormatName = "商";
                }
                bz.append("租金期（").append(resultVo.getChargeSubjectBeginDate()).append("至").append(resultVo.getChargeSubjectEndDate()).append("）");
//                        .append(" 租金合计：").append(invoiceBillList.get(0).getInvoiceMoney()!=null?invoiceBillList.get(0).getInvoiceMoney().setScale(2,BigDecimal.ROUND_HALF_UP):invoiceBillList.get(0).getInvoiceMoney());
            }
        }else{
            BigDecimal invoiceMoneySum = new BigDecimal(0.00);
            List<BbpmBillManagementPageResultVo> billList = new ArrayList<>();
            //循环查询账单列表
            for(InvoiceBillVo invoiceBillVo : invoiceBillList){
                BbpmBillManagementVo billManagementVo = new BbpmBillManagementVo();
                billManagementVo.setProjectId(vo.getProjectId());
                billManagementVo.setBillId(invoiceBillVo.getBillId());
                billManagementVo.setChargeOwner(vo.getInvoiceIssue().getTitleType());
                BbpmBillManagementPageResultVo resultVo = iBbpmBillManagementService.selectSingleBill(billManagementVo);
                billList.add(resultVo);
                invoiceMoneySum = invoiceMoneySum.add(invoiceBillVo.getInvoiceMoney());
            }
            if(billList != null && billList.size() > 0){
                //按照 账单周期 正序排列
                billList = billList.stream().sorted(Comparator.comparing(BbpmBillManagementPageResultVo::getChargeSubjectPeriod)).collect(Collectors.toList());
                BbpmBillManagementPageResultVo maxDateObject = Collections.max(
                        billList,
                        Comparator.comparing(o -> LocalDate.parse(o.getChargeSubjectEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                );

                BbpmBillManagementPageResultVo minDateObject = Collections.min(
                        billList,
                        Comparator.comparing(o -> LocalDate.parse(o.getChargeSubjectBeginDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                );
//                StringBuffer bzAll = new StringBuffer();
//                //分组
//                List<List<BbpmBillManagementPageResultVo>>  groupList = groupConsecutiveNumbers(billList);
//                for(int i=0;i<groupList.size();i++){
//                    List<BbpmBillManagementPageResultVo> bbpmBillManagementPageResultVoList = groupList.get(i);
//                    if(bbpmBillManagementPageResultVoList.size()==1){
//                        bzAll.append(bbpmBillManagementPageResultVoList.get(0).getChargeSubjectBeginDate()).append("至")
//                                .append(bbpmBillManagementPageResultVoList.get(0).getChargeSubjectEndDate()).append("、");
//                    }else{
//                        bzAll.append(bbpmBillManagementPageResultVoList.get(0).getChargeSubjectBeginDate()).append("至")
//                                .append(bbpmBillManagementPageResultVoList.get(bbpmBillManagementPageResultVoList.size()-1).getChargeSubjectEndDate()).append("、");
//                    }
//                }
//                bzAll.deleteCharAt(bzAll.length() - 1);
                bz.append("租金期（").append(minDateObject.getChargeSubjectBeginDate()).append("至").append(maxDateObject.getChargeSubjectEndDate()).append("）");
                BbpmBillManagementPageResultVo resultVo = billList.get(0);
                String projectFormatName = "";
                if(PaymentEnums.PROJECTFORMAT_GZ.getCode().equals(resultVo.getProjectFormat())){
                    projectFormatName = "公";
                }else if(PaymentEnums.PROJECTFORMAT_BZ.getCode().equals(resultVo.getProjectFormat())){
                    projectFormatName = "保";
                }else if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(resultVo.getProjectFormat())){
                    projectFormatName = "商";
                }
//                bz.append(bzAll);
//                        .append(" 租金合计：").append(invoiceMoneySum!=null?invoiceMoneySum.setScale(2,BigDecimal.ROUND_HALF_UP):invoiceMoneySum);
            }
        }

        InvoiceIssueVo oldInvoiceIssue = vo.getInvoiceIssue();
        //为空的转字符串
        InvoiceIssueVo newInvoiceIssue = new InvoiceIssueVo();
        newInvoiceIssue.setKplx(oldInvoiceIssue.getKplx());
        newInvoiceIssue.setFplxdm(oldInvoiceIssue.getFplxdm());
        newInvoiceIssue.setGmfNsrsbh(oldInvoiceIssue.getGmfNsrsbh());
        newInvoiceIssue.setGmfMc(oldInvoiceIssue.getGmfMc());
        newInvoiceIssue.setGmfDz(oldInvoiceIssue.getGmfDz());
        newInvoiceIssue.setGmfDh(oldInvoiceIssue.getGmfDh());
        newInvoiceIssue.setGmfYhzh(oldInvoiceIssue.getGmfYhzh());
        newInvoiceIssue.setGmfYhmc(oldInvoiceIssue.getGmfYhmc());
        newInvoiceIssue.setGmfDzyx(oldInvoiceIssue.getGmfDzyx());
        newInvoiceIssue.setGmfSjh(oldInvoiceIssue.getGmfSjh());
        newInvoiceIssue.setXsfMc(oldInvoiceIssue.getXsfMc());
        newInvoiceIssue.setHouseAddr(oldInvoiceIssue.getHouseAddr());
        newInvoiceIssue.setBz(bz.toString());
        newInvoiceIssue.setTitleType(oldInvoiceIssue.getTitleType());
        newInvoiceIssue.setKprMc(oldInvoiceIssue.getKprMc());
        newInvoiceIssue.setKprDm(oldInvoiceIssue.getKprDm());
        newInvoiceIssue.setTenantCode(oldInvoiceIssue.getTenantCode());
        newInvoiceIssue.setTenantName(oldInvoiceIssue.getTenantName());
        newInvoiceIssue.setAuthorizedAgent(oldInvoiceIssue.getAuthorizedAgent());
        newInvoiceIssue.setAuthorizedAgentMobile(oldInvoiceIssue.getAuthorizedAgentMobile());
        newInvoiceIssue.setResidentialQuartersName(oldInvoiceIssue.getResidentialQuartersName());
        newInvoiceIssue.setProjectFormat(oldInvoiceIssue.getProjectFormat());
        newInvoiceIssue.setYwlbDm(oldInvoiceIssue.getYwlbDm());
        newInvoiceIssue.setSignType(oldInvoiceIssue.getSignType());
        newInvoiceIssue.setPdfFjXqType(oldInvoiceIssue.getPdfFjXqType());
        newInvoiceIssue.setRealEstateAddr(oldInvoiceIssue.getRealEstateAddr());
        newInvoiceIssue.setRealEstateDetailAddr(oldInvoiceIssue.getRealEstateDetailAddr());
        vo.setInvoiceIssue(newInvoiceIssue);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<BbpmInvoiceIssueVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        log.info("调用工银3.19.1发票开具接口请求参数:" + parentRequest.toString());
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setTimeZone(TimeZone.getDefault());
//        String jsonRequest = JSONObject.toJSONString(parentRequest);
        try {
            log.info("调用工银3.19.1发票开具接口请求参数json:" +  objectMapper.writeValueAsString(parentRequest));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        String responseBody = "";
        if (yecaiFeign) {
            responseBody = bfipSettlementFeignClient.issueInvoice(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/einvoice/issueInvoice", parentRequest);
        }
        log.info("调用工银3.19.1发票开具接口返回结果为:" + responseBody);

        FaceMdMapResultTwo<InvoiceIssueResult> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.19.1发票开具接口失败:"+responseBody);
            throw new McpException("*提示:"+faceMdMapResult.getMessage());
        }else{
            JSONObject jsonObject = JSON.parseObject(String.valueOf(faceMdMapResult.getData()));
            InvoiceIssueResult invoiceIssueResult = jsonObject.toJavaObject(InvoiceIssueResult.class);
            if(!("0000").equals(invoiceIssueResult.getReturncode())){
                log.error("调用工银3.19.1发票开具接口失败:"+responseBody);
                throw new McpException("*提示:"+invoiceIssueResult.getReturncode()+":"+invoiceIssueResult.getReturnmsg());
            }

            //根据账单查是否开具凭证，然后作废凭证
            try{
                for(InvoiceBillVo invoiceBillVo : invoiceBillList){
                    BbpmCashCollectionVoucherVo voucherVo = new BbpmCashCollectionVoucherVo();
                    voucherVo.setBillId(invoiceBillVo.getBillId());
                    voucherVo = bbpmCashCollectionVoucherMapper.selectByCollectionNo(voucherVo);
                    if(voucherVo != null && StringUtils.isNotBlank(voucherVo.getVoucherId())){
                        BbpmCashCollectionVoucherEntity upEntity = new BbpmCashCollectionVoucherEntity();
                        upEntity.setVoucherId(voucherVo.getVoucherId());
                        upEntity.setStatus(PaymentEnums.STATUS_VOIDED.getCode());
                        iBbpmCashCollectionVoucherService.updateById(upEntity);
                    }
                }
            }catch (Exception e){
                log.info("发票开具后更新电子凭证表状态失败！！！"+e.getMessage());
                e.printStackTrace();
            }

            return invoiceIssueResult.getId();
        }
    }

    /**
     * 按照周期分组 寻找不连续期次
     * @param bbpmBillManagementPageResultVoList
     * @return
     */
    public static List<List<BbpmBillManagementPageResultVo>> groupConsecutiveNumbers(List<BbpmBillManagementPageResultVo> bbpmBillManagementPageResultVoList) {
        List<List<BbpmBillManagementPageResultVo>> groups = new ArrayList<>();
        List<BbpmBillManagementPageResultVo> currentGroup = new ArrayList<>();
        currentGroup.add(bbpmBillManagementPageResultVoList.get(0));
        for (int i = 1; i < bbpmBillManagementPageResultVoList.size(); i++) {
            int result = bbpmBillManagementPageResultVoList.get(i).getChargeSubjectPeriod().intValue() - bbpmBillManagementPageResultVoList.get(i - 1).getChargeSubjectPeriod().intValue();
            if ( result == 1) {
                currentGroup.add(bbpmBillManagementPageResultVoList.get(i));
            } else {
                groups.add(currentGroup);
                currentGroup = new ArrayList<>();
                currentGroup.add(bbpmBillManagementPageResultVoList.get(i));
            }
        }
        groups.add(currentGroup);
        return groups;
    }

    /**
     * 发票红冲和开具
     * @param vo
     * @return
     */
    @Override
    public String redFlushAndIssue(BbpmInvoiceRedFlushAndIssueVo vo) {
        BbpmInvoiceRedFlushVo bbpmInvoiceRedFlushVo = vo.getBbpmInvoiceRedFlushVo();
        BbpmInvoiceIssueVo bbpmInvoiceIssueVo = vo.getBbpmInvoiceIssueVo();
        if(bbpmInvoiceRedFlushVo == null){
            throw new McpException("发票红冲信息不能为空");
        }
        if(bbpmInvoiceIssueVo == null){
            throw new McpException("发票开具信息不能为空");
        }

        baseService.redFlush(bbpmInvoiceRedFlushVo);

        try{
            baseService.issueInvoice(bbpmInvoiceIssueVo);
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage());
            throw new McpException("发票红冲成功,但发票开具失败("+e.getMessage()+"),请到未开票列表重新申请开票");
        }

        return "重开发票成功";
    }

    /**
     * redflush 发票红冲
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    public String redFlush(BbpmInvoiceRedFlushVo vo) {
        if (vo == null) {
            return null;
        }

        String kprq = vo.getKprq();
        if(StringUtils.isBlank(kprq)){
            throw new McpException("kprq不能为空");
        }
        if(DateComparisonUtils.dateComparison(kprq)){
            throw new McpException("发票开具超过6个月期限，不能再执行红冲操作");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<BbpmInvoiceRedFlushVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        log.info("发票红冲接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("发票红冲接口请求参数json:" + jsonRequest);

        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipSettlementFeignClient.redflush(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/einvoice/redflush", parentRequest);
        }
        log.info("调用工银3.19.2发票红冲接口返回结果为:" + responseBody);

        FaceMdMapResultTwo<InvoiceIssueResult> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.19.2发票红冲接口失败:"+responseBody);
            throw new McpException("*提示:"+faceMdMapResult.getMessage());
        }else{
            JSONObject jsonObject = JSON.parseObject(String.valueOf(faceMdMapResult.getData()));
            InvoiceIssueResult invoiceIssueResult = jsonObject.toJavaObject(InvoiceIssueResult.class);
            if(!("0000").equals(invoiceIssueResult.getReturncode())){
                log.error("调用工银3.19.2发票红冲接口失败:"+responseBody);
                throw new McpException("*提示:"+invoiceIssueResult.getReturncode()+":"+invoiceIssueResult.getReturnmsg());
            }
            return invoiceIssueResult.getPdflj();
        }
    }

    /**
     * redflush 批量发票红冲
     * @param voList 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    public String redFlushBatch(List<BbpmInvoiceRedFlushVo> voList) {
        StringBuffer ids = new StringBuffer();
        StringBuffer reason = new StringBuffer();
        StringBuffer result = new StringBuffer();
        int successNum = 0;
        int failNum = 0;

        if(voList == null || voList.size() == 0){
            return null;
        }
        for(BbpmInvoiceRedFlushVo bbpmInvoiceRedFlushVo : voList ){
            String kprq = bbpmInvoiceRedFlushVo.getKprq();
            if(DateComparisonUtils.dateComparison(kprq)){
                throw new McpException("发票开具超过6个月期限，不能再执行红冲操作");
            }
        }

        for(BbpmInvoiceRedFlushVo bbpmInvoiceRedFlushVo : voList ){
            try {
                String id = baseService.redFlush(bbpmInvoiceRedFlushVo);
                successNum++;
//                ids.append(id).append(",");
            }catch (Exception e){
                e.printStackTrace();
                failNum++;
                reason.append(e.getMessage()).append(";");
            }
        }
        result.append("成功").append(successNum).append("条");
        if(failNum != 0){
            result.append(",失败").append(failNum).append("条");
            result.append("失败原因:").append(reason);
            throw new McpException(result.toString());
        }
        return result.toString();
    }

    /**
     * pushInvoice 发票推送
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    public String pushInvoice(BbpmInvoicePushVo vo) {
        StringBuffer ids = new StringBuffer();
        if (vo == null) {
            return null;
        }
        int num = 1;
        if(StringUtils.isNotBlank(vo.getPhone()) && StringUtils.isNotBlank(vo.getEmail())){
            num = 2;
        }else {
           if(StringUtils.isNotBlank(vo.getPhone())){
                vo.setTslx(InvoiceEnums.SEND_TYPE_PHONE.getCode());
                vo.setLxfs(vo.getPhone());
            }else if(StringUtils.isNotBlank(vo.getEmail())){
               vo.setTslx(InvoiceEnums.SNED_TYPE_EMAIL.getCode());
               vo.setLxfs(vo.getEmail());
           }
        }

        for (int i=0;i<num;i++){
            if(num == 2){
                if(i == 0){
                    vo.setTslx(InvoiceEnums.SEND_TYPE_PHONE.getCode());
                    vo.setLxfs(vo.getPhone());
                }
                if(i == 1){
                    vo.setTslx(InvoiceEnums.SNED_TYPE_EMAIL.getCode());
                    vo.setLxfs(vo.getEmail());
                }
            }
            //发送请求
            String id = rewire(vo);

            ids.append(id);
        }

        return ids.toString();
    }

    public static void main(String[] args){
        for (int i=0;i<2;i++){
            System.out.println(i);
        }
    }

    private String rewire(BbpmInvoicePushVo vo){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<BbpmInvoicePushVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        log.info("发票推送接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("发票推送接口请求参数json:" + jsonRequest);

        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipSettlementFeignClient.pushInvoice(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/einvoice/pushInvoice", parentRequest);
        }
        log.info("调用工银3.19.3发票推送接口返回结果为:" + responseBody);

        FaceMdMapResultTwo<InvoiceIssueResult> faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResultTwo.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.19.3发票推送接口失败:"+responseBody);
            throw new McpException("*提示:"+faceMdMapResult.getMessage());
        }else{
            JSONObject jsonObject = JSON.parseObject(String.valueOf(faceMdMapResult.getData()));
            InvoiceIssueResult invoiceIssueResult = jsonObject.toJavaObject(InvoiceIssueResult.class);
            if(!("0000").equals(invoiceIssueResult.getReturncode())){
                log.error("调用工银3.19.3发票推送接口失败:"+responseBody);
                throw new McpException("*提示:"+invoiceIssueResult.getReturncode()+":"+invoiceIssueResult.getReturnmsg());
            }
            return invoiceIssueResult.getId();
        }
    }

    /**
     * pushInvoice 批量发票推送
     * @param voList 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-05
     * @change
     * 2023-05-05 by binghong.tang for init
     */
    @Override
    public String pushInvoiceBatch(List<BbpmInvoicePushVo> voList) {
        StringBuffer ids = new StringBuffer();
        StringBuffer reason = new StringBuffer();
        StringBuffer result = new StringBuffer();
        int successNum = 0;
        int failNum = 0;
        if(voList == null || voList.size() == 0){
            return null;
        }

        for(BbpmInvoicePushVo bbpmInvoicePushVo : voList ){
            try{
                String id = baseService.pushInvoice(bbpmInvoicePushVo);
                //            ids.append(id).append(",");
                successNum++;
//                ids.append(id).append(",");
            }catch (Exception e){
                e.printStackTrace();
                failNum++;
                reason.append(e.getMessage()).append(";");
            }
        }
        result.append("成功").append(successNum).append("条");
        if(failNum != 0){
            result.append(",失败").append(failNum).append("条");
            result.append("失败原因:").append(reason);
            throw new McpException(result.toString());
        }
        return result.toString();
    }

}
