package com.bonc.ioc.bzf.business.supplementary.dao;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPaymentProductEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.supplementary.vo.*;

import java.util.List;

/**
 * 追加账单产品表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-04-01
 * @change 2025-04-01 by pyj for init
 */
@Mapper
public interface BbpmSupplementaryPaymentProductMapper extends McpBaseMapper<BbpmSupplementaryPaymentProductEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * <AUTHOR>
     * @date 2025-04-01
     * @change 2025-04-01 by pyj for init
     * @since 1.0.0
     */
    List<BbpmSupplementaryPaymentProductPageResultVo> selectByPageCustom(@Param("vo") BbpmSupplementaryPaymentProductPageVo vo);

    /**
     * 根据追加单id删除
     *
     * @param supplementaryId 追加单id
     */
    void deleteBySupplementaryId(@Param("supplementaryId") String supplementaryId);

    /**
     * 根据追加单id更新删除标识
     *
     * @param parentId 上级id
     * @param delFlag  删除标识
     */
    void updateDelFlagByParentId(@Param("parentId") String parentId,
                                 @Param("delFlag") String delFlag);

    /**
     * 根据上级id查询
     *
     * @param parentId 上级id
     * @return 追加账单产品列表
     */
    List<BbpmSupplementaryPaymentProductVo> selectListByParentId(@Param("parentId") String parentId);
}
