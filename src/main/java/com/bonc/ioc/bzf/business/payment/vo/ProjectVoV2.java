package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName ProjectVo
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2023-05-25 15:16
 **/
@Data
@ApiModel(value = "ProjectVo", description = "项目VO")
public class ProjectVoV2 implements java.io.Serializable{

    @ApiModelProperty(value = "项目ID(国信内部使用)")
    private String projectId;

    @ApiModelProperty(value = "项目编号(工银使用)")
    private String projectNo;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
}
