package com.bonc.ioc.bzf.business.supplementary.service;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmApproveInfoEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 审批表 服务类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
public interface IBbpmApproveInfoService extends IMcpBaseService<BbpmApproveInfoEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbpmApproveInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbpmApproveInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param approveId 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String approveId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param approveIdList 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> approveIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的审批表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbpmApproveInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的审批表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbpmApproveInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的审批表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbpmApproveInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的审批表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbpmApproveInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param approveId 需要查询的审批id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    BbpmApproveInfoVo selectByIdRecord(String approveId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbpmApproveInfoPageResultVo>> selectByPageRecord(BbpmApproveInfoPageVo vo);

    /**
     * 根据上级id和审批类型查询审批数据
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 审批数据
     */
    BbpmApproveInfoVo selectByParentIdAndApproveType(String parentId, String approveType);

    /**
     * 提交
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 审批链
     */
    BbpmApproveLinkVo submit(String parentId, String approveType);

    /**
     * 撤销
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 审批链
     */
    BbpmApproveLinkVo cancel(String parentId, String approveType);

    /**
     * 处理审批
     *
     * @param vo            评论 vo实体
     * @param approveType   审批类型
     * @param approveStatus 审批状态
     * @return 审批链
     */
    BbpmApproveLinkVo dealApprove(BbpmRemarkVo vo, String approveType, String approveStatus);

    /**
     * 获取操作记录列表
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 操作记录列表
     */
    List<BbpmApproveDetailInfoVo> operateDetailInfo(String parentId, String approveType);
}
