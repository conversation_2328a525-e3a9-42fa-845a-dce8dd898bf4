package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付参数对象
 *
 * <AUTHOR>
 * @date 2022-03-04
 * @change 2022-03-04 by 姚春雨 for init
 */
@Data
@ApiModel(value = "PayParmsVo对象", description = "支付参数对象")
public class PayParmsVo extends McpBaseVo implements Serializable {

    @ApiModelProperty(hidden = true)
    private String busiId;

    @ApiModelProperty(hidden = true)
    private String time;

    @ApiModelProperty(hidden = true)
    private String traceId;

    @ApiModelProperty(value = "请求数据")
    private PayDataParmsVo data;

}
