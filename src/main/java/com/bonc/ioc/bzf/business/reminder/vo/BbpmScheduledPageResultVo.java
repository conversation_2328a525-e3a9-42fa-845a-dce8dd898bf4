package com.bonc.ioc.bzf.business.reminder.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 缴费定时器 实体类
 *
 * <AUTHOR>
 * @date 2023-08-09
 * @change 2023-08-09 by binghong.tang for init
 */
@ApiModel(value="BbpmScheduledPageResultVo对象", description="缴费定时器")
public class BbpmScheduledPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 唯一标识符
     */
    @ApiModelProperty(value = "唯一标识符")
    @NotBlank(message = "唯一标识符不能为空",groups = {UpdateValidatorGroup.class})
                                  private String cronId;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    @NotBlank(message = "任务名称不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String cronName;

    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
                            private String cron;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 唯一标识符
     */
    public String getCronId() {
        return cronId;
    }

    public void setCronId(String cronId) {
        this.cronId = cronId;
    }

    /**
     * @return 任务名称
     */
    public String getCronName() {
        return cronName;
    }

    public void setCronName(String cronName) {
        this.cronName = cronName;
    }

    /**
     * @return 规则
     */
    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmScheduledPageResultVo{" +
            "cronId=" + cronId +
            ", cronName=" + cronName +
            ", cron=" + cron +
            ", delFlag=" + delFlag +
        "}";
    }
}
