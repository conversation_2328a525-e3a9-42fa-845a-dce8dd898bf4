package com.bonc.ioc.bzf.business.payment.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.math.BigDecimal;
import java.util.List;

/**
 * 3.23  趸租对公收款接口：线下转账和支票收款请求 主类
 */
@Data
@Builder
public class CorporateCollectionRequest {
    //  支付类型	String	是	10	支付方式，目前支持0:线下转账;1,支票
    private String paymentType;
    //附件在文件中心的ID 	String	是	300	附件在文件中心的ID(线下转账，支票)
    private String receiptNoUrl;
    //转账凭证号	String	是	50	支付方式为线下转账，必传
    private String transBillNo;
    //支票流水号	String	是	50	支付方式为支票，必传
    private String checkNo;
    //存款人	String	       否	50	对应存款业务员姓名
    private String depositor;
    //	存款时间	Date	否	10	yyyy/MM/dd存款日期
    private String depositDate;
    //	上传人	String	是	50	对应上传业务员姓名
    private String uploader;
    //上传时间	Date	是	10	yyyy/MM/dd上传日期
    private String uploadDate;
    //	付款方账户号	String	是	50	支付方式为线下转账，必传
    private String payAcctNo;
    //	付款方名称	String	是	50	支付方式为线下转账，必传
    private String payer;
    //	收款方账户号	String	是	50	支付方式为线下转账，必传
    private String recepitAcctNo;
    //	收款方名称	String	是	50	支付方式为线下转账，必传
    private String recepiter;
    //	金额	BigDecimal	是	13.4	支付方式为线下转账，必传
    private BigDecimal amount;
    //	交易日期	Date	是	10	yyyy/MM/dd上传日期支付方式为线下转账，必传
    private String transDate;
    //	摘要	String	否	500	支付方式为线下转账，必传
    private String summary;
    //	附言	String	否	300	支付方式为线下转账，必传
    private String remark;
    //	用途	String	否	200	支付方式为线下转账，必传
    private String useage;

    //	银行总行联行号	String	否	32
    private String bankCode;
    //	项目ID	String	是	64	业务中台ID
    private String projectId;
    //	支付时间	DateTime	是		线转账和支票支付，必传
    private String cashTime;

    //转账银行名称	String	       是	50	支付方式为线下转账，必传
    private String transferBankName;
    //收款银行编码	String	       是	59	支付方式为线下转账，必传
    private String receiptBankCode;
//    //收款银行名称	String	是	59	支付方式为线下转账，必传
//    private String receiptBankName;

    //凭证号或者支票流水号的总金额
    private BigDecimal totalAmount;

    //转账明细单号列表
    @Singular("transfer")
    List<CorporateCollectionSubRequest> transferList;

    // 收款银行账户
    private String receiptBankAcct;

    //收款银行名称
    private String receiptBankName;
    //收款银行账号
    private String receiptBankAcctNo;
    //收款银行户名
    private String receiptBankAccountName;
    //收款银行支行名称
    private String receiptBankBranchName;

    //是否是多项目认款01是02否
    private String  multiProject;

    //chargeCode	收款单号	String	是		补录时用
    private String chargeCode;

    //2024年12月冲刺增加
    //amountReceivable	本次账单应收金额	BigDecimal 平铺算法改造
    private BigDecimal amountReceivable;
}
