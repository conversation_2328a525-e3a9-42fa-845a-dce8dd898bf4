package com.bonc.ioc.bzf.business.reminder.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * (催缴记录)催缴规则消息发送日志--子表V2 实体类
 *
 * <AUTHOR>
 * @date 2024-04-23
 * @change 2024-04-23 by binghong.tang for init
 */
@ApiModel(value="BbpmMessageSendSubLogV2PageVo对象", description="(催缴记录)催缴规则消息发送日志--子表V2")
public class BbpmMessageSendSubLogV2PageVo extends McpBasePageVo implements Serializable{


    /**
     * 通知ID
     */
    @ApiModelProperty(value = "通知ID")
    @NotBlank(message = "通知ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String noticeId;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @NotBlank(message = "主表ID不能为空",groups = {UpdateValidatorGroup.class})
                            private String mainId;

    /**
     * 规则编号
     */
    @ApiModelProperty(value = "规则编号")
    @NotBlank(message = "规则编号不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String rulesId;

    /**
     * 规则子表
     */
    @ApiModelProperty(value = "规则子表")
    @NotBlank(message = "规则子表不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private String rulesSubId;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
                            private String businessId;

    /**
     * 短信消息模板类型
     */
    @ApiModelProperty(value = "短信消息模板类型")
                            private String templateTypeCodeText;

    /**
     * 短信消息模板id
     */
    @ApiModelProperty(value = "短信消息模板id")
                            private String messageTemplateIdText;

    /**
     * 站内消息模板类型
     */
    @ApiModelProperty(value = "站内消息模板类型")
                            private String templateTypeCodeStation;

    /**
     * 站内消息模板id
     */
    @ApiModelProperty(value = "站内消息模板id")
                            private String messageTemplateIdStation;

    /**
     * 租户code
     */
    @ApiModelProperty(value = "租户code")
                            private String tenantCode;

    /**
     * 租户名称
     */
    @ApiModelProperty(value = "租户名称")
                            private String tenantName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
                            private String tenantMobile;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
                            private String certType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
                            private String certNo;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
                            private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
                            private String houseName;

    /**
     * 催缴方式:1自动,2手动
     */
    @ApiModelProperty(value = "催缴方式:1自动,2手动")
                            private String collectionMethod;

    /**
     * 催缴原因:01未缴/未足额缴纳,02已缴但未对平
     */
    @ApiModelProperty(value = "催缴原因:01未缴/未足额缴纳,02已缴但未对平")
                            private String collectionReason;

    /**
     * 短信通知结果:1成功,2失败
     */
    @ApiModelProperty(value = "短信通知结果:1成功,2失败")
                            private String noticeResultText;

    /**
     * 站内信息通知结果:1成功,2失败
     */
    @ApiModelProperty(value = "站内信息通知结果:1成功,2失败")
                            private String noticeResultStation;

    /**
     * 短信发送时间
     */
    @ApiModelProperty(value = "短信发送时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date sendTimeText;

    /**
     * 站内信发送时间
     */
    @ApiModelProperty(value = "站内信发送时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date sendTimeStation;

    /**
     * 趸租单位名称--备用
     */
    @ApiModelProperty(value = "趸租单位名称--备用")
                            private String reletUnitName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 账单ID
     */
    @ApiModelProperty(value = "账单ID")
                            private String billId;

    /**
     * 账单缴费状态
     */
    @ApiModelProperty(value = "账单缴费状态")
                            private String billStatus;

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
                            private String billCycle;

    /**
     * 应缴费日期
     */
    @ApiModelProperty(value = "应缴费日期")
                            private String payableDate;

    /**
     * 合计月
     */
    @ApiModelProperty(value = "合计月")
                            private String totalMonth;

    /**
     * 合计金额
     */
    @ApiModelProperty(value = "合计金额")
                            private String totalAmount;

    /**
     * 房屋实际用途
     */
    @ApiModelProperty(value = "房屋实际用途")
                            private String projectFormat;

    /**
     * 个人/企业标识:01企业,02个人
     */
    @ApiModelProperty(value = "个人/企业标识:01企业,02个人")
                            private String chargeOwner;

    /**
     * 请求消息中心报文
     */
    @ApiModelProperty(value = "请求消息中心报文")
                            private String request;

    /**
     * 消息中心响应报文
     */
    @ApiModelProperty(value = "消息中心响应报文")
                            private String response;

    /**
     * 备用字段1
     */
    @ApiModelProperty(value = "备用字段1")
                            private String fields1;

    /**
     * 备用字段2
     */
    @ApiModelProperty(value = "备用字段2")
                            private String fields2;

    /**
     * 备用字段3
     */
    @ApiModelProperty(value = "备用字段3")
                            private String fields3;


    @ApiModelProperty(value = "催缴原因名称")
    private String collectionReasonName;
    @ApiModelProperty(value = "短信发送时间-开始")
    private String sendTimeTextStart;
    @ApiModelProperty(value = "短信发送时间-结束")
    private String sendTimeTextEnd;

    @ApiModelProperty(value = "项目编号ids 逗号分隔")
    private String projectIdStr;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }
    public String getProjectIdStr() {
        return projectIdStr;
    }

    public void setProjectIdStr(String projectIdStr) {
        this.projectIdStr = projectIdStr;
    }

    public String getCollectionReasonName() {
        return collectionReasonName;
    }

    public void setCollectionReasonName(String collectionReasonName) {
        this.collectionReasonName = collectionReasonName;
    }

    public String getSendTimeTextStart() {
        return sendTimeTextStart;
    }

    public void setSendTimeTextStart(String sendTimeTextStart) {
        this.sendTimeTextStart = sendTimeTextStart;
    }

    public String getSendTimeTextEnd() {
        return sendTimeTextEnd;
    }

    public void setSendTimeTextEnd(String sendTimeTextEnd) {
        this.sendTimeTextEnd = sendTimeTextEnd;
    }

    /**
     * @return 通知ID
     */
    public String getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(String noticeId) {
        this.noticeId = noticeId;
    }

    /**
     * @return 主表ID
     */
    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    /**
     * @return 规则编号
     */
    public String getRulesId() {
        return rulesId;
    }

    public void setRulesId(String rulesId) {
        this.rulesId = rulesId;
    }

    /**
     * @return 规则子表
     */
    public String getRulesSubId() {
        return rulesSubId;
    }

    public void setRulesSubId(String rulesSubId) {
        this.rulesSubId = rulesSubId;
    }

    /**
     * @return 业务id
     */
    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    /**
     * @return 短信消息模板类型
     */
    public String getTemplateTypeCodeText() {
        return templateTypeCodeText;
    }

    public void setTemplateTypeCodeText(String templateTypeCodeText) {
        this.templateTypeCodeText = templateTypeCodeText;
    }

    /**
     * @return 短信消息模板id
     */
    public String getMessageTemplateIdText() {
        return messageTemplateIdText;
    }

    public void setMessageTemplateIdText(String messageTemplateIdText) {
        this.messageTemplateIdText = messageTemplateIdText;
    }

    /**
     * @return 站内消息模板类型
     */
    public String getTemplateTypeCodeStation() {
        return templateTypeCodeStation;
    }

    public void setTemplateTypeCodeStation(String templateTypeCodeStation) {
        this.templateTypeCodeStation = templateTypeCodeStation;
    }

    /**
     * @return 站内消息模板id
     */
    public String getMessageTemplateIdStation() {
        return messageTemplateIdStation;
    }

    public void setMessageTemplateIdStation(String messageTemplateIdStation) {
        this.messageTemplateIdStation = messageTemplateIdStation;
    }

    /**
     * @return 租户code
     */
    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    /**
     * @return 租户名称
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 联系电话
     */
    public String getTenantMobile() {
        return tenantMobile;
    }

    public void setTenantMobile(String tenantMobile) {
        this.tenantMobile = tenantMobile;
    }

    /**
     * @return 证件类型
     */
    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    /**
     * @return 证件号码
     */
    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    /**
     * @return 项目id
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 房源地址
     */
    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    /**
     * @return 催缴方式:1自动,2手动
     */
    public String getCollectionMethod() {
        return collectionMethod;
    }

    public void setCollectionMethod(String collectionMethod) {
        this.collectionMethod = collectionMethod;
    }

    /**
     * @return 催缴原因:01未缴/未足额缴纳,02已缴但未对平
     */
    public String getCollectionReason() {
        return collectionReason;
    }

    public void setCollectionReason(String collectionReason) {
        this.collectionReason = collectionReason;
    }

    /**
     * @return 短信通知结果:1成功,2失败
     */
    public String getNoticeResultText() {
        return noticeResultText;
    }

    public void setNoticeResultText(String noticeResultText) {
        this.noticeResultText = noticeResultText;
    }

    /**
     * @return 站内信息通知结果:1成功,2失败
     */
    public String getNoticeResultStation() {
        return noticeResultStation;
    }

    public void setNoticeResultStation(String noticeResultStation) {
        this.noticeResultStation = noticeResultStation;
    }

    /**
     * @return 短信发送时间
     */
    public Date getSendTimeText(){
        if(sendTimeText!=null){
            return (Date)sendTimeText.clone();
        }else{
            return null;
        }
    }

    public void setSendTimeText(Date sendTimeText) {
        if(sendTimeText==null){
            this.sendTimeText = null;
        }else{
            this.sendTimeText = (Date)sendTimeText.clone();
        }
    }

    /**
     * @return 站内信发送时间
     */
    public Date getSendTimeStation(){
        if(sendTimeStation!=null){
            return (Date)sendTimeStation.clone();
        }else{
            return null;
        }
    }

    public void setSendTimeStation(Date sendTimeStation) {
        if(sendTimeStation==null){
            this.sendTimeStation = null;
        }else{
            this.sendTimeStation = (Date)sendTimeStation.clone();
        }
    }

    /**
     * @return 趸租单位名称--备用
     */
    public String getReletUnitName() {
        return reletUnitName;
    }

    public void setReletUnitName(String reletUnitName) {
        this.reletUnitName = reletUnitName;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 账单ID
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     * @return 账单缴费状态
     */
    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    /**
     * @return 账单周期
     */
    public String getBillCycle() {
        return billCycle;
    }

    public void setBillCycle(String billCycle) {
        this.billCycle = billCycle;
    }

    /**
     * @return 应缴费日期
     */
    public String getPayableDate() {
        return payableDate;
    }

    public void setPayableDate(String payableDate) {
        this.payableDate = payableDate;
    }

    /**
     * @return 合计月
     */
    public String getTotalMonth() {
        return totalMonth;
    }

    public void setTotalMonth(String totalMonth) {
        this.totalMonth = totalMonth;
    }

    /**
     * @return 合计金额
     */
    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * @return 房屋实际用途
     */
    public String getProjectFormat() {
        return projectFormat;
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = projectFormat;
    }

    /**
     * @return 个人/企业标识:01企业,02个人
     */
    public String getChargeOwner() {
        return chargeOwner;
    }

    public void setChargeOwner(String chargeOwner) {
        this.chargeOwner = chargeOwner;
    }

    /**
     * @return 请求消息中心报文
     */
    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    /**
     * @return 消息中心响应报文
     */
    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    /**
     * @return 备用字段1
     */
    public String getFields1() {
        return fields1;
    }

    public void setFields1(String fields1) {
        this.fields1 = fields1;
    }

    /**
     * @return 备用字段2
     */
    public String getFields2() {
        return fields2;
    }

    public void setFields2(String fields2) {
        this.fields2 = fields2;
    }

    /**
     * @return 备用字段3
     */
    public String getFields3() {
        return fields3;
    }

    public void setFields3(String fields3) {
        this.fields3 = fields3;
    }

      @Override
    public String toString() {
        return "BbpmMessageSendSubLogV2PageVo{" +
            "noticeId=" + noticeId +
            ", mainId=" + mainId +
            ", rulesId=" + rulesId +
            ", rulesSubId=" + rulesSubId +
            ", businessId=" + businessId +
            ", templateTypeCodeText=" + templateTypeCodeText +
            ", messageTemplateIdText=" + messageTemplateIdText +
            ", templateTypeCodeStation=" + templateTypeCodeStation +
            ", messageTemplateIdStation=" + messageTemplateIdStation +
            ", tenantCode=" + tenantCode +
            ", tenantName=" + tenantName +
            ", tenantMobile=" + tenantMobile +
            ", certType=" + certType +
            ", certNo=" + certNo +
            ", projectId=" + projectId +
            ", projectName=" + projectName +
            ", houseName=" + houseName +
            ", collectionMethod=" + collectionMethod +
            ", collectionReason=" + collectionReason +
            ", noticeResultText=" + noticeResultText +
            ", noticeResultStation=" + noticeResultStation +
            ", sendTimeText=" + sendTimeText +
            ", sendTimeStation=" + sendTimeStation +
            ", reletUnitName=" + reletUnitName +
            ", contractCode=" + contractCode +
            ", billId=" + billId +
            ", billStatus=" + billStatus +
            ", billCycle=" + billCycle +
            ", payableDate=" + payableDate +
            ", totalMonth=" + totalMonth +
            ", totalAmount=" + totalAmount +
            ", projectFormat=" + projectFormat +
            ", chargeOwner=" + chargeOwner +
            ", request=" + request +
            ", response=" + response +
            ", fields1=" + fields1 +
            ", fields2=" + fields2 +
            ", fields3=" + fields3 +
        "}";
    }
}
