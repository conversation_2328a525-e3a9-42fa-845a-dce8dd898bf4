package com.bonc.ioc.bzf.business.payment.vo;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 账单与收款明细表v3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-12-21
 * @change 2022-12-21 by binghong.tang for init
 */
@ApiModel(value="BbpmBillCollectionDetailsPageVo对象", description="账单与收款明细表v3.0")
public class BbpmBillCollectionDetailsPageVo extends McpBasePageVo implements Serializable{


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String detailsId;

    /**
     * 账单编号
     */
    @ApiModelProperty(value = "账单编号")
                            private String billNo;

    /**
     * 收款id
     */
    @ApiModelProperty(value = "收款id")
                            private String collectionId;

    /**
     * 收款编号
     */
    @ApiModelProperty(value = "收款编号")
                            private String collectionNo;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
                            private String customerId;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
                            private String contractId;

    /**
     * 现金金额
     */
    @ApiModelProperty(value = "现金金额")
                            private BigDecimal cashAmout;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date cashTime;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;
    /**
     * 收费科目当期期次
     */
    @ApiModelProperty(value = "收费科目当期期次")
    private Integer chargeSubjectPeriod;

    @ApiModelProperty(value = "待缴金额")
    private BigDecimal replacePayAmount;

    @ApiModelProperty(value = "账单对应的收费科目")
    private String billChargeSubject;

    public BigDecimal getReplacePayAmount() {
        return replacePayAmount;
    }

    public void setReplacePayAmount(BigDecimal replacePayAmount) {
        this.replacePayAmount = replacePayAmount;
    }

    public String getBillChargeSubject() {
        return billChargeSubject;
    }

    public void setBillChargeSubject(String billChargeSubject) {
        this.billChargeSubject = billChargeSubject;
    }

    public Integer getChargeSubjectPeriod() {
        return chargeSubjectPeriod;
    }

    public void setChargeSubjectPeriod(Integer chargeSubjectPeriod) {
        this.chargeSubjectPeriod = chargeSubjectPeriod;
    }
    /**
     * @return 主键id
     */
    public String getDetailsId() {
        return detailsId;
    }

    public void setDetailsId(String detailsId) {
        this.detailsId = detailsId;
    }

    /**
     * @return 账单编号
     */
    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    /**
     * @return 收款id
     */
    public String getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(String collectionId) {
        this.collectionId = collectionId;
    }

    /**
     * @return 收款编号
     */
    public String getCollectionNo() {
        return collectionNo;
    }

    public void setCollectionNo(String collectionNo) {
        this.collectionNo = collectionNo;
    }

    /**
     * @return 客户id
     */
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * @return 合同id
     */
    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    /**
     * @return 现金金额
     */
    public BigDecimal getCashAmout() {
        return cashAmout;
    }

    public void setCashAmout(BigDecimal cashAmout) {
        this.cashAmout = cashAmout;
    }

    /**
     * @return 支付时间
     */
    public Date getCashTime(){
        if(cashTime!=null){
            return (Date)cashTime.clone();
        }else{
            return null;
        }
    }

    public void setCashTime(Date cashTime) {
        if(cashTime==null){
            this.cashTime = null;
        }else{
            this.cashTime = (Date)cashTime.clone();
        }
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbpmBillCollectionDetailsPageVo{" +
            "detailsId=" + detailsId +
            ", billNo=" + billNo +
            ", collectionId=" + collectionId +
            ", collectionNo=" + collectionNo +
            ", customerId=" + customerId +
            ", contractId=" + contractId +
            ", cashAmout=" + cashAmout +
            ", cashTime=" + cashTime +
            ", delFlag=" + delFlag +
        "}";
    }
}
