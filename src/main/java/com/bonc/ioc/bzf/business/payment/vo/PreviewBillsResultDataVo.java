package com.bonc.ioc.bzf.business.payment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@ApiModel(value="预览数据", description="预览数据")
@Data
public class PreviewBillsResultDataVo implements java.io.Serializable{

    @ApiModelProperty(value = "缴费次数")
    private String chargePeriod;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "缴费开始时间")
    private Date chargeStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "缴费结束时间")
    private Date chargeEndDate;

    @ApiModelProperty(value = "租金单价")
    private String paramprice;

    @ApiModelProperty(value = "面积")
    private String paramarea;

    @ApiModelProperty(value = "月数")
    private Integer months;

    @ApiModelProperty(value = "天数")
    private Integer days;

    @ApiModelProperty(value = "应缴金额")
    private BigDecimal payableMoney;


    @ApiModelProperty(value = "增值税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "不含税金额")
    private BigDecimal excludingRateMoney;


    @ApiModelProperty(value = "增值税额")
    private BigDecimal rateMoney;

    @ApiModelProperty(value = "日租金")
    private BigDecimal dayMoney;

    @ApiModelProperty(value = "房源名称")
    private String houseName;

    @ApiModelProperty(value = "房屋ID")
    private String houseId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "应缴日期")
    private Date payableDate;

    @ApiModelProperty(value = "待缴金额")
    private BigDecimal toBePaidMoney;

    @ApiModelProperty(value = "收费周期 (01 月02季03半年04年)")
    private String chargeCycleStr;

    @ApiModelProperty(value = "支付状态 01待支付02支付中03部分支付04支付完成")
    private String paymentStatusStr;

    @ApiModelProperty(value = "缴费状态 01足额02部分缴03未缴")
    private String chargeStatusStr;

    @ApiModelProperty(value = "账单状态 01正常02冻结03关闭")
    private String statusStr;

    @ApiModelProperty(value = "已缴金额")
    private BigDecimal paidInMoney;



    @ApiModelProperty(value = "月租金")
    private BigDecimal monthAmount;
    @ApiModelProperty(value = "备注(工银实际没有这个东西)")
    private String notes;

    @ApiModelProperty(value = "计费科目编号")
    private String chargeSubjectNo;
}
