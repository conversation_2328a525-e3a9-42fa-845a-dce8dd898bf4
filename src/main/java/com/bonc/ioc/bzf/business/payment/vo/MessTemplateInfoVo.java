package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 消息模板信息表 实体类
 *
 * <AUTHOR>
 * @date 2022-08-03
 * @change 2022-08-03 by sqj for init
 */
@ApiModel(value="MessTemplateInfoVo对象", description="消息模板信息表")
public class MessTemplateInfoVo extends McpBaseVo implements Serializable{


    @NotBlank(message = "不能为空",groups = {UpdateValidatorGroup.class})
    @ApiModelProperty(value = "消息模板id")
                                  private String messTemplateId;

    /**
     * 消息模板名称
     */
    @ApiModelProperty(value = "消息模板名称", required = true)
                            private String messTemplateName;

    /**
     * 消息模板内容
     */
    @ApiModelProperty(value = "消息模板内容;内容中参数替换掉；例：客户姓名替换为${customerName}", required = true)
                            private String messTemplateContent;

    @ApiModelProperty(value = "消息模板内容描述，将内容中的参数添加上标签p、span和strong，用于前端页面显示")
    private String messTemplateContentDesc;

    /**
     * 模板类型id
     */
    @ApiModelProperty(value = "模板类型id", required = true)
                            private String templateTypeId;

    /**
     * 删除标记 1=删除，0=未删除
     */
    @ApiModelProperty(value = "删除标记 1=删除，0=未删除", hidden = true)
                            private Integer isDelete;

    @ApiModelProperty(value = "模板类型名称")
    private String templateTypeName;

    /**
     * @return
     */
    public String getMessTemplateId() {
        return messTemplateId;
    }

    public void setMessTemplateId(String messTemplateId) {
        this.messTemplateId = messTemplateId;
    }

    /**
     * @return 消息模板名称
     */
    public String getMessTemplateName() {
        return messTemplateName;
    }

    public void setMessTemplateName(String messTemplateName) {
        this.messTemplateName = messTemplateName;
    }

    /**
     * @return 消息模板内容
     */
    public String getMessTemplateContent() {
        return messTemplateContent;
    }

    public void setMessTemplateContent(String messTemplateContent) {
        this.messTemplateContent = messTemplateContent;
    }

    /**
     * @return 模板类型id
     */
    public String getTemplateTypeId() {
        return templateTypeId;
    }

    public void setTemplateTypeId(String templateTypeId) {
        this.templateTypeId = templateTypeId;
    }


    /**
     * @return 删除标记 1=删除，0=未删除
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getTemplateTypeName() {
        return templateTypeName;
    }

    public void setTemplateTypeName(String templateTypeName) {
        this.templateTypeName = templateTypeName;
    }

    public String getMessTemplateContentDesc() {
        return messTemplateContentDesc;
    }

    public void setMessTemplateContentDesc(String messTemplateContentDesc) {
        this.messTemplateContentDesc = messTemplateContentDesc;
    }

    @Override
    public String toString() {
        return "MessTemplateInfoVo{" +
                "messTemplateId='" + messTemplateId + '\'' +
                ", messTemplateName='" + messTemplateName + '\'' +
                ", messTemplateContent='" + messTemplateContent + '\'' +
                ", messTemplateContentDesc='" + messTemplateContentDesc + '\'' +
                ", templateTypeId='" + templateTypeId + '\'' +
                ", isDelete=" + isDelete +
                ", templateTypeName='" + templateTypeName + '\'' +
                '}';
    }
}
