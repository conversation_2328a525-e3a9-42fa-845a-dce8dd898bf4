package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.bzf.business.payment.result.create.ChargeSubjectParamsRequest;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 追加账单推送产品相关报文 vo实体类
 *
 * <AUTHOR>
 * @since 2025/4/2
 */
@Data
@ApiModel(value = "追加账单推送产品相关报文", description = "追加账单推送产品相关报文")
public class BbpmAppendBillRoomVo extends McpBaseVo implements Serializable {

    /**
     * 房号
     */
    @ApiModelProperty(value = "房号")
    private String roomNo;

    /**
     * 计费科目列表
     */
    @ApiModelProperty(value = "计费科目列表")
    private List<ChargeSubjectParamsRequest> chargeSubjectList;
}
