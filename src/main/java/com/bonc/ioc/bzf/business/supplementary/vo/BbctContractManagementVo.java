package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 合同主表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-20
 * @change 2023-05-20 by sx for init
 */
@ApiModel(value="BbctContractManagementVo对象", description="合同主表")
@Data
public class BbctContractManagementVo extends BaseVo implements Serializable{

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotBlank(message = "合同id不能为空",groups = {UpdateValidatorGroup.class})
    private String contractId;

    /**
     * 合同分类编号
     */
    @ApiModelProperty(value = "合同分类编号")
    private String contractTypeCode;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 所属趸租合同编号
     */
    @ApiModelProperty(value = "所属趸租合同编号")
    private String parentContractCode;

    @ApiModelProperty(value = "续租来源合同编号")
    private String reletSourceContractCode;

    /**
     * 合同状态（1已生效、2未生效、3已结束、4终止）
     */
    @ApiModelProperty(value = "合同状态（1已生效、2未生效、3已结束、4终止、5已逾期、6作废）")
    private String contractStatus;

    /**
     * 合同模板ID
     */
    @ApiModelProperty(value = "合同模板ID")
    private String contractTemplateId;

    /**
     * 合同审核人ID
     */
    @ApiModelProperty(value = "合同审核人ID")
    private String examineUserId;

    /**
     * 合同审核人姓名
     */
    @ApiModelProperty(value = "合同审核人姓名")
    private String examineUserName;

    /**
     * pdf文件id
     */
    @ApiModelProperty(value = "pdf文件id")
    private String pdfFileId;

    /**
     * 审核后PDF文件ID
     */
    @ApiModelProperty(value = "审核后PDF文件ID")
    private String newpdfFileId;

    /**
     * 合同签署日期
     */
    @ApiModelProperty(value = "合同签署日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractBeginTime;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractEndTime;

    /**
     * 甲方ID
     */
    @ApiModelProperty(value = "甲方ID")
    private String firstId;

    /**
     * 甲方名称
     */
    @ApiModelProperty(value = "甲方名称")
    private String firstName;

    /**
     * 甲方信用代码
     */
    @ApiModelProperty(value = "甲方信用代码")
    private String firstSocialCode;

    /**
     * 合同总份数
     */
    @ApiModelProperty(value = "合同总份数")
    private String totalContractNum;


    /**
     * 签约房源套数
     */
    @ApiModelProperty(value = "签约房源套数")
    private Integer totalSets;

    @ApiModelProperty(value = "租赁家具家电房屋数量")
    private Integer furnitureSets;

    /**
     * 总租赁面积
     */
    @ApiModelProperty(value = "总租赁面积")
    private String totalArea;

    /**
     * 单缴费周期租金
     */
    @ApiModelProperty(value = "单缴费周期租金")
    private Double leaseTermRent;

    /**
     * 押金
     */
    @ApiModelProperty(value = "押金")
    private Double deposit;

    /**
     * 押金标准code(1.1个月 2.2个月 3.3个月)
     */
    @ApiModelProperty(value = "押金标准code(1.1个月 2.2个月 3.3个月)")
    private String cashPledgeCode;

    /**
     * 押金标准名称
     */
    @ApiModelProperty(value = "押金标准名称")
    private String cashPledgeName;

    /**
     * 缴费周期code(01.月付 02.季度付 03.半年付 04.年付)
     */
    @ApiModelProperty(value = "缴费周期code(01.月付 02.季度付 03.半年付 04.年付)")
    private String paymentCycleCode;

    /**
     * 缴费周期名称
     */
    @ApiModelProperty(value = "缴费周期名称")
    private String paymentCycleName;

    /**
     * 缴费货币
     */
    @ApiModelProperty(value = "缴费货币")
    private String paymentCurrency;

    /**
     * 总缴费租金
     */
    @ApiModelProperty(value = "总缴费租金")
    private Double paymentRent;

    @ApiModelProperty(value = "缴费类型(1.比例 2.金额)")
    private String payType;

    /**
     * 交付日期
     */
    @ApiModelProperty(value = "交付日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    /**
     * 业务分类编号1
     */
    @ApiModelProperty(value = "业务分类编号1")
    private String businessTypeCode1;

    /**
     * 业务分类名称1
     */
    @ApiModelProperty(value = "业务分类名称1")
    private String businessTypeName1;

    /**
     * 业务分类编号2
     */
    @ApiModelProperty(value = "业务分类编号2(房屋：01散租，02趸租，03管理协议；04,06家具家电个人，05家具家电企业)")
    private String businessTypeCode2;

    /**
     * 业务分类名称2
     */
    @ApiModelProperty(value = "业务分类名称2")
    private String businessTypeName2;

    /**
     * 业务分类编号3
     */
    @ApiModelProperty(value = "业务分类编号3")
    private String businessTypeCode3;

    /**
     * 业务分类名称3
     */
    @ApiModelProperty(value = "业务分类名称3")
    private String businessTypeName3;

    /**
     * 业务唯一标识
     */
    @ApiModelProperty(value = "业务唯一标识")
    private String businessId;

    /**
     * 合同终止时间
     */
    @ApiModelProperty(value = "合同终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date terminationDate;

    /**
     * 变更次数
     */
    @ApiModelProperty(value = "变更次数")
    private Integer changeNum;

    /**
     * 最后变更日期
     */
    @ApiModelProperty(value = "最后变更日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date changeLastDate;

    /**
     * 变更状态
     */
    @ApiModelProperty(value = "变更状态")
    private String changeCheckStatus;

    @ApiModelProperty("乙方缴费比例")
    private String secondPayPercent;

    @ApiModelProperty("丙方缴费比例")
    private String thirdPayPercent;

    @ApiModelProperty("乙方缴费金额")
    private String secondPayMoney;

    @ApiModelProperty("丙方缴费金额")
    private String thirdPayMoney;

    @ApiModelProperty("是否报盘(0.否 1.是)")
    private String offer;

    @ApiModelProperty("是否开启公积金(0.关闭 1.开启)")
    private String isProvidentFund;

    @ApiModelProperty("公积金状态(0.停用 1.启用)")
    private String providentFundState;

    @ApiModelProperty("公积金停用类型(0.中止提取 1.终止事项)")
    private String providentFundType;

    @ApiModelProperty("是否报送热力公司(0.否 1.是)")
    private String submitHeatingCompany;

    @ApiModelProperty("合同通过日期（签约日期）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date passTime;

    @ApiModelProperty("租金标准code(01.公租租金标准 02.人才租金标准 03.市场租金标准)")
    private String rentStandardCode;

    @ApiModelProperty("租金标准名称")
    private String rentStandardName;

    @ApiModelProperty("基础租金计算方式code")
    private String baseRentCalculate;

    @ApiModelProperty("基础租金计算方式名称")
    private String baseRentCalculateName;

    /**
     * 是否欠缴(是、否)
     */
    @ApiModelProperty(value = "是否欠缴(是、否)")
    private String isOverdue;

    @ApiModelProperty("合同后付款(0.否 1.是)")
    private String afterContractPay;

    @ApiModelProperty("甲方账户id")
    private String firstAccountId;

    @ApiModelProperty("甲方账户名称")
    private String firstAccountName;

    @ApiModelProperty("甲方开户行")
    private String firstBankNameCode;

    @ApiModelProperty("甲方开户行名称")
    private String firstBankName;

    @ApiModelProperty("五期合同（01正常、02候审期、03过渡期、04占用期、05欠费）")
    @McpDictPoint(dictCode = "PAHSE_FIVE",overTransCopyTo = "pahseFive")
    private String pahseFive;

    @ApiModelProperty("签约结果id")
    private String signId;

    @ApiModelProperty("合同信息扩展")
    private String contractExtend;

    /**
     * 签约房间类型
     */
    @ApiModelProperty(value = "签约房间类型")
    private String roomType;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "签约标识")
    private String ext1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "缩租减少面积")
    private String ext2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String ext3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty(value = "扩展字段4")
    private String ext4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty(value = "扩展字段5")
    private String ext5;

    /**
     * 删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String remark;

    @ApiModelProperty(value = "租期")
    private String leaseTerm;

    @ApiModelProperty(value = "租管理协议是否集体变更,1集体变更")
    private String isChangeUnitManagement;

    @ApiModelProperty(value = "需要集体变更的公司编号")
    private String customerNo;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String description;

    /**
     * 押金标准金额
     */
    @ApiModelProperty(value = "押金标准金额")
    private String cashPledgeMoney;

    /**
     * 押金支付方式(1.企业付 2.个人付 3.比例支付)
     */
    @ApiModelProperty(value = "押金支付方式(1.企业付 2.个人付 3.比例支付)")
    private String cashPledgePayMode;

    /**
     * 押金缴费类型(1.比例 2.金额)
     */
    @ApiModelProperty(value = "押金缴费类型(1.比例 2.金额)")
    private String cashPledgePayType;

    /**
     * 押金缴费比例
     */
    @ApiModelProperty(value = "押金缴费比例")
    private String cashPledgePayPercent;

    /**
     * 押金缴费金额
     */
    @ApiModelProperty(value = "押金缴费金额")
    private String cashPledgePayMoney;

    /**
     * 是否有增值服务费(0.否 1.是)
     */
    @ApiModelProperty(value = "是否有增值服务费(0.否 1.是)")
    private String serviceCharge;

    /**
     * 增值服务费租金标准类型(1.比例 2.金额)
     */
    @ApiModelProperty(value = "增值服务费租金标准类型(1.比例 2.金额)")
    private String serviceChargeType;

    /**
     * 增值服务费标准(月租金百分比)
     */
    @ApiModelProperty(value = "增值服务费标准(月租金百分比)")
    private String serviceChargePercent;

    /**
     * 增值服务费标准(金额)
     */
    @ApiModelProperty(value = "增值服务费标准(金额)")
    private String serviceChargeMoney;

    /**
     * 是否有递增(0.否 1.是)
     */
    @ApiModelProperty(value = "是否有递增(0.否 1.是)")
    private String increase;

    /**
     * 递增类型(1.租金 2.租金+增值服务费 3.增值服务费)
     */
    @ApiModelProperty(value = "递增类型(1.租金 2.租金+增值服务费 3.增值服务费)")
    private String increaseType;

    /**
     * 递增json
     */
    @ApiModelProperty(value = "递增json")
    private String increaseJson;

    /**
     * 支付方式(1.企业付 2.个人付 3.比例支付)
     */
    @ApiModelProperty(value = "支付方式(1.企业付 2.个人付 3.比例支付)")
    private String payMode;

    /**
     * 支付标准(1.统一标准 2.非同一标准)
     */
    @ApiModelProperty(value = "支付标准(1.统一标准 2.非同一标准)")
    private String payStandard;

    /**
     * 增值服务费缴费类型(1.比例 2.金额)
     */
    @ApiModelProperty(value = "增值服务费缴费类型(1.比例 2.金额)")
    private String serviceChargePayType;

    /**
     * 增值服务费缴费比例
     */
    @ApiModelProperty(value = "增值服务费缴费比例")
    private String serviceChargePayPercent;

    /**
     * 增值服务费缴费金额
     */
    @ApiModelProperty(value = "增值服务费缴费金额")
    private String serviceChargePayMoney;

    /**
     * 租赁依据
     */
    @ApiModelProperty(value = "租赁依据")
    private String according;

    /**
     * 付款银行账户id
     */
    @ApiModelProperty(value = "付款银行账户id")
    private String payingBankAccountId;

    /**
     * 付款银行账户名称
     */
    @ApiModelProperty(value = "付款银行账户名称")
    private String payingBankAccountName;

    /**
     * 付款银行开户行
     */
    @ApiModelProperty(value = "付款银行开户行")
    private String payingBankNameCode;

    /**
     * 付款银行开户行名称
     */
    @ApiModelProperty(value = "付款银行开户行名称")
    private String payingBankName;

    /**
     * 付款银行开户行支行
     */
    @ApiModelProperty(value = "付款银行开户行支行")
    private String payingSubBankNameCode;

    /**
     * 付款银行开户行支行名称
     */
    @ApiModelProperty(value = "付款银行开户行支行名称")
    private String payingSubBankName;

    /**
     * 合同属性map（接参）
     */
    @ApiModelProperty(value = "合同属性map（接参）",required = true)
    private Map<String,Object> contractAttr;

    /**
     * 合同属性list(返回结果)
     */
    @ApiModelProperty(value = "合同属性list(返回结果)",required = false)
    private List<BbctContractAttrVo> attrVoList;

    /**
     * 创建合同时传入此参数
     */
    @ApiModelProperty(value = "合同参与者信息",required = true)
    private List<BbctContractSignerVo> userList;

    /**
     * 创建合同时传入此参数
     */
    @ApiModelProperty(value = "产品信息")
    private List<BbctContractSubjectMatterVo> subjectMatterList;

    @ApiModelProperty(value = "来源合同编号")
    private String sourceContractCode;

    @ApiModelProperty(value = "合同类型(1.普通合同 2.补充合同 3.续租合同 4.调换房合同 5.主承租人合同变更合同 6.购房补充协议 7.趸租补充协议)")
    private String contractType;

    @ApiModelProperty(value = "面积类型(1.建筑面积 2.套内建筑面积)")
    private String areaType;

    @ApiModelProperty(value = "纸质签约状态:1为纸质签约")
    private String paperSigningStatus;

    @ApiModelProperty("乙方缴费比例(返回参数)")
    private String secondPayPercentMltiply;

    @ApiModelProperty("丙方缴费比例(返回参数)")
    private String thirdPayPercentMltiply;

    @ApiModelProperty("附件名前缀")
    private String attachmentPrefix;

    @ApiModelProperty("预测或实测")
    private String measurement;
}
