package com.bonc.ioc.bzf.business.payment.result;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 现金线下收款请求 子类
 */
@Data
public class CashOfflineCollectionSubRequest {

    private String tenantId;//	租户ID	String	是	20
    private String contractId;//	合同ID	String	是	20
    private String billId;//	账单ID	String	是	20
    private String depositNo;//	存款明细单号	String	是	50	每个租户现金支付的单号
    private BigDecimal cashAmout;//	现金金额	BigDecimal 是	13.4
    private String cashTime	;//支付时间	Time	是		租户支付现金的时间


    //2024年12月冲刺增加字段
    private String billChargeSubject;//	账单的计费科目
    private String chargeSubjectPeriod;//	账单的期次
    private BigDecimal billPayableAmount;//	账单待缴金额

}
