package com.bonc.ioc.bzf.business.payment.config;

import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

@Configuration
@Slf4j
public class FeignConfiguration implements feign.RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        log.info("-------RequestInterceptor----------"+getToken());
        ServletRequestAttributes attributes = (ServletRequestAttributes)
                RequestContextHolder.getRequestAttributes();
//        //开启多线程调用的时候，线程并没有request,防止这里报空指针
//        if(attributes==null){
//            return;
//        }
        if(attributes!=null) {
            HttpServletRequest request = attributes.getRequest();
            Enumeration<String> headerNames = request.getHeaderNames();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String name = headerNames.nextElement();
                    String values = request.getHeader(name);
                    // 跳过 content-length
                    if (name.equals("content-length")) {
                        continue;
                    }
                    requestTemplate.header(name, values);
                }
            }
        }
//        log.info("-------22RequestInterceptor222----------"+getToken());
        //向requestTemplate里面放入一个token，这个token是程序里自定义通过setToken设置的token
        if (StringUtils.isNotEmpty(getToken())) {
//            log.info("-------传递tokenId----------:"+getToken());
            requestTemplate.header("tokenId",getToken());
        }
    }

    private String token;

    public String getToken(){
        return token;
    }
    public void setToken(String token){
        this.token = token;
    }

}