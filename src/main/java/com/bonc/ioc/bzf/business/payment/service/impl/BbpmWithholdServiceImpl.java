package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.bzf.business.payment.dao.BbpmBillManagementMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmBillManagementEntity;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BbsigningFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.FaceMdMapResult;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmWithholdService;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 账单管理(来源业财)v3.0 服务类实现
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmWithholdServiceImpl extends McpBaseServiceImpl<BbpmBillManagementEntity> implements IBbpmWithholdService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmBillManagementMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmBillManagementService baseService;

    /**
     * Bean 字典转化
     */
    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;
    @Resource
    private BfipSettlementFeignClient settlementFeignClient;

    @Resource
    private BbsigningFeignClient bbsigningFeignClient;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Value("${export.maxSize}")
    private Integer exportMaxSize;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;


    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmBillManagementPageResultVo>> selectByPageRecordList(BbpmWithholdListPageVo vo) {
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setCurrent(Integer.valueOf(vo.getPageNumber()+""));

        ParentRequest<BbpmWithholdListPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

         //请求业财接口
        //重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = listByContract(parentRequest);

//        if(pageResult != null && pageResult.getRows() != null){
//            log.info("pageResult.getRows():"+pageResult.getRows().size());
//        }else{
//            log.info("pageResult.getRows():空");
//        }

        return pageResult;
    }

    @Override
    public PageResult<List<BbpmBillManagementPageResultVo>> listByContract(ParentRequest parentRequest){
        log.info("3.21.1手动代扣-未缴账单列表查询接口请求参数:{}",JSONObject.toJSONString(parentRequest));
        //请求业财接口
        String responseBody = null;
        if (yecaiFeign){
            responseBody = bfipSettlementFeignClient.list(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/withhold/list", parentRequest);
        }
        log.info("3.21.1手动代扣-未缴账单列表查询接口返回:{}",responseBody);
        //重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = resetBill(responseBody);
        return pageResult;
    }
    public PageResult<List<BbpmBillManagementPageResultVo>> resetBill(String responseBody){
        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }

        FaceHttpResultTwo<BbpmBillManagementPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            throw new McpException("*提示:"+billListResultFaceHttpResultTwo.getMessage());
        }

        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(null);
        }
        List<BbpmBillManagementPageResultVo> billListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(),BbpmBillManagementPageResultVo.class);

        List<BbpmBillManagementPageResultVo> result = new ArrayList<>();

        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
//        for(BbpmBillManagementPageResultVo bill : billListResultList){
        for(int i=0;i<billListResultList.size();i++){
            BbpmBillManagementPageResultVo bill = billListResultList.get(i);
//            bbpmBillManagementPageResultVo.setBillId(bill.getBillCode()+"");        +"-"+UUID.randomUUID().toString().replace("-", "")
            bill.setBillNo(bill.getBillId());
            bill.setContractNo(bill.getContractCode());
            //账单周期相关  第X期（YYYY/MM/DD - YYYY/MM/DD）
            bill.setBillCycle("第"+bill.getChargeSubjectPeriod()+"期("+bill.getChargeSubjectBeginDate()+"至"+bill.getChargeSubjectEndDate()+")");
           //字典值
            bill.setBillChargeSubjectName(bill.getBillChargeSubject());
            bill.setBillStatusName(bill.getBillStatus());
            bill.setChargeStandardCnyName(bill.getChargeStandardCny());
            bill.setChargeCycleName(bill.getChargeCycle());
            bill.setIsProvisionName(bill.getIsProvision());
            bill.setChargeTypeName(bill.getChargeType());
            bill.setStatusName(bill.getStatus());
            bill.setAccountStatusName(bill.getAccountStatus());
            bill.setInvoicingStatusName(bill.getInvoicingStatus());
            bill.setBillPayStatusName(bill.getBillPayStatus());

            result.add(bill);
        }

        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
//        log.info("重新设置值后的result大小:"+result.size());
        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(),result);
    }

    /**
     * 3.21.3 手动代扣-报盘记录查询
     *
     * @param vo
     * @return
     */
    @Override
    public PageResult<List<BbpmWithholdRecordPageResultVo>> selectByPageRecordRecord(BbpmWithholdRecordPageVo vo) {
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setCurrent(Integer.valueOf(vo.getPageNumber()+""));
        ParentRequest<BbpmWithholdRecordPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        log.info("3.21.3手动代扣-报盘记录查询接口请求参数:{}", JSONObject.toJSONString(parentRequest));
        //请求业财接口
        String responseBody = null;
        if (yecaiFeign){
            responseBody = bfipSettlementFeignClient.record(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/withhold/record", parentRequest);
        }
        log.info("3.21.3手动代扣-报盘记录查询接口返回: {}",responseBody);

        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }
        FaceHttpResultTwo<BbpmWithholdRecordPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            throw new McpException("*提示:"+billListResultFaceHttpResultTwo.getMessage());
        }
        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(null);
        }
        List<BbpmWithholdRecordPageResultVo> billListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(),BbpmWithholdRecordPageResultVo.class);
        List<BbpmWithholdRecordPageResultVo> result = new ArrayList<>();
         for(int i=0;i<billListResultList.size();i++){
            BbpmWithholdRecordPageResultVo bill = billListResultList.get(i);
            result.add(bill);
        }
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);

        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(),result);

    }



    /**
     * 3.21.4 手动代扣-报盘明细查询
     * @param vo
     * @return
     */
    @Override
    public PageResult<List<BbpmWithholdDetailPageResultVo>> selectByPageRecordDetail(BbpmWithholdDetailPageVo vo) {
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        vo.setSize(Integer.valueOf(vo.getPageSize()+""));
        vo.setCurrent(Integer.valueOf(vo.getPageNumber()+""));
        ParentRequest<BbpmWithholdDetailPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        log.info("3.21.4手动代扣-报盘明细查询接口请求参数:{}", JSONObject.toJSONString(parentRequest));
        //请求业财接口
        String responseBody = null;
        if (yecaiFeign){
            responseBody = bfipSettlementFeignClient.detail(parentRequest);
        }else{
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/withhold/detail", parentRequest);
        }
        log.info("3.21.4手动代扣-报盘明细查询接口返回: {}",responseBody);

        if(StringUtils.isBlank(responseBody)){
            return new PageResult<>(null);
        }
        FaceHttpResultTwo<BbpmWithholdDetailPageResultVo> billListResultFaceHttpResultTwo = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceHttpResultTwo.class);

        if(!("00000").equals(billListResultFaceHttpResultTwo.getCode())){
            throw new McpException("*提示:"+billListResultFaceHttpResultTwo.getMessage());
        }
        if(billListResultFaceHttpResultTwo.getData() == null || billListResultFaceHttpResultTwo.getData().getRecords() == null){
            return new PageResult<>(null);
        }
        List<BbpmWithholdDetailPageResultVo> billListResultList = JSON.parseArray(billListResultFaceHttpResultTwo.getData().getRecords().toString(),BbpmWithholdDetailPageResultVo.class);
        List<BbpmWithholdDetailPageResultVo> result = new ArrayList<>();
         for(int i=0;i<billListResultList.size();i++){
             BbpmWithholdDetailPageResultVo bill = billListResultList.get(i);
             bill.setBillCycle("第"+bill.getChargeSubjectPeriod()+"期("+bill.getChargeSubjectBeginDate()+"至"+bill.getChargeSubjectEndDate()+")");
             //字典值
             bill.setBillChargeSubjectName(bill.getBillChargeSubject());
             result.add(bill);
        }
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);

        return new PageResult<>(billListResultFaceHttpResultTwo.getData().getTotal(),result);
    }

    /**
     * 3.21.5 手动代扣-报盘明细下载
     * @param vo
     * @param httpServletResponse
     */
    @Override
    public void exceldownload(ExcelDownloadV2Vo vo, HttpServletResponse httpServletResponse) {
        //拼接请求参数
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<ExcelDownloadV2Vo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);

        log.info("3.21.5手动代扣-报盘明细下载接口请求参数json:"+JSONObject.toJSONString(parentRequest));
        Response response = bfipSettlementFeignClient.exceldownloadV2(parentRequest);
        log.info("3.21.5手动代扣-报盘明细下载接口请求返回状态:"+response.status());

        InputStream inputStream = null;
        OutputStream out = null;
        try {
            Map<String, Collection<String>> header = response.headers();
            header.forEach((key,values)->{
                log.info("3.21.5手动代扣-报盘明细下载接口header信息"+key+":"+values);
            });

            Collection<String> contentDispositions  = header.get("content-disposition");
            Collection<String> contentTypes = header.get("content-type");
            String contentType = "";
            if(contentTypes!=null && contentTypes.size() >0){
                contentType =  contentTypes.toArray()[0].toString();
            }

            if(!contentType.contains("application/vnd.ms-excel")){
                String body = response.body().toString();
                if(StringUtils.isBlank(body)){
                    throw new McpException("body为空,content-length:"+header.get("content-length").toArray()[0].toString());
                }
                log.info("3.21.5手动代扣-报盘明细下载接口下载失败后body信息:"+body);
                FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(body), FaceMdMapResult.class);
                throw new McpException(faceMdMapResult!=null ? ("*提示:"+ faceMdMapResult.getMessage()) : "body为空");
            }

            httpServletResponse.setContentType(contentType);
            if(contentDispositions!=null && contentDispositions.size()>0) {
                String _tmp = contentDispositions.toArray()[0].toString();
                httpServletResponse.setHeader("Content-Disposition", _tmp);
            }
            out = httpServletResponse.getOutputStream();  // new FileOutputStream(new File("e:\\test.jpg"));
            inputStream =response.body().asInputStream();
            byte[] buffer = new byte[2048];
            int i = -1;
            while ((i = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, i);
            }
            out.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            if(out!=null){
                try {
                    out.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            if(inputStream!=null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


}
