package com.bonc.ioc.bzf.business.payment.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 报盘记录查询 实体类
 *
 * <AUTHOR>
 * @date 2024-02-23
 * @change 2024-02-23 by binghong.tang for init
 */
@TableName("bbpm_withhold_record")
@ApiModel(value="BbpmWithholdRecordEntity对象", description="报盘记录查询")
public class BbpmWithholdRecordEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_BATCH_NO = "batch_no";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_TENANT_NUM = "tenant_num";
    public static final String FIELD_BILL_TOTAL_NUM = "bill_total_num";
    public static final String FIELD_BILL_SUCCESS_NUM = "bill_success_num";
    public static final String FIELD_BILL_FAILED_NUM = "bill_failed_num";
    public static final String FIELD_BILL_DEAING_NUM = "bill_deaing_num";
    public static final String FIELD_APPLICANT = "applicant";
    public static final String FIELD_APPLY_TIME = "apply_time";
    public static final String FIELD_BIZ_TYPE = "biz_type";

    /**
     * 申请批次
     */
    @ApiModelProperty(value = "申请批次")
                                @TableId(value = "batch_no", type = IdType.ASSIGN_UUID)
                                  private String batchNo;

    /**
     * 运营项目名称
     */
    @ApiModelProperty(value = "运营项目名称")
                            private String projectName;

    /**
     * 报盘人数
     */
    @ApiModelProperty(value = "报盘人数")
                            private Long tenantNum;

    /**
     * 账单共计条数
     */
    @ApiModelProperty(value = "账单共计条数")
                            private Long billTotalNum;

    /**
     * 成功账单
     */
    @ApiModelProperty(value = "成功账单")
                            private Long billSuccessNum;

    /**
     * 失败账单
     */
    @ApiModelProperty(value = "失败账单")
                            private Long billFailedNum;

    /**
     * 支付中账单
     */
    @ApiModelProperty(value = "支付中账单")
                            private Long billDeaingNum;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
                            private String applicant;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
                            private String applyTime;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
                            private String bizType;

    /**
     * @return 申请批次
     */
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * @return 运营项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 报盘人数
     */
    public Long getTenantNum() {
        return tenantNum;
    }

    public void setTenantNum(Long tenantNum) {
        this.tenantNum = tenantNum;
    }

    /**
     * @return 账单共计条数
     */
    public Long getBillTotalNum() {
        return billTotalNum;
    }

    public void setBillTotalNum(Long billTotalNum) {
        this.billTotalNum = billTotalNum;
    }

    /**
     * @return 成功账单
     */
    public Long getBillSuccessNum() {
        return billSuccessNum;
    }

    public void setBillSuccessNum(Long billSuccessNum) {
        this.billSuccessNum = billSuccessNum;
    }

    /**
     * @return 失败账单
     */
    public Long getBillFailedNum() {
        return billFailedNum;
    }

    public void setBillFailedNum(Long billFailedNum) {
        this.billFailedNum = billFailedNum;
    }

    /**
     * @return 支付中账单
     */
    public Long getBillDeaingNum() {
        return billDeaingNum;
    }

    public void setBillDeaingNum(Long billDeaingNum) {
        this.billDeaingNum = billDeaingNum;
    }

    /**
     * @return 申请人
     */
    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    /**
     * @return 申请时间
     */
    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    /**
     * @return 业务类型
     */
    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

      @Override
    public String toString() {
        return "BbpmWithholdRecordEntity{" +
            "batchNo=" + batchNo +
            ", projectName=" + projectName +
            ", tenantNum=" + tenantNum +
            ", billTotalNum=" + billTotalNum +
            ", billSuccessNum=" + billSuccessNum +
            ", billFailedNum=" + billFailedNum +
            ", billDeaingNum=" + billDeaingNum +
            ", applicant=" + applicant +
            ", applyTime=" + applyTime +
            ", bizType=" + bizType +
        "}";
    }
}