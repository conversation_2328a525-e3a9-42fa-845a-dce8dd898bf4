package com.bonc.ioc.bzf.business.payment.service.impl;

import com.bonc.ioc.bzf.business.payment.entity.BbpmMainLesseeExcelEntity;
import com.bonc.ioc.bzf.business.payment.dao.BbpmMainLesseeExcelMapper;
import com.bonc.ioc.bzf.business.payment.service.IBbpmMainLesseeExcelService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.payment.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 合同变更-主承租人导入参数日志表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-11-01
 * @change 2023-11-01 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmMainLesseeExcelServiceImpl extends McpBaseServiceImpl<BbpmMainLesseeExcelEntity> implements IBbpmMainLesseeExcelService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmMainLesseeExcelMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmMainLesseeExcelService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmMainLesseeExcelVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmMainLesseeExcelEntity entity = new BbpmMainLesseeExcelEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setId(null);
        if(!baseService.insert(entity)) {
            log.error("合同变更-主承租人导入参数日志表新增失败:" + entity.toString());
            throw new McpException("合同变更-主承租人导入参数日志表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getId(),1)) {
                log.error("合同变更-主承租人导入参数日志表新增后保存历史失败:" + entity.toString());
                throw new McpException("合同变更-主承租人导入参数日志表新增后保存历史失败");
            }

            log.debug("合同变更-主承租人导入参数日志表新增成功:"+entity.getId());
            return entity.getId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmMainLesseeExcelVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmMainLesseeExcelEntity> entityList = new ArrayList<>();
        for (BbpmMainLesseeExcelVo item:voList) {
            BbpmMainLesseeExcelEntity entity = new BbpmMainLesseeExcelEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmMainLesseeExcelEntity item:entityList){
            item.setId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("合同变更-主承租人导入参数日志表新增失败");
            throw new McpException("合同变更-主承租人导入参数日志表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmMainLesseeExcelEntity::getId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("合同变更-主承租人导入参数日志表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("合同变更-主承租人导入参数日志表批量新增后保存历史失败");
            }

            log.debug("合同变更-主承租人导入参数日志表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String id) {
        if(!StringUtils.isEmpty(id)) {
            if(!baseService.saveOperationHisById(id,3)) {
                log.error("合同变更-主承租人导入参数日志表删除后保存历史失败:" + id);
                throw new McpException("合同变更-主承租人导入参数日志表删除后保存历史失败");
            }

            if(!baseService.removeById(id)) {
                log.error("合同变更-主承租人导入参数日志表删除失败");
                throw new McpException("合同变更-主承租人导入参数日志表删除失败"+id);
            }
        } else {
            throw new McpException("合同变更-主承租人导入参数日志表删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param idList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> idList) {
        if(!CollectionUtils.isEmpty(idList)) {
            int oldSize = idList.size();
            idList = idList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(idList) || oldSize != idList.size()) {
                throw new McpException("合同变更-主承租人导入参数日志表批量删除失败 存在主键id为空的记录"+StringUtils.join(idList));
            }

            if(!baseService.saveOperationHisByIds(idList,3)) {
                log.error("合同变更-主承租人导入参数日志表批量删除后保存历史失败:" + StringUtils.join(idList));
                throw new McpException("合同变更-主承租人导入参数日志表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(idList)) {
                log.error("合同变更-主承租人导入参数日志表批量删除失败");
                throw new McpException("合同变更-主承租人导入参数日志表批量删除失败"+StringUtils.join(idList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的合同变更-主承租人导入参数日志表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmMainLesseeExcelVo vo) {
        if(vo != null) {
            BbpmMainLesseeExcelEntity entity = new BbpmMainLesseeExcelEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getId())) {
                throw new McpException("合同变更-主承租人导入参数日志表更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("合同变更-主承租人导入参数日志表更新失败");
                throw new McpException("合同变更-主承租人导入参数日志表更新失败"+entity.getId());
            } else {
                if(!baseService.saveOperationHisById(entity.getId(),2)) {
                    log.error("合同变更-主承租人导入参数日志表更新后保存历史失败:" + entity.getId());
                    throw new McpException("合同变更-主承租人导入参数日志表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更-主承租人导入参数日志表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的合同变更-主承租人导入参数日志表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmMainLesseeExcelVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmMainLesseeExcelEntity> entityList = new ArrayList<>();

            for (BbpmMainLesseeExcelVo item:voList){
                BbpmMainLesseeExcelEntity entity = new BbpmMainLesseeExcelEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("合同变更-主承租人导入参数日志表批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("合同变更-主承租人导入参数日志表批量更新失败");
                throw new McpException("合同变更-主承租人导入参数日志表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getId())).map(BbpmMainLesseeExcelEntity::getId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("合同变更-主承租人导入参数日志表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更-主承租人导入参数日志表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的合同变更-主承租人导入参数日志表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmMainLesseeExcelVo vo) {
        if(vo != null) {
            BbpmMainLesseeExcelEntity entity = new BbpmMainLesseeExcelEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("合同变更-主承租人导入参数日志表保存失败");
                throw new McpException("合同变更-主承租人导入参数日志表保存失败"+entity.getId());
            } else {
                if(!baseService.saveOperationHisById(entity.getId(),4)) {
                    log.error("合同变更-主承租人导入参数日志表保存后保存历史失败:" + entity.getId());
                    throw new McpException("合同变更-主承租人导入参数日志表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("合同变更-主承租人导入参数日志表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的合同变更-主承租人导入参数日志表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmMainLesseeExcelVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmMainLesseeExcelEntity> entityList = new ArrayList<>();

            for (BbpmMainLesseeExcelVo item:voList){
                BbpmMainLesseeExcelEntity entity = new BbpmMainLesseeExcelEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("合同变更-主承租人导入参数日志表批量保存失败");
                throw new McpException("合同变更-主承租人导入参数日志表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getId())).map(BbpmMainLesseeExcelEntity::getId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("合同变更-主承租人导入参数日志表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("合同变更-主承租人导入参数日志表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmMainLesseeExcelVo selectByIdRecord(String id) {
        BbpmMainLesseeExcelVo vo = new BbpmMainLesseeExcelVo();

        if(!StringUtils.isEmpty(id)) {
            BbpmMainLesseeExcelEntity entity = baseService.selectById(id);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-11-01
     * @change
     * 2023-11-01 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmMainLesseeExcelPageResultVo>> selectByPageRecord(BbpmMainLesseeExcelPageVo vo) {
        List<BbpmMainLesseeExcelPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
