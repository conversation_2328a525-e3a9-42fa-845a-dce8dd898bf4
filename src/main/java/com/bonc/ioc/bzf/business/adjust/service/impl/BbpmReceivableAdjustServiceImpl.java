package com.bonc.ioc.bzf.business.adjust.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.adjust.dao.BbpmReceivableAdjustBillMapper;
import com.bonc.ioc.bzf.business.adjust.dao.BbpmReceivableAdjustExamineMapper;
import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustBillEntity;
import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustEntity;
import com.bonc.ioc.bzf.business.adjust.dao.BbpmReceivableAdjustMapper;
import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustExamineEntity;
import com.bonc.ioc.bzf.business.adjust.service.IBbpmReceivableAdjustBillService;
import com.bonc.ioc.bzf.business.adjust.service.IBbpmReceivableAdjustExamineService;
import com.bonc.ioc.bzf.business.adjust.service.IBbpmReceivableAdjustService;
import com.bonc.ioc.bzf.business.adjust.utils.RequestUtil;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BmsFeignClient;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.service.IBbpmBillManagementService;
import com.bonc.ioc.bzf.business.payment.service.IChangeContractService;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserReq;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceGetOneUserResp;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserAuthReq;
import com.sinovatech.rd.bms.api.user.vo.BmsUserRpcServiceUserAuthResp;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import org.springframework.beans.BeanUtils;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 应收调整 服务类实现
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@Slf4j
@Service
public class BbpmReceivableAdjustServiceImpl extends McpBaseServiceImpl<BbpmReceivableAdjustEntity> implements IBbpmReceivableAdjustService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmReceivableAdjustMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmReceivableAdjustService baseService;

    @Autowired
    private BmsFeignClient bmsFeignClient;

    @Resource
    private IBbpmReceivableAdjustExamineService examineService;

    @Resource
    private IBbpmReceivableAdjustBillService billService;

    @Resource
    private BbpmReceivableAdjustBillMapper billMapper;

    @Resource
    private BbpmReceivableAdjustExamineMapper examineMapper;

    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private IChangeContractService iChangeContractService;

    @Resource
    private IBbpmBillManagementService billManagementService;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmReceivableAdjustVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmReceivableAdjustEntity entity = new BbpmReceivableAdjustEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setId(null);
        if(!baseService.insert(entity)) {
            log.error("应收调整新增失败:" + entity.toString());
            throw new McpException("应收调整新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getId(),1)) {
                log.error("应收调整新增后保存历史失败:" + entity.toString());
                throw new McpException("应收调整新增后保存历史失败");
            }

            log.debug("应收调整新增成功:"+entity.getId());
            return entity.getId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmReceivableAdjustVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmReceivableAdjustEntity> entityList = new ArrayList<>();
        for (BbpmReceivableAdjustVo item:voList) {
            BbpmReceivableAdjustEntity entity = new BbpmReceivableAdjustEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmReceivableAdjustEntity item:entityList){
            item.setId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("应收调整新增失败");
            throw new McpException("应收调整新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmReceivableAdjustEntity::getId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("应收调整批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("应收调整批量新增后保存历史失败");
            }

            log.debug("应收调整新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String id) {
        if(!StringUtils.isEmpty(id)) {
            if(!baseService.saveOperationHisById(id,3)) {
                log.error("应收调整删除后保存历史失败:" + id);
                throw new McpException("应收调整删除后保存历史失败");
            }

            BbpmReceivableAdjustEntity entity = new BbpmReceivableAdjustEntity();
            entity.setId(id);
            entity.setDelFlag(0);
            if(!baseService.updateById(entity)) {
                log.error("应收调整删除失败");
                throw new McpException("应收调整删除失败"+id);
            }
        } else {
            throw new McpException("应收调整删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param idList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> idList) {
        if(!CollectionUtils.isEmpty(idList)) {
            int oldSize = idList.size();
            idList = idList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(idList) || oldSize != idList.size()) {
                throw new McpException("应收调整批量删除失败 存在主键id为空的记录"+StringUtils.join(idList));
            }

            if(!baseService.saveOperationHisByIds(idList,3)) {
                log.error("应收调整批量删除后保存历史失败:" + StringUtils.join(idList));
                throw new McpException("应收调整批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(idList)) {
                log.error("应收调整批量删除失败");
                throw new McpException("应收调整批量删除失败"+StringUtils.join(idList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的应收调整
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmReceivableAdjustVo vo) {
        if(vo != null) {
            BbpmReceivableAdjustEntity entity = new BbpmReceivableAdjustEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getId())) {
                throw new McpException("应收调整更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("应收调整更新失败");
                throw new McpException("应收调整更新失败"+entity.getId());
            } else {
                if(!baseService.saveOperationHisById(entity.getId(),2)) {
                    log.error("应收调整更新后保存历史失败:" + entity.getId());
                    throw new McpException("应收调整更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("应收调整更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的应收调整
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmReceivableAdjustVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReceivableAdjustEntity> entityList = new ArrayList<>();

            for (BbpmReceivableAdjustVo item:voList){
                BbpmReceivableAdjustEntity entity = new BbpmReceivableAdjustEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("应收调整批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("应收调整批量更新失败");
                throw new McpException("应收调整批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getId())).map(BbpmReceivableAdjustEntity::getId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("应收调整批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("应收调整批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应收调整
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String saveByIdRecord(BbpmReceivableAdjustVo vo) {
        if(vo != null) {
            // 校验合同编号是否重复--暂存或者提交的时候
            if(vo.getAdjustStatus() != null && (vo.getAdjustStatus().equals("0") || vo.getAdjustStatus().equals("1"))) {
                String[] contractNoList = baseMapper.selectContractForAdjustId(vo.getId(), vo.getContractNo());
                if (contractNoList != null && contractNoList.length > 0) {
                    throw new McpException("当前合同存在进行中的账单调整申请，不支持继续发起调整");
                }
            }
            BbpmReceivableAdjustEntity entity = new BbpmReceivableAdjustEntity();
            BeanUtils.copyProperties(vo, entity);

            if(vo.getId() == null || vo.getId().isEmpty()){
                // 创建调整单编号
                String tzdbh = "YSTZ";
                LocalDate currentDate = LocalDate.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                String formattedDate = currentDate.format(formatter);
                String tzbh = baseMapper.selectTzbh(formattedDate);
                if (tzbh == null) {
                    tzbh = "0";
                }
                int tzbhInt = Integer.parseInt(tzbh);
                tzbhInt++;
                String formattedNumber = "";
                if(tzbhInt > 9999){
                     formattedNumber = String.format("%05d", tzbhInt);
                }else {
                     formattedNumber = String.format("%04d", tzbhInt);
                }
                tzdbh = tzdbh + formattedDate + formattedNumber;
                entity.setAdjustNo(tzdbh);
                entity.setDelFlag(1);
                //只有新增时保存创建人信息
                try {
                    Request<BmsUserRpcServiceUserAuthReq> req = new Request<>();
                    BmsUserRpcServiceUserAuthReq data = new BmsUserRpcServiceUserAuthReq();
                    data.setToken(RequestUtil.getUserToken());
                    req.setData(data);
                    Response<BmsUserRpcServiceUserAuthResp> response = bmsFeignClient.userAuth(req);
                    if(response.isSuccess()){
                        entity.setCreateUserName(response.getData().getUserRealName());
                    }else{
                        entity.setCreateUserName("--");
                    }
                } catch (Exception e) {
                    log.error("调用服务bphc-bms-service获取创建人姓名失败");
                }
            }
            if(entity.getAdjustStatus() != null && (entity.getAdjustStatus().equals("0") || entity.getAdjustStatus().equals("1"))) {
                //暂存或者提交时，保存试算结果
                if(vo.getCalculationResultVo() != null){
                    String jsonStr = JSONObject.toJSONString(vo.getCalculationResultVo());
                    entity.setCalculationResults(jsonStr);
                }
            }
            if(!baseService.saveById(entity)) {
                log.error("应收调整保存失败");
                throw new McpException("应收调整保存失败"+entity.getId());
            } else {
                if(entity.getAdjustStatus() != null && (entity.getAdjustStatus().equals("0") || entity.getAdjustStatus().equals("1"))) {
                    // ++++++新增账单信息-开始+++++++
                    //先删除关联账单
                    billMapper.removeByAdjustId(entity.getId());
                    if (vo.getBillList() != null && vo.getBillList().size() > 0) {
                        for (BbpmReceivableAdjustBillEntity item : vo.getBillList()) {
                            item.setAdjustId(entity.getId());
                            if (!billService.insert(item)) {
                                log.error("应收调整账单表新增失败:" + item.toString());
                                throw new McpException("应收调整账单表新增失败");
                            } else {
                                if (!billService.saveOperationHisById(item.getId(), 1)) {
                                    log.error("应收调整账单表新增后保存历史失败:" + item.toString());
                                    throw new McpException("应收调整账单表新增后保存历史失败");
                                }

                                log.debug("应收调整账单表新增成功:" + item.getId());
                            }
                        }
                    }
                    // ++++++新增账单信息-结束+++++++
                }
                if(entity.getAdjustStatus() != null && !entity.getAdjustStatus().equals("") && !entity.getAdjustStatus().equals("0")){
                    // ++++++除暂存或者修改退款路径外，都保存审核记录+++++++
                    if(entity.getRefundType() == null || entity.getRefundType().equals("")){
                        BbpmReceivableAdjustExamineEntity examineEntity = new BbpmReceivableAdjustExamineEntity();
                        examineEntity.setAdjustId(entity.getId());
                        examineEntity.setExamineStatus(entity.getAdjustStatus());
                        examineEntity.setExamineDescribe(vo.getExamineDescribe());
                        try {
                            Request<BmsUserRpcServiceUserAuthReq> req = new Request<>();
                            BmsUserRpcServiceUserAuthReq data = new BmsUserRpcServiceUserAuthReq();
                            data.setToken(RequestUtil.getUserToken());
                            req.setData(data);
                            Response<BmsUserRpcServiceUserAuthResp> response = bmsFeignClient.userAuth(req);
                            if(response.isSuccess()){
                                examineEntity.setCreateUserName(response.getData().getUserRealName());
                            }else{
                                examineEntity.setCreateUserName("--");
                            }
                        } catch (Exception e) {
                            log.error("调用服务bphc-bms-service获取创建人姓名失败");
                        }
                        if(!examineService.insert(examineEntity)) {
                            log.error("应收调整审核表新增失败:" + examineEntity.toString());
                            throw new McpException("应收调整审核表新增失败");
                        }else {
                            if(!examineService.saveOperationHisById(examineEntity.getId(),1)) {
                                log.error("应收调整审核表新增后保存历史失败:" + examineEntity.toString());
                                throw new McpException("应收调整审核表新增后保存历史失败");
                            }

                            log.debug("应收调整审核表新增成功:"+examineEntity.getId());
                        }
                    }
                    if(entity.getAdjustStatus().equals("3")){
                        BbpmReceivableAdjustVo voForSh = baseMapper.selectByIdecord(entity.getId());
                        if(entity.getRefundType() == null || entity.getRefundType().equals("")){
                            //审核通过--推送工银,修改退款路径的时候不需要调用
                            CalculationParamVo calculation = new CalculationParamVo();
                            calculation.setProjectId(voForSh.getProjectId());
                            calculation.setChangeType("14");
                            ChargeableAdjustDTO chargeableAdjustDTO = new ChargeableAdjustDTO();
                            chargeableAdjustDTO.setDeductionType(voForSh.getAmountHandle());
                            chargeableAdjustDTO.setRefundChannel("1");
                            chargeableAdjustDTO.setAdjustBillDTOList(billMapper.selectByAdjustId(entity.getId()));
                            calculation.setChargeableAdjustDTO(chargeableAdjustDTO);
                            try {
                                billService.updateBill(calculation);
                            }catch (Exception e){
                                log.error("推送工银失败");
                                throw new McpException("*应收调整异常");
                            }
                        }
                        if(vo.getCalculationResultVo() != null && vo.getChangeId() == null){
                            BusinessGeneratePaymentByGXParamVo tkVo = new BusinessGeneratePaymentByGXParamVo();
                            tkVo.setContractCode(voForSh.getContractNo());
                            tkVo.setProjectId(voForSh.getProjectId());
                            BusinessGeneratePaymentByGXResultVo gxResultVo = new BusinessGeneratePaymentByGXResultVo();
                            if((entity.getRefundType() != null && (entity.getRefundType().equals("1") || entity.getRefundType().equals("3"))) || (vo.getCalculationResultVo().calculateTotalPersonalRefundAmount() != null && vo.getCalculationResultVo().calculateTotalPersonalRefundAmount().compareTo(BigDecimal.ZERO) > 0)){
                                //个人退款金额不为空
                                System.out.println(vo.getCalculationResultVo().calculateTotalPersonalRefundAmount());
                                tkVo.setName(voForSh.getAccountHolder());
                                tkVo.setCertNo(voForSh.getBankIdNumber());
                                tkVo.setBankAccountNo(voForSh.getBankCard());
                                tkVo.setBankProvinceName(voForSh.getOpeningProvinceName());
                                tkVo.setBankCityName(voForSh.getOpeningCityName());
                                tkVo.setBankName(voForSh.getOpeningBankName());
                                tkVo.setBankCode(voForSh.getOpeningBank());
                                tkVo.setBankBranchName(voForSh.getBankBranchName());
                                tkVo.setBankBranchCode(voForSh.getBankBranchCode());
                                tkVo.setIsOriginReturn((voForSh.getRefundPath() != null && voForSh.getRefundPath().equals("3")) ? "01":"02");
                                tkVo.setRegenerate(entity.getRefundType() == null || entity.getRefundType().equals("") ? "0" : "1");
                                tkVo.setSource("2");
                                tkVo.setOwner("02");
                                tkVo.setPaymentCode(entity.getExt1());
                                gxResultVo = iChangeContractService.businessGeneratePaymentByGX(tkVo);
                                BbpmReceivableAdjustEntity entityForGx = new BbpmReceivableAdjustEntity();
                                entityForGx.setId(entity.getId());
                                entityForGx.setRefundStatus("0");
                                entityForGx.setExt1(gxResultVo.getPaymentCode());
                                if(!baseService.updateById(entityForGx)) {
                                    log.error("应收调整初始化企业退款状态失败");
                                    throw new McpException("应收调整初始化企业退款状态失败"+entity.getId());
                                }
                            }
                            if((entity.getRefundType() != null && (entity.getRefundType().equals("2") || entity.getRefundType().equals("3"))) || (vo.getCalculationResultVo().calculateTotalCompanyRefundAmount() != null && vo.getCalculationResultVo().calculateTotalCompanyRefundAmount().compareTo(BigDecimal.ZERO) > 0)){
                                //公司退款金额不为空
                                System.out.println(vo.getCalculationResultVo().calculateTotalCompanyRefundAmount());
                                tkVo.setName(voForSh.getQyAccountHolder());
                                tkVo.setCertNo(voForSh.getQyBankIdNumber());
                                tkVo.setBankAccountNo(voForSh.getQyBankCard());
                                tkVo.setBankProvinceName(voForSh.getQyOpeningProvinceName());
                                tkVo.setBankCityName(voForSh.getQyOpeningCityName());
                                tkVo.setBankName(voForSh.getQyOpeningBankName());
                                tkVo.setBankCode(voForSh.getQyOpeningBank());
                                tkVo.setBankBranchName(voForSh.getQyBankBranchName());
                                tkVo.setBankBranchCode(voForSh.getQyBankBranchCode());
                                tkVo.setIsOriginReturn("02");   //企业没有原路退回
                                tkVo.setRegenerate(entity.getRefundType() == null || entity.getRefundType().equals("") ? "0" : "1");
                                tkVo.setSource("2");
                                tkVo.setOwner("01");
                                tkVo.setPaymentCode(entity.getExt2());
                                gxResultVo = iChangeContractService.businessGeneratePaymentByGX(tkVo);
                                BbpmReceivableAdjustEntity entityForGx = new BbpmReceivableAdjustEntity();
                                entityForGx.setId(entity.getId());
                                entityForGx.setExt3("3");
                                entityForGx.setExt2(gxResultVo.getPaymentCode());
                                if(!baseService.updateById(entityForGx)) {
                                    log.error("应收调整初始化企业退款状态失败");
                                    throw new McpException("应收调整初始化企业退款状态失败"+entity.getId());
                                }
                            }
//                            System.out.println(vo.getCalculationResultVo().calculateTotalPersonalRefundAmount());
//                            if((vo.getCalculationResultVo().calculateTotalPersonalRefundAmount() == null || vo.getCalculationResultVo().calculateTotalPersonalRefundAmount().compareTo(BigDecimal.ZERO) <= 0) && (vo.getCalculationResultVo().calculateTotalCompanyRefundAmount() == null || vo.getCalculationResultVo().calculateTotalCompanyRefundAmount().compareTo(BigDecimal.ZERO) <= 0)){
//                                throw new McpException("个人/企业没有可退款金额，请重新调整"+entity.getId());
//                            }
                        }
                    }

                }
                // ++++++新增审核记录+++++++
                if(!baseService.saveOperationHisById(entity.getId(),4)) {
                    log.error("应收调整保存后保存历史失败:" + entity.getId());
                    throw new McpException("应收调整保存后保存历史失败");
                }
                return entity.getId();
            }
        } else {
            throw new McpException("应收调整保存失败传入为空");
        }
    }

    @Override
    public String adjustmentImport(BbpmReceivableAdjustVo vo) {
        if(vo != null) {
            // 校验合同编号是否重复--暂存或者提交的时候
            if(vo.getAdjustStatus() != null && (vo.getAdjustStatus().equals("0") || vo.getAdjustStatus().equals("1"))) {
                String[] contractNoList = baseMapper.selectContractForAdjustId(vo.getId(), vo.getContractNo());
                if (contractNoList != null && contractNoList.length > 0) {
                    return "当前合同存在进行中的账单调整申请，不支持继续发起调整";
                }
            }
            BbpmReceivableAdjustEntity entity = new BbpmReceivableAdjustEntity();
            BeanUtils.copyProperties(vo, entity);

            if(vo.getId() == null || vo.getId().isEmpty()){
                // 创建调整单编号
                String tzdbh = "YSTZ";
                LocalDate currentDate = LocalDate.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                String formattedDate = currentDate.format(formatter);
                String tzbh = baseMapper.selectTzbh(formattedDate);
                if (tzbh == null) {
                    tzbh = "0";
                }
                int tzbhInt = Integer.parseInt(tzbh);
                tzbhInt++;
                String formattedNumber = "";
                if(tzbhInt > 9999){
                    formattedNumber = String.format("%05d", tzbhInt);
                }else {
                    formattedNumber = String.format("%04d", tzbhInt);
                }
                tzdbh = tzdbh + formattedDate + formattedNumber;
                entity.setAdjustNo(tzdbh);
                entity.setDelFlag(1);
            }
            if(vo.getCalculationResultVo() != null){
                String jsonStr = JSONObject.toJSONString(vo.getCalculationResultVo());
                entity.setCalculationResults(jsonStr);
            }
            if(!baseService.saveById(entity)) {
                log.error("应收调整保存失败");
                return "应收调整保存失败";
            } else {
                // ++++++新增账单信息-开始+++++++
                //先删除关联账单
//                billMapper.removeByAdjustId(entity.getId());
                if (vo.getBillList() != null && vo.getBillList().size() > 0) {
                    for (BbpmReceivableAdjustBillEntity item : vo.getBillList()) {
                        item.setAdjustId(entity.getId());
                        if (!billService.insert(item)) {
                            log.error("应收调整账单表新增失败:" + item.toString());
                            return "应收调整账单表新增失败";
                        } else {
                            if (!billService.saveOperationHisById(item.getId(), 1)) {
                                log.error("应收调整账单表新增后保存历史失败:" + item.toString());
                                return "应收调整账单表新增后保存历史失败";
                            }

                            log.debug("应收调整账单表新增成功:" + item.getId());
                        }
                    }
                }
                // ++++++新增账单信息-结束+++++++
                if(entity.getAdjustStatus() != null && !entity.getAdjustStatus().equals("") && !entity.getAdjustStatus().equals("0")){
                    if(entity.getAdjustStatus().equals("3")){
                        if(entity.getRefundType() == null || entity.getRefundType().equals("")){
                            //审核通过--推送工银,修改退款路径的时候不需要调用
                            CalculationParamVo calculation = new CalculationParamVo();
                            calculation.setProjectId(entity.getProjectId());
                            calculation.setChangeType("14");
                            ChargeableAdjustDTO chargeableAdjustDTO = new ChargeableAdjustDTO();
                            chargeableAdjustDTO.setDeductionType(entity.getAmountHandle() != null && !entity.getAmountHandle().equals("") ? entity.getAmountHandle() : "0");
                            chargeableAdjustDTO.setRefundChannel("1");
                            chargeableAdjustDTO.setAdjustBillDTOList(billMapper.selectByAdjustId(entity.getId()));
                            calculation.setChargeableAdjustDTO(chargeableAdjustDTO);
//                                billService.updateBill(calculation);
                            ChargeRespondVo<List<CalculationResultVo>> tzResult = null;
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                            ParentRequest<CalculationParamVo> parentRequest = new ParentRequest<>();
                            parentRequest.setTime(sdf.format(new Date()));
                            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
                            parentRequest.setData(calculation);
                            tzResult = bfipChargeFeignClient.updateBill(parentRequest);

                            if(vo.getCalculationResultVo() != null){
                                BusinessGeneratePaymentByGXParamVo tkVo = new BusinessGeneratePaymentByGXParamVo();
                                tkVo.setContractCode(entity.getContractNo());
                                tkVo.setProjectId(entity.getProjectId());
                                ChargeRespondVo<BusinessGeneratePaymentByGXResultVo> gxResultResult = null;
                                if(vo.getCalculationResultVo().calculateTotalPersonalRefundAmount() != null && vo.getCalculationResultVo().calculateTotalPersonalRefundAmount().compareTo(BigDecimal.ZERO) > 0){
                                    //个人退款金额不为空
                                    System.out.println(vo.getCalculationResultVo().calculateTotalPersonalRefundAmount());
                                    tkVo.setName(entity.getAccountHolder());
                                    tkVo.setCertNo(entity.getBankIdNumber());
                                    tkVo.setBankAccountNo(entity.getBankCard());
                                    tkVo.setBankProvinceName(entity.getOpeningProvinceName());
                                    tkVo.setBankCityName(entity.getOpeningCityName());
                                    tkVo.setBankName(entity.getOpeningBankName());
                                    tkVo.setBankCode(entity.getOpeningBank());
                                    tkVo.setBankBranchName(entity.getBankBranchName());
                                    tkVo.setBankBranchCode(entity.getBankBranchCode());
                                    tkVo.setIsOriginReturn("02");
                                    tkVo.setRegenerate("0");
                                    tkVo.setSource("2");
                                    tkVo.setOwner("02");
                                    ParentRequest<BusinessGeneratePaymentByGXParamVo> bankRequestVo = new ParentRequest<>();
                                    bankRequestVo.setData(tkVo);
                                    gxResultResult = bfipSettlementFeignClient.businessGeneratePaymentByGX(bankRequestVo);
                                    BbpmReceivableAdjustEntity entityForGx = new BbpmReceivableAdjustEntity();
                                    entityForGx.setId(entity.getId());
                                    entityForGx.setExt1(gxResultResult.getData().getPaymentCode());
                                    if (!"00000".equals(gxResultResult.getCode())) {
                                        //退款失败-设置为个人退款失败
                                        entityForGx.setRefundStatus("1");
                                    }else {
                                        //退款成功-设置为个人退款处理中
                                        entityForGx.setRefundStatus("0");
                                    }
                                    if(!baseService.updateById(entityForGx)) {
                                        log.error("应收调整初始化个人退款状态失败");
                                        return "应收调整初始化个人退款状态失败";
                                    }
                                }
                                if(vo.getCalculationResultVo().calculateTotalCompanyRefundAmount() != null && vo.getCalculationResultVo().calculateTotalCompanyRefundAmount().compareTo(BigDecimal.ZERO) > 0){
                                    //公司退款金额不为空
                                    System.out.println(vo.getCalculationResultVo().calculateTotalCompanyRefundAmount());
                                    tkVo.setName(entity.getQyAccountHolder());
                                    tkVo.setCertNo(entity.getQyBankIdNumber());
                                    tkVo.setBankAccountNo(entity.getQyBankCard());
                                    tkVo.setBankProvinceName(entity.getQyOpeningProvinceName());
                                    tkVo.setBankCityName(entity.getQyOpeningCityName());
                                    tkVo.setBankName(entity.getQyOpeningBankName());
                                    tkVo.setBankCode(entity.getQyOpeningBank());
                                    tkVo.setBankBranchName(entity.getQyBankBranchName());
                                    tkVo.setBankBranchCode(entity.getQyBankBranchCode());
                                    tkVo.setIsOriginReturn("02");   //企业没有原路退回
                                    tkVo.setRegenerate("0");
                                    tkVo.setSource("2");
                                    tkVo.setOwner("01");
                                    ParentRequest<BusinessGeneratePaymentByGXParamVo> bankRequestVo = new ParentRequest<>();
                                    bankRequestVo.setData(tkVo);
                                    gxResultResult = bfipSettlementFeignClient.businessGeneratePaymentByGX(bankRequestVo);
                                    BbpmReceivableAdjustEntity entityForGx = new BbpmReceivableAdjustEntity();
                                    entityForGx.setId(entity.getId());
                                    entityForGx.setExt2(gxResultResult.getData().getPaymentCode());
                                    if (!"00000".equals(gxResultResult.getCode())) {
                                        //退款失败-设置为企业退款失败
                                        entityForGx.setExt3("4");
                                    }else {
                                        //退款成功-设置为企业退款处理中
                                        entityForGx.setExt3("3");
                                    }
                                    if(!baseService.updateById(entityForGx)) {
                                        log.error("应收调整初始化企业退款状态失败");
                                        return "应收调整初始化企业退款状态失败";
                                    }


//                                    BbpmReceivableAdjustEntity entityForGx = new BbpmReceivableAdjustEntity();
//                                    entityForGx.setId(entity.getId());
//                                    entityForGx.setExt3("3");
//                                    entityForGx.setExt2(gxResultVo.getPaymentCode());
//                                    if(!baseService.updateById(entityForGx)) {
//                                        log.error("应收调整初始化企业退款状态失败");
//                                        return "应收调整初始化企业退款状态失败";
//                                    }
                                }
                            }

                            if (!"00000".equals(tzResult.getCode())) {
                                //调整失败，删除该单
                                BbpmReceivableAdjustEntity delectEntity = new BbpmReceivableAdjustEntity();
                                delectEntity.setId(entity.getId());
                                delectEntity.setDelFlag(0);
                                if(!baseService.updateById(delectEntity)) {
                                    log.error("应收调整删除失败");
                                    return "应收调整删除失败";
                                }
                                return "*提示:" + tzResult.getMessage();
                            }else {
                                return "应收调整成功";
                            }
                        }
                    }

                }
                // ++++++新增审核记录+++++++
                if(!baseService.saveOperationHisById(entity.getId(),4)) {
                    log.error("应收调整保存后保存历史失败:" + entity.getId());
                    return "应收调整保存后保存历史失败";
                }
                return "应收调整成功";
            }
        } else {
            return "应收调整保存失败传入为空";
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的应收调整
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmReceivableAdjustVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReceivableAdjustEntity> entityList = new ArrayList<>();

            for (BbpmReceivableAdjustVo item:voList){
                BbpmReceivableAdjustEntity entity = new BbpmReceivableAdjustEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("应收调整批量保存失败");
                throw new McpException("应收调整批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getId())).map(BbpmReceivableAdjustEntity::getId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("应收调整批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("应收调整批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmReceivableAdjustVo selectByIdRecord(String id) {

        if(!StringUtils.isEmpty(id)) {
            BbpmReceivableAdjustVo vo = baseMapper.selectByIdecord(id);
            mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(vo);
            vo.setBillList(billMapper.selectBillForAdjust(id));
            List<BbpmReceivableAdjustExamineEntity> examineList = examineMapper.selectExamineForAdjust(id);
            mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(examineList);
            vo.setExamineList(examineList);

            if(vo.getCalculationResults() != null && !"".equals(vo.getCalculationResults())){
                CalculationResultVo resultVosForJson = JSONObject.parseObject(vo.getCalculationResults(),CalculationResultVo.class);
                vo.setCalculationResultVo(resultVosForJson);
            }
            if(vo != null) {
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByCcid 根据商业合同变更主键查询
     * @param ccid 业合同变更主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmReceivableAdjustVo selectByCcid(String ccid) {

        if(!StringUtils.isEmpty(ccid)) {
            BbpmReceivableAdjustVo vo = baseMapper.selectByCcid(ccid);
            mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(vo);
            vo.setBillList(billMapper.selectBillForAdjust(vo.getId()));
//            List<BbpmReceivableAdjustExamineEntity> examineList = examineMapper.selectExamineForAdjust(id);
//            mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(examineList);
//            vo.setExamineList(examineList);

            if(vo.getCalculationResults() != null && !"".equals(vo.getCalculationResults())){
                CalculationResultVo resultVosForJson = JSONObject.parseObject(vo.getCalculationResults(),CalculationResultVo.class);
                vo.setCalculationResultVo(resultVosForJson);
            }
            if(vo != null) {
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmReceivableAdjustPageResultVo>> selectByPageRecord(BbpmReceivableAdjustPageVo vo) {
        //项目id，用于数据权限控制
        String projectIdStr = RequestUtil.getProjects();
        log.info("---------------当前用户项目权限----------------:"+projectIdStr);
        vo.setProjectIdStr(projectIdStr);
        List<BbpmReceivableAdjustPageResultVo> result = baseMapper.selectByPageCustom(vo);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    @Override
    public List<AdjustBillExportVO> selectByExportList(BbpmReceivableAdjustPageVo vo) {
        //项目id，用于数据权限控制
        String projectIdStr = RequestUtil.getProjects();
        log.info("---------------应收调整导出，当前用户项目权限----------------:"+projectIdStr);
        vo.setProjectIdStr(projectIdStr);
        List<AdjustBillExportVO> resultList = new ArrayList<AdjustBillExportVO>();
        List<BbpmReceivableAdjustPageResultVo> result = baseMapper.selectByExportList(vo);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        for (BbpmReceivableAdjustPageResultVo adjustItem:result){
            List<CalculationResultVo> resultVos = new ArrayList<CalculationResultVo>();
            String calculationResultsJson =adjustItem.getCalculationResults();
            if(calculationResultsJson != null && !"".equals(calculationResultsJson)){
                CalculationResultVo resultVosForJson = JSONObject.parseObject(calculationResultsJson,CalculationResultVo.class);
                resultVos.add(resultVosForJson);
            }else {
                CalculationParamVo calculationVo = new CalculationParamVo();
                calculationVo.setProjectId(adjustItem.getProjectId());
                ChargeableAdjustDTO dto = new ChargeableAdjustDTO();
                dto.setDeductionType(adjustItem.getAmountHandle());
                dto.setAdjustBillDTOList(billMapper.selectByAdjustId(adjustItem.getId()));
                calculationVo.setChargeableAdjustDTO(dto);
                resultVos = billService.adjustBillView(calculationVo);
            }
            if(resultVos != null && resultVos.size() > 0){
                // 将json字符串转成对象----处理转换报错问题
                String jsonStr = JSONObject.toJSONString(resultVos);
                List<CalculationResultVo> resultVosForJson = JSONObject.parseArray(jsonStr,CalculationResultVo.class);
                for (CalculationResultVo calculationResultVo:resultVosForJson){
                    List<ChargeableAdjustBillVO> billVOList = calculationResultVo.getChargeableAdjustBillVOList();
                    mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(billVOList);
                    if(billVOList != null && billVOList.size() > 0){
                        for (ChargeableAdjustBillVO billVO:billVOList){
                            AdjustBillExportVO resultVO = new AdjustBillExportVO();
                            BeanUtils.copyProperties(billVO,resultVO);
                            resultVO.setProductAddress(adjustItem.getProductAddress());
                            resultVO.setCustomerName(adjustItem.getCustomerName());
                            resultVO.setCustomerIdNumber(adjustItem.getCustomerIdNumber());
                            resultVO.setCompany(adjustItem.getCompany());
                            resultVO.setCustomerCreditCode(adjustItem.getCustomerCreditCode());
                            resultVO.setContractNo(adjustItem.getContractNo());
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            String formattedBeginTime = "";
                            if(adjustItem.getContractBeginTime() != null){
                                formattedBeginTime = sdf.format(adjustItem.getContractBeginTime());
                            }
                            String formattedEndTime = "";
                            if(adjustItem.getContractEndTime() != null){
                                formattedEndTime = sdf.format(adjustItem.getContractEndTime());
                            }
                            resultVO.setZlqx(formattedBeginTime+"~"+formattedEndTime);
                            resultVO.setContractTypeName(adjustItem.getContractTypeName());
                            resultVO.setChargePeriod(billVO.getChargePeriod());
                            resultVO.setChargeSubject(billVO.getChargeSubjectName());
                            resultVO.setPayableMoney(billVO.getPayableMoney() != null ? billVO.getPayableMoney().toString() : "0");
                            resultVO.setAdjustPayableMoney(billVO.getAdjustPayableMoney() != null ? billVO.getAdjustPayableMoney().toString() : "0");
                            resultVO.setAdjustMoney(billVO.getAdjustMoney() != null ? billVO.getAdjustMoney().toString() : "0");
                            resultVO.setPaidInMoney(billVO.getPaidInMoney() != null ? billVO.getPaidInMoney().toString() : "0");
                            resultVO.setAdjustPaidInMoney(billVO.getAdjustPaidInMoney() != null ? billVO.getAdjustPaidInMoney().toString() : "0");
                            resultVO.setRentingOutMoney(billVO.getRentingOutMoney() != null ? billVO.getRentingOutMoney().toString() : "0");
                            resultVO.setZrje(billVO.getAdjustInDeductionMoney() != null ? billVO.getAdjustInDeductionMoney().toString() : "0");
                            resultVO.setZcje(billVO.getAdjustDeductionMoney() != null ? billVO.getAdjustDeductionMoney().toString() : "0");
                            resultList.add(resultVO);
                        }
                    }
                }
            }
        }
        return resultList;
    }

    @Override
    public void updateForTk(AdjustGxVo vo) {
        log.info("修改退款状态报文:" + vo.toString());
        if(vo != null && !"".equals(vo.getOwner())){
            if(vo.getOwner().equals("02")){
                baseMapper.updateForGrTk(vo);
            } else if (vo.getOwner().equals("01")) {
                baseMapper.updateForQyTk(vo);
            }
        }else {
            throw new McpException("请传退款类型");
        }
    }

    @Override
    public AdjustTj selectForTj() {
        BbpmReceivableAdjustPageVo vo = new BbpmReceivableAdjustPageVo();
        //项目id，用于数据权限控制
        String projectIdStr = RequestUtil.getProjects();
        log.info("---------------当前用户项目权限----------------:"+projectIdStr);
        vo.setProjectIdStr(projectIdStr);
        return baseMapper.selectForTj(vo);
    }

    public List<BbpmBillForAdjustPageVo> selectBillForAdjust(String adjustId) {
        BbpmReceivableAdjustVo adjustVo = baseMapper.selectByIdecord(adjustId);
        List<AdjustBillDTO> adjustBillDTOList = billMapper.selectByAdjustId(adjustId);
        List<BbpmBillForAdjustPageVo> resultList = new ArrayList<BbpmBillForAdjustPageVo>();
        for (AdjustBillDTO adjustBillDTO:adjustBillDTOList){
            BbpmBillManagementPageVo billManagementPageVo = new BbpmBillManagementPageVo();
            billManagementPageVo.setPageSize(10);
            billManagementPageVo.setPageNumber(1);
            billManagementPageVo.setProjectId(adjustVo.getProjectId());
            billManagementPageVo.setChargeOwner(adjustBillDTO.getExt1());
            billManagementPageVo.setBillId(adjustBillDTO.getBillCode().toString());
            PageResult<List<BbpmBillManagementPageResultVo>> billPageResult = billManagementService.selectByPageRecord(billManagementPageVo);
            List<BbpmBillManagementPageResultVo> billList = billPageResult.getRows();
            for (BbpmBillManagementPageResultVo billManagementPageResultVo:billList){
                BbpmBillForAdjustPageVo resVo= new BbpmBillForAdjustPageVo();
                BeanUtils.copyProperties(billManagementPageResultVo,resVo);
                resVo.setAdjustMoney(adjustBillDTO.getAdjustMoney());
                resultList.add(resVo);
            }
        }
        return resultList;
    }

    @Override
    public String[] selectContractForAdjustId(String id) {
        return baseMapper.selectContractForAdjustId(id,null);
    }

    /**
     * 修改追加账单状态
     * @param id
     * @param status
     */
    @Override
    public void updateAdjustStatus(String id,String status) {
        this.baseMapper.updateAdjustStatus(id,status);
    }
}
