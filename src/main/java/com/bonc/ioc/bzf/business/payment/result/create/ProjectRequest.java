package com.bonc.ioc.bzf.business.payment.result.create;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 项目信息
 */
@Data
@Builder
public class ProjectRequest {
    @ApiModelProperty(value = "项目ID")
    @NotBlank(message = "项目ID", groups = {UpdateValidatorGroup.class})
    private String projectId;
    @ApiModelProperty(value = "项目编号")
    @NotBlank(message = "项目编号", groups = {UpdateValidatorGroup.class})
    private String projectNo;
    @ApiModelProperty(value = "项目名称")
    @NotBlank(message = "项目名称", groups = {UpdateValidatorGroup.class})
    private String projectName;
    @ApiModelProperty(value = "项目简称")
    @NotBlank(message = "项目简称", groups = {UpdateValidatorGroup.class})
    private String projectShortName;
    @ApiModelProperty(value = "运营主体类型")
    @NotBlank(message = "运营主体类型", groups = {UpdateValidatorGroup.class})
    private String operateEntityType;
    @ApiModelProperty(value = "运营主体名称")
    @NotBlank(message = "运营主体名称", groups = {UpdateValidatorGroup.class})
    private String operateEntityName;
    @ApiModelProperty(value = "运营单位业务中台编号")
    @NotBlank(message = "运营单位业务中台编号", groups = {UpdateValidatorGroup.class})
    private String operateUnitBusinessNo;
    @ApiModelProperty(value = "运营单位编号")
    @NotBlank(message = "运营单位编号", groups = {UpdateValidatorGroup.class})
    private String operateUnitNo;
    @ApiModelProperty(value = "运营单位名称")
    @NotBlank(message = "运营单位名称", groups = {UpdateValidatorGroup.class})
    private String operateUnitName;
    @ApiModelProperty(value = "项目区域业务中台编号")
    @NotBlank(message = "项目区域业务中台编号", groups = {UpdateValidatorGroup.class})
    private String projectAreaBusinessNo;
    @ApiModelProperty(value = "项目区域编号")
    @NotBlank(message = "项目区域编号", groups = {UpdateValidatorGroup.class})
    private String projectAreaNo;
    @ApiModelProperty(value = "项目区域名称")
    @NotBlank(message = "项目区域名称", groups = {UpdateValidatorGroup.class})
    private String projectAreaName;
    @ApiModelProperty(value = "项目业态")
    @NotBlank(message = "项目业态", groups = {UpdateValidatorGroup.class})
    private String projectFormat;
    @ApiModelProperty(value = "项目所属区县")
    @NotBlank(message = "项目所属区县", groups = {UpdateValidatorGroup.class})
    private String projectDistrict;
    @ApiModelProperty(value = "项目坐落")
    @NotBlank(message = "项目坐落", groups = {UpdateValidatorGroup.class})
    private String projectLocation;
    @ApiModelProperty(value = "所在小区或楼宇名称")
    @NotBlank(message = "所在小区或楼宇名称", groups = {UpdateValidatorGroup.class})
    private String projectEstate;

    @ApiModelProperty(value = "房源相关列表")
    @Singular("roomRequest")
    private List<RoomRequest> roomList;

    @ApiModelProperty(value = "二级业态")
    private String secBusinessType;

    @Override
    public String toString() {
        return "ProjectVo{" +
                "projectId='" + projectId + '\'' +
                ", projectNo='" + projectNo + '\'' +
                ", projectName='" + projectName + '\'' +
                ", projectShortName='" + projectShortName + '\'' +
                ", operateEntityType='" + operateEntityType + '\'' +
                ", operateEntityName='" + operateEntityName + '\'' +
                ", operateUnitBusinessNo='" + operateUnitBusinessNo + '\'' +
                ", operateUnitNo='" + operateUnitNo + '\'' +
                ", operateUnitName='" + operateUnitName + '\'' +
                ", projectAreaBusinessNo='" + projectAreaBusinessNo + '\'' +
                ", projectAreaNo='" + projectAreaNo + '\'' +
                ", projectAreaName='" + projectAreaName + '\'' +
                ", projectFormat='" + projectFormat + '\'' +
                ", projectDistrict='" + projectDistrict + '\'' +
                ", projectLocation='" + projectLocation + '\'' +
                ", projectEstate='" + projectEstate + '\'' +
                ", roomList=" + roomList +
                '}';
    }
}
