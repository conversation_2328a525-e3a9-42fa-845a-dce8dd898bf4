package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.vo.*;

/**
 * 收款请求处理
 *
 */
public interface ISendCollectionService {

    /**
     *现金 单个
     * @param vo
     * @return
     */
    String collectionSingleCash(BbpmCollectionVo vo);

    /**
     * 现金 批量
     * @param vo
     * @return
     */
    String collectionBatchCash(BbpmCollectionBatchVo vo);


    /**
     *线下收款 单个
     * @param vo
     * @return
     */
    String collectionSingleOffline(BbpmCollectionVo vo);

    /**
     * 线下收款 批量
     * @param vo
     * @return
     */
    String collectionBatchOffline(BbpmCollectionBatchVo vo);

    /**
     *支票 单个
     * @param vo
     * @return
     */
    String collectionSingleCheque(BbpmCollectionVo vo);

    /**
     * 支票 批量
     * @param vo
     * @return
     */
    String collectionBatchCheque(BbpmCollectionBatchVo vo);

}
