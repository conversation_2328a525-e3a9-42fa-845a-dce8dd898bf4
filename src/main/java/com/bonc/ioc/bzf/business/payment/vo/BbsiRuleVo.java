package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 签约规则表  实体类
 *
 * <AUTHOR>
 * @date 2022-08-08
 * @change 2022-08-08 by wtl for init
 */
@Data
@ApiModel(value="BbsiRuleVo对象", description="签约规则表 ")
public class BbsiRuleVo extends McpBaseVo implements Serializable{


    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
                                  private String ruleId;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID",required = true)
                            private String planId;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id",required = true)
                            private String communityId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称",required = true)
                            private String ruleName;

    /**
     * 签约方式 1.线上签约 2.线下签约
     */
    @ApiModelProperty(value = "签约方式 1.线上签约 2.线下签约",required = true)
    @McpDictPoint(dictCode = "SIGNING_METHOD",overTransCopyTo = "signingMethod")
                            private String signingMethod;

    /**
     * 签约告知书id
     */
    @ApiModelProperty(value = "签约告知书id")
                            private String templeteId;

    /**
     * 签约告知书名称
     */
    @ApiModelProperty(value = "签约告知书名称")
    private String templeteName;




    @Override
    public String toString() {
        return "BbsiRuleVo{" +
                "ruleId='" + ruleId + '\'' +
                ", planId='" + planId + '\'' +
                ", communityId='" + communityId + '\'' +
                ", ruleName='" + ruleName + '\'' +
                ", signingMethod='" + signingMethod + '\'' +
                ", templeteId='" + templeteId + '\'' +
                ", templeteName='" + templeteName + '\'' +
                '}';
    }
}
