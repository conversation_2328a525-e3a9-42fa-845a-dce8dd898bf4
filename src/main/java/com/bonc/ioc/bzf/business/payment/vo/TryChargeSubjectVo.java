package com.bonc.ioc.bzf.business.payment.vo;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.result.create.IncreaseRulesParamsRequest;
import com.bonc.ioc.bzf.business.payment.result.create.PreferentRulesParamsRequest;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Singular;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * chargeSubject	计费科目列表
 */
@Data
public class TryChargeSubjectVo {

//    @ApiModelProperty(value = "计费科目编号")
//    private String chargeSubjectNo;
//
//    @ApiModelProperty(value = "计费科目金额")
//    private BigDecimal chargeSubjectAmount;
//    @ApiModelProperty(value = "计费科目收款周期")
//    private String chargeSubjectPeriod;
//    @ApiModelProperty(value = "优惠类别")
//    private String preferentialCategory;
//    @ApiModelProperty(value = "优惠金额")
//    private BigDecimal preferentialAmount;
//    @ApiModelProperty(value = "优惠比例")
//    private BigDecimal preferentialRatio;
//    @ApiModelProperty(value = "参数列表")
//    private JSONObject paramList;
//    @ApiModelProperty(value = "参数值列表")
//    private JSONObject paramValueList;
//
//    @ApiModelProperty(value = "递增规则列表")
//    private List<TryIncreaseRuleVo> increaseRules;
//
//    @ApiModelProperty(value = "增值服务费计费规则")
//    private Integer serviceFeeChargeRule;
//    @ApiModelProperty(value = "增值服务费月租金百分比")
//    private String monthlyRentServiceFeeRatio;
//    @ApiModelProperty(value = "增值服务费单价")
//    private String serviceFeePricePerSquareMeter;
//    @ApiModelProperty(value = "增值服务费固定值")
//    private String fixedServiceFee;
//    @ApiModelProperty(value = "服务费分摊方式")
//    private String serviceFeeShareType;
//    @ApiModelProperty(value = "服务费企业支付金额")
//    private String serviceFeeCompanyAmount;
//    @ApiModelProperty(value = "服务费企业支付比例")
//    private String serviceFeeCompanyRate;

    @ApiModelProperty(value = "计费科目编号")
    @NotBlank(message = "计费科目编号", groups = {UpdateValidatorGroup.class})
    private String chargeSubjectNo;

    @ApiModelProperty(value = "计费科目金额")
    @NotBlank(message = "计费科目金额", groups = {UpdateValidatorGroup.class})
    private BigDecimal chargeSubjectAmount;

    /**
     * 计费科目金额类型
     */
    @ApiModelProperty(value = "计费科目金额类型")
    private String amountType;

    @ApiModelProperty(value = "计费科目税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "循环计费或单次计费")
    @NotBlank(message = "循环计费或单次计费", groups = {UpdateValidatorGroup.class})
    private String cyclicOrSingle;

    @ApiModelProperty(value = "计费科目收款周期")
    @NotBlank(message = "计费科目收款周期", groups = {UpdateValidatorGroup.class})
    private String chargeSubjectPeriod;


    @ApiModelProperty(value = "押金比例 只有02押金才有")
    @NotBlank(message = "押金比例", groups = {UpdateValidatorGroup.class})
    private Integer depositProportion;

    @ApiModelProperty(value = "优惠类别")
    private String preferentialCategory;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal preferentialAmount;

    @ApiModelProperty(value = "优惠比例")
    private BigDecimal preferentialRatio;

    @ApiModelProperty(value = "计费规则编号")
    private String chargeRuleNo;

    @ApiModelProperty(value = "计费规则名称")
    private String chargeRuleName;

    @ApiModelProperty(value = "参数列表")
//    private ChargeRuleSubParamsVo paramList;
    private JSONObject paramList;

    @ApiModelProperty(value = "参数值列表")
//    private ChargeRuleSubParamsVo paramValueList;
    private JSONObject paramValueList;

    @ApiModelProperty(value = "参数值列表")
//    private List<IncreaseRulesParamsRequest> increaseRules;
    private List<TryIncreaseRuleVo> increaseRules;

//    @ApiModelProperty(value = "优惠规则列表")
//    private List<PreferentRulesParamsRequest> preferentRules;

    @ApiModelProperty(value = "分摊方式")
    private String shareType;
    @ApiModelProperty(value = "企业支付比例")
    private BigDecimal companyRate;
    @ApiModelProperty(value = "个人支付比例")
    private BigDecimal personalRate;
    @ApiModelProperty(value = "企业支付金额")
    private BigDecimal companyAmount;
    @ApiModelProperty(value = "个人支付金额")
    private BigDecimal personalAmount;

    //增值服务费新增字段
    @ApiModelProperty(value = "是否包含增值服务费int0不包含1包含")
    private Integer hasServiceFee;
    @ApiModelProperty(value = "增值服务费计费规则1月租金百分比2增值服务费单价3增值服务费固定值")
    private Integer serviceFeeChargeRule;
    @ApiModelProperty(value = "月租金百分比70.00代表70%")
    private String monthlyRentServiceFeeRatio;
    @ApiModelProperty(value = "增值服务费单价保留两位小数,例如 12.00")
    private String serviceFeePricePerSquareMeter;
    @ApiModelProperty(value = "增值服务费固定值保留两位小数，例如12.01")
    private String fixedServiceFee;

    /**
     * 分摊方式
     */
    @ApiModelProperty(value = "分摊方式")
    private String serviceFeeShareType;

    /**
     * 服务费企业支付金额
     */
    @ApiModelProperty(value = "服务费企业支付金额")
    private String serviceFeeCompanyAmount;

    /**
     * 服务费企业支付比例
     */
    @ApiModelProperty(value = "服务费企业支付比例")
    private String serviceFeeCompanyRate;

}
