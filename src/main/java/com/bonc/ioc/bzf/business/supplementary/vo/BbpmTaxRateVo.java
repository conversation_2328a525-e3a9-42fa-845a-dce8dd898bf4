package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 税率 vo实体类
 *
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
@ApiModel(value = "税率 vo实体", description = "税率 vo实体")
public class BbpmTaxRateVo extends McpBaseVo implements Serializable {

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 管理协议时个人税率
     */
    @ApiModelProperty(value = "管理协议时个人税率")
    private BigDecimal personalTaxRate;
}
