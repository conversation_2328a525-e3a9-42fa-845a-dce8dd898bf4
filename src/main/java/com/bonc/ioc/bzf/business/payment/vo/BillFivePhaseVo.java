package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 五期合同生成账单 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/25
 */
@Data
@ApiModel(value = "五期合同生成账单", description = "五期合同生成账单")
public class BillFivePhaseVo extends McpBaseVo implements Serializable {

    /**
     * 趸租大合同id
     */
    @ApiModelProperty(value = "趸租大合同id")
    private String parentContractId;

    /**
     * 原合同ID
     */
    @ApiModelProperty(value = "原合同ID")
    private String originalContractId;

    /**
     * 五期合同状态
     */
    @ApiModelProperty(value = "五期合同状态")
    private String contractStatus;

    /**
     * 期限开始时间
     */
    @ApiModelProperty(value = "期限开始时间")
    private String periodBeginDate;

    /**
     * 期限结束时间
     */
    @ApiModelProperty(value = "期限结束时间")
    private String periodEndDate;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 房屋ID
     */
    @ApiModelProperty(value = "房屋ID")
    private String houseId;

    /**
     * 计费科目列表
     */
    @ApiModelProperty(value = "计费科目列表")
    private List<ChargeSubjectVo> chargeSubjectList;

    /**
     * 五期合同项目列表
     */
    @ApiModelProperty(value = "五期合同项目列表")
    private List<BillProjectVo> projectList;

}
