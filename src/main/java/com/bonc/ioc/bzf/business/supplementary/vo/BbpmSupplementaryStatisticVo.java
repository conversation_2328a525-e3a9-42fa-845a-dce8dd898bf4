package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 追加单信息统计 实体类
 *
 * <AUTHOR>
 * @since 2025/3/28
 */
@Data
@ApiModel(value = "追加单信息统计", description = "追加单信息统计")
public class BbpmSupplementaryStatisticVo extends McpBaseVo implements Serializable {

    /**
     * 待办数量
     */
    @ApiModelProperty(value = "待办数量")
    private Integer dbSize;

    /**
     * 待办数量
     */
    @ApiModelProperty(value = "待办数量")
    private Integer ybSize;
}
