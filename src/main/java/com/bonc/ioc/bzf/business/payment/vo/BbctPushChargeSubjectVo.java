package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 推送的计费科目信息 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/25
 */
@Data
@ApiModel(value = "推送的计费科目信息", description = "推送的计费科目信息")
public class BbctPushChargeSubjectVo extends McpBaseVo implements Serializable {

    /**
     * 计费科目编号
     */
    @ApiModelProperty(value = "计费科目编号")
    private String chargeSubjectNo;

    /**
     * 循环计费或单次计费
     */
    @ApiModelProperty(value = "循环计费或单次计费")
    private String cyclicOrSingle;

    /**
     * 计费科目收款周期
     */
    @ApiModelProperty(value = "计费科目收款周期")
    private String chargeSubjectPeriod;

    /**
     * 计费规则编号
     */
    @ApiModelProperty(value = "计费规则编号")
    private String chargeRuleNo;

    /**
     * 计费规则名称
     */
    @ApiModelProperty(value = "计费规则名称")
    private String chargeRuleName;

    /**
     * 参数列表
     */
    @ApiModelProperty(value = "参数列表")
    private ChargeRuleSubParamsVo paramList;

    /**
     * 参数值列表
     */
    @ApiModelProperty(value = "参数值列表")
    private ChargeRuleSubParamsVo paramValueList;

    /**
     * 计费科目金额
     */
    @ApiModelProperty(value = "计费科目金额")
    private BigDecimal chargeSubjectAmount;

    /**
     * 计费科目金额类型
     */
    @ApiModelProperty(value = "计费科目金额类型")
    private String amountType;

    /**
     * 押金比例
     */
    @ApiModelProperty(value = "押金比例")
    private Integer depositProportion;

    /**
     * 递增规则列表
     */
    @ApiModelProperty(value = "递增规则列表")
    private List<BbctIncreaseRuleVo> increaseRules;

    /**
     * 优惠规则列表
     */
    @ApiModelProperty(value = "优惠规则列表")
    private List<BbctPreferentRuleVo> preferentRules;


    @ApiModelProperty(value = "计费科目税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "个人计费科目税率")
    private BigDecimal personalTaxRate;

    /**
     * 分摊方式
     */
    @ApiModelProperty(value = "分摊方式")
    private String shareType;

    //增值服务费新增字段
    @ApiModelProperty(value = "是否包含增值服务费int0不包含1包含")
    private Integer hasServiceFee;
    @ApiModelProperty(value = "增值服务费计费规则1月租金百分比2增值服务费单价3增值服务费固定值")
    private Integer serviceFeeChargeRule;
    @ApiModelProperty(value = "月租金百分比70.00代表70%")
    private String monthlyRentServiceFeeRatio;
    @ApiModelProperty(value = "增值服务费单价保留两位小数,例如 12.00")
    private String serviceFeePricePerSquareMeter;
    @ApiModelProperty(value = "增值服务费固定值保留两位小数，例如12.01")
    private String fixedServiceFee;


    /**
     * 分摊方式
     */
    @ApiModelProperty(value = "分摊方式")
    private String serviceFeeShareType;
    /**
     * 服务费企业支付金额
     */
    @ApiModelProperty(value = "服务费企业支付金额")
    private String serviceFeeCompanyAmount;

    /**
     * 服务费企业支付比例
     */
    @ApiModelProperty(value = "服务费企业支付比例")
    private String serviceFeeCompanyRate;


    @ApiModelProperty(value = "优惠类别")
    private String preferentialCategory;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal preferentialAmount;

    @ApiModelProperty(value = "优惠比例")
    private BigDecimal preferentialRatio;
}
