package com.bonc.ioc.bzf.business.supplementary.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.bonc.ioc.bzf.business.supplementary.entity.BbpmApproveInfoEntity;
import com.bonc.ioc.bzf.business.supplementary.dao.BbpmApproveInfoMapper;
import com.bonc.ioc.bzf.business.supplementary.enums.ApproveStatusEnum;
import com.bonc.ioc.bzf.business.supplementary.enums.DelFlagEnum;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmApproveDetailInfoService;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmApproveInfoService;
import com.bonc.ioc.bzf.utils.common.user.UserUtil;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

/**
 * 审批表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Slf4j
@Service
public class BbpmApproveInfoServiceImpl extends McpBaseServiceImpl<BbpmApproveInfoEntity> implements IBbpmApproveInfoService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmApproveInfoMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmApproveInfoService baseService;

    /**
     * 签约明细相关 服务实例
     */
    @Resource
    private IBbpmApproveDetailInfoService approveDetailInfoService;

    /**
     * 用户相关 服务实例
     */
    @Resource
    private UserUtil userUtil;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbpmApproveInfoVo vo) {
        if (vo == null) {
            return null;
        }

        BbpmApproveInfoEntity entity = new BbpmApproveInfoEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setApproveId(null);
        if (!baseService.insert(entity)) {
            log.error("审批表新增失败:" + entity.toString());
            throw new McpException("审批表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getApproveId(), 1)) {
                log.error("审批表新增后保存历史失败:" + entity.toString());
                throw new McpException("审批表新增后保存历史失败");
            }

            log.debug("审批表新增成功:" + entity.getApproveId());
            return entity.getApproveId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmApproveInfoVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmApproveInfoEntity> entityList = new ArrayList<>();
        for (BbpmApproveInfoVo item : voList) {
            BbpmApproveInfoEntity entity = new BbpmApproveInfoEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmApproveInfoEntity item : entityList) {
            item.setApproveId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("审批表新增失败");
            throw new McpException("审批表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbpmApproveInfoEntity::getApproveId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("审批表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("审批表批量新增后保存历史失败");
            }

            log.debug("审批表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param approveId 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String approveId) {
        if (!StringUtils.isEmpty(approveId)) {
            if (!baseService.saveOperationHisById(approveId, 3)) {
                log.error("审批表删除后保存历史失败:" + approveId);
                throw new McpException("审批表删除后保存历史失败");
            }

            if (!baseService.removeById(approveId)) {
                log.error("审批表删除失败");
                throw new McpException("审批表删除失败" + approveId);
            }
        } else {
            throw new McpException("审批表删除失败审批id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param approveIdList 需要删除的审批id
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> approveIdList) {
        if (!CollectionUtils.isEmpty(approveIdList)) {
            int oldSize = approveIdList.size();
            approveIdList = approveIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(approveIdList) || oldSize != approveIdList.size()) {
                throw new McpException("审批表批量删除失败 存在主键id为空的记录" + StringUtils.join(approveIdList));
            }

            if (!baseService.saveOperationHisByIds(approveIdList, 3)) {
                log.error("审批表批量删除后保存历史失败:" + StringUtils.join(approveIdList));
                throw new McpException("审批表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(approveIdList)) {
                log.error("审批表批量删除失败");
                throw new McpException("审批表批量删除失败" + StringUtils.join(approveIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的审批表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmApproveInfoVo vo) {
        if (vo != null) {
            BbpmApproveInfoEntity entity = new BbpmApproveInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getApproveId())) {
                throw new McpException("审批表更新失败传入审批id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("审批表更新失败");
                throw new McpException("审批表更新失败" + entity.getApproveId());
            } else {
                if (!baseService.saveOperationHisById(entity.getApproveId(), 2)) {
                    log.error("审批表更新后保存历史失败:" + entity.getApproveId());
                    throw new McpException("审批表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("审批表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的审批表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmApproveInfoVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmApproveInfoEntity> entityList = new ArrayList<>();

            for (BbpmApproveInfoVo item : voList) {
                BbpmApproveInfoEntity entity = new BbpmApproveInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getApproveId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("审批表批量更新失败 存在审批id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("审批表批量更新失败");
                throw new McpException("审批表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getApproveId())).map(BbpmApproveInfoEntity::getApproveId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("审批表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("审批表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的审批表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmApproveInfoVo vo) {
        if (vo != null) {
            BbpmApproveInfoEntity entity = new BbpmApproveInfoEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("审批表保存失败");
                throw new McpException("审批表保存失败" + entity.getApproveId());
            } else {
                if (!baseService.saveOperationHisById(entity.getApproveId(), 4)) {
                    log.error("审批表保存后保存历史失败:" + entity.getApproveId());
                    throw new McpException("审批表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("审批表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的审批表
     * @return void
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmApproveInfoVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmApproveInfoEntity> entityList = new ArrayList<>();

            for (BbpmApproveInfoVo item : voList) {
                BbpmApproveInfoEntity entity = new BbpmApproveInfoEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("审批表批量保存失败");
                throw new McpException("审批表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getApproveId())).map(BbpmApproveInfoEntity::getApproveId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("审批表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("审批表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param approveId 需要查询的审批id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmApproveInfoVo selectByIdRecord(String approveId) {
        BbpmApproveInfoVo vo = new BbpmApproveInfoVo();

        if (!StringUtils.isEmpty(approveId)) {
            BbpmApproveInfoEntity entity = baseService.selectById(approveId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-03-26
     * @change 2025-03-26 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmApproveInfoPageResultVo>> selectByPageRecord(BbpmApproveInfoPageVo vo) {
        List<BbpmApproveInfoPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据上级id和审批类型查询审批数据
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 审批数据
     */
    @Override
    public BbpmApproveInfoVo selectByParentIdAndApproveType(String parentId, String approveType) {
        BbpmApproveInfoEntity entity = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbpmApproveInfoEntity::getParentId, parentId)
                .eq(BbpmApproveInfoEntity::getApproveType, approveType)
                .eq(BbpmApproveInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .one();
        if (Objects.isNull(entity)) {
            return null;
        } else {
            BbpmApproveInfoVo vo = new BbpmApproveInfoVo();
            BeanUtils.copyProperties(entity, vo);
            return vo;
        }
    }

    /**
     * 提交
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 审批链
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmApproveLinkVo submit(String parentId, String approveType) {
        BbpmApproveLinkVo resultVo = new BbpmApproveLinkVo();
        BbpmApproveInfoVo approveInfoVo = selectByParentIdAndApproveType(parentId, approveType);
        String approveId;
        if (Objects.isNull(approveInfoVo)) {
            // 新增
            approveInfoVo = new BbpmApproveInfoVo();
            approveInfoVo.setParentId(parentId);
            approveInfoVo.setApproveType(approveType);
            approveInfoVo.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
            approveInfoVo.setSubmitTime(new Date());
            approveInfoVo.setSubmitUserId(userUtil.getUserId());
            approveInfoVo.setSubmitUserName(userUtil.getUserName());
            approveInfoVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
            approveId = insertRecord(approveInfoVo);
            approveInfoVo.setApproveId(approveId);
            // 设置审批链的旧审批
            resultVo.setOldApproveInfoVo(new BbpmApproveInfoVo());
            // 设置审批链的新审批
            resultVo.setNewApproveInfoVo(approveInfoVo);
        } else {
            // 更新
            // 若已经通过无需重新提交
            if (ApproveStatusEnum.APPROVED.getCode().equals(approveInfoVo.getApproveStatus())) {
                log.error("提交失败签约已经审批通过:" + parentId);
                throw new McpException("提交失败签约已经审批通过");
            } else if (ApproveStatusEnum.WAIT_APPROVE.getCode().equals(approveInfoVo.getApproveStatus())) {
                log.error("提交失败签约已经提交:" + parentId);
                throw new McpException("提交失败签约已经提交");
            }
            // 设置审批链的旧审批
            resultVo.setOldApproveInfoVo(SerializationUtils.clone(approveInfoVo));
            approveInfoVo.setApproveStatus(ApproveStatusEnum.WAIT_APPROVE.getCode());
            approveInfoVo.setSubmitTime(new Date());
            approveInfoVo.setSubmitUserId(userUtil.getUserId());
            approveInfoVo.setSubmitUserName(userUtil.getUserName());
            approveInfoVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
            List<String> approveStatusList = new ArrayList<>();
            approveStatusList.add(ApproveStatusEnum.UNAPPROVED.getCode());
            approveStatusList.add(ApproveStatusEnum.CANCEL.getCode());
            boolean success = new LambdaUpdateChainWrapper<>(baseMapper)
                    .set(BbpmApproveInfoEntity::getApproveStatus, approveInfoVo.getApproveStatus())
                    .set(BbpmApproveInfoEntity::getSubmitTime, approveInfoVo.getSubmitTime())
                    .set(BbpmApproveInfoEntity::getSubmitUserId, approveInfoVo.getSubmitUserId())
                    .set(BbpmApproveInfoEntity::getSubmitUserName, approveInfoVo.getSubmitUserName())
                    .set(BbpmApproveInfoEntity::getApproveTime, null)
                    .set(BbpmApproveInfoEntity::getApproverUserId, null)
                    .eq(BbpmApproveInfoEntity::getApproveId, approveInfoVo.getApproveId())
                    .in(BbpmApproveInfoEntity::getApproveStatus, approveStatusList)
                    .update();
            if (!success) {
                log.error("提交失败根据id和审批状态列表更新失败:" + approveInfoVo.toString());
                throw new McpException("提交失败");
            }
            // 设置审批链的新审批
            resultVo.setNewApproveInfoVo(approveInfoVo);
            approveId = approveInfoVo.getApproveId();
        }
        // 新增审批明细数据
        BbpmApproveDetailInfoVo approveDetailInfoVo = new BbpmApproveDetailInfoVo();
        approveDetailInfoVo.setApproveInfo(approveInfoVo);
        approveDetailInfoVo.setApproveId(approveId);
        approveDetailInfoVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        approveDetailInfoService.insertRecord(approveDetailInfoVo);
        return resultVo;
    }

    /**
     * 撤销
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 审批链
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmApproveLinkVo cancel(String parentId, String approveType) {
        BbpmApproveLinkVo resultVo = new BbpmApproveLinkVo();
        BbpmApproveInfoVo approveInfoVo = selectByParentIdAndApproveType(parentId, approveType);
        if (Objects.isNull(approveInfoVo)) {
            log.error(String.format("撤回失败审批数据不存在:parentId=%s approveType=%s", parentId, approveType));
            throw new McpException("撤回失败审批数据不存在");
        }
        if (ApproveStatusEnum.APPROVED.getCode().equals(approveInfoVo.getApproveStatus()) ||
                ApproveStatusEnum.UNAPPROVED.getCode().equals(approveInfoVo.getApproveStatus())) {
            log.error("撤回失败签约已经审批完成:" + parentId);
            throw new McpException("撤回失败签约已经审批完成");
        }
        // 设置审批链的旧审批
        resultVo.setOldApproveInfoVo(SerializationUtils.clone(approveInfoVo));
        approveInfoVo.setApproveStatus(ApproveStatusEnum.CANCEL.getCode());
        boolean success = new LambdaUpdateChainWrapper<>(baseMapper)
                .set(BbpmApproveInfoEntity::getApproveStatus, approveInfoVo.getApproveStatus())
                .eq(BbpmApproveInfoEntity::getApproveId, approveInfoVo.getApproveId())
                .eq(BbpmApproveInfoEntity::getApproveStatus, ApproveStatusEnum.WAIT_APPROVE.getCode())
                .update();
        if (!success) {
            log.error("撤回失败根据id和审批状态列表更新失败:" + approveInfoVo.toString());
            throw new McpException("撤回失败");
        }
        // 设置审批链的新审批
        resultVo.setNewApproveInfoVo(approveInfoVo);
        // 新增审批明细数据
        BbpmApproveDetailInfoVo approveDetailInfoVo = new BbpmApproveDetailInfoVo();
        approveDetailInfoVo.setApproveInfo(approveInfoVo);
        approveDetailInfoVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
        approveDetailInfoService.insertRecord(approveDetailInfoVo);
        return resultVo;
    }

    /**
     * 处理审批
     *
     * @param vo            评论 vo实体
     * @param approveType   审批类型
     * @param approveStatus 审批状态
     * @return 审批链
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmApproveLinkVo dealApprove(BbpmRemarkVo vo, String approveType, String approveStatus) {
        BbpmApproveLinkVo resultVo = new BbpmApproveLinkVo();
        String parentId = vo.getId();
        if (ApproveStatusEnum.APPROVED.getCode().equals(approveStatus) ||
                ApproveStatusEnum.UNAPPROVED.getCode().equals(approveStatus)) {
            BbpmApproveInfoVo approveInfoVo = selectByParentIdAndApproveType(parentId, approveType);
            if (Objects.isNull(approveInfoVo)) {
                log.error(String.format("审批失败审批数据不存在:parentId=%s approveType=%s", parentId, approveType));
                throw new McpException("审批失败审批数据不存在");
            }
            if (ApproveStatusEnum.APPROVED.getCode().equals(approveInfoVo.getApproveStatus()) ||
                    ApproveStatusEnum.UNAPPROVED.getCode().equals(approveInfoVo.getApproveStatus())) {
                log.error("审批失败签约已经审批完成:" + parentId);
                throw new McpException("审批失败签约已经审批完成");
            } else if (ApproveStatusEnum.CANCEL.getCode().equals(approveInfoVo.getApproveStatus())) {
                log.error("审批失败审批已撤回:" + parentId);
                throw new McpException("审批失败审批已撤回");
            }
            // 设置审批链的旧审批
            resultVo.setOldApproveInfoVo(SerializationUtils.clone(approveInfoVo));
            approveInfoVo.setApproveStatus(approveStatus);
            approveInfoVo.setApproveTime(vo.getDate());
            approveInfoVo.setApproverUserId(userUtil.getUserId());
            approveInfoVo.setApproverUserName(userUtil.getUserName());
            boolean success = new LambdaUpdateChainWrapper<>(baseMapper)
                    .set(BbpmApproveInfoEntity::getApproveStatus, approveInfoVo.getApproveStatus())
                    .set(BbpmApproveInfoEntity::getApproveTime, approveInfoVo.getApproveTime())
                    .set(BbpmApproveInfoEntity::getApproverUserId, approveInfoVo.getApproverUserId())
                    .set(BbpmApproveInfoEntity::getApproverUserName, approveInfoVo.getApproverUserName())
                    .eq(BbpmApproveInfoEntity::getApproveId, approveInfoVo.getApproveId())
                    .eq(BbpmApproveInfoEntity::getApproveStatus, ApproveStatusEnum.WAIT_APPROVE.getCode())
                    .update();
            if (!success) {
                log.error("审批失败根据id和审批状态列表更新失败:" + approveInfoVo.toString());
                throw new McpException("审批失败");
            }
            // 设置审批链的新审批
            resultVo.setNewApproveInfoVo(approveInfoVo);
            // 新增审批明细数据
            BbpmApproveDetailInfoVo approveDetailInfoVo = new BbpmApproveDetailInfoVo();
            approveDetailInfoVo.setApproveInfo(approveInfoVo);
            approveDetailInfoVo.setApproverUserId(approveInfoVo.getApproverUserId());
            approveDetailInfoVo.setApproveRemark(vo.getRemark());
            approveDetailInfoVo.setDelFlag(DelFlagEnum.UNDELETED.getCode());
            approveDetailInfoService.insertRecord(approveDetailInfoVo);
        } else {
            log.error(String.format("审批失败审批状态异常:parentId=%s approveStatus=%s", parentId, approveStatus));
            throw new McpException("审批失败审批状态异常");
        }
        return resultVo;
    }

    /**
     * 获取操作记录列表
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 操作记录列表
     */
    @Override
    public List<BbpmApproveDetailInfoVo> operateDetailInfo(String parentId, String approveType) {
        return approveDetailInfoService.selectListByParentId(parentId, approveType);
    }
}
