package com.bonc.ioc.bzf.business.payment.controller;

import com.bonc.ioc.bzf.business.payment.service.IBbpDictService;
import com.bonc.ioc.bzf.business.payment.vo.BbpDictPageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpDictPageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpDictVo;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 字典表 前端控制器
 *
 * <AUTHOR>
 * @date 2022-09-22
 * @change 2022-09-22 by wtl for init
 */
@RestController
@RequestMapping("/v2/atomic/bbpDictEntity")
@Api(tags = "字典表")
@Validated
public class BbpDictController extends McpBaseController {
    @Resource
    private IBbpDictService baseService;

    /**
     * Bean 字典转化
     */
    @Resource
    private McpDictSession mcpDictSession;


    @GetMapping(value = "/selectByDictCode", produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据字典code查询code对应字典（统一返回报文）", notes = "根据字典code查询code对应字典")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<McpDictEntity>> selectByDictCode(@ApiParam(value = "需要查询的字典编码" ,required = false) @RequestParam(required = false) String dictCode){
        AppReply<List<McpDictEntity>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        try{
            appReply.setData(mcpDictSession.getMcpDictUtil().getPairsListByDictCode(dictCode));
        }catch (Exception e){
            //查询字典  报空指针  直接返回空list
            appReply.setData(new ArrayList<>());
        }
        return appReply;
    }


    /**
     * insertRecord 新增
     * @param vo 需要新增的记录
     * @return  com.bonc.ioc.common.util.AppReply<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "wtl")
    @ApiOperation(value = "新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "字典表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpDictVo vo){
        AppReply<String> appReply = new AppReply<String>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertRecord(vo));
        return appReply;
    }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要新增的记录 列表
     * @return  com.bonc.ioc.common.util.AppReply<List<String>> 返回新增后的主键 列表
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @PostMapping(value = "/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "wtl")
    @ApiOperation(value = "批量新增", notes = "新增全表数据", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200,message = "data:新增主键集合")})
    public AppReply<List<String>> insertBatchRecord(@ApiParam(value = "字典表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbpDictVo> voList){
        AppReply<List<String>> appReply = new AppReply<List<String>>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.insertBatchRecord(voList));
        return appReply;
     }

    /**
     * removeByIdRecord 根据主键删除
     * @param dictId 需要删除的字典表主键
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "wtl")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdRecord(@ApiParam(value = "需要删除的字典表主键" ,required = false) @RequestBody String dictId){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdRecord(dictId);
        return appReply;
     }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param dictIdList 需要删除的字典表主键集合
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @PostMapping(value = "/batch/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "wtl")
    @ApiOperation(value = "根据主键删除-批量", notes = "根据主键批量删除表中信息 物理删除", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply removeByIdsRecord(@ApiParam(value = "需要删除的字典表主键" ,required = false) @RequestBody List<String> dictIdList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.removeByIdsRecord(dictIdList);
        return appReply;
     }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的字典表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "wtl")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateByIdRecord(@ApiParam(value = "需要更新的字典表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbpDictVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateByIdRecord(vo);
        return appReply;
     }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的字典表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @PostMapping(value = "/batch/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "wtl")
    @ApiOperation(value = "根据主键更新-批量", notes = "根据主键批量更新表中信息 更新全部信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply updateBatchByIdRecord(@ApiParam(value = "需要更新的字典表" ,required = false) @RequestBody @Validated(UpdateValidatorGroup.class) List<BbpDictVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.updateBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的字典表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @PostMapping(value = "/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "wtl")
    @ApiOperation(value = "根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveByIdRecord(@ApiParam(value = "需要更新或新增的字典表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpDictVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveByIdRecord(vo);
        return appReply;
     }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的字典表
     * @return  com.bonc.ioc.common.util.AppReply 无返回参数 删除失败直接报错
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @PostMapping(value = "/batch/saveById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "wtl")
    @ApiOperation(value = "根据主键更新或新增-批量", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply saveBatchByIdRecord(@ApiParam(value = "需要更新或新增的字典表" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) List<BbpDictVo> voList){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        baseService.saveBatchByIdRecord(voList);
        return appReply;
     }

    /**
     * selectByIdRecord 根据主键查询
     * @param dictId 需要查询的字典表主键
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "wtl")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpDictVo> selectByIdRecord(@ApiParam(value = "需要查询的字典表主键" ,required = false) @RequestParam(required = false) String dictId){
        AppReply<BbpDictVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByIdRecord(dictId));
        return appReply;
     }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-09-22
     * @change
     * 2022-09-22 by wtl for init
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "wtl")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = true)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpDictPageResultVo>>> selectByPageRecord(BbpDictPageVo vo){
        AppReply<PageResult<List<BbpDictPageResultVo>>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByPageRecord(vo));
        return appReply;
     }

}

