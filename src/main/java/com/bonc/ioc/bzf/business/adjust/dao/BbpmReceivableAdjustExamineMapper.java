package com.bonc.ioc.bzf.business.adjust.dao;

import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustExamineEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import java.util.List;

/**
 * 应收调整审核表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@Mapper
public interface BbpmReceivableAdjustExamineMapper extends McpBaseMapper<BbpmReceivableAdjustExamineEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change 2025-02-18 by yuanxuesong for init
     */
    List<BbpmReceivableAdjustExaminePageResultVo> selectByPageCustom(@Param("vo") BbpmReceivableAdjustExaminePageVo vo );

    List<BbpmReceivableAdjustExamineEntity> selectExamineForAdjust(@Param("adjustId") String adjustId);
}
