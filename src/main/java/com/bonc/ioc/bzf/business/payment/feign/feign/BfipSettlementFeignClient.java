package com.bonc.ioc.bzf.business.payment.feign.feign;

import com.bonc.ioc.bzf.business.payment.feign.fallback.BfipSettlementFeignClientFallback;
import com.bonc.ioc.bzf.business.payment.result.FaceHttpResultTwo;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.bzf.utils.common.mock.Mock;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import feign.Response;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 *  bfip-settlement 结算中心
 */

@FeignClient(value = "bfip-settlement",fallback = BfipSettlementFeignClientFallback.class, configuration = FeignExceptionConfiguration.class)
public interface BfipSettlementFeignClient {

	/**
	 * 3.10现金线下收款更新账单状态接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/bill/updateBillOffline")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String updateBillOffline(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.23  趸租对公收款接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/public/bill/updateBillByOffline")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String updateBillOfflinePublic(@RequestBody ParentRequest parentRequest);


	/**
	 * 支付
	 *
	 * @creator YaoChunyu
	 * @date 2023/3/4 22:51
	 * @createDescribe
	 *
	 * @modifiedBy
	 * @modifieTime
	 * @modifieDescribe
	 *
	 * @param vo 请求参数对象
	 * @return
	 * @exception
	 */
	@PostMapping(value = "/settlement/v1/aggregatepayment/pay")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String pay(@RequestBody PayParmsVo vo);

	/**
	 * 支付解锁
	 *
	 * @creator YaoChunyu
	 * @date 2023/3/4 22:51
	 * @createDescribe
	 *
	 * @modifiedBy
	 * @modifieTime
	 * @modifieDescribe
	 *
	 * @param vo 请求参数对象
	 * @return
	 * @exception
	 */
	@PostMapping(value = "/settlement/v1/aggregatepayment/pay/unlock")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String payUnlock(@RequestBody Map vo);

	/**
	 * 3.21 手动代扣（手工报盘）
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/withhold/manual")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String manualOffer(@RequestBody ParentRequest parentRequest);


	/**
	 * 3.19.4 发票查询
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/einvoice/queryInvoice")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String queryInvoice(@RequestBody ParentRequest parentRequest);


	/**
	 * 3.19.1 发票开具
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/einvoice/issueInvoice")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String issueInvoice(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.19.5 账单预校验
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/einvoice/preCheckBill")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String preCheckBill(@RequestBody ParentRequest parentRequest);


	/**
	 * 3.19.2 发票红冲
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/einvoice/redflush")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String redflush(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.19.3 发票推送
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/einvoice/pushInvoice")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String pushInvoice(@RequestBody ParentRequest parentRequest);

	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR>  根据项目查询开户行
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/settlement/v1/centerCharge/listByProjectId")
	@LogPoint(system = "bfip-settlement")
	ChargeRespondVo<List<ChargeBankResultVo>> listByProjectId(@RequestBody BankRequestVo<ChargeBankParamsVo> bankRequestVo);

	/**
	 * 3.25. 查询收款凭证接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/receipt/transfer/info")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String info(@RequestBody ParentRequest parentRequest);


	/**
	 * 3.40. 报盘文件分页查询接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/chargestatement/page")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String page(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.41. 报盘文件下载接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/chargestatement/exceldownload")
	@FeignExceptionCheck
//	@LogPoint(system = "bfip-settlement")
	Response exceldownload(@RequestBody ParentRequest parentRequest);


	/**
	 * @param  bankRequestVo
	 * @return
	 * @version 1.0
	 * <AUTHOR>  3.44根据项目查询保障房收款开户行
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/settlement/v1/centerCharge/listBankByProjectId")
	@LogPoint(system = "bfip-settlement")
	@Mock(key = "2503eedff9a003")
	ChargeRespondVo<List<ChargeBankResultV2Vo>> listBankByProjectId(@RequestBody BankRequestVo<ChargeBankParamsV2Vo> bankRequestVo);
	/**
	 * @param  parentRequest
	 * @return
	 * @version 1.0
	 * <AUTHOR>  3.42. 分行银行编码以及名称分页查询接口
	 * @Date 2022/12/13
	 */
	@PostMapping(value = "/settlement/v1/centerCharge/listBankBranchCode")
	@LogPoint(system = "bfip-settlement")
	String listBankBranchCode(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.38 查询税率配置列表
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/taxrate/list")
	@LogPoint(system = "bfip-settlement")
	String getTaxRateList(@RequestBody ParentRequest parentRequest);



	/**
	 * 3.21.1 手动代扣-未缴账单列表查询
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/withhold/list")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String list(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.21.3 手动代扣-报盘记录查询
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/withhold/record")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String record(@RequestBody ParentRequest parentRequest);

	/**
	 * 3.21.4 手动代扣-报盘明细查询
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/withhold/detail")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String detail(@RequestBody ParentRequest parentRequest);


	/**
	 * 3.21.5 手动代扣-报盘明细下载
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/withhold/exceldownload")
	@FeignExceptionCheck
//	@LogPoint(system = "bfip-settlement")
	Response exceldownloadV2(@RequestBody ParentRequest parentRequest);


	/**
	 * 3.52 根据项目等查询现金存款单回退信息
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/v1/bill/getOffLinePages")
	@FeignExceptionCheck
	@LogPoint(system = "bfip-settlement")
	String getOffLinePages(@RequestBody ParentRequest parentRequest);


	/**
	 * 3.59.商业合同变更退款生成付款单接口
	 * @param parentRequest
	 * @return
	 */
	@PostMapping(value = "/settlement/payment/businessGeneratePaymentByGX" )
	@LogPoint(system = "bfip-charge")
	@Mock(key = "2719e1fdbcb03a")
	ChargeRespondVo<BusinessGeneratePaymentByGXResultVo> businessGeneratePaymentByGX(@RequestBody ParentRequest<BusinessGeneratePaymentByGXParamVo> parentRequest);


	@PostMapping(value = "/settlement/payment/businessGeneratePaymentByGX" )
	@LogPoint(system = "bfip-charge")
	@Mock(key = "2719e1fdbcb03a")
	String businessGeneratePaymentByGXString(@RequestBody ParentRequest<BusinessGeneratePaymentByGXParamVo> parentRequest);

}
