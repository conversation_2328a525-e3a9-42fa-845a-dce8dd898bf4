package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 追加单试算后的账单 vo实体类
 *
 * <AUTHOR>
 * @since 2025/4/8
 */
@Data
@ApiModel(value = "追加单试算后的账单 vo实体", description = "追加单试算后的账单 vo实体")
public class BbpmSupplementAppendBillVo extends McpBaseVo implements Serializable {

    /**
     * 账单周期
     */
    @ApiModelProperty(value = "账单周期")
    private String chargeSubjectPeriod;

    /**
     * 收费科目起始日期
     */
    @ApiModelProperty(value = "收费科目起始日期")
    private String chargeSubjectBeginDate;

    /**
     * 收费科目结束日期
     */
    @ApiModelProperty(value = "收费科目结束日期")
    private String chargeSubjectEndDate;

    /**
     * 应缴日期
     */
    @ApiModelProperty(value = "应缴日期")
    private String payableDate;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String roomAddress;

    /**
     * 费用项目
     */
    @ApiModelProperty(value = "费用项目")
    private String chargeSubject;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
    private BigDecimal payableMoney;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal noTaxMoney;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 增值税额
     */
    @ApiModelProperty(value = "增值税额")
    private BigDecimal rateMoney;

    /**
     * 账单类型(01.个人 02.企业)
     */
    @ApiModelProperty(value = "账单类型(01.个人 02.企业)")
    private String billOwner;

    /**
     * 计费方式(01.循环 02.单次)
     */
    @ApiModelProperty(value = "计费方式(01.循环 02.单次)")
    private String cyclicOrSingle;
}
