package com.bonc.ioc.bzf.business.payment.controller;

import com.bonc.ioc.bzf.business.payment.service.IBbpmCashPledgeService;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCashPledgePageResultVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCashPledgePageVo;
import com.bonc.ioc.bzf.business.payment.vo.BbpmCashPledgeVo;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 押金条 前端控制器
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by binghong.tang for init
 */
@RestController
@RequestMapping("/v2/business/bbpmCashPledgeEntity")
@Api(tags = "押金条")
@Validated
public class BbpmCashPledgeBusinessController extends McpBaseController {
    @Resource
    private IBbpmCashPledgeService baseService;



    /**
     * selectByConditions 根据账单等查询押金条
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    @GetMapping(value = "/selectByConditions", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "binghong.tang")
    @ApiOperation(value = "根据账单等查询押金条信息", notes = "根据账单等查询押金条信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmCashPledgeVo> selectByConditions(BbpmCashPledgeVo vo){
        AppReply<BbpmCashPledgeVo> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByConditions(vo));
        return appReply;
     }

    /**
     * selectByConditions 查询押金条列表
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-05-26
     * @change
     * 2025-05-26 by binghong.tang for init
     */
    @GetMapping(value = "/selectByList", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "binghong.tang")
    @ApiOperation(value = "查询押金条列表", notes = "查询押金条列表", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmCashPledgeVo>> selectByList(BbpmCashPledgeVo vo){
        AppReply<List<BbpmCashPledgeVo>> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(baseService.selectByList(vo));
        return appReply;
    }
}

