package com.bonc.ioc.bzf.business.invoice.service;

import com.bonc.ioc.bzf.business.invoice.entity.BbpmInvoiceQueryEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.invoice.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 已开发票查询 服务类
 *
 * <AUTHOR>
 * @date 2023-05-13
 * @change 2023-05-13 by binghong.tang for init
 */
public interface IBbpmInvoiceQueryService extends IMcpBaseService<BbpmInvoiceQueryEntity>{


    /**
     * selectByIdRecord 根据主键查询
     * @param vo 需要查询的发票请求流水号
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-13
     * @change
     * 2023-05-13 by binghong.tang for init
     */
    BbpmInvoiceQueryVo selectByIdRecord(BbpmInvoiceQueryVo vo);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-13
     * @change
     * 2023-05-13 by binghong.tang for init
     */
    PageResult<List<BbpmInvoiceQueryPageResultVo>> selectByPageRecord(BbpmInvoiceQueryPageVo vo);
}
