package com.bonc.ioc.bzf.business.supplementary.service;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmApproveDetailInfoEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.supplementary.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 审批明细表 服务类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
public interface IBbpmApproveDetailInfoService extends IMcpBaseService<BbpmApproveDetailInfoEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    String insertRecord(BbpmApproveDetailInfoVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    List<String> insertBatchRecord(List<BbpmApproveDetailInfoVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param approveDetailId 需要删除的审批明细id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void removeByIdRecord(String approveDetailId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param approveDetailIdList 需要删除的审批明细id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void removeByIdsRecord(List<String> approveDetailIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void updateByIdRecord(BbpmApproveDetailInfoVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void updateBatchByIdRecord(List<BbpmApproveDetailInfoVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void saveByIdRecord(BbpmApproveDetailInfoVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的审批明细表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    void saveBatchByIdRecord(List<BbpmApproveDetailInfoVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param approveDetailId 需要查询的审批明细id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    BbpmApproveDetailInfoVo selectByIdRecord(String approveDetailId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-03-26
     * @change
     * 2025-03-26 by pyj for init
     */
    PageResult<List<BbpmApproveDetailInfoPageResultVo>> selectByPageRecord(BbpmApproveDetailInfoPageVo vo);

    /**
     * 获取操作记录列表
     *
     * @param parentId    上级id
     * @param approveType 审批类型
     * @return 操作记录列表
     */
    List<BbpmApproveDetailInfoVo> selectListByParentId(String parentId, String approveType);
}
