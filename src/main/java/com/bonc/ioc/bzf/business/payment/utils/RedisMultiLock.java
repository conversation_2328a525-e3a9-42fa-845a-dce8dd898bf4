package com.bonc.ioc.bzf.business.payment.utils;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RedisMultiLock {
    @Autowired
    private RedissonClient redissonClient;

    /**

     *   加锁
     */
    public boolean multiLock(List<String> redisKeyList) {
        try {
            RLock multiLock = getMultiLock(redisKeyList);
            return multiLock.tryLock();
        } catch (Exception e) {
            throw new UnsupportedOperationException();
        }
    }

    private RLock getMultiLock(List<String> redisKeyList) {
        RLock[] locks = new RLock[redisKeyList.size()];
        for (int i = 0; i < redisKeyList.size(); i++) {
            RLock lock = redissonClient.getLock(redisKeyList.get(i));
            locks[i] = lock;
        }
        return redissonClient.getMultiLock(locks);
    }

    /**

     *  解锁
     */
    public void unMultiLock(List<String> redisKeyList) {
        RLock multiLock = getMultiLock(redisKeyList);
        multiLock.unlock();
    }
}
