package com.bonc.ioc.bzf.business.reminder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum ReminderEnums {

    ENABLESTATUS_OK("02","启用"),
    ENABLESTATUS_NO("01","停用"),

    BILLDUEAROUND_BEFORE("01","前"),
    BILLDUEAROUND_AFTER("02","后"),

    NOTICE_METHOD_STATION("1","站内信"),
    NOTICE_METHOD_TEXT("2","短信"),

    CONTRACTTYPE_SZ("01","散租合同"),
    CONTRACTTYPE_DZ("02","趸租合同"),
    CONTRACTTYPE_GL("03","趸租管理协议"),

    OWER_COMPANY("01","企业"),
    OWER_PERSON("02","个人"),

    BILL_COLLECTION_METHOD_AUTO("1", "自动"),
    BILL_COLLECTION_METHOD_MANUAL("2", "手动"),
    BILLING_COLLECTION_RESULT_SUCCESS("1", "成功"),
    BILLING_COLLECTION_RESULT_FAIL("2", "失败"),
    BILLING_COLLECTION_REASON_UNPAID("01", "未缴/未足额缴纳"),
    BILLING_COLLECTION_REASON_UNEQUAL("02", "已缴但未对平"),

    CHARGE_STATUS_ALL("01","已足额缴费"),
    CHARGE_STATUS_PART("02","已缴部分支付"),
    CHARGE_STATUS_NOT("03","未缴"),

    LOG_STATUS_ST("ST","开始"),
    LOG_STATUS_EN("EN","完成"),
    LOG_STATUS_EX("EX","异常");


    private final String code;
    private final String msg;

}

