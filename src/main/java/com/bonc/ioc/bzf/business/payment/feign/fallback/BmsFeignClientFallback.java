package com.bonc.ioc.bzf.business.payment.feign.fallback;

import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.entity.SystemLocaIdEntity;
import com.bonc.ioc.bzf.business.payment.feign.feign.BmsFeignClient;
import com.sinovatech.rd.bms.api.location.vo.*;
import com.sinovatech.rd.bms.api.menu.vo.MenuRpcServiceGetMenuByUserIdReq;
import com.sinovatech.rd.bms.api.menu.vo.MenuRpcServiceGetMenuByUserIdResp;
import com.sinovatech.rd.bms.api.role.vo.BmsRoleRpcServiceGetOneRoleReq;
import com.sinovatech.rd.bms.api.role.vo.BmsRoleRpcServiceGetOneRoleResp;
import com.sinovatech.rd.bms.api.role.vo.BmsRoleRpcServiceQueryUsersByLocationsAndRoleReq;
import com.sinovatech.rd.bms.api.role.vo.BmsRoleRpcServiceQueryUsersByLocationsAndRoleResp;
import com.sinovatech.rd.bms.api.user.vo.*;
import com.sinovatech.saas.base.spec.bean.Request;
import com.sinovatech.saas.base.spec.bean.Response;

/**
 * 描述内容
 *
 * <AUTHOR>
 * @date 2022/2/21 16:55
 * @change 2022/2/21 16:55 by <EMAIL> for init
 */
public class BmsFeignClientFallback implements BmsFeignClient {


    @Override
    public Response<BmsUserRpcServiceUserLoginResp> userLogin(Request<BmsUserRpcServiceUserLoginReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceGetOneUserResp> getOneUser(Request<BmsUserRpcServiceGetOneUserReq> req) {
        return null;
    }

    @Override
    public Response<MenuRpcServiceGetMenuByUserIdResp> getMenuByUserId(Request<MenuRpcServiceGetMenuByUserIdReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceUserLogOutResp> userLogOut(Request<BmsUserRpcServiceUserLogOutReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceGetAllUsersResp> selectUserListTwo(Request<BmsUserRpcServiceGetAllUsersReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceGetAllUserForPageResp> getAllUserForPage(Request<BmsUserRpcServiceGetAllUserForPageReq> req) {
        return null;
    }

    @Override
    public Response<BmsLocationRpcServiceGetAllResp> selectDeptAll(Request<BmsLocationRpcServiceGetAllReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceUpdUserPwdResp> updUserPwd(Request<BmsUserRpcServiceUpdUserPwdReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceSendCodeResp> sendCode(Request<BmsUserRpcServiceSendCodeReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServicePhoneLoginResp> phoneLogin(Request<BmsUserRpcServicePhoneLoginReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserMRpcServiceLoginByPhoneCodeResp> loginByPhoneCode(Request<BmsUserMRpcServiceLoginByPhoneCodeReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceUserAuthResp> userAuth(Request<BmsUserRpcServiceUserAuthReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserMRpcServiceLoginWithOutPwdResp> loginWithOutPwd(Request<BmsUserMRpcServiceLoginWithOutPwdReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceUpdUserPwdAndPhonResp> updUserPwdAndPhon(Request<BmsUserRpcServiceUpdUserPwdAndPhonReq> req) {
        return null;
    }

    @Override
    public Response<BmsRoleRpcServiceGetOneRoleResp> getOneRole(Request<BmsRoleRpcServiceGetOneRoleReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceGetLocUsersResp> getLocUsers(Request<BmsUserRpcServiceGetLocUsersReq> req) {
        return null;
    }

    @Override
    public Response<BmsLocationRpcServiceGetOneResp> selectOneOrg(Request<BmsLocationRpcServiceGetOneReq> req) {
        return null;
    }

    @Override
    public Response<JSONObject> selectOne1(Request<BmsLocationRpcServiceGetOneReq> req) {
        return null;
    }

    @Override
    public Response<BmsLocationRpcServiceGetAllSubLocResp> selectAllSubOrg(Request<BmsLocationRpcServiceGetAllSubLocReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceGetUserRolesResp> getUserRoles(Request<BmsUserRpcServiceGetUserRolesReq> req) {
        return null;
    }

    @Override
    public Response<BmsLocationRpcServiceQueryUserByLocationIdResp> getUserByLocationId(Request<BmsLocationRpcServiceQueryUserByLocationIdReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceGetAllResp> selectUserAll(Request<BmsUserRpcServiceGetAllReq> req) {
        return null;
    }

    @Override
    public Response<BmsUserRpcServiceGetUsersByIdsResp> getUserByIds(Request<BmsUserRpcServiceGetUsersByIdsReq> req) {
        return null;
    }

    @Override
    public Response<BmsLocationRpcServiceGetUserCountByLocationIdResp> getUserCountByLocationId(Request<BmsLocationRpcServiceGetUserCountByLocationIdReq> req) {
        return null;
    }

    @Override
    public Response<BmsRoleRpcServiceQueryUsersByLocationsAndRoleResp> getQueryUsersByLocationsAndRole(Request<BmsRoleRpcServiceQueryUsersByLocationsAndRoleReq> req) {
        return null;
    }

    @Override
    public Response<SystemLocaIdEntity> getSystemLocaIdByLocationId(Request<SystemLocaIdEntity> req) {
        return null;
    }
}
