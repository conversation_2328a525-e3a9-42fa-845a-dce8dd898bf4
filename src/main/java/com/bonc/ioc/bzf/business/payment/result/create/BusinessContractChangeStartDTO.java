package com.bonc.ioc.bzf.business.payment.result.create;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * businessContractChangeStartDTO(商业起租日变更)
 */
@Data
public class BusinessContractChangeStartDTO implements Serializable {
    @ApiModelProperty(value = "变更合同号")
    private String contractCode;//变更合同号	String
    @ApiModelProperty(value = "合同变更后起租日")
    private String contractStartDate;//String	是
    @ApiModelProperty(value = "合同变更后终租日")
    private String contractEndDate;//合同变更后终租日	String	是
    @ApiModelProperty(value = "协议号")
    private String agreementCode;//String	是


}
