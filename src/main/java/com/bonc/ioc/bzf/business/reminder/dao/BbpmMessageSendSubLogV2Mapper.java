package com.bonc.ioc.bzf.business.reminder.dao;

import com.bonc.ioc.bzf.business.reminder.entity.BbpmMessageSendSubLogV2Entity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.business.reminder.vo.*;
import java.util.List;

/**
 * (催缴记录)催缴规则消息发送日志--子表V2 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-04-23
 * @change 2024-04-23 by binghong.tang for init
 */
@Mapper
public interface BbpmMessageSendSubLogV2Mapper extends McpBaseMapper<BbpmMessageSendSubLogV2Entity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-04-23
     * @change 2024-04-23 by binghong.tang for init
     */
    List<BbpmMessageSendSubLogV2PageResultVo> selectByPageCustom(@Param("vo") BbpmMessageSendSubLogV2PageVo vo );
}
