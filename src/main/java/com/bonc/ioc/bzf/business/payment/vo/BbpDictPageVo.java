package com.bonc.ioc.bzf.business.payment.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 字典表 实体类
 *
 * <AUTHOR>
 * @date 2022-09-22
 * @change 2022-09-22 by wtl for init
 */
@ApiModel(value="BbpDictPageVo对象", description="字典表")
public class BbpDictPageVo extends McpBasePageVo implements Serializable{


    /**
     * 字典表主键
     */
    @ApiModelProperty(value = "字典表主键")
    @NotBlank(message = "字典表主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String dictId;

    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
                            private String typeName;

    /**
     * 字典编码
     */
    @ApiModelProperty(value = "字典编码")
                            private String typeCode;

    /**
     * 对照（代码）
     */
    @ApiModelProperty(value = "对照（代码）")
                            private String code;

    /**
     * 对照（值）
     */
    @ApiModelProperty(value = "对照（值）")
                            private String meaning;

    /**
     * 键值对序列号
     */
    @ApiModelProperty(value = "键值对序列号")
                            private Integer seqNum;

    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "扩展信息")
                            private String expand;

    /**
     * @return 字典表主键
     */
    public String getDictId() {
        return dictId;
    }

    public void setDictId(String dictId) {
        this.dictId = dictId;
    }

    /**
     * @return 字典名称
     */
    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    /**
     * @return 字典编码
     */
    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    /**
     * @return 对照（代码）
     */
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    /**
     * @return 对照（值）
     */
    public String getMeaning() {
        return meaning;
    }

    public void setMeaning(String meaning) {
        this.meaning = meaning;
    }

    /**
     * @return 键值对序列号
     */
    public Integer getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(Integer seqNum) {
        this.seqNum = seqNum;
    }

    /**
     * @return 扩展信息
     */
    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

      @Override
    public String toString() {
        return "BbpDictPageVo{" +
            "dictId=" + dictId +
            ", typeName=" + typeName +
            ", typeCode=" + typeCode +
            ", code=" + code +
            ", meaning=" + meaning +
            ", seqNum=" + seqNum +
            ", expand=" + expand +
        "}";
    }
}
