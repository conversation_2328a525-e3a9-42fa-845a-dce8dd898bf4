package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.entity.BbpmCashPledgeEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;
import java.util.List;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 押金条 服务类
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by binghong.tang for init
 */
public interface IBbpmCashPledgeService extends IMcpBaseService<BbpmCashPledgeEntity>{
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    String insertRecord(BbpmCashPledgeVo vo);

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    List<String> insertBatchRecord(List<BbpmCashPledgeVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     * @param cashPledgeId 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    void removeByIdRecord(String cashPledgeId);

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param cashPledgeIdList 需要删除的唯一标识符
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    void removeByIdsRecord(List<String> cashPledgeIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的押金条
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    void updateByIdRecord(BbpmCashPledgeVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的押金条
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    void updateBatchByIdRecord(List<BbpmCashPledgeVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的押金条
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    void saveByIdRecord(BbpmCashPledgeVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的押金条
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    void saveBatchByIdRecord(List<BbpmCashPledgeVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     * @param cashPledgeId 需要查询的唯一标识符
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    BbpmCashPledgeVo selectByIdRecord(String cashPledgeId);

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-05-17
     * @change
     * 2023-05-17 by binghong.tang for init
     */
    PageResult<List<BbpmCashPledgePageResultVo>> selectByPageRecord(BbpmCashPledgePageVo vo);

    /**
     * 条件查询 不分页
     * @param vo
     * @return
     */
    BbpmCashPledgeVo selectByConditions(BbpmCashPledgeVo vo);


    /**
     * 条件查询 不分页
     * @param vo
     * @return
     */
    List<BbpmCashPledgeVo> selectByList(BbpmCashPledgeVo vo);
}
