package com.bonc.ioc.bzf.business.payment.service.impl;

import com.bonc.ioc.bzf.business.payment.entity.BbpmCashCollectionVoucherEntity;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCashCollectionVoucherMapper;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCashCollectionVoucherService;
import com.bonc.ioc.bzf.business.payment.service.IBbpmCollectionService;
import com.bonc.ioc.bzf.business.payment.utils.RedisDistributedId;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.payment.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 现金收缴凭证表v3.0 服务类实现
 *
 * <AUTHOR>
 * @date 2022-12-02
 * @change 2022-12-02 by binghong.tang for init
 */
@Slf4j
@Service
public class BbpmCashCollectionVoucherServiceImpl extends McpBaseServiceImpl<BbpmCashCollectionVoucherEntity> implements IBbpmCashCollectionVoucherService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmCashCollectionVoucherMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmCashCollectionVoucherService baseService;
    @Resource
    private IBbpmCollectionService collectionService;

    @Autowired
    RedisDistributedId redisDistributedId;

    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmCashCollectionVoucherVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmCashCollectionVoucherEntity entity = new BbpmCashCollectionVoucherEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setVoucherId(null);
        entity.setIssuingTime(new Date());

        if(!baseService.insert(entity)) {
            log.error("开具电子凭据新增失败:" + entity.toString());
            throw new McpException("开具电子凭据表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getVoucherId(),1)) {
                log.error("开具电子凭据表v3.0新增后保存历史失败:" + entity.toString());
                throw new McpException("开具电子凭据表新增后保存历史失败");
            }

            log.debug("开具电子凭据表v3.0新增成功:"+entity.getVoucherId());
            return entity.getVoucherId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmCashCollectionVoucherVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmCashCollectionVoucherEntity> entityList = new ArrayList<>();
        for (BbpmCashCollectionVoucherVo item:voList) {
            BbpmCashCollectionVoucherEntity entity = new BbpmCashCollectionVoucherEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmCashCollectionVoucherEntity item:entityList){
            item.setVoucherId(null);
            item.setIssuingTime(new Date());
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("现金收缴凭证表v3.0新增失败");
            throw new McpException("现金收缴凭证表v3.0新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmCashCollectionVoucherEntity::getVoucherId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("现金收缴凭证表v3.0批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("现金收缴凭证表v3.0批量新增后保存历史失败");
            }

            log.debug("现金收缴凭证表v3.0新增成功:"+ StringUtils.join(kidList));
            for (BbpmCashCollectionVoucherVo bbpmCashCollectionVoucherVo:voList) {
                collectionService.updateCertificateStateById(bbpmCashCollectionVoucherVo.getCollectionNo());
            }
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param voucherId 需要删除的主键id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String voucherId) {
        if(!StringUtils.isEmpty(voucherId)) {
            if(!baseService.saveOperationHisById(voucherId,3)) {
                log.error("现金收缴凭证表v3.0删除后保存历史失败:" + voucherId);
                throw new McpException("现金收缴凭证表v3.0删除后保存历史失败");
            }

            if(!baseService.removeById(voucherId)) {
                log.error("现金收缴凭证表v3.0删除失败");
                throw new McpException("现金收缴凭证表v3.0删除失败"+voucherId);
            }
        } else {
            throw new McpException("现金收缴凭证表v3.0删除失败主键id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param voucherIdList 需要删除的主键id
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> voucherIdList) {
        if(!CollectionUtils.isEmpty(voucherIdList)) {
            int oldSize = voucherIdList.size();
            voucherIdList = voucherIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(voucherIdList) || oldSize != voucherIdList.size()) {
                throw new McpException("现金收缴凭证表v3.0批量删除失败 存在主键id为空的记录"+StringUtils.join(voucherIdList));
            }

            if(!baseService.saveOperationHisByIds(voucherIdList,3)) {
                log.error("现金收缴凭证表v3.0批量删除后保存历史失败:" + StringUtils.join(voucherIdList));
                throw new McpException("现金收缴凭证表v3.0批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(voucherIdList)) {
                log.error("现金收缴凭证表v3.0批量删除失败");
                throw new McpException("现金收缴凭证表v3.0批量删除失败"+StringUtils.join(voucherIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的现金收缴凭证表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmCashCollectionVoucherVo vo) {
        if(vo != null) {
            BbpmCashCollectionVoucherEntity entity = new BbpmCashCollectionVoucherEntity();
            BeanUtils.copyProperties(vo, entity);

            entity.setIssuingTime(new Date());

            if(StringUtils.isEmpty(entity.getVoucherId())) {
                throw new McpException("现金收缴凭证表v3.0更新失败传入主键id为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("现金收缴凭证表v3.0更新失败");
                throw new McpException("现金收缴凭证表v3.0更新失败"+entity.getVoucherId());
            } else {
                if(!baseService.saveOperationHisById(entity.getVoucherId(),2)) {
                    log.error("现金收缴凭证表v3.0更新后保存历史失败:" + entity.getVoucherId());
                    throw new McpException("现金收缴凭证表v3.0更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("现金收缴凭证表v3.0更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的现金收缴凭证表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmCashCollectionVoucherVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmCashCollectionVoucherEntity> entityList = new ArrayList<>();

            for (BbpmCashCollectionVoucherVo item:voList){
                BbpmCashCollectionVoucherEntity entity = new BbpmCashCollectionVoucherEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getVoucherId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("现金收缴凭证表v3.0批量更新失败 存在主键id为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("现金收缴凭证表v3.0批量更新失败");
                throw new McpException("现金收缴凭证表v3.0批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getVoucherId())).map(BbpmCashCollectionVoucherEntity::getVoucherId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("现金收缴凭证表v3.0批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("现金收缴凭证表v3.0批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的现金收缴凭证表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmCashCollectionVoucherVo vo) {
        if(vo != null) {
            BbpmCashCollectionVoucherEntity entity = new BbpmCashCollectionVoucherEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("现金收缴凭证表v3.0保存失败");
                throw new McpException("现金收缴凭证表v3.0保存失败"+entity.getVoucherId());
            } else {
                if(!baseService.saveOperationHisById(entity.getVoucherId(),4)) {
                    log.error("现金收缴凭证表v3.0保存后保存历史失败:" + entity.getVoucherId());
                    throw new McpException("现金收缴凭证表v3.0保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("现金收缴凭证表v3.0保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的现金收缴凭证表v3.0
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmCashCollectionVoucherVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmCashCollectionVoucherEntity> entityList = new ArrayList<>();

            for (BbpmCashCollectionVoucherVo item:voList){
                BbpmCashCollectionVoucherEntity entity = new BbpmCashCollectionVoucherEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("现金收缴凭证表v3.0批量保存失败");
                throw new McpException("现金收缴凭证表v3.0批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getVoucherId())).map(BbpmCashCollectionVoucherEntity::getVoucherId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("现金收缴凭证表v3.0批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("现金收缴凭证表v3.0批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param voucherId 需要查询的主键id
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmCashCollectionVoucherVo selectByIdRecord(String voucherId) {
        BbpmCashCollectionVoucherVo vo = new BbpmCashCollectionVoucherVo();

        if(!StringUtils.isEmpty(voucherId)) {
            BbpmCashCollectionVoucherEntity entity = baseService.selectById(voucherId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }




    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmCashCollectionVoucherPageResultVo>> selectByPageRecord(BbpmCashCollectionVoucherPageVo vo) {
        List<BbpmCashCollectionVoucherPageResultVo> result = baseMapper.selectByPageCustom(vo);
        if(result!=null && result.size() > 0){
            for(BbpmCashCollectionVoucherPageResultVo pageResultVo : result){
                if(pageResultVo.getPaidInAmount() != null){
                    pageResultVo.setPaidInAmount(pageResultVo.getPaidInAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return new PageResult(result);
    }

    /**
     * 新增
     * @param vo 需要保存的记录
     * @return
     */
    @Override
    public String insertRecordV2(BbpmCashCollectionVo vo) {
        if(vo == null) {
            return null;
        }

        String contratCode = "";
        String projectId = "";
        StringBuffer billCodes = new StringBuffer();
        StringBuffer chargeCodes = new StringBuffer();
        if(vo.getBbpmCollectionVoList() != null && vo.getBbpmCollectionVoList().size() > 0){
            for(BbpmCollectionPageResultVo bbpmBillManagementVo : vo.getBbpmCollectionVoList()){
                billCodes.append(bbpmBillManagementVo.getBillCode()).append(",");
                chargeCodes.append(bbpmBillManagementVo.getChargeCode()).append(",");
            }
            billCodes = billCodes.deleteCharAt(billCodes.length() - 1);
            chargeCodes = chargeCodes.deleteCharAt(chargeCodes.length() - 1);

            contratCode = vo.getBbpmCollectionVoList().get(0).getContractCode();
            projectId = vo.getBbpmCollectionVoList().get(0).getProjectId();
        }

        BbpmCashCollectionVoucherEntity entity = new BbpmCashCollectionVoucherEntity();
        entity.setVoucherId(null);

//        entity.setVoucherNumber(vo.getVoucherNumber());
//        entity.setVoucherNumber(redisDistributedId.nextIdV2("cashCollectionVoucher","000"));
        entity.setCollectionNo(chargeCodes.toString());
        entity.setPaidInAmount(vo.getPaidInAmount());
//        entity.setCredentialFileAddress(vo.getCredentialFileAddress());
        entity.setIssuingTime(new Date());
        entity.setBillId(billCodes.toString());
        entity.setContractCode(contratCode);
        entity.setProjectId(projectId);
//        entity.setRequestParams(vo.getRequestParams());

        if(!baseService.insert(entity)) {
            log.error("现金收缴凭证新增失败:" + entity.toString());
            throw new McpException("现金收缴凭证新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getVoucherId(),1)) {
                log.error("现金收缴凭证新增后保存历史失败:" + entity.toString());
                throw new McpException("现金收缴凭证新增后保存历史失败");
            }

            log.debug("现金收缴凭证新增成功:"+entity.getVoucherId());
            return entity.getVoucherId();
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmCashCollectionVoucherVo selectByCollectionNo(BbpmCashCollectionVoucherVo vo) {
        BbpmCashCollectionVoucherVo result = baseMapper.selectByCollectionNo(vo);
        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(result);
        return result;
    }


}
