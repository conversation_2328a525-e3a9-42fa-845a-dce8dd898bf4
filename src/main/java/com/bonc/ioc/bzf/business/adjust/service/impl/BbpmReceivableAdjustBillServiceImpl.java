package com.bonc.ioc.bzf.business.adjust.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustBillEntity;
import com.bonc.ioc.bzf.business.adjust.dao.BbpmReceivableAdjustBillMapper;
import com.bonc.ioc.bzf.business.adjust.service.IBbpmReceivableAdjustBillService;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.ChangeContractRequest;
import com.bonc.ioc.bzf.business.payment.result.FaceMdMapResult;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsResultVo;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.util.AppReply;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;
import com.bonc.ioc.bzf.business.adjust.vo.*;
import org.springframework.beans.BeanUtils;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 应收调整账单表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-02-18
 * @change 2025-02-18 by yuanxuesong for init
 */
@Slf4j
@Service
public class BbpmReceivableAdjustBillServiceImpl extends McpBaseServiceImpl<BbpmReceivableAdjustBillEntity> implements IBbpmReceivableAdjustBillService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmReceivableAdjustBillMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmReceivableAdjustBillService baseService;


    @Value("${yecai.feign}")
    private boolean yecaiFeign;


    @Value("${yecai.url}")
    private String yecaiUrl;


    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;


    @Resource
    private RestTemplateUtil restTemplateUtil ;
    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbpmReceivableAdjustBillVo vo) {
        if(vo == null) {
            return null;
        }

        BbpmReceivableAdjustBillEntity entity = new BbpmReceivableAdjustBillEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setId(null);
        if(!baseService.insert(entity)) {
            log.error("应收调整账单表新增失败:" + entity.toString());
            throw new McpException("应收调整账单表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getId(),1)) {
                log.error("应收调整账单表新增后保存历史失败:" + entity.toString());
                throw new McpException("应收调整账单表新增后保存历史失败");
            }

            log.debug("应收调整账单表新增成功:"+entity.getId());
            return entity.getId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmReceivableAdjustBillVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmReceivableAdjustBillEntity> entityList = new ArrayList<>();
        for (BbpmReceivableAdjustBillVo item:voList) {
            BbpmReceivableAdjustBillEntity entity = new BbpmReceivableAdjustBillEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmReceivableAdjustBillEntity item:entityList){
            item.setId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("应收调整账单表新增失败");
            throw new McpException("应收调整账单表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbpmReceivableAdjustBillEntity::getId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("应收调整账单表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("应收调整账单表批量新增后保存历史失败");
            }

            log.debug("应收调整账单表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param id 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String id) {
        if(!StringUtils.isEmpty(id)) {
            if(!baseService.saveOperationHisById(id,3)) {
                log.error("应收调整账单表删除后保存历史失败:" + id);
                throw new McpException("应收调整账单表删除后保存历史失败");
            }

            if(!baseService.removeById(id)) {
                log.error("应收调整账单表删除失败");
                throw new McpException("应收调整账单表删除失败"+id);
            }
        } else {
            throw new McpException("应收调整账单表删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param idList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> idList) {
        if(!CollectionUtils.isEmpty(idList)) {
            int oldSize = idList.size();
            idList = idList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(idList) || oldSize != idList.size()) {
                throw new McpException("应收调整账单表批量删除失败 存在主键id为空的记录"+StringUtils.join(idList));
            }

            if(!baseService.saveOperationHisByIds(idList,3)) {
                log.error("应收调整账单表批量删除后保存历史失败:" + StringUtils.join(idList));
                throw new McpException("应收调整账单表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(idList)) {
                log.error("应收调整账单表批量删除失败");
                throw new McpException("应收调整账单表批量删除失败"+StringUtils.join(idList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的应收调整账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmReceivableAdjustBillVo vo) {
        if(vo != null) {
            BbpmReceivableAdjustBillEntity entity = new BbpmReceivableAdjustBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getId())) {
                throw new McpException("应收调整账单表更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("应收调整账单表更新失败");
                throw new McpException("应收调整账单表更新失败"+entity.getId());
            } else {
                if(!baseService.saveOperationHisById(entity.getId(),2)) {
                    log.error("应收调整账单表更新后保存历史失败:" + entity.getId());
                    throw new McpException("应收调整账单表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("应收调整账单表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的应收调整账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmReceivableAdjustBillVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReceivableAdjustBillEntity> entityList = new ArrayList<>();

            for (BbpmReceivableAdjustBillVo item:voList){
                BbpmReceivableAdjustBillEntity entity = new BbpmReceivableAdjustBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("应收调整账单表批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("应收调整账单表批量更新失败");
                throw new McpException("应收调整账单表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getId())).map(BbpmReceivableAdjustBillEntity::getId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("应收调整账单表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("应收调整账单表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的应收调整账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmReceivableAdjustBillVo vo) {
        if(vo != null) {
            BbpmReceivableAdjustBillEntity entity = new BbpmReceivableAdjustBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("应收调整账单表保存失败");
                throw new McpException("应收调整账单表保存失败"+entity.getId());
            } else {
                if(!baseService.saveOperationHisById(entity.getId(),4)) {
                    log.error("应收调整账单表保存后保存历史失败:" + entity.getId());
                    throw new McpException("应收调整账单表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("应收调整账单表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的应收调整账单表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmReceivableAdjustBillVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbpmReceivableAdjustBillEntity> entityList = new ArrayList<>();

            for (BbpmReceivableAdjustBillVo item:voList){
                BbpmReceivableAdjustBillEntity entity = new BbpmReceivableAdjustBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("应收调整账单表批量保存失败");
                throw new McpException("应收调整账单表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getId())).map(BbpmReceivableAdjustBillEntity::getId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("应收调整账单表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("应收调整账单表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmReceivableAdjustBillVo selectByIdRecord(String id) {
        BbpmReceivableAdjustBillVo vo = new BbpmReceivableAdjustBillVo();

        if(!StringUtils.isEmpty(id)) {
            BbpmReceivableAdjustBillEntity entity = baseService.selectById(id);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmReceivableAdjustBillPageResultVo>> selectByPageRecord(BbpmReceivableAdjustBillPageVo vo) {
        List<BbpmReceivableAdjustBillPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 试算
     * @param vo
     * @return
     */
    @Override
    public List<CalculationResultVo> adjustBillView(CalculationParamVo vo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<CalculationParamVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        //log.info("3.61.应收调整试算接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.61.应收调整试算接口请求参数json:" + jsonRequest);

        ChargeRespondVo<List<CalculationResultVo>> result = null;

        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("3.61.应收调整试算接口, 请求参数(工银feign)：" + mapper.writeValueAsString(parentRequest));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result =  bfipChargeFeignClient.calculation(parentRequest);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/trial/calculation";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(parentRequest));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }

        log.info("3.61.应收调整试算接口,工银返回:"+ JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }
        return result.getData();
    }

    /**
     * 更新账单
     *
     * @param vo
     * @return
     */
    @Override
    public Boolean updateBill(CalculationParamVo vo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<CalculationParamVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(vo);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        //log.info("3.61.应收调整试算接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.5.合同变更单据更新接口-应收调整请求参数json:" + jsonRequest);

        ChargeRespondVo<List<CalculationResultVo>> result = null;

        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("3.5.合同变更单据更新接口-应收调整, 请求参数(工银feign)：" + mapper.writeValueAsString(parentRequest));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.updateBill(parentRequest);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/updateByContract";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(parentRequest));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }

        log.info("3.5.合同变更单据更新接口-应收调整,工银返回:" + JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }
        return true;
    }
}
