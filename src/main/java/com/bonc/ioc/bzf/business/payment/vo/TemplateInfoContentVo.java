package com.bonc.ioc.bzf.business.payment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 模板信息内容
 *
 * <AUTHOR>
 * @date 2022-06-15 19:04
 * @change 2022-06-15 19:04 by sqj for init
 */
@Data
public class TemplateInfoContentVo {

    @ApiModelProperty(value = "用户中心模板标题")
    private String title;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty(value = "模板id")
    private String templateId;

    @ApiModelProperty(value = "模板状态：0 失效，1 生效")
    private String status;

    @ApiModelProperty(value = "消息模板类型 viewOrder-看房预约；viewNotice-看房通知; selectionNotice-选房通知；signingNotice-签约通知；checkInNotice-入住通知")
    private String sendMessageType;
}
