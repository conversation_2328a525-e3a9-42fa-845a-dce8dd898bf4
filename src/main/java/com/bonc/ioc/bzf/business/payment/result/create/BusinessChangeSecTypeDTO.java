package com.bonc.ioc.bzf.business.payment.result.create;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 商业业态变更（businessChangeSecTypeDTO）
 */
@Data
public class BusinessChangeSecTypeDTO implements Serializable {
    @ApiModelProperty(value = "变更合同号")
    private String contractCode;//变更合同号	String

    @ApiModelProperty(value = "协议号")
    private String agreementCode;
    @ApiModelProperty(value = "二级业态")
    private String secBusinessType;//二级业态	String	是  01餐饮、02医疗保健、03住宿、04养老、05便民服务、06办公、07休闲娱乐、08培训教育
}
