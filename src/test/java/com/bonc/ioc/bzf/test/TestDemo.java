package com.bonc.ioc.bzf.test;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;

import com.alibaba.druid.filter.config.ConfigTools;
import com.bonc.ioc.bzf.AbstractServiceTest;
import com.bonc.ioc.common.base.mybatisplus.generator.CodeGeneration;
import com.bonc.ioc.common.util.SpringContextUtils;

/**
 * test demo
 *
 * <AUTHOR>
 * @date 2021/5/20 10:35
 * @change: 2021/5/20 10:35 by jin.xu for init
 */
public class TestDemo extends AbstractServiceTest {
//    @Test
//    public void dataBasePasswordEncode() throws Exception {
//        String password = "bzf_manager";
//        String[] arr = ConfigTools.genKeyPair(512);
//        System.out.println("password : " + password);
//        System.out.println("privateKey : " + arr[0]);
//        System.out.println("publicKey : " + arr[1]);
//        System.out.println("password : " + ConfigTools.encrypt(arr[0], password));
//    }

    @Test
    public void codeGeneration() {
        CodeGeneration codeGeneration = SpringContextUtils.getBean(CodeGeneration.class);
        List<String> tableNameList = new ArrayList<>();
        tableNameList.add("bbpm_supplementary_preview_bill");
        String packageName = "com.bonc.ioc.bzf.business.supplementary";
        String author = "pyj";
        codeGeneration.processMysqlBase(author, tableNameList, packageName);
    }
}
