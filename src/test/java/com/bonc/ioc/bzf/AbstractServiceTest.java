package com.bonc.ioc.bzf;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.bonc.ioc.bzf.business.payment.BzfBusinessPaymentApplication;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = BzfBusinessPaymentApplication.class,
                webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public abstract class AbstractServiceTest {

}
