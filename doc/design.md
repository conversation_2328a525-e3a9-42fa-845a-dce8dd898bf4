# 设计文档

## 概述

商业违约金系统是一个基于 Spring Boot 的微服务应用，用于处理逾期付款的违约金计算和管理。系统采用分层架构，包含定时任务处理、违约金计算引擎、数据管理和用户界面四个主要部分。

## 架构

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   定时任务      │    │   外部服务      │
│                 │    │                 │    │                 │
│ - 试算账单列表  │    │ - 每日违约金    │    │ - 合同中心      │
│ - 完结账单管理  │    │   计算任务      │    │ - 工银账单      │
│ - 违约金台账    │    │ - 断点续传      │    │ - 缴费中心      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────┐
         │                  应用层                             │
         │                                                     │
         │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
         │ │ Controller  │ │ Service     │ │ Schedule    │    │
         │ │             │ │             │ │             │    │
         │ │ - 账单管理  │ │ - 违约金    │ │ - 定时任务  │    │
         │ │ - 台账管理  │ │   计算服务  │ │ - 任务调度  │    │
         │ │ - 详情查看  │ │ - 数据处理  │ │             │    │
         │ └─────────────┘ └─────────────┘ └─────────────┘    │
         └─────────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────┐
         │                  数据层                             │
         │                                                     │
         │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
         │ │ DAO/Mapper  │ │ Entity      │ │ Feign       │    │
         │ │             │ │             │ │ Client      │    │
         │ │ - 违约金    │ │ - 试算账单  │ │ - 合同中心  │    │
         │ │   数据访问  │ │ - 完结账单  │ │ - 工银接口  │    │
         │ │             │ │ - 台账记录  │ │ - 缴费接口  │    │
         │ └─────────────┘ └─────────────┘ └─────────────┘    │
         └─────────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────┐
         │                  存储层                             │
         │                                                     │
         │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │
         │ │ MySQL       │ │ Redis       │ │ 任务状态    │    │
         │ │ 数据库      │ │ 缓存        │ │ 存储        │    │
         │ └─────────────┘ └─────────────┘ └─────────────┘    │
         └─────────────────────────────────────────────────────┘
```

### 技术栈

- **框架**: Spring Boot 2.x, Spring Cloud
- **数据库**: MySQL 8.0
- **缓存**: Redis (Redisson)
- **任务调度**: Spring Scheduling
- **服务调用**: OpenFeign
- **ORM**: MyBatis
- **构建工具**: Maven

## 业务流程和表关联

### 违约金处理完整流程

```mermaid
graph TD
    A[定时任务启动] --> B[查询逾期账单]
    B --> C[创建试算账单]
    C --> D[计算违约金]
    D --> E[生成每日明细]
    E --> F{是否满足完结条件}
    F -->|是| G[移至完结账单]
    F -->|否| H[继续试算]
    H --> D
    G --> I{处置类型}
    I -->|人工处置| J[标记已处置]
    I -->|移至台账| K[创建台账记录]
    J --> L[流程结束]
    K --> L
```

### 表关联优化说明

#### 1. 数据冗余策略

为了提高查询性能，在下游表中适当冗余关键业务字段：

- **完结账单表**: 冗余试算账单的核心业务字段（项目名称、商户名称、合同号等）
- **台账表**: 冗余完结账单和试算账单的关键字段
- **好处**: 减少多表关联查询，提高列表页面的查询性能

#### 2. 数据关联设计

- 通过业务逻辑保证数据的引用完整性
- 使用索引优化关联查询性能
- 便于数据追溯和审计

#### 3. 状态同步机制

- 试算账单状态变更时，自动触发完结账单创建
- 完结账单处置时，自动更新相关状态
- 通过数据库触发器或应用层事务保证一致性

## 组件和接口

### 1. 定时任务组件 (Schedule)

#### PenaltyFeeCalculationTask

```java
@Component
public class PenaltyFeeCalculationTask {
    @Scheduled(cron = "${penalty.calculation.cron:0 0 1 * * ?}")
    public void calculateDailyPenaltyFees();
}
```

**职责:**

- 每日凌晨执行违约金计算
- 支持断点续传功能
- 处理异常和重试机制

### 2. 服务层组件 (Service)

#### IPenaltyFeeTrialService

```java
public interface IPenaltyFeeTrialService {
    // 计算违约金试算账单
    void calculateTrialBills();

    // 分页查询试算账单
    PageResult<PenaltyFeeTrialBillVo> queryTrialBills(PenaltyFeeTrialQueryVo query);

    // 查看违约金计算明细
    PenaltyFeeDetailVo getCalculationDetail(String billId);

    // 查看违约金分阶段汇总明细
    List<PenaltyFeeStageSummaryVo> getStageSummary(String billId);

    // 查看违约金每日计算明细
    List<PenaltyFeeDailyDetailVo> getDailyDetails(String billId);

    // 批量移动到完结账单
    void moveToFinalizedBills(List<String> billIds);

    // 单个账单移动到完结账单
    void moveToFinalizedBill(String billId, String finalizedReason);

    // 自动完结满足条件的试算账单
    void autoFinalizeTrialBills();

    // 基于对账状态变化完结账单
    void finalizeByReconciliation(String trialBillId);

    // 查询满足自动完结条件的账单
    List<String> getAutoFinalizeBills();
}
```

#### IPenaltyFeeFinalizedService

```java
public interface IPenaltyFeeFinalizedService {
    // 分页查询完结账单
    PageResult<PenaltyFeeFinalizedBillVo> queryFinalizedBills(PenaltyFeeFinalizedQueryVo query);

    // 根据ID查询完结账单详情
    PenaltyFeeFinalizedBillVo getFinalizedBillById(String id);

    // 根据试算账单ID查询完结账单
    PenaltyFeeFinalizedBillVo getFinalizedBillByTrialBillId(String trialBillId);

    // 批量处置完结账单
    void processFinalizedBills(List<String> billIds, String processType, String processRemark);

    // 单个完结账单处置
    void processFinalizedBill(String billId, String processType, String processRemark, String processUser);

    // 批量移动到违约金台账
    void moveToLedger(List<String> billIds);

    // 单个账单移动到台账
    void moveToLedger(String billId, String createUser);

    // 统计完结账单数量
    Long countFinalizedBills(PenaltyFeeFinalizedQueryVo query);
}
```

#### IPenaltyFeeLedgerService

```java
public interface IPenaltyFeeLedgerService {
    // 分页查询违约金台账
    PageResult<PenaltyFeeLedgerVo> queryLedger(PenaltyFeeLedgerQueryVo query);

    // 导出台账数据
    void exportLedger(PenaltyFeeLedgerQueryVo query);
}
```

#### IPenaltyFeeCalculationService

```java
public interface IPenaltyFeeCalculationService {
    // 计算单个账单的违约金
    PenaltyFeeCalculationResult calculatePenaltyFee(BillInfo billInfo, List<PaymentRecord> payments);

    // 分阶段计算违约金
    List<PenaltyFeeStage> calculateStages(BillInfo billInfo, List<PaymentRecord> payments);

    // 获取逾期率配置
    BigDecimal getOverdueRate(String contractNo);

    // 重新计算违约金（用于对账状态变化）
    PenaltyFeeCalculationResult recalculatePenaltyFee(String billId, String reconciliationStatus);

    // 检查收款单对账状态变化
    void checkReconciliationStatusChange();
}
```

### 3. 外部服务接口 (Feign Client)

#### ContractCenterFeignClient

```java
@FeignClient(value = "contract-center")
public interface ContractCenterFeignClient {
    // 获取合同列表
    @GetMapping("/api/contracts")
    List<ContractInfo> getContracts();

    // 获取合同逾期率配置
    @GetMapping("/api/contracts/{contractNo}/overdue-rate")
    BigDecimal getOverdueRate(@PathVariable String contractNo);
}
```

#### BankBillFeignClient

```java
@FeignClient(value = "bank-bill-service")
public interface BankBillFeignClient {
    // 根据合同号查询逾期账单
    @GetMapping("/api/bills/overdue")
    List<BankBillInfo> getOverdueBills(@RequestParam String contractNo,
                                      @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate queryDate);

    // 根据账单ID查询收款单
    @GetMapping("/api/payments/by-bill")
    List<PaymentRecord> getPaymentsByBill(@RequestParam String billId);
}
```

### 4. 控制器组件 (Controller)

#### PenaltyFeeTrialController

```java
@RestController
@RequestMapping("/api/penalty-fee/trial")
public class PenaltyFeeTrialController {

    @GetMapping("/bills")
    public ApiResponse<PageResult<PenaltyFeeTrialBillVo>> queryTrialBills(@SpringQueryMap PenaltyFeeTrialQueryVo query);

    @GetMapping("/bills/{billId}/detail")
    public ApiResponse<PenaltyFeeDetailVo> getCalculationDetail(@PathVariable String billId);

    @GetMapping("/bills/{billId}/stage-summary")
    public ApiResponse<List<PenaltyFeeStageSummaryVo>> getStageSummary(@PathVariable String billId);

    @GetMapping("/bills/{billId}/daily-details")
    public ApiResponse<List<PenaltyFeeDailyDetailVo>> getDailyDetails(@PathVariable String billId);

    @PostMapping("/bills/move-to-finalized")
    public ApiResponse<Void> moveToFinalizedBills(@RequestBody MoveToFinalizedRequest request);

    @PostMapping("/bills/{billId}/finalize")
    public ApiResponse<Void> finalizeBill(@PathVariable String billId, @RequestBody FinalizeRequest request);

    @GetMapping("/bills/auto-finalize-candidates")
    public ApiResponse<List<String>> getAutoFinalizeCandidates();

    @PostMapping("/bills/auto-finalize")
    public ApiResponse<Void> executeAutoFinalize();
}

// 请求对象定义
class MoveToFinalizedRequest {
    private List<String> billIds;
    private String reason; // 可选的完结原因说明

    // getters and setters...
}

class FinalizeRequest {
    private String reason = "MANUAL_FINALIZE";
    private String remark; // 完结备注

    // getters and setters...
}
```

#### PenaltyFeeFinalizedController

```java
@RestController
@RequestMapping("/api/penalty-fee/finalized")
public class PenaltyFeeFinalizedController {

    @GetMapping("/bills")
    public ApiResponse<PageResult<PenaltyFeeFinalizedBillVo>> queryFinalizedBills(@SpringQueryMap PenaltyFeeFinalizedQueryVo query);

    @GetMapping("/bills/{billId}")
    public ApiResponse<PenaltyFeeFinalizedBillVo> getFinalizedBillById(@PathVariable String billId);

    @PostMapping("/bills/process")
    public ApiResponse<Void> processFinalizedBills(@RequestBody ProcessFinalizedRequest request);

    @PostMapping("/bills/{billId}/process")
    public ApiResponse<Void> processFinalizedBill(@PathVariable String billId, @RequestBody ProcessRequest request);

    @PostMapping("/bills/move-to-ledger")
    public ApiResponse<Void> moveToLedger(@RequestBody MoveToLedgerRequest request);

    @GetMapping("/bills/count")
    public ApiResponse<Long> countFinalizedBills(@SpringQueryMap PenaltyFeeFinalizedQueryVo query);
}

// 请求对象定义
class ProcessFinalizedRequest {
    private List<String> billIds;
    private String processType; // PROCESSED, MOVED_TO_LEDGER
    private String processRemark;

    // getters and setters...
}

class ProcessRequest {
    private String processType;
    private String processRemark;

    // getters and setters...
}

class MoveToLedgerRequest {
    private List<String> billIds;

    // getters and setters...
}
```

## 数据模型

### 字段命名说明

在实现过程中，请注意以下字段命名约定：

- **数据库表字段**：使用下划线命名法，与工银接口字段保持一致
  - 如：`bill_id`、`contract_code`、`tenant_code`、`tenant_name`、`project_id`、`house_name`
  - 特殊字段：`bill_cycle`（账单周期使用 billCycle 而非 chargeSubjectPeriod）
- **Java 实体类字段**：使用驼峰命名法
  - 如：`billId`、`contractCode`、`tenantCode`、`tenantName`、`projectId`、`houseName`、`billCycle`
- **VO 类和 DTO 类**：与实体类保持一致，使用驼峰命名法

### 工银接口字段映射

主要字段映射关系：

- `billId` ↔ `bill_id` (账单 ID)
- `contractCode` ↔ `contract_code` (合同编号)
- `tenantCode` ↔ `tenant_code` (租户 ID)
- `tenantName` ↔ `tenant_name` (租户名称/商户名称)
- `projectId` ↔ `project_id` (项目 ID)
- `projectName` ↔ `project_name` (项目名称)
- `houseName` ↔ `house_name` (房源名称/房源地址)
- `chargeSubjectPeriod` ↔ `bill_cycle` (账单周期，注意：数据库使用 billCycle 字段名)
- `chargeSubjectBeginDate` ↔ `charge_subject_begin_date` (收费科目起始日期)
- `chargeSubjectEndDate` ↔ `charge_subject_end_date` (收费科目终止日期)
- `chargeSubjectPeriod` ↔ `charge_subject_period` (账单周期数值，int 类型，从 1 开始递增)
- `payableDate` ↔ `payable_date` (应缴费日期)
- `shouldPayAmount` ↔ `should_pay_amount` (应缴金额)
- `payedAmount` ↔ `payed_amount` (实缴金额)
- `replacePayAmount` ↔ `replace_pay_amount` (待缴金额)
- `billStatus` ↔ `bill_status` (账单缴费状态)
- `accountStatus` ↔ `account_status` (对账状态)

### 1. 违约金试算账单 (bbpm_penalty_fee_trial_bill)

```sql
CREATE TABLE `bbpm_penalty_fee_trial_bill` (
    `trial_bill_id` varchar(64) NOT NULL COMMENT '试算账单ID',
    `bill_id` varchar(20) NOT NULL COMMENT '账单ID（工银账单ID）',
    `bill_cycle` varchar(30) DEFAULT NULL COMMENT '账单周期',
    `project_name` varchar(50) DEFAULT NULL COMMENT '项目名称',
    `house_name` varchar(200) DEFAULT NULL COMMENT '房源名称（房源地址）',
    `tenant_code` varchar(64) DEFAULT NULL COMMENT '租户ID',
    `tenant_name` varchar(50) DEFAULT NULL COMMENT '租户名称（商户名称）',
    `contract_code` varchar(30) DEFAULT NULL COMMENT '合同编号',
    `charge_subject_begin_date` date DEFAULT NULL COMMENT '收费科目起始日期',
    `charge_subject_end_date` date DEFAULT NULL COMMENT '收费科目终止日期',
    `charge_subject_period` int(11) DEFAULT NULL COMMENT '账单周期数值（从1开始递增）',
    `payable_date` date DEFAULT NULL COMMENT '应缴费日期',
    `should_pay_amount` decimal(18,2) DEFAULT NULL COMMENT '应缴金额',
    `payed_amount` decimal(18,2) DEFAULT NULL COMMENT '实缴金额',
    `replace_pay_amount` decimal(18,2) DEFAULT NULL COMMENT '待缴金额（未缴金额）',
    `overdue_days` int(11) DEFAULT NULL COMMENT '逾期天数',
    `daily_overdue_rate` decimal(5,2) DEFAULT NULL COMMENT '每日逾期率',
    `total_penalty_amount` decimal(18,2) DEFAULT NULL COMMENT '违约金总金额',
    `bill_charge_subject` varchar(2) DEFAULT NULL COMMENT '账单对应的收费科目',
    `bill_status` varchar(2) DEFAULT NULL COMMENT '账单缴费状态：01-已缴足额支付，02-已缴部分支付，03-未缴',
    `account_status` varchar(2) DEFAULT NULL COMMENT '对账状态：01-对齐，02-未对齐',
    `status` varchar(1) DEFAULT '1' COMMENT '状态：1-试算中，2-已完结',
    `del_flag` varchar(1) DEFAULT '1' COMMENT '有效标识',
    `project_id` varchar(64) DEFAULT NULL COMMENT '项目ID',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户',
    `cid` bigint(20) DEFAULT NULL COMMENT '变化ID',
    `object_version_number` int(11) DEFAULT NULL COMMENT '行版本',
    PRIMARY KEY (`trial_bill_id`) USING BTREE,
    INDEX `idx_project_id` (`project_id`) USING BTREE COMMENT '项目ID索引',
    INDEX `idx_contract_code` (`contract_code`) USING BTREE COMMENT '合同编号索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违约金试算账单';
```

### 2. 违约金计费完结账单 (bbpm_penalty_fee_finalized_bill)

```sql
CREATE TABLE `bbpm_penalty_fee_finalized_bill` (
    `finalized_bill_id` varchar(64) NOT NULL COMMENT '完结账单ID',
    `trial_bill_id` varchar(64) DEFAULT NULL COMMENT '试算账单ID',
    `bill_id` varchar(20) DEFAULT NULL COMMENT '账单ID（工银账单ID）',
    `bill_cycle` varchar(30) DEFAULT NULL COMMENT '账单周期',
    `project_name` varchar(50) DEFAULT NULL COMMENT '项目名称',
    `house_name` varchar(200) DEFAULT NULL COMMENT '房源名称（房源地址）',
    `tenant_code` varchar(64) DEFAULT NULL COMMENT '租户ID',
    `tenant_name` varchar(50) DEFAULT NULL COMMENT '租户名称（商户名称）',
    `contract_code` varchar(30) DEFAULT NULL COMMENT '合同编号',
    `charge_subject_begin_date` date DEFAULT NULL COMMENT '收费科目起始日期',
    `charge_subject_end_date` date DEFAULT NULL COMMENT '收费科目终止日期',
    `charge_subject_period` int(11) DEFAULT NULL COMMENT '账单周期数值（从1开始递增）',
    `payable_date` date DEFAULT NULL COMMENT '应缴费日期',
    `should_pay_amount` decimal(18,2) DEFAULT NULL COMMENT '应缴金额',
    `payed_amount` decimal(18,2) DEFAULT NULL COMMENT '实缴金额',
    `actual_pay_date` date DEFAULT NULL COMMENT '实际缴费日期（最后一次收款的银行回单日期）',
    `replace_pay_amount` decimal(18,2) DEFAULT NULL COMMENT '待缴金额（未缴金额）',
    `overdue_days` int(11) DEFAULT NULL COMMENT '逾期天数',
    `daily_overdue_rate` decimal(5,2) DEFAULT NULL COMMENT '每日逾期率',
    `total_penalty_amount` decimal(18,2) DEFAULT NULL COMMENT '违约金总金额',
    `reduction_amount` decimal(18,2) DEFAULT NULL COMMENT '减免金额',
    `bill_generation_amount` decimal(18,2) DEFAULT NULL COMMENT '生成违约金账单金额',
    `bill_charge_subject` varchar(2) DEFAULT NULL COMMENT '账单对应的收费科目',
    `finalized_reason` varchar(1) DEFAULT NULL COMMENT '完结原因：1-已缴足额支付，2-退租办结',
    `finalized_date` date DEFAULT NULL COMMENT '完结日期',
    `penalty_disposal_no` varchar(20) DEFAULT NULL COMMENT '违约金处置编号',
    `disposal_status` varchar(1) DEFAULT '1' COMMENT '处置状态：1-未处置，2-处置中，3-已处置',
    `disposal_type` varchar(1) DEFAULT NULL COMMENT '处置类型：1-减免违约金，2-不减免违约金',
    `process_time` datetime DEFAULT NULL COMMENT '处置时间',
    `process_remark` varchar(500) DEFAULT NULL COMMENT '处置备注',
    `del_flag` varchar(1) DEFAULT '1' COMMENT '有效标识',
    `project_id` varchar(64) DEFAULT NULL COMMENT '项目ID',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '系统租户ID',
    `cid` bigint(20) DEFAULT NULL COMMENT '变化ID',
    `object_version_number` int(11) DEFAULT NULL COMMENT '行版本',
    PRIMARY KEY (`finalized_bill_id`) USING BTREE,
    INDEX `idx_penalty_disposal_no` (`penalty_disposal_no`) USING BTREE COMMENT '违约金处置编号索引，用于台账关联',
    INDEX `idx_project_id` (`project_id`) USING BTREE COMMENT '项目ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违约金计费完结账单';
```

### 3. 违约金台账 (bbpm_penalty_fee_ledger)

```sql
CREATE TABLE `bbpm_penalty_fee_ledger` (
    `ledger_id` varchar(64) NOT NULL COMMENT '台账ID',
    `penalty_disposal_no` varchar(20) DEFAULT NULL COMMENT '违约金处置编号',
    `status` int(4) DEFAULT NULL COMMENT '状态：0-暂存，1-审批中，2-已通过，3-未通过',
    `project_id` varchar(64) DEFAULT NULL COMMENT '项目ID',
    `project_name` varchar(50) DEFAULT NULL COMMENT '项目名称',
    `tenant_code` varchar(64) DEFAULT NULL COMMENT '租户ID',
    `tenant_name` varchar(50) DEFAULT NULL COMMENT '租户名称（商户名称）',
    `house_name` varchar(200) DEFAULT NULL COMMENT '房源名称（房源地址）',
    `contract_code` varchar(30) DEFAULT NULL COMMENT '合同编号',
    `disposal_type` varchar(1) DEFAULT NULL COMMENT '处置类型：1-减免违约金，2-不减免违约金',
    `reduction_amount` decimal(18,2) DEFAULT NULL COMMENT '减免金额',
    `bill_generation_amount` decimal(18,2) DEFAULT NULL COMMENT '生成违约金账单金额',
    `disposal_basis_file_id` varchar(500) DEFAULT NULL COMMENT '违约金处置依据文件ID',
    `disposal_basis_description` text COMMENT '违约金处置依据说明',
    `del_flag` varchar(1) DEFAULT '1' COMMENT '有效标识',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '系统租户ID',
    `cid` bigint(20) DEFAULT NULL COMMENT '变化ID',
    `object_version_number` int(11) DEFAULT NULL COMMENT '行版本',
    PRIMARY KEY (`ledger_id`) USING BTREE,
    UNIQUE INDEX `uk_penalty_disposal_no` (`penalty_disposal_no`) USING BTREE COMMENT '违约金处置编号唯一索引',
    INDEX `idx_project_id` (`project_id`) USING BTREE COMMENT '项目ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违约金台账';
```

### 4. 违约金每日计算明细 (bbpm_penalty_fee_daily_detail)

```sql
CREATE TABLE `bbpm_penalty_fee_daily_detail` (
    `daily_detail_id` varchar(64) NOT NULL COMMENT '每日明细ID',
    `bill_id` varchar(20) DEFAULT NULL COMMENT '账单ID（工银账单ID）',
    `calculation_date` date DEFAULT NULL COMMENT '计算日期',
    `replace_pay_amount` decimal(18,2) DEFAULT NULL COMMENT '当日待缴金额（未缴金额）',
    `daily_overdue_rate` decimal(5,2) DEFAULT NULL COMMENT '每日逾期率',
    `daily_penalty_amount` decimal(18,2) DEFAULT NULL COMMENT '当日违约金金额（可为负数）',
    `entry_type` varchar(1) DEFAULT NULL COMMENT '记录类型：1-正常计算，2-调整红冲',
    `adjustment_reason` text COMMENT '调整原因',
    `bill_status` varchar(2) DEFAULT NULL COMMENT '账单缴费状态：01-已缴足额支付，02-已缴部分支付，03-未缴',
    `account_status` varchar(2) DEFAULT NULL COMMENT '对账状态：01-对齐，02-未对齐',
    `charge_time` date DEFAULT NULL COMMENT '收款时间（银行回单日期，记录影响当日计算的收款时间）',
    `original_entry_id` varchar(64) DEFAULT NULL COMMENT '原始记录ID（红冲时关联到daily_detail_id）',
    `adjustment_seq` int(11) DEFAULT '1' COMMENT '调整序号（同一天多次调整时递增，正常计算记录固定为1）',
    `del_flag` varchar(1) DEFAULT '1' COMMENT '有效标识',
    `project_id` varchar(64) DEFAULT NULL COMMENT '项目ID',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '系统租户ID',
    `cid` bigint(20) DEFAULT NULL COMMENT '变化ID',
    `object_version_number` int(11) DEFAULT NULL COMMENT '行版本',
    PRIMARY KEY (`daily_detail_id`) USING BTREE,
    UNIQUE INDEX `uk_bill_date_entry_seq` (`bill_id`, `calculation_date`, `entry_type`, `adjustment_seq`) USING BTREE COMMENT '账单ID、计算日期、记录类型和调整序号唯一索引，防止重复计算',
    INDEX `idx_bill_id_date` (`bill_id`, `calculation_date` DESC) USING BTREE COMMENT '账单ID和计算日期复合索引，用于查询账单明细',
    INDEX `idx_bill_id_entry_type` (`bill_id`, `entry_type`) USING BTREE COMMENT '账单ID和记录类型复合索引，用于查询正常计算记录',
    INDEX `idx_original_entry_id` (`original_entry_id`) USING BTREE COMMENT '原始记录ID索引，用于红冲记录关联'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违约金每日计算明细';
```

**设计说明**：

- 试算账单表不存储 `charge_time`，因为一个账单可能有多次收款，每次收款都有不同的银行回单日期
- 每日明细表中的 `charge_time` 记录影响当日违约金计算的具体收款时间
- 这样设计可以准确追踪每次收款对违约金计算的影响

**重要日期字段说明**：

- 违约金计算截止日期：可通过查询 `bbpm_penalty_fee_daily_detail` 表中最后一条正常计算记录获得
- `finalized_date`：账单完结日期，表示账单从试算状态移至完结状态的日期
- 这样设计避免了数据冗余，确保数据一致性

### 5. 违约金分阶段汇总表 (bbpm_penalty_fee_stage_summary)

```sql
CREATE TABLE `bbpm_penalty_fee_stage_summary` (
    `stage_summary_id` varchar(64) NOT NULL COMMENT '分阶段汇总ID',
    `trial_bill_id` varchar(64) DEFAULT NULL COMMENT '试算账单ID',
    `penalty_start_date` date DEFAULT NULL COMMENT '违约金计算开始日期',
    `penalty_end_date` date DEFAULT NULL COMMENT '违约金计算截止日期（银行回单日期）',
    `overdue_days` int(11) DEFAULT NULL COMMENT '逾期天数',
    `daily_overdue_rate` decimal(5,2) DEFAULT NULL COMMENT '每日逾期率',
    `unpaid_amount` decimal(18,2) DEFAULT NULL COMMENT '欠缴金额',
    `stage_penalty_amount` decimal(18,2) DEFAULT NULL COMMENT '本阶段违约金金额',
    `del_flag` varchar(1) DEFAULT '1' COMMENT '有效标识',
    `project_id` varchar(64) DEFAULT NULL COMMENT '项目ID',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '系统租户ID',
    `cid` bigint(20) DEFAULT NULL COMMENT '变化ID',
    `object_version_number` int(11) DEFAULT NULL COMMENT '行版本',
    PRIMARY KEY (`stage_summary_id`) USING BTREE,
    INDEX `idx_trial_bill_id` (`trial_bill_id`) USING BTREE COMMENT '试算账单ID索引，用于查询账单的分阶段汇总'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违约金分阶段汇总表';
```

**设计说明**：

- 每个试算账单可以有多个阶段，每个阶段对应一个违约金计算时间段
- `stage_no` 按时间顺序递增，便于前端排序显示
- `penalty_end_date` 是该阶段违约金计算的截止日期，通常对应银行回单日期
- 专注于核心的违约金计算信息：时间段、逾期天数、违约金金额
- 移除了状态相关字段（`bill_status`、`account_status`、`stage_type`）以简化设计
- 如需状态信息，可通过关联查询每日明细表或试算账单表获取

### 6. 任务执行状态 (bbpm_penalty_task_status)

```sql
CREATE TABLE `bbpm_penalty_task_status` (
    `task_status_id` varchar(64) NOT NULL COMMENT '任务状态ID',
    `task_date` date DEFAULT NULL COMMENT '任务日期',
    `contract_code` varchar(30) DEFAULT NULL COMMENT '合同编号',
    `process_status` varchar(1) DEFAULT NULL COMMENT '处理状态：1-处理中，2-已完成，3-失败',
    `bill_count` int(11) DEFAULT '0' COMMENT '处理的账单数量',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `error_message` text COMMENT '错误信息',
    `del_flag` int(4) DEFAULT '1' COMMENT '有效标识',
    `project_id` varchar(100) DEFAULT NULL COMMENT '项目ID',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户',
    `cid` bigint(20) DEFAULT NULL COMMENT '变化ID',
    `object_version_number` int(11) DEFAULT NULL COMMENT '行版本',
    PRIMARY KEY (`task_status_id`) USING BTREE,
    UNIQUE INDEX `uk_task_date_contract` (`task_date`, `contract_code`) USING BTREE COMMENT '任务日期和合同编号唯一索引，防止重复任务',
    INDEX `idx_task_date_status` (`task_date`, `process_status`) USING BTREE COMMENT '任务日期和处理状态复合索引，用于断点续传查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='任务执行状态';
```

### 7. 接口调用记录表 (bbpm_penalty_api_call_log)

```sql
CREATE TABLE `bbpm_penalty_api_call_log` (
    `api_call_log_id` varchar(64) NOT NULL COMMENT '接口调用日志ID',
    `task_status_id` varchar(64) DEFAULT NULL COMMENT '任务状态ID（关联bbpm_penalty_task_status表的task_status_id）',
    `task_date` date DEFAULT NULL COMMENT '任务日期',
    `contract_code` varchar(100) DEFAULT NULL COMMENT '合同编号',
    `bill_id` varchar(20) DEFAULT NULL COMMENT '账单ID（收款单接口调用时有值）',
    `api_type` varchar(1) DEFAULT NULL COMMENT '接口类型：1-账单接口，2-收款单接口',
    `api_url` varchar(500) DEFAULT NULL COMMENT '接口URL',
    `request_params` text COMMENT '请求参数（JSON格式）',
    `response_data` longtext COMMENT '响应数据（JSON格式）',
    `call_duration` int(11) DEFAULT NULL COMMENT '调用耗时（毫秒）',
    `call_status` varchar(1) DEFAULT NULL COMMENT '调用状态：1-成功，2-失败',
    `error_message` text COMMENT '错误信息',
    `call_time` datetime DEFAULT NULL COMMENT '调用时间',
    `del_flag` int(4) DEFAULT '1' COMMENT '有效标识',
    `project_id` varchar(100) DEFAULT NULL COMMENT '项目ID',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modify_user` varchar(50) DEFAULT NULL COMMENT '修改人',
    `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户',
    `cid` bigint(20) DEFAULT NULL COMMENT '变化ID',
    `object_version_number` int(11) DEFAULT NULL COMMENT '行版本',
    PRIMARY KEY (`api_call_log_id`) USING BTREE,
    INDEX `idx_task_status_id` (`task_status_id`) USING BTREE COMMENT '任务状态ID索引，用于关联查询',
    INDEX `idx_contract_code_call_time` (`contract_code`, `call_time` DESC) USING BTREE COMMENT '合同编号和调用时间复合索引，用于合同接口调用历史',
    INDEX `idx_bill_id_api_type` (`bill_id`, `api_type`) USING BTREE COMMENT '账单ID和接口类型复合索引，用于账单相关接口调用查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='接口调用记录表';
```

## 索引设计说明

### 索引设计原则

基于需求分析和代码实现中的查询模式，为每个表设计了合适的索引和唯一键约束：

#### 1. 唯一性约束设计

- **业务唯一性**：确保核心业务字段的唯一性，防止重复数据
- **关联完整性**：保证表间关联关系的一对一或一对多约束
- **防重复计算**：防止定时任务重复执行导致的数据重复

#### 2. 查询性能优化

- **分页查询优化**：为常用的分页查询场景创建复合索引
- **条件查询优化**：为 WHERE 条件中的常用字段组合创建索引
- **关联查询优化**：为 JOIN 操作中的关联字段创建索引

#### 3. 索引数量控制

- 每个表控制在 3-5 个索引以内，避免过多索引影响写入性能
- 优先为高频查询场景创建索引
- 复合索引优于多个单列索引

### 各表索引详细说明

#### 试算账单表索引

- `uk_bill_id`: 确保工银账单 ID 唯一性
- `idx_status_create_time`: 支持按状态分页查询
- `idx_project_id`: 支持项目维度查询
- `idx_contract_code`: 支持合同查询

#### 完结账单表索引

- `uk_trial_bill_id`: 确保与试算账单一对一关系
- `uk_bill_id`: 确保工银账单 ID 唯一性
- `idx_penalty_disposal_no`: 支持台账关联查询
- `idx_disposal_status_create_time`: 支持处置状态分页查询
- `idx_project_id`: 支持项目维度查询

#### 台账表索引

- `uk_penalty_disposal_no`: 确保处置编号唯一性
- `idx_status_create_time`: 支持审批状态分页查询
- `idx_project_id`: 支持项目维度查询

#### 每日明细表索引（重点优化）

- `uk_bill_date_entry_seq`: **唯一约束**，防止重复计算同一天的违约金
- `idx_bill_id_date`: 支持账单明细查询和汇总计算
- `idx_bill_id_entry_type`: 支持查询正常计算记录
- `idx_original_entry_id`: 支持红冲记录关联查询

#### 任务状态表索引

- `uk_task_date_contract`: 防止重复任务执行
- `idx_task_date_status`: 支持断点续传查询

#### 接口调用日志表索引

- `idx_task_status_id`: 支持任务关联查询
- `idx_contract_code_call_time`: 支持合同接口调用历史查询
- `idx_bill_id_api_type`: 支持账单相关接口调用查询

#### 分阶段汇总表索引

- `idx_trial_bill_id`: 支持查询账单的分阶段汇总

### 索引使用场景

| 查询场景         | 涉及表         | 使用索引                        | 查询示例                                            |
| ---------------- | -------------- | ------------------------------- | --------------------------------------------------- |
| 试算账单分页查询 | trial_bill     | idx_status_create_time          | WHERE status='1' ORDER BY create_time DESC          |
| 完结账单分页查询 | finalized_bill | idx_disposal_status_create_time | WHERE disposal_status='1' ORDER BY create_time DESC |
| 账单明细查询     | daily_detail   | idx_bill_id_date                | WHERE bill_id='xxx' ORDER BY calculation_date DESC  |
| 违约金汇总计算   | daily_detail   | idx_bill_id_entry_type          | WHERE bill_id='xxx' AND entry_type='1'              |
| 断点续传查询     | task_status    | idx_task_date_status            | WHERE task_date='2024-01-01' AND process_status='2' |
| 防重复计算       | daily_detail   | uk_bill_date_entry_seq          | 插入时检查唯一性约束                                |

## 状态字段说明汇总

### 1. 试算账单状态 (penalty_fee_trial_bill.status)

- `1`: 试算中，正在进行违约金计算
- `2`: 已完结，移至完结账单列表

### 2. 账单缴费状态 (bill_status)

**适用表**: 试算账单表、每日明细表

- `01`: 已缴足额支付，账单已完全缴费
- `02`: 已缴部分支付，账单部分缴费
- `03`: 未缴，账单未缴费

**注意**: 完结账单表不包含此字段，因为完结的账单默认为足额支付状态。

### 3. 对账状态 (account_status)

**适用表**: 试算账单表、每日明细表

- `01`: 对齐，收款已完成对账确认
- `02`: 未对齐，有收款但未完成对账
- `03`: 账单关闭，账单已关闭

**注意**: 完结账单表不包含此字段，因为完结的账单默认为对账确认状态。

### 4. 违约金处置状态 (penalty_fee_finalized_bill.disposal_status)

- `1`: 未处置，等待人工处理
- `2`: 处置中，正在进行处置操作
- `3`: 已处置，已完成处置操作

### 5. 违约金处置类型 (penalty_fee_finalized_bill.disposal_type)

- `1`: 减免违约金，对违约金进行减免处理
- `2`: 不减免违约金，系统自动生成违约金缴费账单

### 6. 完结原因 (penalty_fee_finalized_bill.finalized_reason)

- `1`: 已缴足额支付，账单已完全缴费完结
- `2`: 退租办结，因租户退租导致的账单完结

### 7. 台账审批状态 (penalty_fee_ledger.status)

- `0`: 暂存，数据已保存但未提交审批
- `1`: 审批中，正在进行审批流程
- `2`: 已通过，审批通过可以执行
- `3`: 未通过，审批未通过或被驳回

### 8. 任务处理状态 (penalty_task_status.process_status)

- `1`: 处理中，任务正在执行
- `2`: 已完成，任务成功完成
- `3`: 失败，任务执行失败

### 9. 记录类型 (penalty_fee_daily_detail.entry_type)

- `1`: 正常计算，常规的违约金计算记录
- `2`: 调整红冲，用于纠正错误计算的红冲记录

### 10. 接口类型 (penalty_api_call_log.api_type)

- `1`: 账单接口，查询工银账单信息的接口
- `2`: 收款单接口，查询收款单信息的接口

### 11. 接口调用状态 (penalty_api_call_log.call_status)

- `1`: 成功，接口调用成功
- `2`: 失败，接口调用失败

## 表关联关系说明

### 主要数据流转路径

```
penalty_fee_trial_bill (试算账单)
    ↓ (status: TRIAL → FINALIZED)
penalty_fee_finalized_bill (完结账单)
    ↓ (多个完结账单按penalty_disposal_no分组)
penalty_fee_ledger (违约金台账) - 按处置编号汇总管理
    ↓ (status: 0 → 1 → 2/3)
执行违约金处置操作
```

### 关联关系约束

1. **试算账单 → 完结账单**: 一对一关系，通过 `trial_bill_id` 关联
2. **完结账单 → 台账**: 多对一关系，多个完结账单通过相同的 `penalty_disposal_no` 关联到一个台账记录
3. **试算账单 → 每日明细**: 一对多关系，通过 `bill_id`（工银账单 ID）关联

### 数据一致性保证

1. **外键约束**: 确保关联数据的完整性
2. **状态同步**: 试算账单状态变更时，自动创建完结账单
3. **冗余字段**: 在下游表中冗余关键业务字段，减少关联查询
4. **级联限制**: 使用 `ON DELETE RESTRICT` 防止误删除

## 错误处理

### 1. 定时任务错误处理

- **网络异常**: 实现重试机制，最多重试 3 次
- **数据异常**: 记录错误日志，跳过当前合同继续处理
- **系统异常**: 保存当前进度，支持断点续传

### 2. 计算错误处理

- **数据缺失**: 记录警告日志，使用默认值或跳过
- **计算溢出**: 使用 BigDecimal 进行精确计算
- **逻辑错误**: 详细记录错误信息，便于排查

### 3. 接口调用错误处理

- **服务不可用**: 使用 Feign 的 fallback 机制
- **超时处理**: 配置合理的超时时间和重试策略
- **数据格式错误**: 进行数据校验和转换

## 测试策略

### 1. 单元测试

- **服务层测试**: 测试违约金计算逻辑的准确性
- **工具类测试**: 测试日期计算、金额计算等工具方法
- **数据访问测试**: 测试 DAO 层的数据操作

### 2. 集成测试

- **定时任务测试**: 测试完整的违约金计算流程
- **接口测试**: 测试与外部服务的集成
- **数据库测试**: 测试数据的一致性和完整性

### 3. 性能测试

- **批量处理测试**: 测试大量合同的处理性能
- **并发测试**: 测试系统的并发处理能力
- **内存测试**: 测试长时间运行的内存使用情况

### 4. 业务测试

- **分阶段计算测试**: 测试复杂的分阶段违约金计算
- **断点续传测试**: 测试任务中断后的恢复能力
- **边界条件测试**: 测试各种边界情况的处理
