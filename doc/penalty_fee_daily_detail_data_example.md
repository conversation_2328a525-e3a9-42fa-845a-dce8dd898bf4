# penalty_fee_daily_detail 表数据示例

## 业务场景说明

**账单基础信息：**

- 账单 ID: BILL_001
- 应缴费日期: 2025-01-05
- 应缴金额: 10000 元
- 每日逾期率: 0.05%
- 1 月 10 日收款 3000 元，但对账状态为"未对平"
- 1 月 15 日发现对账状态变为"已对平"，银行回单日期为 1 月 10 日

## 数据变化完整记录

### 第一阶段：正常每日计算（1 月 6 日-1 月 14 日）

**记录 1：** 1 月 6 日定时任务

```sql
id: DETAIL_001
trial_bill_id: BILL_001
calculation_date: 2025-01-06
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNPAID
bank_receipt_date: NULL
original_entry_id: NULL
```

**记录 2：** 1 月 7 日定时任务

```sql
id: DETAIL_002
trial_bill_id: BILL_001
calculation_date: 2025-01-07
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNPAID
bank_receipt_date: NULL
original_entry_id: NULL
```

**记录 3：** 1 月 8 日定时任务

```sql
id: DETAIL_003
trial_bill_id: BILL_001
calculation_date: 2025-01-08
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNPAID
bank_receipt_date: NULL
original_entry_id: NULL
```

**记录 4：** 1 月 9 日定时任务

```sql
id: DETAIL_004
trial_bill_id: BILL_001
calculation_date: 2025-01-09
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNPAID
bank_receipt_date: NULL
original_entry_id: NULL
```

**记录 5：** 1 月 10 日定时任务（收款但未对平）

```sql
id: DETAIL_005
trial_bill_id: BILL_001
calculation_date: 2025-01-10
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNRECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: NULL
```

**记录 6：** 1 月 11 日定时任务（仍未对平）

```sql
id: DETAIL_006
trial_bill_id: BILL_001
calculation_date: 2025-01-11
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNRECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: NULL
```

**记录 7：** 1 月 12 日定时任务

```sql
id: DETAIL_007
trial_bill_id: BILL_001
calculation_date: 2025-01-12
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNRECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: NULL
```

**记录 8：** 1 月 13 日定时任务

```sql
id: DETAIL_008
trial_bill_id: BILL_001
calculation_date: 2025-01-13
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNRECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: NULL
```

**记录 9：** 1 月 14 日定时任务

```sql
id: DETAIL_009
trial_bill_id: BILL_001
calculation_date: 2025-01-14
unpaid_amount: 10000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: 5.00
entry_type: NORMAL
adjustment_reason: NULL
reconciliation_status: UNRECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: NULL
```

### 第二阶段：1 月 15 日发现对账状态变化，执行调整

**调整策略说明：**
由于表中有唯一约束 `UNIQUE KEY uk_bill_date_type (trial_bill_id, calculation_date, entry_type)`，同一个账单在同一天只能有一条 NORMAL 类型的记录。因此，我们采用以下策略：

1. 先插入红冲记录（ADJUSTMENT 类型）
2. 然后更新原有的 NORMAL 记录，而不是插入新的 NORMAL 记录

**记录 10：** 红冲 1 月 10 日多计算的部分

```sql
id: DETAIL_ADJ_001
trial_bill_id: BILL_001
calculation_date: 2025-01-10
unpaid_amount: 3000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: -1.50
entry_type: ADJUSTMENT
adjustment_reason: 对账状态变更，红冲多计算部分(3000元×0.05%×1天)
reconciliation_status: RECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: DETAIL_005
```

**记录 11：** 红冲 1 月 11 日多计算的部分

```sql
id: DETAIL_ADJ_002
trial_bill_id: BILL_001
calculation_date: 2025-01-11
unpaid_amount: 3000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: -1.50
entry_type: ADJUSTMENT
adjustment_reason: 对账状态变更，红冲多计算部分(3000元×0.05%×1天)
reconciliation_status: RECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: DETAIL_006
```

**记录 12：** 红冲 1 月 12 日多计算的部分

```sql
id: DETAIL_ADJ_003
trial_bill_id: BILL_001
calculation_date: 2025-01-12
unpaid_amount: 3000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: -1.50
entry_type: ADJUSTMENT
adjustment_reason: 对账状态变更，红冲多计算部分(3000元×0.05%×1天)
reconciliation_status: RECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: DETAIL_007
```

**记录 13：** 红冲 1 月 13 日多计算的部分

```sql
id: DETAIL_ADJ_004
trial_bill_id: BILL_001
calculation_date: 2025-01-13
unpaid_amount: 3000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: -1.50
entry_type: ADJUSTMENT
adjustment_reason: 对账状态变更，红冲多计算部分(3000元×0.05%×1天)
reconciliation_status: RECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: DETAIL_008
```

**记录 14：** 红冲 1 月 14 日多计算的部分

```sql
id: DETAIL_ADJ_005
trial_bill_id: BILL_001
calculation_date: 2025-01-14
unpaid_amount: 3000.00
daily_overdue_rate: 0.0005
daily_penalty_amount: -1.50
entry_type: ADJUSTMENT
adjustment_reason: 对账状态变更，红冲多计算部分(3000元×0.05%×1天)
reconciliation_status: RECONCILED
bank_receipt_date: 2025-01-10
original_entry_id: DETAIL_009
```

**说明：** 采用精确红冲方式，原有的 NORMAL 记录保持不变，只红冲多计算的部分。

**优势：**

1. **精确调整**：只调整实际的差额部分，避免不必要的全额红冲
2. **数据简洁**：原有记录保持不变，减少数据变更
3. **审计清晰**：红冲记录明确显示调整的具体金额和原因
4. **计算准确**：最终结果完全一致，但过程更精确

## 最终汇总结果

### 总违约金计算

```sql
SELECT SUM(daily_penalty_amount) FROM penalty_fee_daily_detail WHERE trial_bill_id = 'BILL_001';
```

**计算过程：**

- 1 月 6 日-1 月 9 日正常计算：5.00 × 4 = 20.00 元
- 1 月 10 日-1 月 14 日原计算：5.00 × 5 = 25.00 元
- 红冲多计算部分：-1.50 × 5 = -7.50 元

**总计：** 20.00 + 25.00 - 7.50 = **37.50 元**

### 逾期天数计算

```sql
SELECT COUNT(*) FROM penalty_fee_daily_detail
WHERE trial_bill_id = 'BILL_001'
AND entry_type = 'NORMAL'
AND daily_penalty_amount > 0;
```

**计算过程：**

- 1 月 6 日-1 月 9 日：4 天
- 1 月 10 日-1 月 14 日重新计算：5 天

**总计：** 4 + 5 = **9 天**

## 数据汇总表格

| 日期       | 原计算    | 红冲差额  | 当日净额  |
| ---------- | --------- | --------- | --------- |
| 1 月 6 日  | 5.00      | -         | 5.00      |
| 1 月 7 日  | 5.00      | -         | 5.00      |
| 1 月 8 日  | 5.00      | -         | 5.00      |
| 1 月 9 日  | 5.00      | -         | 5.00      |
| 1 月 10 日 | 5.00      | -1.50     | 3.50      |
| 1 月 11 日 | 5.00      | -1.50     | 3.50      |
| 1 月 12 日 | 5.00      | -1.50     | 3.50      |
| 1 月 13 日 | 5.00      | -1.50     | 3.50      |
| 1 月 14 日 | 5.00      | -1.50     | 3.50      |
| **合计**   | **45.00** | **-7.50** | **37.50** |

## 最终数据库状态

经过调整后，`penalty_fee_daily_detail` 表中的最终数据状态如下：

| ID             | 计算日期   | 未缴金额 | 违约金    | 记录类型   | 调整原因       | 原始记录 ID |
| -------------- | ---------- | -------- | --------- | ---------- | -------------- | ----------- |
| DETAIL_001     | 2025-01-06 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_002     | 2025-01-07 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_003     | 2025-01-08 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_004     | 2025-01-09 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_005     | 2025-01-10 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_006     | 2025-01-11 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_007     | 2025-01-12 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_008     | 2025-01-13 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_009     | 2025-01-14 | 10000.00 | 5.00      | NORMAL     | -              | -           |
| DETAIL_ADJ_001 | 2025-01-10 | 3000.00  | **-1.50** | ADJUSTMENT | 红冲多计算部分 | DETAIL_005  |
| DETAIL_ADJ_002 | 2025-01-11 | 3000.00  | **-1.50** | ADJUSTMENT | 红冲多计算部分 | DETAIL_006  |
| DETAIL_ADJ_003 | 2025-01-12 | 3000.00  | **-1.50** | ADJUSTMENT | 红冲多计算部分 | DETAIL_007  |
| DETAIL_ADJ_004 | 2025-01-13 | 3000.00  | **-1.50** | ADJUSTMENT | 红冲多计算部分 | DETAIL_008  |
| DETAIL_ADJ_005 | 2025-01-14 | 3000.00  | **-1.50** | ADJUSTMENT | 红冲多计算部分 | DETAIL_009  |

**关键说明：**

1. **NORMAL 记录**：原有的 DETAIL_005 到 DETAIL_009 记录保持不变，仍然是按 10000 元计算的 5.00 元
2. **ADJUSTMENT 记录**：新增的红冲记录，只红冲多计算的部分（3000 元 ×0.05%=1.50 元）
3. **精确红冲**：避免了全额红冲再重新计算的复杂操作，只调整差额部分
4. **数据简洁**：减少了数据变更，原有记录保持历史真实性
5. **审计清晰**：红冲记录明确显示调整的具体金额和计算依据

## 精确红冲的优势

相比全额红冲方式，精确红冲具有以下优势：

1. **计算精确**：只调整实际的差额部分，避免不必要的全额红冲
2. **数据简洁**：原有记录保持不变，减少数据变更操作
3. **审计友好**：红冲记录明确显示调整的具体原因和金额
4. **性能更好**：减少了数据库的更新操作，提高系统性能
5. **逻辑清晰**：每条红冲记录都有明确的业务含义和计算依据

这个示例完整展示了违约金按天记录和精确红冲调整的完整业务逻辑。
